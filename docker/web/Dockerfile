ARG NGINX_VERSION=1.21.3
ARG NODE_VERSION=16

#######################################
# Development build
#
FROM nginx:${NGINX_VERSION} AS base-build

#######################################
# NPM dependencies
#
FROM node:${NODE_VERSION} AS npm-build

WORKDIR /var/www/html
COPY package.json package-lock.json webpack.mix.js tsconfig.json /var/www/html/
RUN npm ci

COPY ./resources/ /var/www/html/resources/
COPY ./public/ /var/www/html/public
RUN npm run production

#######################################
# Production build
#
FROM base-build AS ci-build

COPY ./docker/web/nginx_template_staging.conf /etc/nginx/conf.d/default.conf
RUN mkdir /etc/nginx/ssl
COPY --chown=www-data --from=npm-build /var/www/html/public/ /var/www/html/public/
# Do I need also to COPY --chown=www-data ./ /var/www/html/

RUN ln -sr /var/www/html/storage/app/public /var/www/html/public/storage

#######################################
# Production build
#
FROM base-build AS production-build

COPY ./docker/web/nginx_template_prod.conf /etc/nginx/conf.d/default.conf
RUN mkdir /etc/nginx/ssl
COPY --chown=www-data --from=npm-build /var/www/html/public/ /var/www/html/public/
# Do I need also to COPY --chown=www-data ./ /var/www/html/
RUN ln -sr /var/www/html/storage/app/public /var/www/html/public/storage
