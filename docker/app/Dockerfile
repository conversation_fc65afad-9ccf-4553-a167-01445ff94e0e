ARG PHP_VERSION=8.0
ARG COMPOSER_VERSION=2.1
ARG NODE_VERSION=16
ARG MYSQL_VERSION=8.0

#######################################
# Composer base
#
FROM composer:${COMPOSER_VERSION} AS composer-base

#######################################
# mysql server base
#
FROM mysql:${MYSQL_VERSION} AS mysql-base

#######################################
# Base build
#
FROM php:${PHP_VERSION}-fpm AS base-build

ENV TZ=Europe/Athens

RUN ln -snf /usr/share/zoneinfo/${TZ} /etc/localtime && echo ${TZ} >/etc/timezone

WORKDIR /var/www/html

RUN apt-get update \
    && apt-get install --quiet --yes --no-install-recommends \
        libzip-dev \
        libpng-dev \
        libldap2-dev \
        libc-client-dev \
        libkrb5-dev \
        libicu-dev \
        unzip \
        # Install built-in PHP extensions
        && docker-php-ext-install gd pdo pdo_mysql zip ldap exif pcntl intl \
        # Extract PHP source to prepare for PECL installation
        && docker-php-source extract \
        # Install and enable the imap extension via PECL (since it's no longer bundled in PHP 8.4)
        && pecl install imap \
        && docker-php-ext-enable imap \
        # Install and enable the redis extension via PECL
        && pecl install redis \
        && docker-php-ext-enable redis \
        # Delete the PHP source to reduce image size
        && docker-php-source delete \
        # Clean up apt caches to reduce image size
        && apt-get clean \
        && rm -rf /var/lib/apt/lists/*

#######################################
# Development build
#
FROM base-build AS development-build

COPY --from=composer-base /usr/bin/composer /usr/bin/composer

# we need this because we have squashed migrations and we use syncdb
RUN apt-get update \
    && apt-get install --quiet --yes --no-install-recommends \
        libncurses6 \
        pv \
        openssh-client \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

COPY --from=mysql-base /usr/bin/mysql /usr/bin/mysql
COPY --from=mysql-base /usr/bin/mysqldump /usr/bin/mysqldump

RUN groupadd --gid 1000 appuser \
    && useradd --uid 1000 -g appuser -G www-data,root --shell /bin/bash --create-home appuser \
    && mkdir -p /home/<USER>/.ssh \
    && chown appuser:appuser /home/<USER>/.ssh \
    && chmod 700 /home/<USER>/.ssh
USER appuser

#######################################
# Development build w/ xdebug
#
FROM development-build AS development-build-xdebug

USER root

RUN pecl install xdebug && docker-php-ext-enable xdebug
#COPY ./docker/app/xdebug.ini ${PHP_INI_DIR}/conf.d/xdebug.ini

USER appuser

#######################################
# Composer dependencies
#
FROM composer-base AS composer-build

WORKDIR /var/www/html

COPY composer.json composer.lock /var/www/html/
RUN composer install --no-dev --prefer-dist --no-scripts --no-autoloader --no-progress --ignore-platform-reqs

#######################################
# NPM dependencies
#
FROM node:${NODE_VERSION} AS npm-build

WORKDIR /var/www/html

COPY package.json package-lock.json webpack.mix.js tsconfig.json /var/www/html/
RUN npm ci

COPY ./resources/ /var/www/html/resources/
COPY ./public/ /var/www/html/public
RUN npm run production

#######################################
# Production build
#
FROM base-build AS production-build

ENV APP_ENV production

# Enabe Tinker REPL for debuging in production
RUN mkdir -p /var/www/.config/psysh \
    && chown -R www-data:www-data /var/www/.config

# Initialize PHP for production
COPY ./docker/app/php.ini-production ${PHP_INI_DIR}/php.ini

# Install and configure opcache
RUN docker-php-ext-install opcache
COPY ./docker/app/opcache.ini ${PHP_INI_DIR}/conf.d/opcache.ini

# Install cron+supervisor+gosu
RUN apt-get update \
    && apt-get install --quiet --yes --no-install-recommends \
        cron \
        supervisor \
        gosu \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Configure cron
COPY ./docker/app/example-crontab /etc/cron.d/crontab
RUN chmod 0644 /etc/cron.d/crontab \
    && crontab /etc/cron.d/crontab \
    && touch /var/log/cron.log

# Configure supervisor
COPY ./docker/app/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Copy production code and adjust ownership
COPY --chown=www-data ./ /var/www/html/
COPY --chown=www-data --from=composer-build /var/www/html/vendor/ /var/www/html/vendor/
COPY --chown=www-data --from=npm-build /var/www/html/public/ /var/www/html/public/
COPY docker/app/app-entrypoint.sh /usr/local/bin/

COPY --from=composer-build /usr/bin/composer /usr/bin/composer
RUN composer dump -o \
    && composer check-platform-reqs \
    && rm -f /usr/bin/composer

# Expose websocket server listening port
EXPOSE 6001

# Set Bugsnag app version
ARG BUGSNAG_APP_VERSION
ENV BUGSNAG_APP_VERSION=${BUGSNAG_APP_VERSION}

USER root

ENTRYPOINT ["app-entrypoint.sh"]

CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]

#######################################
# CI build
#
FROM production-build AS ci-build

COPY --from=composer-base /usr/bin/composer /usr/bin/composer

RUN apt-get update \
    && apt-get install --quiet --yes --no-install-recommends \
        libncurses6 \
        pv \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy mysql client tools from mysql-base
COPY --from=mysql-base /usr/bin/mysql /usr/bin/mysql
COPY --from=mysql-base /usr/bin/mysqldump /usr/bin/mysqldump
