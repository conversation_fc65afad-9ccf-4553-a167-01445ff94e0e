#!/bin/bash

_group="▶ "
_endgroup=""
_color_green="\033[0;32m"
_color_blue="\033[0;34m"
_color_red="\033[0;31m"
_endcolor="\033[0m"

#######################################################
# Read secrets defined in *_FILE variables
# and temporary export them to env
#######################################################
function file_env() {
  local var="$1"
  local fileVar="${var}_FILE"
  local def="${2:-}"
  if [ "${!var:-}" ] && [ "${!fileVar:-}" ]; then
    echo -e "\033[0;31mERROR: Both $var and $fileVar are set (but are exclusive)\033[0m\n" >&2
    exit 1
  fi
  local val="$def"
  if [ "${!var:-}" ]; then
    val="${!var}"
  elif [ "${!fileVar:-}" ]; then
    val="$(<"${!fileVar}")"
  fi
  export "$var"="$val"
  unset "$fileVar"
}

#######################################################
# Write secret to the .env file
#######################################################
function set_env() {
  local key="$1"
  local value
  value=$(printf '%s\n' "$2" | sed -e 's/[]\/$*.^&[]/\\&/g') # https://stackoverflow.com/a/2705678/2235814
  sed -i "s/^${key}=.*/${key}=${value}/" /var/www/html/.env
}

function main() {
  set -e

  echo -e "${_color_blue}INFO: Bootstraping application service...${_endcolor}\n"

  #######################################################
  #
  # Create .env file from secrets
  #
  echo "${_group}Creating production .env file ..."

  # The list of secrets
  local envs=(
    DB_USERNAME
    DB_PASSWORD
    APP_KEY
    LDAP_USERNAME
    LDAP_PASSWORD
    BUGSNAG_API_KEY
    PUBLIC_CONTRACTUALS_API_KEY
    PUSHER_APP_KEY
    PUSHER_APP_SECRET
    MAIL_PASSWORD
  )

  # Read *_FILE secrets and export them to temorary env variables
  for e in "${envs[@]}"; do
    file_env "$e"
  done

  # Overwrite .env file with the value of the temporary env variables
  for e in "${envs[@]}"; do
    set_env "$e" "${!e}"
  done

  # Now that we're definitely done writing configuration, let's clear out the relevant environment
  # variables (so that stray "phpinfo()" calls don't leak secrets from our code)
  for e in "${envs[@]}"; do
    unset "$e"
  done

  echo -e "Secrets added to .env file successfully"
  echo "${_endgroup}"

  #######################################################
  # Setup custom CA certificates
  # This is required to access the sox.culture.gov.gr HTTPS endpoint
  #
  echo "${_group}Setting up custom CA certificates ..."

#   # Ensure correct permissions on certificate files
#   chmod 644 /usr/local/share/ca-certificates/custom/*.crt

  # Update CA certificates
  update-ca-certificates

  echo "${_endgroup}"


  #######################################################
  #
  # Prepare database
  #
  echo "${_group}Preparing database ..."

  gosu www-data php artisan migrate --force

  echo "${_endgroup}"

  #######################################################
  #
  # Generate passport keys
  #
  echo "${_group}Generating passport keys ..."

  gosu www-data php artisan passport:keys

  echo "${_endgroup}"

  #######################################################
  #
  # Cache configuration
  #
  echo "${_group}Caching framework bootstrap files ..."

  gosu www-data php artisan config:cache &&
  gosu www-data php artisan route:cache &&
  gosu www-data php artisan view:cache

  echo "${_endgroup}"

  echo -e "${_color_green}NOTICE: Application is ready to serve requests!${_endcolor}\n"

  exec "$@"
}

main "$@"
