ARG PHP_VERSION
#ARG COMPOSER_VERSION

# TODO from php:8.0-alpine (https://github.com/beyondcode/laravel-websockets/issues/81)
FROM php:${PHP_VERSION}-fpm AS php-fpm-base
#FROM composer:${COMPOSER_VERSION} AS composer-base
#
########################################
## Composer dependencies
##
#FROM composer-base AS composer-build
#
#WORKDIR /app
#
#COPY composer.json composer.lock /app/
#RUN composer install --no-dev --prefer-dist --no-scripts --no-autoloader --no-progress --ignore-platform-reqs

#######################################
# Base build
#
FROM php-fpm-base AS base-build

ENV TZ=Europe/Athens

RUN ln -snf /usr/share/zoneinfo/${TZ} /etc/localtime && echo ${TZ} >/etc/timezone

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    supervisor

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install \
    pdo_mysql \
    mbstring \
    exif \
    pcntl \
    bcmath \
    gd

# Configure supervisor
COPY ./docker/websocket/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Create system user to run Composer and Artisan Commands
RUN useradd -G www-data,root -u 1000 -d /home/<USER>

RUN mkdir -p /home/<USER>/.composer && \
    chown -R ws:ws /home/<USER>

WORKDIR /var/www/html

#######################################
# Development build
#
FROM base-build AS development-build

#COPY --from=composer-base /usr/bin/composer /usr/bin/composer

EXPOSE 6001

CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
