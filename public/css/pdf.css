@page {
    margin: 30px 40px 70px;
}

html, body {
    font-family: 'OpenSans', sans-serif;
    font-size: 14px;
    line-height: 1.2em;
}

table, th, td {
    font-family: 'RobotoCondensed', sans-serif;
}
h2 {
    font-size: 1.3em;
}
h3 {
    font-size: 1.2em;
    text-align: center;
}
h4 {
    font-size: 1.1em;
}
label {
    font-weight: bold;
}
table {
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
}
td, th {
    border: 1px solid #000000;
    padding: 2px 4px;
    font-size: 0.8em;
    height: 1em;
    line-height: 1em;
}
td.nums, th.nums {
    text-align: right !important;
}
tr.table-footer td {
    color: #fff;
    background-color: #757575;
}
table.signature-area td, table.signature-area th {
    border: 0px solid #000000;
    page-break-inside: avoid;
    padding-top: 40px;
    font-size: 1em;
    font-family: "OpenSans", sans-serif;
}
table.page-break {
    page-break-before: always;
}
/* Rule not working, use blade directive.
table.page-break:first-child {
    page-break-before: avoid;
}*/
.newpage {
    page-break-before: always;
}
.header {
    position: relative;
    left: 0;
    top: 0;
    width: 100%;
    text-align: left;
    font-size: 0.8em;
    line-height: 0.9em;
    font-weight: bold;
    margin-bottom: 40px;
    font-family: 'RobotoCondensed'
}
.footer {
    position: fixed;
    left: 0;
    bottom: -28px;
    padding-right: 1px;
    height: 12px;
    width: 100%;
    border-top: 0.5px solid #757575;
    font-size: 10px;
}
.footer div {
    display: inline-block;
}
.footer .brand {
    width: 40%;
}
.footer .page:after {
    content: counter(page);
}
.footer .page {
    width: 20%;
    text-align: center;
}
.footer .time {
    width: 40%;
    text-align: right;
}
.text-left {
    text-align: left;
}
.text-right {
    text-align: right;
}
.text-center {
    text-align: center;
}
.signature {
    text-align: center;
    font-weight: 700;
}