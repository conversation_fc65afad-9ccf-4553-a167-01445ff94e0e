<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted'             => 'Το πεδίο :attribute δεν είναι αποδεκτό.',
    'active_url'           => 'Το πεδίο :attribute δεν είναι έγκυρο URL.',
    'after'                => 'Το πεδίο :attribute πρέπει να είναι ημερομηνία μεταγενέστερη από :date.',
    'alpha'                => 'Το πεδίο :attribute μπορεί να περιέχει μόνο γράμματα.',
    'alpha_dash'           => 'Το πεδίο :attribute μπορεί να περιέχει μόνο γράμματα, αριθμούς και παύλες.',
    'alpha_num'            => 'To πεδίο :attribute μπορεί να περιέχει μόνο γράμματα και αριθμούς.',
    'array'                => 'Το πεδίο :attribute πρέπει να είναι σύνολο αντικειμένων (πίνακας).',
    'before'               => 'Το πεδίο :attribute πρέπει να είναι μία ημερομηνία πριν από :date.',
    'between'              => [
        'numeric' => 'Το πεδίο :attribute πρέπει να είναι από :min έως :max.',
        'file'    => 'Το αρχείο :attribute πρέπει να είναι από :min έως :max kilobytes.',
        'string'  => 'Το πεδίο :attribute πρέπει να είναι από :min έως :max χαρακτήρες.',
        'array'   => 'Το σύνολο :attribute πρέπει να περιέχει από :min έως :max αντικείμενα.',
    ],
    'boolean'              => 'Το πεδίο :attribute πρέπει να είναι ναι ή οχι.',
    'confirmed'            => 'Η επικύρωση του πεδίο :attribute δεν είναι αποδεκτή.',
    'date'                 => 'Το πεδίο :attribute δεν είναι αποδεκτή ημερομηνία.',
    'date_format'          => 'Το πεδίο :attribute δεν είναι αποδεκτή ημερομηνία. Πρέπει να είναι της μορφής :format.',
    'date_format_gr'       => 'Το πεδίο :attribute δεν είναι αποδεκτή ημερομηνία. Πρέπει να είναι της μορφής HH-MM-EEEE (πχ. 20-12-2016).',
    'different'            => 'The :attribute and :other must be different.',
    'digits'               => 'Το πεδίο :attribute πρέπει να είναι αριθμός με :digits ψηφία.',
    'digits_between'       => 'Το πεδίο :attribute πρέπει να είναι αριθμός από :min έως :max ψηφία.',
    'email'                => 'Το πεδίο :attribute πρέπει να είναι μία έγκυρη διεύθυνση e-mail.',
    'exists'               => 'The selected :attribute is invalid.',
    'filled'               => 'The :attribute field is required.',
    'image'                => 'The :attribute must be an image.',
    'in'                   => 'The selected :attribute is invalid.',
    'integer'              => 'Το πεδίο :attribute πρέπει να είναι ακέραιος αριθμός.',
    'ip'                   => 'The :attribute must be a valid IP address.',
    'json'                 => 'The :attribute must be a valid JSON string.',
    'max'                  => [
        'numeric' => 'To πεδίο :attribute δεν μπορεί να ξεπερνά το :max.',
        'file'    => 'To πεδίο :attribute δεν μπορεί να ξεπερνά τα :max kbs.',
        'string'  => 'To πεδίο :attribute δεν μπορεί να ξεπερνά τους :max χαρακτήρες.',
        'array'   => 'To πεδίο :attribute δεν μπορεί να έχει περισσότερα από :max αντικείμενα.',
    ],
    'mimes'                => 'The :attribute must be a file of type: :values.',
    'min'                  => [
        'numeric' => 'Το πεδίο :attribute πρέπει να είναι τουλάχιστον :min.',
        'file'    => 'Το πεδίο :attribute πρέπει να είναι τουλάχιστον :min kbs.',
        'string'  => 'Το πεδίο :attribute πρέπει να είναι τουλάχιστον :min χαρακτήρες.',
        'array'   => 'Το πεδίο :attribute πρέπει να έχει τουλάχιστον :min αντικείμενα.',
    ],
    'not_in'               => 'The selected :attribute is invalid.',
    'numeric'              => 'To πεδίο :attribute πρέπει να είναι αριθμός.',
    'regex'                => 'The :attribute format is invalid.',
    'required'             => 'Το πεδίο :attribute είναι υποχρεωτικό.',
    'required_if'          => 'Το πεδίο :attribute είναι υποχρεωτικό όταν το πεδίο :other είναι :value.',
    'required_unless'      => 'Το πεδίο :attribute είναι υποχρεωτικό εκτός αν το πεδίο :other είναι :values.',
    'required_with'        => 'The :attribute field is required when :values is present.',
    'required_with_all'    => 'The :attribute field is required when :values is present.',
    'required_without'     => 'The :attribute field is required when :values is not present.',
    'required_without_all' => 'The :attribute field is required when none of :values are present.',
    'same'                 => 'The :attribute and :other must match.',
    'size'                 => [
        'numeric' => 'The :attribute must be :size.',
        'file'    => 'The :attribute must be :size kilobytes.',
        'string'  => 'The :attribute must be :size characters.',
        'array'   => 'The :attribute must contain :size items.',
    ],
    'string'               => 'Το πεδίο :attribute πρέπει να είναι αλφαριθμητικό.',
    'timezone'             => 'The :attribute must be a valid zone.',
    'unique'               => 'Υπάρχει ήδη καταχώρηση με την ίδια τιμή για το πεδίο :attribute.',
    'url'                  => 'Μη αποδεκτή διεύθυνση url στο πεδίο :attribute.',
    'afm'                  => 'Μη έγκυρο ΑΦΜ.',
    'amka'                 => 'Μη έγκυρο ΑΜΚΑ.',
    'date_in_year'         => 'Η ημερομηνία του πεδίου :attribute πρέπει να είναι εντός του έτους :year.',
    'unique_date_range'    => 'Το διάστημα από :start έως :attribute συμπίπτει με προγενέστερη εγγραφή.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap attribute place-holders
    | with something more reader friendly such as E-Mail Address instead
    | of "email". This simply helps us make messages a little cleaner.
    |
    */

    'attributes' => [],

];
