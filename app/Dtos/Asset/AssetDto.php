<?php

namespace App\Dtos\Asset;

use App\Models\Assets\Asset;
use Carbon\Carbon;

/**
 * Data Transfer Object for Asset
 */
readonly class AssetDto
{
    /**
     * Create a new AssetDto instance
     */
    public function __construct(
        public ?int $id = null,
        public ?int $contractId = null,
        public ?int $assetCategoryId = null,
        public ?string $serialNumber = null,
        public ?int $quantity = null,
        public ?Carbon $dateOfReceipt = null,
        public ?string $location = null,
        public ?int $acquisitionCostInCents = null,
        public ?int $userId = null,
        public ?int $unitId = null,
        public ?Carbon $submittedAt = null,
    ) {
    }

    /**
     * Create an AssetDto from an array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['id'] ?? null,
            contractId: $data['contract_id'] ?? null,
            assetCategoryId: $data['asset_category_id'] ?? null,
            serialNumber: $data['serial_number'] ?? null,
            quantity: isset($data['quantity']) ? (int) $data['quantity'] : null,
            dateOfReceipt: isset($data['date_of_receipt']) ? Carbon::parse($data['date_of_receipt']) : null,
            location: $data['location'] ?? null,
            acquisitionCostInCents: isset($data['acquisition_cost_in_cents']) ? (int) $data['acquisition_cost_in_cents'] : null,
            userId: $data['user_id'] ?? null,
            unitId: $data['unit_id'] ?? null,
            submittedAt: isset($data['submitted_at']) ? Carbon::parse($data['submitted_at']) : null,
        );
    }

    /**
     * Create an AssetDto from an Asset model
     */
    public static function fromModel(Asset $asset): self
    {
        return new self(
            id: $asset->id,
            contractId: $asset->contract_id,
            assetCategoryId: $asset->asset_category_id,
            serialNumber: $asset->serial_number,
            quantity: $asset->quantity,
            dateOfReceipt: $asset->date_of_receipt,
            location: $asset->location,
            acquisitionCostInCents: $asset->acquisition_cost_in_cents,
            userId: $asset->user_id,
            unitId: $asset->unit_id,
            submittedAt: $asset->submitted_at,
        );
    }

    /**
     * Convert the DTO to an array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'contract_id' => $this->contractId,
            'asset_category_id' => $this->assetCategoryId,
            'serial_number' => $this->serialNumber,
            'quantity' => $this->quantity,
            'date_of_receipt' => $this->dateOfReceipt?->format('Y-m-d'),
            'location' => $this->location,
            'acquisition_cost_in_cents' => $this->acquisitionCostInCents,
            'user_id' => $this->userId,
            'submitted_at' => $this->submittedAt?->format('Y-m-d H:i:s'),
        ];
    }
}
