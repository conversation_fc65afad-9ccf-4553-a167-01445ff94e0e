<?php

namespace App\Dtos\Assets;

use App\Models\Assets\Contract;

class ContractDto
{
    /**
     * Create a new ContractDto instance.
     */
    public function __construct(
        public readonly ?int $id = null,
        public readonly string $contractNumber = '',
        public readonly ?int $unitId = null,
    ) {
    }

    /**
     * Create a DTO from an array.
     */
    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['id'] ?? null,
            contractNumber: $data['contract_number'] ?? '',
            unitId: $data['unit_id'] ?? null,
        );
    }

    /**
     * Create a DTO from a model.
     */
    public static function fromModel(Contract $contract): self
    {
        return new self(
            id: $contract->id,
            contractNumber: $contract->contract_number,
            unitId: $contract->unit_id,
        );
    }

    /**
     * Convert the DTO to an array.
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'contract_number' => $this->contractNumber,
            'unitId' => $this->unitId,
        ];
    }
}
