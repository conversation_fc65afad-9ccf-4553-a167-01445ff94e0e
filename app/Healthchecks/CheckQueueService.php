<?php

namespace App\Healthchecks;

use Illuminate\Support\Facades\Http;
use Laravel\Horizon\Contracts\MasterSupervisorRepository;

class CheckQueueService
{
    public function __invoke(): void
    {
        $horizon = app(MasterSupervisorRepository::class);

        try {
            $masterSupervisors = $horizon->all();
            Http::get('https://hc-ping.com/cd2d130f-b34e-4b38-8481-ececf68db05e'); // redis healthcheck ok

            if (count($masterSupervisors) === 0) {
                Http::get('https://hc-ping.com/33eff2df-fbbd-40fa-8181-4fdf19aab1ef/fail'); // horizon healthcheck fail
                return;
            }

            $masterSupervisor = $masterSupervisors[0];

            if ($masterSupervisor->status === 'paused') {
                Http::get('https://hc-ping.com/33eff2df-fbbd-40fa-8181-4fdf19aab1ef/fail'); // ping horizon healthcheck fail
                return;
            }

            Http::get('https://hc-ping.com/33eff2df-fbbd-40fa-8181-4fdf19aab1ef'); // horizon healthcheck ok
            return;


        } catch (\RedisException $e) {
            Http::get('https://hc-ping.com/cd2d130f-b34e-4b38-8481-ececf68db05e/fail'); // redis healthcheck fail
        }
    }
}
