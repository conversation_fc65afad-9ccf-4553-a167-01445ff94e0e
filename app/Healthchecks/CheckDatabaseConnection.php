<?php

namespace App\Healthchecks;

use Illuminate\Support\Facades\Http;

class CheckDatabaseConnection
{
    public function __invoke(): void
    {
        try {
            \DB::connection()->getPdo();
            Http::get('https://hc-ping.com/998f7a2c-a0fb-4a44-ba3d-45ef72757f7e');
        } catch (\Exception $e) {
            Http::get('https://hc-ping.com/998f7a2c-a0fb-4a44-ba3d-45ef72757f7e/fail');
        }
    }
}
