<?php

namespace App\Healthchecks;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\File;

class CheckFileserverAccessibility
{
    public function __invoke()
    {
        if (File::isDirectory(storage_path('app/medialibrary/contractuals'))) {
            // TODO: expose check UUIDs via docker secrets
            Http::get('https://hc-ping.com/88819a95-ed83-4789-adc4-e59d2521fcc0');
        } else {
            Http::get('https://hc-ping.com/88819a95-ed83-4789-adc4-e59d2521fcc0/fail');
        }
    }
}
