<?php

namespace App\Exports\Contractuals;

use App\Models\Contractuals\Position;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class TotalRejectedRankingsExport4765 implements WithMultipleSheets
{
    use Exportable;

    /** @var Collection<Position> */
    protected Collection $positions;

    protected bool $isForPublication;

    protected int $calculationId;

    public function __construct(Collection $positions, int $calculationId, bool $forPublication)
    {
        $this->positions = $positions;
        $this->calculationId = $calculationId;
        $this->isForPublication = $forPublication;
    }

    public function sheets(): array
    {
        $sheets = [];
        foreach ($this->positions as $position) {
            $sheets[] = new RejectedRankingsExport4765(
                position: $position,
                calculationId: $this->calculationId,
                forPublication: $this->isForPublication,
            );
        }

        return $sheets;
    }
}
