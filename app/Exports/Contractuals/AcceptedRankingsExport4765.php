<?php

namespace App\Exports\Contractuals;

use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Position;
use App\Models\Contractuals\Ranking;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class AcceptedRankingsExport4765 implements FromQuery, WithMapping, WithHeadings, WithEvents, WithColumnFormatting, ShouldAutoSize, WithColumnWidths, WithTitle
{
    use Exportable, RegistersEventListeners;

    public const AUXILIARY_LEVELS = ['1', 'Α', 'B', 'Γ'];

    protected int $specializationTypeId;
    protected Calculation $calculation;

    public function __construct(
        protected Position $position,
        protected int $calculationId,
        protected bool $forPublication,
    ) {
        $this->specializationTypeId = $position->specialization->specialization_type_id;
        $this->calculation = Calculation::find($this->calculationId);
    }

    public function query()
    {
        return Ranking::query()
            ->with(['application', 'positionRating.applicationRating'])
            ->where('position_id', $this->position->id)
            ->where('rejected', 0)
            ->where('calculation_id', $this->calculationId)
            ->orderBy('rank');
    }

    public function map($ranking): array
    {
//        $application = Application::find($ranking->application_id);
        $fields = [
            $ranking->application->protocol_number,
            $ranking->application->surname,
            $ranking->application->name,
            $this->forPublication ? '' : $ranking->application->fathername,
            $this->forPublication ? '' : $ranking->application->policeid_number,
            $ranking->positionRating->applicationRating->impediment_eight_months ? 'ΝΑΙ' : 'ΟΧΙ',
            $ranking->locality ? 'ΝΑΙ' : 'ΟΧΙ',
        ];

        if ($this->specializationTypeId !== 4) {
            array_push(
                $fields,
                $this::AUXILIARY_LEVELS[$ranking->positionRating->applicationRating->auxiliary_level]
            );
        }

        array_push(
            $fields,
            $ranking->positionRating->applicationRating->unemployments_continued,
            $ranking->positionRating->applicationRating->unemployments,
            $ranking->positionRating->applicationRating->multi_child_families_eligibility ? 'ΝΑΙ' : 'ΟΧΙ',
            $ranking->positionRating->applicationRating->three_child_families_eligibility ? 'ΝΑΙ' : 'ΟΧΙ',
            $ranking->positionRating->applicationRating->single_parent_families_eligibility ? 'ΝΑΙ' : 'ΟΧΙ',
            $ranking->positionRating->applicationRating->minors,
        );

        if ($this->specializationTypeId === 1 || $this->specializationTypeId === 2) {
            array_push(
                $fields,
                $ranking->positionRating->applicationRating->degrees,
                $ranking->positionRating->applicationRating->doctorates ? 'ΝΑΙ' : 'ΟΧΙ',
                $ranking->positionRating->applicationRating->postgraduates ? 'ΝΑΙ' : 'ΟΧΙ',
                $ranking->positionRating->applicationRating->postgraduates_integrated ? 'ΝΑΙ' : 'ΟΧΙ',
                $ranking->positionRating->applicationRating->second_degrees ? 'ΝΑΙ' : 'ΟΧΙ',
                $ranking->positionRating->applicationRating->other_degrees > 0 ? $ranking->positionRating->applicationRating->other_degrees : '',
            );
        } elseif ($this->specializationTypeId === 3) {
            array_push(
                $fields,
                $ranking->positionRating->applicationRating->degrees,
                $ranking->positionRating->applicationRating->second_degrees ? 'ΝΑΙ' : 'ΟΧΙ',
            );
        }

        array_push(
            $fields,
            $ranking->positionRating->applicationRating->experiences,
            $ranking->positionRating->applicationRating->disabilities_eligibility ? 'ΝΑΙ' : 'ΟΧΙ',
            $ranking->positionRating->applicationRating->family_disabilities_eligibility ? 'ΝΑΙ' : 'ΟΧΙ',
            $ranking->positionRating->applicationRating->unemployments_continued_points,
            $ranking->positionRating->applicationRating->unemployments_points,
            $ranking->positionRating->applicationRating->multi_child_families_points,
            $ranking->positionRating->applicationRating->three_child_families_points,
            $ranking->positionRating->applicationRating->single_parent_families_points,
            $ranking->positionRating->applicationRating->minors_points,
        );

        if ($this->specializationTypeId === 1 || $this->specializationTypeId === 2) {
            array_push(
                $fields,
                $ranking->positionRating->applicationRating->degrees_points,
                $ranking->positionRating->applicationRating->doctorates_points,
                $ranking->positionRating->applicationRating->postgraduates_points,
                $ranking->positionRating->applicationRating->postgraduates_integrated_points,
                $ranking->positionRating->applicationRating->second_degrees_points,
                $ranking->positionRating->applicationRating->other_degrees_points,
            );
        } elseif ($this->specializationTypeId === 3) {
            array_push(
                $fields,
                $ranking->positionRating->applicationRating->degrees_points,
                $ranking->positionRating->applicationRating->second_degrees_points,
            );
        }

        if ($this->calculation->hire_runners_up && !$this->forPublication) {
            if ($ranking->employable && $ranking->accepted == 1 && $ranking->was_runner_up) {
                $employable = 'Προσλαμβάνεται (Ε)';
            } elseif ($ranking->employable && $ranking->accepted == 1 && !$ranking->was_runner_up) {
                $employable = 'Προσλαμβάνεται';
            } elseif ($ranking->employable && $ranking->accepted === 0) {
                $employable = 'Δεν Απεδέχθη';
            } else {
                $employable = '';
            }
        } else {
            $employable = ($ranking->employable && $ranking->accepted !== 0) ? 'Προσλαμβάνεται' : '';
        }

        array_push(
            $fields,
            $ranking->positionRating->applicationRating->experiences_points,
            $ranking->positionRating->applicationRating->disabilities_points,
            $ranking->positionRating->applicationRating->family_disabilities_points,
            $ranking->positionRating->applicationRating->score,
            $ranking->rank,
            $employable
        );

        return $fields;
    }

    public function headings(): array
    {
        $headings = [
            ['Φορέας: '.$this->position->contest->organization, '', '', '', '', 'ΠΡΟΣΛΗΨΗ ΠΡΟΣΩΠΙΚΟΥ ΜΕ ΣΥΜΒΑΣΗ ΟΡΙΣΜΕΝΟΥ ΧΡΟΝΟΥ', '', '', '', '', '', '', '', '', '', '', '', 'Προκήρυξη:'],
            ['Υπηρεσία: '.$this->position->unit->name, '', '', '', '', 'ΠΙΝΑΚΑΣ ΚΑΤΑΤΑΞΗΣ & ΒΑΘΜΟΛΟΓΙΑΣ', '', '', '', '', '', '', '', '', '', '', '', $this->position->contest->name],
            ['Διάρκεια Σύμβασης: '.$this->position->contest->contract_duration, '', '', '', '', 'ΥΠΟΨΗΦΙΩΝ ΚΑΤΗΓΟΡΙΑΣ '.$this->position->specialization->specializationType->name, '', '', '', '', '', '', '', '', '', '', '', "Αρ.Πρωτ: {$this->position->contest->protocol_number}/{$this->position->contest->protocol_date->format('d-m-Y')}"],
            ['', '', '', '', '', 'ΚΩΔΙΚΟΣ ΘΕΣΗΣ: '.$this->position->code, '', '', '', '', '', '', '', '', '', '', '', "ΑΔΑ: {$this->position->contest->ada}"],
            ['', '', '', '', '', 'ΕΙΔΙΚΟΤΗΤΑ: '.$this->position->specialization->name],
            [],
            [],
        ];

        $rowHeaders = [
            'Α.Μ.',
            'ΕΠΩΝΥΜΟ',
            'ΟΝΟΜΑ',
            'ΟΝΟΜΑ ΠΑΤΡΟΣ',
            'ΑΡΙΘΜ. ΤΑΥΤΟΤ.',
            'ΚΩΛΥΜΑ 8ΜΗΝΗΣ ΑΠΑΣΧΟΛΗΣΗΣ',
            'ΕΝΤΟΠΙΟΤΗΤΑ',
        ];

        if ($this->specializationTypeId !== 4) {
            array_push($rowHeaders, 'ΚΥΡΙΑ ΠΡΟΣΟΝΤΑ(1) / ΣΕΙΡΑ ΕΠΙΚΟΥΡΙΑΣ');
        }

        array_push(
            $rowHeaders,
            'ΧΡΟΝΟΣ ΣΥΝΕΧΟΜΕΝΗΣ ΑΝΕΡΓΙΑΣ (σε μήνες) (1α)',
            'ΧΡΟΝΟΣ ΜΗ ΣΥΝΕΧΟΜΕΝΗΣ ΑΝΕΡΓΙΑΣ (σε μήνες) (1β)',
            'ΠΟΛΥΤΕΚΝΟΣ ή ΤΕΚΝΟ ΠΟΛΥΤΕΚΝΗΣ ΟΙΚΟΓΕΝΕΙΑΣ (2)',
            'ΤΡΙΤΕΚΝΟΣ ή ΤΕΚΝΟ ΤΡΙΤΕΚΝΗΣ ΟΙΚΟΓΕΝΕΙΑΣ (3)',
            'ΜΟΝΟΓΟΝΕΑΣ ή ΤΕΚΝΟ ΜΟΝΟΓΟΝΕΪΚΗΣ ΟΙΚΟΓΕΝΕΙΑΣ (4)',
            'ΑΝΗΛΙΚΑ ΤΕΚΝΑ (αριθμ. ανήλικων τέκνων) (5)',
        );

        if ($this->specializationTypeId === 1 || $this->specializationTypeId === 2) {
            array_push(
                $rowHeaders,
                'ΒΑΘΜΟΣ ΒΑΣΙΚΟΥ ΤΙΤΛΟΥ (6)',
                'ΔΙΔΑΚΤΟΡΙΚΟ (7)',
                'ΜΕΤΑΠΤΥΧΙΑΚΟ (8)',
                'ΕΝΙΑΙΟΣ ΚΑΙ ΑΔΙΑΣΠΑΣΤΟΣ INTEGRATED MASTER (Ναι εάν ισχύει) (9)',
                'ΔΕΥΤΕΡΟΣ ΤΙΤΛΟΣ (Ναι εάν ισχύει) (10)',
                'ΠΕΡΙΣΣΟΤΕΡΑ ΤΟΥ ΕΝΟΣ ΔΙΔΑΚΤΟΡΙΚΟΥ / ΜΕΤΑΠΤΥΧΙΑΚΟΥ / INTEGRATED / ΔΕΥΤΕΡΟΥ ΤΙΤΛΟΥ (11)',
                'ΕΜΠΕΙΡΙΑ (σε μήνες, μέχρι 84 μήνες) (12)',
                $this->forPublication ? 'ΕΙΔΙΚΟ ΚΡΙΤΗΡΙΟ 1 (13)' : 'ΑΝΑΠΗΡΙΑ ΥΠΟΨΗΦΙΟΥ ΜΕ ΠΟΣΟΣΤΟ ΤΟΥΛΑΧΙΣΤΟΝ 50% (Ναι εάν ισχύει) (13)',
                $this->forPublication ? 'ΕΙΔΙΚΟ ΚΡΙΤΗΡΙΟ 2 (14)' :'ΑΝΑΠΗΡΙΑ ΓΟΝΕΑ, ΤΕΚΝΟΥ, ΑΔΕΡΦΟΥ ή ΣΥΖΥΓΟΥ (14)',
                'ΜΟΝΑΔΕΣ (1α)',
                'ΜΟΝΑΔΕΣ (1β)'
            );
            for ($i = 2; $i <= 14; $i++) {
                array_push(
                    $rowHeaders,
                    "ΜΟΝΑΔΕΣ ($i)"
                );
            }
        } elseif ($this->specializationTypeId === 3) {
            array_push(
                $rowHeaders,
                'ΒΑΘΜΟΣ ΒΑΣΙΚΟΥ ΤΙΤΛΟΥ (6)',
                'ΔΕΥΤΕΡΟΣ ΤΙΤΛΟΣ (Ναι εάν ισχύει) (7)',
                'ΕΜΠΕΙΡΙΑ (σε μήνες) (8)',
                $this->forPublication ? 'ΕΙΔΙΚΟ ΚΡΙΤΗΡΙΟ 1 (9)' : 'ΑΝΑΠΗΡΙΑ ΥΠΟΨΗΦΙΟΥ ΜΕ ΠΟΣΟΣΤΟ ΤΟΥΛΑΧΙΣΤΟΝ 50% (Ναι εάν ισχύει) (9)',
                $this->forPublication ? 'ΕΙΔΙΚΟ ΚΡΙΤΗΡΙΟ 2 (10)' :'ΑΝΑΠΗΡΙΑ ΓΟΝΕΑ, ΤΕΚΝΟΥ, ΑΔΕΡΦΟΥ ή ΣΥΖΥΓΟΥ (10)',
                'ΜΟΝΑΔΕΣ (1α)',
                'ΜΟΝΑΔΕΣ (1β)'
            );
            for ($i = 2; $i <= 10; $i++) {
                array_push(
                    $rowHeaders,
                    "ΜΟΝΑΔΕΣ ($i)"
                );
            }
        } else {
            array_push(
                $rowHeaders,
                'ΕΜΠΕΙΡΙΑ (σε μήνες) (6)',
                $this->forPublication ? 'ΕΙΔΙΚΟ ΚΡΙΤΗΡΙΟ 1 (7)' : 'ΑΝΑΠΗΡΙΑ ΥΠΟΨΗΦΙΟΥ ΜΕ ΠΟΣΟΣΤΟ ΤΟΥΛΑΧΙΣΤΟΝ 50% (Ναι εάν ισχύει) (7)',
                $this->forPublication ? 'ΕΙΔΙΚΟ ΚΡΙΤΗΡΙΟ 2 (8)' :'ΑΝΑΠΗΡΙΑ ΓΟΝΕΑ, ΤΕΚΝΟΥ, ΑΔΕΡΦΟΥ ή ΣΥΖΥΓΟΥ (8)',
                'ΜΟΝΑΔΕΣ (1α)',
                'ΜΟΝΑΔΕΣ (1β)'
            );
            for ($i = 2; $i <= 8; $i++) {
                array_push($rowHeaders, "ΜΟΝΑΔΕΣ ($i)");
            }
        }

        array_push(
            $rowHeaders,
            'ΣΥΝΟΛΟ ΜΟΝΑΔΩΝ',
            'Σειρά Κατάταξης',
            'Παρατηρήσεις',
        );

        array_push($headings, $rowHeaders);

        return $headings;
    }

    /**
     * @return array
     */
    public function columnFormats(): array
    {
        if ($this->specializationTypeId === 1 || $this->specializationTypeId === 2) {
            return [
                'O'  => NumberFormat::FORMAT_NUMBER_00, // Βασικός Τίτλος με δεκαδικά
                'AD' => NumberFormat::FORMAT_NUMBER_00, // Μονάδες Βασικού Τίτλου
                'AM' => NumberFormat::FORMAT_NUMBER_00, // Σύνολο
            ];
        } elseif ($this->specializationTypeId === 3) {
            return [
                'O'  => NumberFormat::FORMAT_NUMBER_00, // Βασικός Τίτλος με δεκαδικά
                'Z' => NumberFormat::FORMAT_NUMBER_00, // Μονάδες Βασικού Τίτλου
                'AE' => NumberFormat::FORMAT_NUMBER_00, // Σύνολο
            ];
        } else {
            return [
                'Z' => NumberFormat::FORMAT_NUMBER_00, // Σύνολο
            ];
        }
    }

    public function columnWidths(): array
    {
        $widths = [
            'A' => 8,
            'B' => 15,
            'C' => 15,
            'D' => 10,
            'E' => 11,
            'F' => 7,
            'G' => 7,
            'H' => 7,
            'I' => 7,
            'J' => 7,
            'K' => 7,
            'L' => 7,
            'M' => 7,
            'N' => 7,
            'O' => 7,
            'P' => 7,
            'Q' => 7,
            'R' => 7,
            'S' => 7,
            'T' => 7,
            'U' => 7,
            'V' => 7,
            'W' => 7,
            'X' => 7,
            'Y' => 7,
            'Z' => 10,
            'AA' => 7,
            'AB' => 7,
            'AC' => 7,
            'AD' => 10,
            'AE' => 7,
            'AF' => 7,
            'AG' => 7,
            'AH' => 7,
            'AI' => 7,
            'AJ' => 7,
            'AK' => 7,
            'AL' => 7,
            'AM' => 10,
            'AN' => 7,
            'AO' => 20,
        ];

        if ($this->specializationTypeId === 1 || $this->specializationTypeId === 2) {
            $widths['AM'] = 12;
            $widths['AN'] = 5;
            $widths['AO'] = 20;
        } elseif ($this->specializationTypeId === 3) {
            $widths['AE'] = 12;
            $widths['AF'] = 5;
            $widths['AG'] = 20;
        } else {
            $widths['Z'] = 12;
            $widths['AA'] = 5;
            $widths['AB'] = 20;
        }

        return $widths;
    }

    public static function afterSheet(AfterSheet $event)
    {
        $styleMainHeading = [
            'font' => [
                'bold' => true,
                'color' => ['rgb' => '0060df'],
            ],
            'alignment' => [
                'horizontal' => 'center',
            ],
        ];

        $styleSecondaryHeading = [
            'font' => [
                'bold' => true,
            ],
        ];

        $styleVerticalHeaders = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'textRotation' => 90,
                'horizontal' => 'center',
                'vertical' => 'center',
            ],
        ];

        $styleAlignLeft = [
            'alignment' => [
                'horizontal' => 'left',
            ],
        ];

        // Top Headers bold
        $event->sheet->getStyle('F1:F5')->applyFromArray($styleMainHeading);
        $event->sheet->getStyle('A1:A3')->applyFromArray($styleSecondaryHeading);
        $event->sheet->getStyle('R1:R2')->applyFromArray($styleSecondaryHeading);

        // Table headers vertical orientation
        $event->sheet->getStyle('A8:AO8')->applyFromArray($styleVerticalHeaders);
        $event->sheet->getStyle('A8:AO8')->getAlignment()->setWrapText(true);
        $event->sheet->getRowDimension(8)->setRowHeight(170);
        // Headers
        $event->sheet->mergeCells('A1:D1');
        $event->sheet->mergeCells('F1:P1');
        $event->sheet->mergeCells('R1:Z1');

        $event->sheet->mergeCells('A2:D2');
        $event->sheet->mergeCells('F2:P2');
        $event->sheet->mergeCells('R2:Z2');

        $event->sheet->mergeCells('A3:D3');
        $event->sheet->mergeCells('F3:P3');
        $event->sheet->mergeCells('R3:Z3');

        $event->sheet->mergeCells('A4:D4');
        $event->sheet->mergeCells('F4:P4');
        $event->sheet->mergeCells('R4:Z4');

        $event->sheet->mergeCells('A5:D5');
        $event->sheet->mergeCells('F5:P5');

        // First column align left
        $event->sheet->getStyle('A:A')->applyFromArray($styleAlignLeft);

//        // Format top right header (contest name)
//        $event->sheet->mergeCells('R2:AI5');
//        $event->sheet->getDelegate()->getStyle('R2:AI5')->getAlignment()->setWrapText(true);

        // Signature
        $event->sheet->appendRows([
            [''],
            ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
                $event->getConcernable()->position->contest->undersigned_title_first_row, ],
            ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
                $event->getConcernable()->position->contest->undersigned_title_second_row, ],
            [''],
            ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
                $event->getConcernable()->position->contest->undersigned_name, ],
        ], $event->getSheet());

        $signatureName = $event->sheet->getHighestDataRow();
        $signatureHeader1 = $signatureName - 3;
        $signatureHeader2 = $signatureName - 2;
        $event->sheet->mergeCells("U$signatureHeader1:AD$signatureHeader1");
        $event->sheet->mergeCells("U$signatureHeader2:AD$signatureHeader2");
        $event->sheet->mergeCells("U$signatureName:AD$signatureName");

        $styleSignature = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => 'center',
            ],
        ];
        $event->sheet->getStyle("U$signatureHeader1")->applyFromArray($styleSignature);
        $event->sheet->getStyle("U$signatureHeader2")->applyFromArray($styleSignature);
        $event->sheet->getStyle("U$signatureName")->applyFromArray($styleSignature);

        $event->sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
        $event->sheet->getPageSetup()->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);
        $event->sheet->getPageSetup()->setScale(38);
    }

    protected function chooseLargest($a, $b)
    {
        if ($a >= $b) {
            return $a;
        }

        return $b;
    }

    public function title(): string
    {
        return 'ΘΕΣΗ '.$this->position->code;
    }
}
