<?php

namespace App\Exports\Assets;

use App\Models\Assets\Asset;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class UnsubmittedAssetsExport implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithStyles
{
    protected array $exportFilters;

    public function __construct($exportFilters)
    {
        $this->exportFilters = $exportFilters;
    }

    public function collection(): Collection
    {
        return Asset::query()
            ->with(['contract', 'unit'])
            ->unsubmitted()
            ->filter($this->exportFilters)
            ->get();
    }

    public function headings(): array
    {
        return [
            'Κωδικός Εταιρείας',
            'Κωδικός Παγίου',
            'Κωδικός Υπό-Παγίου',
            'Κατηγορία Παγίου',
            'Περιγραφή Παγίου',
            'Σειριακός αριθμός',
            'Ποσότητα',
            'Μονάδα μέτρησης',
            'Ημερομηνία Κεφαλαιοποίησης Παγίου',
            'Ημερομηνία Ενεργοποίησης Παγίου',
            'Ημερομηνία Πώλησης/Διαγραφής Παγίου',
            'Εγκατάσταση',
            'Κέντρο Κόστους',
            'Περιοχή Απόσβεσης',
            'Κλειδί Απόσβεσης',
            'Ωφέλιμη Ζωή Παγίου (Σε χρόνια)',
            'Ωφέλιμη Ζωή Παγίου (Σε μήνες)',
            'Ημερομηνία Έναρξης Αποσβέσεων',
            'Υπολειμματική Αξία',
            'Κόστος Απόκτησης ή Παραγωγής Παγίου',
            'Σωρρευμένη Απόσβεση Παγίου',
            'Μέθοδος Επιμέτρησης',
            'Κόστη και προσθήκες',
            'Απομειώσεις και ζημιές',
            'Χαρακτηρισμός παγίου',
            'Κριτήριο καθορισμού ωφέλιμης ζωής',
        ];
    }

    public function map($asset): array
    {
        return [
            'ΑΦΜ',
            '',
            '',
            $asset->assetCategory?->code,
            $asset->assetCategory?->description,
            $asset->serial_number,
            $asset->quantity,
            $asset->assetCategory?->measure_unit,
            $asset->date_of_receipt?->format('d.m.Y'),
            $asset->date_of_receipt?->format('d.m.Y'),
            '',
            $asset->location,
            '',
            'Ωφέλιμη ζωή',
            'Σταθερή Μέθοδος Απόσβεσης',
            $asset->assetCategory?->duration_years,
            '0',
            '01.01.2026',
            '0.00',
            number_format($this->getCost($asset), 2, '.', ''),
            number_format($this->getDepreciation($asset), 2, '.', ''),
            'ΚΟΣΤΟΣ ΚΤΗΣΗΣ',
            '0.00',
            '0.00',
            'ΙΔΙΟ',
            'ΙΔΙΑ ΚΡΙΣΗ',
        ];
    }

    public function columnFormats(): array
    {
        return [
            //            'F' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'S' => NumberFormat::FORMAT_CURRENCY_EUR,
            'T' => NumberFormat::FORMAT_CURRENCY_EUR,
            'U' => NumberFormat::FORMAT_CURRENCY_EUR,
            'W' => NumberFormat::FORMAT_CURRENCY_EUR,
            'X' => NumberFormat::FORMAT_CURRENCY_EUR,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Apply light gray background to the first row, ensure headers are NOT bold
        $sheet->getStyle('A1:Z1')->applyFromArray([
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => 'E0E0E0'], // Light gray color
            ],
            'font' => [
                'bold' => true, // Explicitly set to not bold
            ],
        ]);
    }

    protected function getCost($asset): float
    {
        return $asset->acquisition_cost_in_cents ? ($asset->acquisition_cost_in_cents / 100) : 0;
    }

    protected function getDepreciation($asset): float
    {
        if (empty($asset->acquisition_cost_in_cents) || empty($asset->assetCategory)) {
            return 0;
        }

        $cost = $asset->acquisition_cost_in_cents / 100;
        $usefulLifeYears = $asset->assetCategory->duration_years;
        
        if ($usefulLifeYears <= 0) {
            return 0;
        }

        $receiptYear = $asset->date_of_receipt?->format('Y') ?? 0;
        $yearsSinceReceipt = 2024 - $receiptYear;
        
        // Cap depreciation at the useful life of the asset
        $depreciationYears = $yearsSinceReceipt;
        if ($depreciationYears < 0) {
            $depreciationYears = 0;
        }
        if ($depreciationYears > $usefulLifeYears) {
            $depreciationYears = $usefulLifeYears;
        }
        
        return $cost * $depreciationYears / $usefulLifeYears;
    }
}
