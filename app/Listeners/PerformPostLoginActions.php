<?php

namespace App\Listeners;

use App\Models\User;
use Illuminate\Auth\Events\Login;
use Illuminate\Support\Facades\Request;

class PerformPostLoginActions
{
    public function __construct()
    {
    }

    public function handle(Login $event)
    {
        /** @var User $loggedInUser */
        $loggedInUser = $event->user;

        // Save the login to the activity log
        activity()
           ->causedBy($loggedInUser)
           ->withProperties(['ip' => Request::ip()])
           ->log('login');

        // Set user's assigned unit to primary unit
        if ($loggedInUser->unit_id !== $loggedInUser->primary_unit_id) {
            $loggedInUser->update(['unit_id' => $loggedInUser->primary_unit_id]);
        }
    }
}
