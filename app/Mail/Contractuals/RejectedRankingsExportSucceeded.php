<?php

namespace App\Mail\Contractuals;

use App\Models\Contractuals\Calculation;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class RejectedRankingsExportSucceeded extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        protected bool $forPublication,
        protected Calculation $calculation,
    ) {}

    public function envelope()
    {
        $subject = 'Επιτυχής δημιουργία πίνακα απορριπτέων';
        if ($this->forPublication) {
            $subject = $subject.' (Διαύγεια)';
        }
        return new Envelope(
            subject: $subject
        );
    }

    public function content()
    {
        return new Content(
            markdown: 'emails.contractuals.rejected-rankings-export-succeeded',
            with: [
                'for_publication' => $this->forPublication,
                'calculation' => $this->calculation,
            ]
        );
    }

    public function attachments()
    {
        return [];
    }
}
