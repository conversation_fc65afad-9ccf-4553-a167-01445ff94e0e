<?php

namespace App\Mail\Contractuals;

use App\Models\Contractuals\Calculation;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AcceptedRankingsExportFailed extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        protected bool $forPublication,
        protected Calculation $calculation,
    ) {}

    public function envelope()
    {
        $subject = 'Αποτυχία δημιουργία πίνακα επιτυχόντων';
        if ($this->forPublication) {
            $subject = $subject.' (Διαύγεια)';
        }

        return new Envelope(
            subject: $subject,
        );
    }

    public function content()
    {
        return new Content(
            markdown: 'emails.contractuals.accepted-rankings-export-failed',
            with: [
                'for_publication' => $this->forPublication,
                'calculation' => $this->calculation,
            ]
        );
    }

    public function attachments()
    {
        return [];
    }
}
