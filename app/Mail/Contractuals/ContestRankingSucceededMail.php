<?php

namespace App\Mail\Contractuals;

use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Contest;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContestRankingSucceededMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Contest $contest,
        public Calculation $calculation,
    ) {}

    public function envelope()
    {
        return new Envelope(
            subject: 'Επιτυχής υπολογισμός πινάκων',
        );
    }

    public function content()
    {
        return new Content(
            markdown: 'emails.contractuals.contest-ranking-succeeded',
            with: [
                'contest' => $this->contest,
                'calculation' => $this->calculation,
                'url' => "https://apptree.culture.gr/contractuals/contests/{$this->contest->id}/positions",
            ],
        );
    }

    public function attachments()
    {
        return [];
    }
}
