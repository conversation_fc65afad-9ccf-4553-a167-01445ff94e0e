<?php

namespace App\Mail\Contractuals;

use App\Models\Contractuals\Contest;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContestRankingFailedMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Contest $contest,
        public string $calculationStartedAt,
        public string $calculationDescription,
    ) {}

    public function envelope()
    {
        return new Envelope(
            subject: 'Ανεπιτυχής υπολογισμός πινάκων'
        );
    }

    public function content()
    {
        return new Content(
            markdown: 'emails.contractuals.contest-ranking-failed',
            with: [
                'contest' => $this->contest,
                'calculationStartedAt' => $this->calculationStartedAt,
                'calculationDescription' => $this->calculationDescription,
            ],
        );
    }

    public function attachments()
    {
        return [];
    }
}
