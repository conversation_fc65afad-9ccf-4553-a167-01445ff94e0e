<?php

namespace App\Mail\Contractuals;

use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Contest;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContestDistributionSucceededMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Calculation $calculation,
        public Contest $contest
    ) {}

    public function envelope()
    {
        return new Envelope(
            subject: 'Επιτυχής υπολογισμός πινάκων',
        );
    }

    public function content()
    {
        return new Content(
            markdown: 'emails.contractuals.contest-distribution-succeeded',
            with: [
                'url' => "https://apptree.culture.gr/contractuals/contests/{$this->contest->id}",
                'calculation' => $this->calculation,
                'contest' => $this->contest,
            ],
        );
    }

    public function attachments()
    {
        return [];
    }
}
