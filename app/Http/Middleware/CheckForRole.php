<?php

namespace App\Http\Middleware;

use Closure;

class CheckForRole
{
    /**
     * Run the request filter.
     */
    public function handle($request, Closure $next, ...$roles)
    {
        $authorized = false;

        foreach($roles as $role) {
            $role = trim($role);
            if ($request->user()->hasRole($role)) {
                $authorized = true;
            }
        }

        if (! $authorized) {
            abort(403);
        }

        return $next($request);
    }

}
