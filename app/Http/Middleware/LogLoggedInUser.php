<?php

namespace App\Http\Middleware;

use Closure;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class LogLoggedInUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        if (Auth::check()) {
            $userId = Auth::user()->id;
            $expiresAt = Carbon::now()->addMinutes(5);
            $lastActivity = Carbon::now();
            Cache::put("user_{$userId}_is_online", $lastActivity, $expiresAt);
        }

        return $next($request);
    }
}
