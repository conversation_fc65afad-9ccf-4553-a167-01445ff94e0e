<?php

namespace App\Http\Resources\Assets;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AssetResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'contract_id' => $this->contract_id,
            'asset_category_id' => $this->asset_category_id,
            'serial_number' => $this->serial_number,
            'quantity' => $this->quantity,
            'date_of_receipt' => $this->date_of_receipt,
            'location' => $this->location,
            'acquisition_cost' => $this->acquisition_cost_formatted,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            // Include relationships when they are loaded
            'contract' => $this->when($this->relationLoaded('contract'), function () {
                return $this->contract;
            }),
            'asset_category' => $this->when($this->relationLoaded('assetCategory'), function () {
                return $this->assetCategory;
            }),
        ];
    }
}
