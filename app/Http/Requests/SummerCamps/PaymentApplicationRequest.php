<?php

namespace App\Http\Requests\SummerCamps;

use App\Rules\Educational\AtLeastOneParticipantPerTargetType;
use App\Rules\Educational\AtLeastOnePerCategory;
use App\Rules\Educational\DateInPeriod;
use Illuminate\Foundation\Http\FormRequest;

class PaymentApplicationRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'payroll_id' => 'nullable|string|max:20',
            'personal_phone' => 'nullable|numeric',
            'mobile_phone' => 'nullable|numeric',
            'work_phone' => 'nullable|numeric',
            'email_address' => 'nullable|email:rfc,strict',
        ];
    }

    public function attributes(): array
    {
        return [
            'payroll_id' => 'Αριθμός Μητρώου Μισθοδοσίας',
            'personal_phone' => 'Καταχωρήστε το τηλέφωνο μόνο με αριθμούς χωρίς κενά',
            'mobile_phone' => 'Καταχωρήστε το τηλέφωνο μόνο με αριθμούς χωρίς κενά',
            'work_phone' => 'Καταχωρήστε το τηλέφωνο μόνο με αριθμούς χωρίς κενά',
            'email_address' => 'Email Επικοινωνίας'
        ];
    }
}
