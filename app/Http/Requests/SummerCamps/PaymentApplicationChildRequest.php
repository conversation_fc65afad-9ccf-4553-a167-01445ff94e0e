<?php

namespace App\Http\Requests\SummerCamps;

use App\Rules\Educational\AtLeastOneParticipantPerTargetType;
use App\Rules\Educational\AtLeastOnePerCategory;
use App\Rules\Educational\DateInPeriod;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class PaymentApplicationChildRequest extends FormRequest
{
    protected $stopOnFirstFailure = true;

    public function rules(): array
    {
        return [
            'full_name' => 'required',
            'has_disability' => 'nullable|boolean',
            'summer_camps' => 'required|string',
        ];
    }

    public function attributes(): array
    {
        return [
            'full_name' => 'Ονοματεπώνυμο',
            'has_disability' => 'ΑμεΑ',
            'summer_camps' => 'Πάροχοι Κατασκήνωσης',
        ];
    }
}
