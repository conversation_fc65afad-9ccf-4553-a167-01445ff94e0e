<?php

namespace App\Http\Requests\SummerCamps;


use App\Rules\Educational\AllowedMimeTypes;
use App\Rules\Educational\MaxAttachmentSize;
use Illuminate\Foundation\Http\FormRequest;

class ApplicationAttachmentRequest extends FormRequest
{
    public function rules()
    {
        return [
            'attachments' => [
                new AllowedMimeTypes,
                new MaxAttachmentSize(4),
            ],
        ];
    }

    public function messages()
    {
        return [
            'attachments.mimetypes' => 'Το αρχείο πρέπει να είναι ένα αρχείο PDF, JPEG ή PNG',
        ];
    }
}
