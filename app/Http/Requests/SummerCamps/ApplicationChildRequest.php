<?php

namespace App\Http\Requests\SummerCamps;

use App\Rules\Educational\AtLeastOneParticipantPerTargetType;
use App\Rules\Educational\AtLeastOnePerCategory;
use App\Rules\Educational\DateInPeriod;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class ApplicationChildRequest extends FormRequest
{
    protected $stopOnFirstFailure = true;

    public function rules(): array
    {
        return [
            'full_name' => 'required',
            'has_disability' => 'nullable|boolean',
            'disability_percentage' => 'exclude_unless:has_disability,true|integer|min:1|max:100',
            'days' => 'required|integer|min:1|max:21',
            'birthdate' => [
                'required',
                'date',
                function($attribute, $value, $fail) {
                    $age = Carbon::parse($value)->endOfYear()->diffInYears(now()->endOfYear());
                    if ($this->has_disability && $this->disability_percentage >= 50) {
                        if ($age < 6 || $age > 16) {
                            $fail('Εκτός ορίων ηλικίας');
                        }
                    } else {
                        if ($age < 6 || $age > 14) {
                            $fail('Εκτός ορίων ηλικίας');
                        }
                    }
                },
            ],
            'summer_camps' => 'nullable|string',
        ];
    }

    public function attributes(): array
    {
        return [
            'full_name' => 'Ονοματεπώνυμο',
            'birthdate' => 'Ημ. Γέννησης',
            'has_disability' => 'ΑμεΑ',
            'disability_percentage' => 'Ποσοστό Αναπηρίας',
            'days' => 'Αριθμός Ημερών Διαμονής',
            'summer_camps' => 'Πάροχοι Κατασκήνωσης',
        ];
    }
}
