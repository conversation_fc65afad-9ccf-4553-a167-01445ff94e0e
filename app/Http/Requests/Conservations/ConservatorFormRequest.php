<?php

namespace App\Http\Requests\Conservations;

use App\Http\Requests\Request;
use Illuminate\Validation\Rule;

class ConservatorFormRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'registration_number'   => [
                'required',
                'integer',
                Rule::unique('mysql_conservations.conservators')->ignore($this->input('id')),
                //'unique:mysql_conservations.conservators,registration_number,{request()->id},id"
            ],
            'name'                  => 'required|max:40',
            'surname'               => 'required|max:60',
            'fathername'            => 'required|max:60',
            'birthdate'             => 'nullable|date',
            'policeid_date'         => 'nullable|date',
            'status_date'           => 'nullable|date',
            'amka'                  => [
                'nullable',
                'amka',
                Rule::unique('mysql_conservations.conservators')->ignore($this->input('id')),
            ],
            'afm'                   => [
                'nullable',
                'afm',
                Rule::unique('mysql_conservations.conservators')->ignore($this->input('id')),
            ],
            'phonenumber1'          => 'nullable|max:15',
            'phonenumber2'          => 'nullable|max:15',
            'email'                 => 'nullable|email|max:60',
            'doy'                   => 'max:255',
            'street'                => 'max:255',
            'street_number'         => 'max:20',
            'postcode'              => 'max:20',
            'city'                  => 'max:60',
            'active'                => 'boolean',
            'materials.*.material_id' => 'required',
            'materials.*.issued_at' => 'required|date',
            'materials.*.issued_record_number' => 'required'
        ];
    }

    /**
     * Get the validation custom messages.
     *
     * @return array
     */
    public function messages()
    {
        return [

        ];
    }

    /**
     * Get the validation attributes 'nice names'.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'registration_number'   => 'Αρ. Μητρώου',
            'name'                  => 'Όνομα',
            'surname'               => 'Επώνυμο',
            'fathername'            => 'Πατρώνυμο',
            'birthdate'             => 'Ημ. Γέννησης',
            'policeid_date'         => 'Ημ. Ταυτότητας',
            'status_date'           => 'Ημ. Κατάστασης',
            'amka'                  => 'ΑΜΚΑ',
            'afm'                   => 'ΑΦΜ',
            'phonenumber1'          => 'Αρ. Τηλεφώνου 1',
            'phonenumber2'          => 'Αρ. Τηλεφώνου 2',
            'email'                 => 'E-mail',
            'doy'                   => 'ΔΟΥ',
            'street'                => 'Οδός',
            'street_number'         => '',
            'postcode'              => 'Τ.Κ.',
            'city'                  => 'Πόλη',
            'active'                => '',
            'materials.*.material_id' => 'Υλικό',
            'materials.*.issued_at' => 'Ημ. Χορήγησης',
            'materials.*.issued_record_number' => 'Αρ. Πρακτικού'
        ];
    }



}
