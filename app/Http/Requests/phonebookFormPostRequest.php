<?php

namespace App\Http\Requests;

use App\Http\Requests\Request;

class phonebookFormPostRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        //TODO: learn how to make conditional validation rules
        return [
//            'name'    => 'required|alpha',
//            'surname' => 'required|alpha',
//            'email'   => 'required|unique:mysql_personnel.employees,email',
//            'telephones.*.tel' => 'digits:10',
            //TODO: add more fields
        ];
    }
}
