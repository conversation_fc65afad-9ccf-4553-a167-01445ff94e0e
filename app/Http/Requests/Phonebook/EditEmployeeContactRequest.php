<?php

namespace App\Http\Requests\Phonebook;

use App\Http\Requests\Request;
use App\Models\Phonebook\EmployeeContact;

class EditEmployeeContactRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'email' => 'nullable|ends_with:@culture.gr,@gga.gov.gr',
            'telephones.*.tel' => [
               'regex:/^[0-9-]{1,9}\d$/',
               'required',
//               function ($attribute, $value, $fail) {
//                   $employeeContact = $this->route()->parameter('employee_contact');
//                   if ($employeeContact->telephones->pluck('tel')->contains($value)) {
//                       $fail('Το τηλέφωνο είναι ήδη καταχωρημένο για αυτή την επαφή');
//                   }
//               },
            ],
            'telephones.*.type_id' => 'required_with:telephones.*.tel',
            'telephones' => [
                'required',
                function ($attribute, $value, $fail) {
//                    /* @var EmployeeContact $employeeContact*/
//                    $employeeContact = $this->route()->parameter('employee_contact');
//                    $existingTelephoneTypes = $employeeContact->telephones->pluck('type_id');
                    $telephoneTypes = array_column($value, 'type_id');
                    if (
                        count($telephoneTypes) !== count(array_unique($telephoneTypes))
//                        || $existingTelephoneTypes->intersect($telephoneTypes)->count() > 0
                    ) {
                        $fail('Τα τηλέφωνα πρέπει να είναι διαφορετικού είδους (πχ Εργασίας 1 και Εργασίας 2)');
                    }
                },
            ],
        ];
    }

    public function messages()
    {
        return [
            'email.ends_with' => 'Το email πρέπει να τελειώνει σε @culture.gr ή @gga.gov.gr',
            'telephones.required' => 'Πρέπει να εισάγετε τουλάχιστον ένα τηλέφωνο',
            'telephones.*.tel.regex' => 'Το τηλεφωνο πρέπει να αποτελείται από έως 10 αριθμητικά ψηφία ή "-"',
            'telephones.*.tel.required' => 'Δεν επιλέξατε τηλέφωνο',
            'telephones.*.type_id.required_with' => 'Επιλέξτε είδος τηλεφώνου',
        ];
    }
}
