<?php

namespace App\Http\Requests\Phonebook;

use App\Http\Requests\Request;

class CreateEmployeeContactRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name'         => 'required',
            'surname'      => 'required',
            'email' => 'nullable|ends_with:@culture.gr,@gga.gov.gr',
            'telephones' => [
                'required',
                function ($attribute, $value, $fail) {
                    $telephones = request()->collect('telephones')->pluck('type_id');
                    $telephoneTypes = array_column($value, 'type_id');
                    if (count($telephoneTypes) !== count(array_unique($telephoneTypes))) {
                        $fail('Τα τηλέφωνα πρέπει να είναι διαφορετικού είδους (πχ Εργασίας 1 και Εργασίας 2)');
                    }
                },
            ],
            'telephones.*.tel' => [
                'required',
                'regex:/^[0-9-]{1,9}\d$/'
            ],
            'telephones.*.type_id' => 'required_with:telephones.*.tel',
        ];
    }

    public function messages()
    {
        return [
            'email.ends_with' => 'Το email πρέπει να τελειώνει σε @culture.gr ή @gga.gov.gr',
            'telephones.required' => 'Πρέπει να εισάγετε τουλάχιστον ένα τηλέφωνο',
            'telephones.*.tel.regex' => 'Το τηλεφωνο πρέπει να αποτελείται από έως 10 αριθμητικά ψηφία ή "-"',
            'telephones.*.tel.required' => 'Δεν επιλέξατε τηλέφωνο',
            'telephones.*.type_id.required_with' => 'Επιλέξτε είδος τηλεφώνου',
        ];
    }

    public function attributes()
    {
        return [
            'name'         => '"Όνομα"',
            'surname'      => '"Επώνυμο"',
        ];
    }
}
