<?php

namespace App\Http\Requests\Assets;

use App\Dtos\Assets\ContractDto;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by policies
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'contract_number' => [
                'required',
                'string',
                'max:255',
                Rule::unique('mysql_assets.contracts', 'contract_number')->ignore($this->route('contract')),
            ],
        ];
    }

    /**
     * Convert the request to a ContractDto
     */
    public function toDto(): ContractDto
    {
        return new ContractDto(
            id: $this->route('contract')->id,
            contractNumber: $this->input('contract_number'),
        );
    }
}
