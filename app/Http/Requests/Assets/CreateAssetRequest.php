<?php

namespace App\Http\Requests\Assets;

use App\Dtos\Asset\AssetDto;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateAssetRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'contract_id' => ['required', Rule::exists('mysql_assets.contracts', 'id')],
            'asset_category_id' => ['required', Rule::exists('mysql_assets.asset_categories', 'id')],
            'serial_number' => ['required', 'string', 'max:255'],
            'quantity' => ['nullable', 'integer', 'min:1'],
            'date_of_receipt' => ['nullable', 'date', 'after_or_equal:2019-01-01', 'before_or_equal:2024-12-31'],
            'location' => ['required', 'string', 'max:255'],
            'acquisition_cost' => ['nullable', 'numeric', 'min:0'],
            'unit_id' => ['nullable', Rule::exists('mysql_main.units', 'id')],
        ];
    }

    public function attributes(): array
    {
        return [
            'contract_id' => 'Αριθμός σύμβασης',
            'asset_category_id' => 'Περιγραφή Παγίου',
            'serial_number' => 'Σειριακός Αριθμός',
            'quantity' => 'Ποσότητα',
            'date_of_receipt' => 'Ημερομηνία Κεφαλαιοποίησης Παγίου',
            'location' => 'Εγκατάσταση',
            'acquisition_cost' => 'Κόστος Απόκτησης ή Παραγωγής Παγίου',
            'unit_id' => 'Υπηρεσία',
        ];
    }

    /**
     * Convert the request to an AssetDto
     */
    public function toDto(): AssetDto
    {
        return new AssetDto(
            // id is null for new entities and will be set when saved to database
            contractId: $this->input('contract_id'),
            assetCategoryId: $this->input('asset_category_id'),
            serialNumber: $this->input('serial_number'),
            quantity: $this->has('quantity') ? (int) $this->input('quantity') : null,
            dateOfReceipt: $this->input('date_of_receipt') ? Carbon::parse($this->input('date_of_receipt')) : null,
            location: $this->input('location'),
            acquisitionCostInCents: $this->has('acquisition_cost') ? (int) ($this->input('acquisition_cost') * 100) : null, // Convert from euros to cents
            unitId: $this->input('unit_id'),
            // userId will be set by the action
        );
    }
}
