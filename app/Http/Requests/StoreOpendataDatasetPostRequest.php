<?php

namespace App\Http\Requests;

class StoreOpendataDatasetPostRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name'                 => 'required|max:255',
            'description'          => 'required|max:500',
            'location'             => 'required|max:255',
            'type_id'              => 'required',
            'records'              => 'nullable|integer',
            'unobtainable_reasons' => 'required_unless:obtainable,true,1,"on"',
            'physical_format'      => 'required_if:type_id,2',
            'personaldata_info'    => 'required_if:personaldata,1,"on",true',
            'restrictions'         => 'required_if:restricted,1,"on",true',
            'unit'                 => 'required',
            'user_id'              => 'required',
        ];
    }

    public function messages()
    {
        return [
            'unobtainable_reasons.required_unless' => 'Εφόσον το Σύνολο δεν είναι Διαθέσιμο, πρέπει να επιλέξετε αιτιολογία.',
            'personaldata_info.required_if'        => 'Πρέπει να συμπληρώσετε την αιτιολογία για τα Προσωπικά Δεδομένα.',
            'restrictions.required_if'             => 'Πρέπει να επιλέξετε τουλάχιστον μία αιτιολογία για τους Λοιπούς Περιορισμούς.',
            'physical_format.required_if'          => 'Δε συμπληρώσατε τη Μορφή των Αρχείων του Φυσικού Συνόλου.',
            'unit.required'                        => 'Δεν επιλέξατε Υπηρεσία',
        ];
    }

    public function attributes()
    {
        return [
            'name'            => 'Τίτλος',
            'description'     => 'Περιγραφή',
            'location'        => 'Θέση Αρχείου/Κάτοχος',
            'type_id'         => 'Τύπος',
            'records'         => 'Αριθμός Εγγραφών',
            'physical_format' => 'Μορφή Φυσικού Αρχείου',
        ];
    }
}
