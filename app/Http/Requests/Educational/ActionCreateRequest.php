<?php

namespace App\Http\Requests\Educational;

use Illuminate\Foundation\Http\FormRequest;

class ActionCreateRequest extends FormRequest
{
    public function rules()
    {
        return [
            'type_id' => 'required|exists:mysql_educational.types,id',
            'unit_id' => 'required|exists:mysql_main.units,id',
        ];
    }

    public function messages()
    {
        return [
            'type_id.required' => 'Πρέπει να επιλέξετε υποχρεωτικά το είδος της δράσης σας.',
            'type_id.exists' => 'The type does not exist.',
            'unit_id.required' => 'Πρέπει να επιλέξετε υποχρεωτικά την Υπηρεσία για την οποία γίνεται η καταχώρηση της Δράσης.',
            'unit_id.exists' => 'The unit does not exist.',
        ];
    }
}
