<?php

namespace App\Http\Requests\Educational;

use App\Rules\Educational\AllowedMimeTypes;
use App\Rules\Educational\MaxAttachmentSize;
use App\Rules\Educational\MaxTotalAttachmentSize;
use Illuminate\Foundation\Http\FormRequest;

class ActionAttachmentRequest extends FormRequest
{
    public function rules()
    {
        return [
            'attachments' => [
                'required',
                new AllowedMimeTypes,
                new MaxAttachmentSize(2),
                new MaxTotalAttachmentSize($this->action, 2),
            ],
        ];
    }

    public function messages()
    {
        return [
            'attachments.required' => 'Πρέπει να προσθέσετε ένα ή περισσότερα αρχεία',
            'attachments.mimetypes' => 'Το αρχείο πρέπει να είναι ένα αρχείο PDF, JPEG ή PNG',
        ];
    }
}
