<?php

namespace App\Http\Requests\Contractuals;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class ApplicationFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // Contest information
            'contest_id'      => 'required',
//            'protocol_number' => 'required|string',
            'applicant_category'       => 'required',
            'invalidation_description_id' => 'required_if:invalidated,true',

            // Position selection
            'positions'      => 'required|array|min:1',
            'positions.*.id' => 'required',
            'positions.*.pivot.auxiliary_level' => 'required',

            // Personal details
            'name'       => 'required|string',
            'surname'    => 'required|string',
            'fathername' => 'required|string',
            'birthdate'       => 'required|date_format:"Y-m-d"',
//            'policeid_number' => 'required|string|max:20',
//            'afm' => [
//                'nullable',
//                'sometimes',
//                'afm',
//                Rule::unique('mysql_contractuals.applicants')->ignore($this->input('applicant_id')),
//            ],
//            'amka' => [
//                'nullable',
//                'sometimes',
//                'amka',
//                Rule::unique('mysql_contractuals.applicants')->ignore($this->input('applicant_id')),
//            ],

            // Contact details

            // Degrees
            'degrees' => [
                'exclude_if:applicant_category,4',
                'array',
                'min:1',
                function ($attribute, $value, $fail) {
                    $primaryDegreesCount = collect($value)->filter(function ($degree) {
                        return isset($degree['is_primary']) && $degree['is_primary'] == true;
                    })->count();
                    if ($primaryDegreesCount !== 1) {
                        $fail('Πρέπει να δηλώσετε έναν (μοναδικό) κύριο τίτλο σπουδών!');
                    }
                }
            ],
            'degrees.*.name' => 'required|string',
//            'degrees.*.issuer' => 'required|string',
            'degrees.*.mark' => 'required|numeric|min:0',
            'degrees.*.year' => 'required|digits:4',

            // Postgraduates
            'postgraduates.*.name'   => 'required|string',
//            'postgraduates.*.issuer' => 'required|string',
//            'postgraduates.*.year'   => 'required|digits:4',

            // Doctorates
            'doctorates.*.name'   => 'required|string',
//            'doctorates.*.issuer' => 'required|string',
//            'doctorates.*.year'   => 'required|digits:4',

            // Experiences
//            'experiences.*.name' => 'required|string',
            'experiences.*.months' => 'required|integer|min:0',

            // Language skills
            'language_skills.*.language_id'       => 'required',
            'language_skills.*.language_level_id' => 'required',

            // Compute skills
            'computer_skills.*.name' => 'required|string',

            // Greek languages
//            'greek_languages.*.name' => 'required_if:eu_citizen,1',
//            'greek_languages.*.level' => 'required_if:eu_citizen,1'
        ];
    }

    /**
     * Get the validation attributes 'nice names'.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'contest_id'               => 'Διαγωνισμός',
            'protocol_number'          => 'Αριθμός Πρωτοκόλλου',
            'protocol_date'            => 'Ημερομηνία',
            'applicant_category'       => 'Κατηγορία',
            'invalidated'              => 'Ακυρη Αίτηση',
            'invalidation_description_id' => 'Αιτιολογία Ακύρωσης',

            'positions'      => 'Θέσεις',
            'positions.*.id' => 'Επιλογή Θέσης',

            'name'            => 'Όνομα',
            'surname'         => 'Επώνυμο',
            'fathername'      => 'Ον/νυμο Πατέρα',
            'mothername'      => 'Ον/νυμο Μητέρας',
            'policeid_number' => 'Α.Δ.Τ.',
            'policeid_date'   => 'Ημ/νία Έκδοσης',
            'afm'             => 'Α.Φ.Μ.',
            'amka'            => 'Α.Μ.Κ.Α.',
            'birthdate'       => 'Ημ/νία Γέννησης',

            'street_number' => 'Αριθμός',
            'postcode'      => 'Τ.Κ.',
            'phonenumber1'  => 'Τηλέφωνο 1',
            'phonenumber2'  => 'Τηλέφωνο 2',
            'email'         => 'Email',

            'degrees.*.name'   => 'Τίτλος',
            'degrees.*.issuer' => 'Ίδρυμα',
            'degrees.*.mark'   => 'Βαθμός',
            'degrees.*.year'   => 'Έτος',

            'postgraduates.*.name'   => 'Τίτλος',
            'postgraduates.*.issuer' => 'Ίδρυμα',
            'postgraduates.*.year'   => 'Έτος',

            'doctorates.*.name'   => 'Τίτλος',
            'doctorates.*.issuer' => 'Ίδρυμα',
            'doctorates.*.year'   => 'Έτος',

            'experiences.*.name'       => 'Ειδικότητα',
            'experiences.*.issuer'     => 'Φορέας',
            'experiences.*.months'     => 'Μήνες',
            'experiences.*.started_at' => 'Ημ/νία Από',
            'experiences.*.ended_at'   => 'Ημ/νία Έως',

            'language_skills.*.language_id'       => 'Γλώσσα',
            'language_skills.*.language_level_id' => 'Επίπεδο',
            'language_skills.*.name'              => 'Τίτλος',
            'language_skills.*.issuer'            => 'Φορέας',

            'computer_skills.*.name' => 'Τίτλος',

            'greek_languages.*.name' => 'Ελληνομάθεια',
            'greek_languages.*.level' => 'Επίπεδο',

            'positions.*.pivot.auxiliary_level' => 'Επικουρία',
        ];
    }

    public function messages()
    {
        return [
            'invalidation_description_id.required_if' => 'Το πεδίο :attribute είναι υποχρεωτικό όταν η αίτηση έχει σημανθεί ως άκυρη',
            'greek_languages.*.name.required_if' => 'Το πεδίο :attribute είναι υποχρεωτικό για πολίτες ΕΕ',
            'greek_languages.*.level.required_if' => 'Το πεδίο :attribute είναι υποχρεωτικό για πολίτες ΕΕ',
            'degrees.min' => 'Απαιτείται τουλάχιστον ένας τίτλος σπουδών',
        ];
    }
}
