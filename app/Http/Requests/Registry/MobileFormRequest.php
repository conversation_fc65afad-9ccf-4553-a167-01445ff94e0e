<?php

namespace App\Http\Requests\Registry;

use Carbon\Carbon;
use App\Http\Requests\Request;
use Illuminate\Validation\Rule;

class MobileFormRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'mobile_provider_id' => 'required|integer|min:0',
            'number'   => [
                'required',
                'digits_between:0,12',
                Rule::unique('mysql_registry.mobiles')
                    ->where(function ($query) {
                        $query->whereNull('deleted_at')
                            ->where(function ($query) {
                                $query->whereNull('valid_to')
                                    ->orWhere('valid_to', '>=', Carbon::createFromFormat('Y-m-d', $this->valid_from)->format('Y-m-d'));
                            });
                    })
                    ->ignore($this->id),
            ],
            'holder'            => 'nullable|string|max:120',
            'valid_from'        => 'required|date',
            'valid_to'          => 'nullable|date|after:valid_from',
        ];
    }

    /**
     * Get the validation attributes 'nice names'.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'mobile_provider_id' => '',
            'number'            => '',
            'holder'            => '',
            'valid_from'        => 'Έναρξη',
            'valid_to'          => 'Λήξη'
        ];
    }
}
