<?php

namespace App\Http\Requests\Registry;

use Carbon\Carbon;
use App\Http\Requests\Request;
use Illuminate\Validation\Rule;

class VehicleFormRequest extends Request
{

    protected $stopOnFirstFailure = true;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'brand'                 => 'required|min:3|max:100',
            'model'                 => 'required|min:2|max:120',
            'vehicle_type_id'       => 'required|integer',
            'frame_number'          => 'nullable|max:50',
            'cc'                    => 'integer|nullable',
            'valid_from'            => 'required|date',
            'valid_to'              => 'nullable|date|after:valid_from',
            'registration_number'   => [
                'nullable',
                'string',
                'min:6',
                'max:12',
                Rule::unique('mysql_registry.vehicles')
                    ->where(function ($query) {
                        $query->whereNull('deleted_at')
                            ->where(function ($query) {
                                $query->whereNull('valid_to')
                                    ->orWhere('valid_to', '>=', Carbon::createFromFormat('Y-m-d', $this->valid_from)->format('Y-m-d'));
                            });
                    })
                    ->ignore($this->id),
            ],
            'license_date'          => 'nullable|date',
            'license_number'        => [
                'nullable',
                'string',
                'max:120',
                Rule::unique('mysql_registry.vehicles')
                    ->where(function ($query) {
                        $query->whereNull('deleted_at')
                            ->where(function ($query) {
                                $query->whereNull('valid_to')
                                    ->orWhere('valid_to', '>=', Carbon::createFromFormat('Y-m-d', $this->valid_from)->format('Y-m-d'));
                            });
                    })
                    ->ignore($this->id),
            ],
            'employee_id'           => 'nullable|integer',
            'employee_other'        => 'nullable|string|max:255',
            'insurance_obligation'  => 'required|boolean',
        ];
    }

    /**
     * Get the validation attributes 'nice names'.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'brand'                 => 'Μάρκα',
            'model'                 => 'Μοντέλο',
            'vehicle_type_id'       => 'Είδος Οχήματος',
            'frame_number'          => 'Αριθμός Πλαισίου',
            'cc'                    => 'Κυβικά Εκατοστά',
            'registration_number'   => 'Αριθμός Κυκλοφορίας',
            'license_date'          => 'Αριθμός Αδείας',
            'license_number'        => 'Ημερομηνία 1ης Αδείας',
            'employee_id'           => 'Χρήστης - Οδηγός',
            'employee_other'        => 'Χρήστης - Οδηγός',
            'insurance_obligation'  => '',
            'valid_from'            => 'Έναρξη Χρήσης',
            'valid_to'              => 'Λήξη Χρήσης'
        ];
    }
}
