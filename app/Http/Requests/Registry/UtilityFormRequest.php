<?php

namespace App\Http\Requests\Registry;

use Carbon\Carbon;
use App\Http\Requests\Request;
use Illuminate\Validation\Rule;

class UtilityFormRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'building_id'       => 'required|integer|min:0',
            'utility_type_id'     => 'required|integer|min:0',
            'utility_provider_id' => 'required|integer|min:0',
            'supply_number'     => 'nullable|string|max:50',
            'paid_centrally'    => 'nullable|boolean',
            'valid_from'        => 'required|date',
            'valid_to'          => 'nullable|date|after:valid_from'
        ];
    }

    /**
     * Get the validation attributes 'nice names'.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'building_id'       => '',
            'utility_type_id'     => '',
            'utility_provider_id' => '',
            'supply_number'     => '',
            'paid_centrally'    => '',
            'valid_from'        => '',
            'valid_to'          => ''
        ];
    }


}
