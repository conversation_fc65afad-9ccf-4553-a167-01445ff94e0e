<?php

namespace App\Http\Requests\Registry;

use Carbon\Carbon;
use App\Http\Requests\Request;
use Illuminate\Validation\Rule;

class VehicleInsuranceFormRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'provider' => 'required|string|max:255',
            'contract_number' => 'required|string|max:255',
            'amount' => 'required|numeric',
            'valid_from' => 'required|date',
            'valid_to'   => [
                'required' ,
                'date',
                'after:valid_from',
                'unique_date_range:valid_from,registry.vehicle_insurances,id,vehicle_id,'.$this->vehicle_id,
            ]
        ];
    }

    /**
     * Get the validation attributes 'nice names'.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'provider' => '',
            'contract_number' => '',
            'amount' => '',
            'valid_from' => 'Έναρξη Ασφάλειας',
            'valid_to'   => 'Λήξη Ασφάλειας',

        ];
    }


}
