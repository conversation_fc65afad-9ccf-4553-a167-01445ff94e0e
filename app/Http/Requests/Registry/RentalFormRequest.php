<?php

namespace App\Http\Requests\Registry;

use Carbon\Carbon;
use App\Http\Requests\Request;
use Illuminate\Validation\Rule;

class RentalFormRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'building_id'       => 'required|integer|min:0',
            'rental_process_id' => 'required|integer|min:0',
            'monthly_rent'      => 'required|numeric|min:0',
            'valid_from'        => 'required|date',
            'valid_to'          => [
                'required' ,
                'date',
                'after:valid_from',
                'unique_date_range:valid_from,registry.rentals,id,building_id,'.$this->building_id,
            ]
        ];
    }

    /**
     * Get the validation attributes 'nice names'.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'building_id'           => '',
            'rental_process_id'     => '',
            'monthly_rent'          => 'Μηνιαίο Μίσθωμα',
            'valid_from'            => 'Έναρξη Εκμίσθωσης',
            'valid_to'              => 'Λήξη Εκμίσθωσης',
        ];
    }


}
