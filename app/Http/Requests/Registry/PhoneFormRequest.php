<?php

namespace App\Http\Requests\Registry;

use App\Http\Requests\Request;
use Carbon\Carbon;
use Illuminate\Validation\Rule;

class PhoneFormRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'building_id'       => 'required|integer|min:0',
            'phone_provider_id' => 'required|integer|min:0',
            'supply_number'     => 'nullable|string|max:50',
            'valid_from'        => 'required|date',
            'valid_to'          => 'nullable|date|after:valid_from',
            'number'   => [
                'required',
                'digits_between:0,12',
                Rule::unique('mysql_registry.phones')
                    ->where(function ($query) {
                        $query->whereNull('deleted_at')
                            ->where(function ($query) {
                                $query->whereNull('valid_to')
                                    ->orWhere('valid_to', '>=', Carbon::createFromFormat('Y-m-d', $this->valid_from)->format('Y-m-d'));
                            });
                    })
                    ->ignore($this->id),
            ],
        ];
    }

    /**
     * Get the validation attributes 'nice names'.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'building_id'       => '',
            'phone_provider_id' => '',
            'number'            => '',
            'supply_number'     => '',
            'valid_from'        => '',
            'valid_to'          => '',
        ];
    }
}
