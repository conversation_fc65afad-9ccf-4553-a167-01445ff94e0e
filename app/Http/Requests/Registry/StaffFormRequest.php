<?php

namespace App\Http\Requests\Registry;

use Carbon\Carbon;
use App\Http\Requests\Request;
use Illuminate\Validation\Rule;

class StaffFormRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'building_id'               => 'required|integer|min:0',
            'unit_id'                   => 'required|integer|min:0',
            'regular_scientists'        => 'nullable|integer|min:0',
            'regular_administratives'   => 'nullable|integer|min:0',
            'regular_technicians'       => 'nullable|integer|min:0',
            'regular_guards'            => 'nullable|integer|min:0',
            'regular_workers'           => 'nullable|integer|min:0',
            'regular_others'            => 'nullable|integer|min:0',
            'contactual_scientists'     => 'nullable|integer|min:0',
            'contactual_administratives'=> 'nullable|integer|min:0',
            'contactual_technicians'    => 'nullable|integer|min:0',
            'contactual_guards'         => 'nullable|integer|min:0',
            'contactual_workers'        => 'nullable|integer|min:0',
            'contactual_others'         => 'nullable|integer|min:0',
        ];
    }

    /**
     * Get the validation attributes 'nice names'.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'building_id'               => '',
            'unit_id'                   => '',
            'regular_scientists'        => '',
            'regular_administratives'   => '',
            'regular_technicians'       => '',
            'regular_guards'            => '',
            'regular_workers'           => '',
            'regular_others'            => '',
            'contactual_scientists'     => '',
            'contactual_administratives'=> '',
            'contactual_technicians'    => '',
            'contactual_guards'         => '',
            'contactual_workers'        => '',
            'contactual_others'         => ''
        ];
    }


}
