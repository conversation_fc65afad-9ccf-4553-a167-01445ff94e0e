<?php

namespace App\Http\Requests\Registry;

use App\Http\Requests\Request;
use Illuminate\Validation\Rule;

class BuildingFormRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'street'                => 'required|string|min:3|max:255',
            'street_number'         => 'nullable|string|max:50',
            'city'                  => 'required|string|min:3|max:120',
            'postcode'              => 'required|string|max:20',
            'region_id'             => 'nullable|integer|min:0',
            'prefecture_id'         => 'nullable|integer|min:0',
            'floor'                 => 'nullable|string|max:20',
            'description'           => 'nullable|string|max:255',
            'interior_area'         => 'required|numeric|min:0',
            'exterior_area'         => 'required|numeric|min:0',
            'building_usage_id'     => 'required|integer|min:0',
            'building_usage_other'  => 'nullable|string|max:500',
            'ownership_type_id'     => 'required|integer|min:0',
            'valid_from'            => 'required|date',
            'valid_to'              => 'nullable|date|after:valid_from'
        ];
    }

    /**
     * Get the validation attributes 'nice names'.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'street'                => 'Οδός',
            'street_number'         => 'Αριθμός',
            'city'                  => 'Πόλη',
            'postcode'              => 'Τ.Κ.',
            'region_id'             => 'Περιφέρεια',
            'prefecture_id'         => 'Περιφερειακή Ενότητα',
            'floor'                 => 'Όροφοι',
            'description'           => 'Περιγραφή',
            'interior_area'         => 'Τετραγωνικά ωφέλιμου χώρου',
            'exterior_area'         => 'Τετραγωνικά περιβάλλοντος χώρου',
            'building_usage_id'     => 'Χρήση',
            'building_usage_other'  => 'Χρήση (άλλο)',
            'ownership_type_id'     => 'Ιδιοκτησιακό Καθεστώς',
            'valid_from'            => 'Έναρξη Χρήσης',
            'valid_to'              => 'Λήξη Χρήσης'
        ];
    }

}
