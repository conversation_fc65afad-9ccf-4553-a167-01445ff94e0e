<?php

namespace App\Http\Controllers\Opendata;

use App\Http\Controllers\ReadyFormController;
use App\Models\Opendata\Filetype;

class FiletypeController extends ReadyFormController
{
    protected array $permissions = [
        'index'  => 'opendata.admin',
        'create' => 'opendata.admin',
        'read'   => 'opendata.admin',
        'update' => 'opendata.admin',
        'delete' => 'opendata.admin',
    ];

    protected array $indexFields = [
        'name',
        'description',
        'machine_readable',
    ];

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Όνομα', 'type' => 'text'],
        ['name' => 'description', 'label' => 'Περιγραφή', 'type' => 'text'],
        ['name' => 'machine_readable', 'label' => 'Μηχαναγνώσιμο', 'type' => 'checkbox'],
    ];

    protected string $formTitle = 'Είδος ψηφιακού αρχείου';

    protected array $validationRules = [
        'name' => 'required|min:3',
    ];

    protected string $bladeLayout = 'layouts.opendata';

    public function __construct(Filetype $filetype)
    {
        parent::__construct($filetype);
    }
}
