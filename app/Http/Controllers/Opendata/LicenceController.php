<?php

namespace App\Http\Controllers\Opendata;

use App\Http\Controllers\ReadyFormController;
use App\Models\Opendata\Licence;

class LicenceController extends ReadyFormController
{
    protected array $permissions = [
        'index'  => 'opendata.admin',
        'create' => 'opendata.admin',
        'read'   => 'opendata.admin',
        'update' => 'opendata.admin',
        'delete' => 'opendata.admin',
    ];

    protected array $indexFields = [
        'name',
        'description',
    ];

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Όνομα', 'type' => 'text'],
        ['name' => 'description', 'label' => 'Περιγραφή', 'type' => 'text'],
    ];

    protected string $formTitle = 'Άδειες διάθεσης συνόλων';

    protected array $validationRules = [
        'name' => 'required|min:3',
    ];

    protected string $bladeLayout = 'layouts.opendata';

    public function __construct(Licence $license)
    {
        parent::__construct($license);
    }
}
