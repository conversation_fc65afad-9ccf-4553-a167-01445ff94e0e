<?php

namespace App\Http\Controllers\Opendata;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreOpendataDatasetPostRequest;
use App\Models\Opendata\Dataset;
use App\Models\Opendata\Filetype;
use App\Models\Opendata\Licence;
use App\Models\Opendata\Restriction;
use App\Models\Opendata\UnobtainableReason;
use App\Models\Opendata\Updaterate;
use App\Models\Unit;
use Auth;
use Illuminate\Http\Request;

class DatasetController extends Controller
{
    public function search()
    {
        $filteredUnits = Auth::user()->unit()->get();

        return view('opendata.dataset.search', compact('filteredUnits'));
    }

    public function showSearchResults(Request $request)
    {
        $filters = collect($request->all())->forget('_token')->filter(function ($value, $key) {
            return '' != $value;
        });

        $datasets = Dataset::queryWithFilters($filters)->get();
        $datasets->load('type')
            ->load('licence')
            ->load('updaterate')
            ->load('filetypes')
            ->load('unit')
            ->load('user');

        return view('opendata.dataset.listing', compact('datasets', 'filters'));
    }

    public function directorateListing()
    {
        $userUnit = Auth::user()->unit_id;
        $filters = collect(['units' => [$userUnit]]);
        $datasets = Dataset::queryWithFilters($filters)->get();
        $datasets->load([
            'type',
            'licence',
            'updaterate',
            'filetypes',
            'user',
        ]);

        return view('opendata.dataset.listing', compact('datasets', 'filters'));
    }

    public function listByCategory()
    {
        $filteredUnitsId = request()->has('filtered_unit_id')
            ? request('filtered_unit_id')
            : collect(Auth::user()->unit_id);
        $filteredUnits = Unit::find($filteredUnitsId);

        $filters = collect([
            'units' => $filteredUnitsId,
        ]);

        $datasets['cat_a'] = Dataset::queryWithFilters($filters)->catA()->get();
        $datasets['cat_b'] = Dataset::queryWithFilters($filters)->catB()->get();
        $datasets['cat_c'] = Dataset::queryWithFilters($filters)->catC()->get();
        $datasets['cat_d'] = Dataset::queryWithFilters($filters)->catD()->get();

        foreach ($datasets as $datasets_cat) {
            $datasets_cat->load([
                'type',
                'licence',
                'updaterate',
                'filetypes',
                'user',
            ]);
        }

        return view('opendata/dataset/listingByCategory', compact('datasets', 'filters', 'filteredUnits'));
    }

    public function index()
    {
        return redirect()->route('opendata.home');
    }

    public function create()
    {
        $this->authorize('opendata.update');

        $unobtainable_reasons = UnobtainableReason::orderBy('open')->orderBy('name')->get();
        $restrictions = Restriction::orderBy('open')->orderBy('name')->get();
        $filetypes = Filetype::pluck('name', 'id');
        $licences = Licence::orderBy('name')->get()->pluck('fullname', 'id');
        $updaterates = Updaterate::pluck('name', 'id');
        $unit = Auth::user()->unit()->get();

        return view(
            'opendata.dataset.create',
            compact('unobtainable_reasons', 'restrictions', 'filetypes', 'licences', 'updaterates', 'unit')
        );
    }

    public function store(StoreOpendataDatasetPostRequest $request)
    {
        $this->authorize('opendata.update');

        $dataset = new Dataset();

        $dataset->saveToDb($request);

        if (Auth::user()->cannot('update', $dataset)) {
            $dataset->forceDelete();
            flash()->error('Σφάλμα!', 'Μη επαρκή δικαιώματα.');

            return redirect()->route('opendata.home');
        }

        flash()->success('Επιτυχία!', 'To νέο Σύνολο Δεδομένων αποθηκεύτηκε.');

        return redirect()->route('opendata.dataset.show', $dataset);
    }

    public function show(Dataset $dataset)
    {
        $dataset->load('unobtainable_reasons');

        return view('opendata.dataset.show', compact('dataset'));
    }

    public function edit(Dataset $dataset)
    {
        $this->authorize('update', $dataset);

        $dataset->load('unobtainable_reasons');

        $datasetFiletypes = $dataset->filetypes()->pluck('id')->toArray();

        $unit = $dataset->unit()->get();

        $unobtainable_reasons = UnobtainableReason::withTrashedAfter($dataset->unobtainable_reasons()->pluck('id')
            ->toArray())->orderBy('open')->orderBy('name')->get();
        $restrictions = Restriction::withTrashedAfter($dataset->restrictions()->pluck('id')->toArray())->orderBy('open')
            ->orderBy('name')->get();
        $filetypes = Filetype::withTrashedAfter($datasetFiletypes)->pluck('name', 'id');
        $licences = Licence::withTrashedAfter($dataset->licence_id)->orderBy('name')->get()->pluck('fullname', 'id');
        $updaterates = Updaterate::withTrashedAfter($dataset->updaterate_id)->pluck('name', 'id');

        return view(
            'opendata.dataset.edit',
            compact(
                'dataset',
                'datasetFiletypes',
                'unobtainable_reasons',
                'restrictions',
                'filetypes',
                'licences',
                'updaterates',
                'unit'
            )
        );
    }

    public function update(StoreOpendataDatasetPostRequest $request, Dataset $dataset)
    {
        $this->authorize('update', $dataset);

        $dataset->saveToDb($request);

        flash()->success('Επιτυχία!', 'To Σύνολο Δεδομένων ενημερώθηκε.');

        return redirect()->route('opendata.dataset.show', $dataset);
    }

    public function destroy(Request $request, Dataset $dataset)
    {
        $this->authorize('delete', $dataset);

        $dataset->delete();

        flash()->success('Επιτυχία!', 'Το Σύνολο Δεδομένων διαγράφηκε.');

        return redirect('/opendata/dataset/search');
    }
}
