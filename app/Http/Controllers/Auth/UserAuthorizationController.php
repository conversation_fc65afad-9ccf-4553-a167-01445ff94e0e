<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;

class UserAuthorizationController extends Controller
{
    public function getUser()
    {
        return response()->json(auth()->user()->only([
            'id', 'name', 'username', 'email', 'unit_id',
        ]));
    }

    public function getUserPermissions()
    {
        return response()->json(auth()->user()->roles()->get()->flatMap(function ($role) {
            return $role->permissions()->pluck('name');
        }));
    }

    /**
     * Get the user's authorized actions for a given model.
     *
     * In order to get the results, the corresponding
     * Policies should be defined and registred.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserAuthorizedActions()
    {
        $modelId = request('model.id') !== null ? (int) request('model.id') : null;
        $modelClass = (string) request('model.class');
        $unitId = request('context.unitId') !== null ? (int) request('context.unitId') : null;
        $modelFQN = 'App\Models\\'.request('model.class');

        if ($modelId === null) {
            return response()->json([
                'class' => $modelClass,
                'id' => null,
                'permissions' => [
                    'create' => $this->userCanCreate($modelFQN, $unitId),
                    'read' => null,
                    'update' => null,
                    'delete' => null,
                ],
            ]);
        }

        $modelInstance = $modelFQN::findOrFail($modelId);

        return response()->json([
            'class' => $modelClass,
            'id' => $modelId,
            'permissions' => [
                'create' => null,
                'read' => auth()->user()->can('read', $modelInstance),
                'update' => auth()->user()->can('read', $modelInstance),
                'delete' => auth()->user()->can('delete', $modelInstance),
            ],
        ]);
    }

    protected function userCanCreate(string $modelClass, int $unitId): bool
    {
        return (auth()->user()->can('create', $modelClass) && auth()->user()->unit_id === $unitId)
            || (auth()->user()->can('createGd', $modelClass) && auth()->user()->getGdirectorates()->pluck('id')->contains($unitId))
            || auth()->user()->can('createAll', $modelClass);
    }
}
