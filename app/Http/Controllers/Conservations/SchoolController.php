<?php

namespace App\Http\Controllers\Conservations;

use App\Http\Controllers\ReadyFormController;
use App\Models\Conservations\School;

class SchoolController extends ReadyFormController
{
    protected array $formFields = [
        ['name' => 'name', 'label' => 'Ονομασία', 'type' => 'text'],
    ];

    protected array $indexFields = ['name'];

    protected string $formTitle = 'Εκπαιδευτικά Ιδρύματα';

    protected bool $withTrashed = true;

    protected array $validationRules = [
        'name' => 'required|min:3|max:255',
    ];

    protected string $bladeLayout = 'layouts.conservations';

    protected array $permissions = [
        'index'  => 'conservations.read',
        'create' => 'conservations.admin',
        'read'   => 'conservations.read',
        'update' => 'conservations.admin',
        'delete' => 'conservations.admin',
    ];

    public function __construct(School $school)
    {
        $this->middleware('permission:conservations.read');

        parent::__construct($school);
    }
}
