<?php

namespace App\Http\Controllers\Conservations;

use App\Http\Controllers\ReadyFormController;
use App\Models\Conservations\Material;

class MaterialController extends ReadyFormController
{
    protected array $formFields = [['name' => 'name', 'label' => 'Ονομασία Υλικού', 'type' => 'text']];

    protected array $indexFields = ['name'];

    protected string $formTitle = 'Υλικά';

    protected bool $withTrashed = true;

    protected array $validationRules = ['name' => 'required|min:3|max:255'];

    protected string $bladeLayout = 'layouts.conservations';

    protected array $permissions = [
        'index'  => 'conservations.read',
        'create' => 'conservations.admin',
        'read'   => 'conservations.read',
        'update' => 'conservations.admin',
        'delete' => 'conservations.admin',
    ];

    public function __construct(Material $material)
    {
        $this->middleware('permission:conservations.read');

        parent::__construct($material);
    }
}
