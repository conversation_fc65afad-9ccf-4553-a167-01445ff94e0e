<?php

namespace App\Http\Controllers\Registry;

use Illuminate\Http\Request;
use App\Models\Registry\Rental;
use App\Models\Registry\Building;
use App\Http\Controllers\Controller;
use App\Http\Requests\Registry\RentalFormRequest;

class RentalController extends Controller
{

    public function store(RentalFormRequest $request)
    {
        $building = Building::findOrFail($request->input('building_id'));
        $this->authorize('update', $building);

        $rental = Rental::create(array_merge($request->all(), ['unit_id' => $building->unit_id]));

        return response()->json(['message' => 'H εκμίσθωση αποθηκεύτηκε επιτυχώς.', 'rental' => $rental]);
    }


    public function update(RentalFormRequest $request, Rental $rental)
    {
        $building = Building::findOrFail($rental->building_id);
        $this->authorize('update', $building);

        $rental->update($request->all());

        return response()->json([ 'message' => 'H εκμίσθωση ενημερώθηκε επιτυχώς.', 'rental' => $rental ]);
    }


    public function destroy(Request $request, Rental $rental)
    {
        $building = Building::findOrFail($rental->building_id);
        $this->authorize('update', $building);

        $rental->delete();

        return response()->json([ 'message' => 'H εκμίσθωση διαγράφτηκε επιτυχώς.']);
    }

    public function search()
    {
        $this->authorize('registry.read');

        $filters = '';
        $relatedModels = collect(Building::getRelatedModels());

        return view('registry.rental.search', compact('filters', 'relatedModels'));
    }

    public function showSearchResults(Request $request)
    {
        $this->authorize('registry.read');

        $filters = collect($request->all())->forget('_token');

        $queryFilters = $filters->filter(function ($value, $key) {
            // Filter out the empty inputs
            return $value != '';
        });

        $rentals = Rental::queryWithFilters($queryFilters)
            ->orderBy('building_id', 'asc')
            ->with(['unit', 'rentalProcess', 'building', 'building.associates', 'building.owner', 'building.ownershipType', 'building.buildingUsage', 'building.region', 'building.prefecture', 'building.rentals', 'building.phones', 'building.phones.unit', 'building.utilities', 'building.unit', 'building.hostedUnits', 'building.staff', 'building.staff.unit'])
            ->get();

        // dd($rentals->toJson());
        return response()->json(['message' => 'Ολοκλήρωση αναζήτησης.', 'resource' => $rentals, 'filters' => $filters]);
    }
}
