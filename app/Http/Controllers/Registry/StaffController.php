<?php

namespace App\Http\Controllers\Registry;

use Illuminate\Http\Request;
use App\Models\Registry\Staff;
use App\Models\Registry\Building;
use App\Http\Controllers\Controller;
use App\Http\Requests\Registry\StaffFormRequest;

class StaffController extends Controller
{

    public function store(StaffFormRequest $request)
    {
        $this->authorize('create', Staff::class);

        $staff = Staff::create($request->all());

        $staff->load(['unit']);

        return response()->json(['message' => 'Η καταγραφή προσωπικού Υπηρεσίας αποθηκεύτηκε επιτυχώς.', 'staff' => $staff]);
    }


    public function update(StaffFormRequest $request, Staff $staff)
    {
        $this->authorize('update', $staff);

        $staff->update($request->all());

        $staff->load(['unit']);

        return response()->json([ 'message' => 'Η καταγραφή προσωπικού Υπηρεσίας ενημερώθηκε επιτυχώς.', 'staff' => $staff ]);
    }


    public function destroy(Request $request, Staff $staff)
    {
        $this->authorize('delete', $staff);

        $staff->delete();

        return response()->json([ 'message' => 'Η καταγραφή προσωπικού Υπηρεσίας διαγράφτηκε επιτυχώς.']);
    }
}
