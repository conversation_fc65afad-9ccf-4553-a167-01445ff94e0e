<?php

namespace App\Http\Controllers\Registry;

use Illuminate\Http\Request;
use App\Models\Registry\Utility;
use App\Models\Registry\Building;
use App\Http\Controllers\Controller;
use App\Http\Requests\Registry\UtilityFormRequest;

class UtilityController extends Controller
{

    public function store(UtilityFormRequest $request)
    {
        $building = Building::findOrFail($request->input('building_id'));
        $this->authorize('update', $building);

        $utility = Utility::create(array_merge($request->all(), ['unit_id' => $building->unit_id]));

        return response()->json(['message' => 'Η παροχή αποθηκεύτηκε επιτυχώς.', 'utility' => $utility]);
    }


    public function update(UtilityFormRequest $request, Utility $utility)
    {
        $building = Building::findOrFail($utility->building_id);
        $this->authorize('update', $building);

        $utility->update($request->all());

        return response()->json([ 'message' => 'Η παροχή ενημερώθηκε επιτυχώς.', 'utility' => $utility ]);
    }


    public function destroy(Request $request, Utility $utility)
    {
        $building = Building::findOrFail($utility->building_id);
        $this->authorize('update', $building);

        $utility->delete();

        return response()->json([ 'message' => 'Η παροχή διαγράφτηκε επιτυχώς.']);
    }

    public function search()
    {
        $this->authorize('registry.read');

        $filters = '';
        $relatedModels = collect(Building::getRelatedModels());

        return view('registry.utility.search', compact('filters', 'relatedModels'));
    }

    public function showSearchResults(Request $request)
    {
        $this->authorize('registry.read');

        $filters = collect($request->all())->forget('_token');

        $queryFilters = $filters->filter(function ($value, $key) {
            // Filter out the empty inputs
            return $value != '';
        });

        $utilities = Utility::queryWithFilters($queryFilters)
            ->orderBy('building_id', 'asc')
            ->with('unit', 'utilityType', 'utilityProvider', 'building', 'building.ownershipType', 'building.buildingUsage', 'building.region', 'building.prefecture', 'building.rentals', 'building.associates', 'building.phones', 'building.phones.unit', 'building.utilities', 'building.unit', 'building.hostedUnits', 'building.staff', 'building.staff.unit')
            ->get();

        return response()->json(['message' => 'Ολοκλήρωση αναζήτησης.', 'resource' => $utilities, 'filters' => $filters]);
    }
}
