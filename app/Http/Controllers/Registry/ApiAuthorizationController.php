<?php

namespace App\Http\Controllers\Registry;

use App\Http\Controllers\Controller;
use Illuminate\Support\Str;

class ApiAuthorizationController extends Controller
{
    public function authorizePermission($permission)
    {
        return response()->json(request()->user()->can($permission));
    }

    public function authorizePolicy($app, $model, $id = null)
    {
        // This is simplistic. Better way here: http://stackoverflow.com/questions/33422627/laravel-5-1-dynamically-create-class-object-based-on-string/33422826
        $modelClass = "App\Models\\".Str::studly($app).'\\'.Str::studly($model);

        $instance = $modelClass::find($id);

        $permissions = [
            'create' => request()->user()->can('create', $modelClass),
            'read' => request()->user()->can('read', $instance),
            'update' => request()->user()->can('update', $instance),
            'delete' => request()->user()->can('delete', $instance),
        ];

        return response()->json($permissions);
    }

    public function authorizeCreateModel($app, $model)
    {
        $modelClass = "App\Models\\".Str::studly($app).'\\'.Str::studly($model);

        $permission = request()->user()->can('create', $modelClass);

        return response()->json($permission);
    }
}
