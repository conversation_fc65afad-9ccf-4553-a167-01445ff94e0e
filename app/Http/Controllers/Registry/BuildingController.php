<?php

namespace App\Http\Controllers\Registry;

use App\Http\Controllers\Controller;
use App\Http\Requests\Registry\BuildingFormRequest;
use App\Models\Registry\Building;
use App\Models\Registry\Staff;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BuildingController extends Controller
{
    public function show(Building $building)
    {
        $this->authorize('read', $building);

        $building->load([
            'ownershipType', 'buildingUsage', 'region', 'prefecture', 'hostedUnits', 'staff', 'staff.unit', 'rentals',
            'associates', 'phones', 'phones.unit', 'utilities', 'unit'
        ]);

        $relatedModels = collect(Building::getRelatedModels());

        return view('registry.building.show', compact('building', 'relatedModels'));
    }

    public function create()
    {
        $this->authorize('create', Building::class);

        $relatedModels = collect(Building::getRelatedModels());

        return view('registry.building.create', compact('relatedModels'));
    }

    public function store(BuildingFormRequest $request)
    {
        $this->authorize('create', Building::class);

        $building = Building::create(array_merge($request->all(), ['unit_id' => auth()->user()->unit_id]));
        $hostedUnits = [];
        if ($request->input('hosted_units')) {
            $building->hostedUnits()->attach((array) $request->input('hosted_units'));
            $building->logAttached('hostedUnits', (array) $request->input('hosted_units'));

            foreach ($request->input('hosted_units') as $unitId) {
                $hostedUnits[] = ['unit_id' => $unitId];
            }
        } else {
            $building->hostedUnits()->attach([auth()->user()->unit_id]);
            $building->logAttached('hostedUnits', [auth()->user()->unit_id]);

            $hostedUnits[] = ['unit_id' => auth()->user()->unit_id];
        }
        $building->staff()->createMany($hostedUnits);

        $building->load([
            'ownershipType', 'buildingUsage', 'hostedUnits', 'staff', 'staff.unit', 'region', 'prefecture',
            'rentals', 'associates', 'phones', 'phones.unit', 'utilities', 'unit'
        ]);

        return response()->json(['message' => 'Το ακίνητο αποθηκεύτηκε επιτυχώς.', 'building' => $building]);
    }

    public function edit(Building $building)
    {
        $this->authorize('update', $building);

        $relatedModels = collect(Building::getRelatedModels());

        $building->load(['ownershipType', 'buildingUsage', 'hostedUnits', 'unit']);

        return view('registry.building.edit', compact('building', 'relatedModels'));
    }

    public function update(BuildingFormRequest $request, Building $building)
    {
        $this->authorize('update', $building);

        // Find out if we have any new directorates to add to staff table
        $newHostedUnits = [];
        foreach ($request->input('hosted_units') as $unitId) {
            if (! in_array($unitId, $building->hostedUnits()->get()->pluck('id')->toArray(), true)) {
                $newHostedUnits[] = ['unit_id' => $unitId];
            }
        }
        if (count($newHostedUnits) > 0) {
            $building->staff()->createMany($newHostedUnits);
        }

        // Find and remove obsolete rows from staff table
        $oldHostedUnits = $building->staff->whereNotIn('unit_id', $request->input('hosted_units'));
        foreach ($oldHostedUnits as $staff_row) {
            $staff_row->delete();
        }

        $building->update($request->all());
        $syncedUnits = $building->hostedUnits()->sync((array) $request->input('hosted_units'));
        $building->logSynced('hostedUnits', $syncedUnits);

        $building->load([
            'ownershipType', 'buildingUsage', 'hostedUnits', 'staff', 'staff.unit', 'region', 'prefecture', 'rentals',
            'associates', 'phones', 'phones.unit', 'utilities', 'unit'
        ]);

        return response()->json(['message' => 'Το ακίνητο ενημερώθηκε επιτυχώς.', 'building' => $building]);
    }

    public function destroy(Request $request, Building $building)
    {
        $this->authorize('delete', $building);

        $building->delete();

        // flash()->success('Επιτυχία', 'Το ακίνητο διαγράφηκε.');

        return response()->json(['message' => 'To ακίνητο διαγράφτηκε επιτυχώς.']);
    }

    public function search()
    {
        $this->authorize('registry.read');

        $filters = '';
        $relatedModels = collect(Building::getRelatedModels());

        return view('registry.building.search', compact('filters', 'relatedModels'));
    }

    public function showSearchResults(Request $request)
    {
        $this->authorize('registry.read');

        $filters = collect($request->all())->forget('_token');

        $queryFilters = $filters->filter(function ($value, $key) {
            // Filter out the empty inputs
            return $value != '';
        });

        $buildings = Building::queryWithFilters($queryFilters)
            ->orderBy('id', 'asc')
            ->with(
                'ownershipType',
                'buildingUsage',
                'hostedUnits',
                'staff',
                'staff.unit',
                'region',
                'prefecture',
                'rentals',
                'associates',
                'phones',
                'phones.unit',
                'utilities',
                'unit'
            )
            ->get();

        return response()->json(['message' => 'Ολοκλήρωση αναζήτησης.', 'resource' => $buildings, 'filters' => $filters]);
    }

    public function printout(Building $building, $test = null)
    {
        $this->authorize('read', $building);

        $building->load([
            'ownershipType', 'buildingUsage', 'region', 'prefecture', 'hostedUnits', 'staff', 'staff.unit', 'rentals',
            'associates', 'phones', 'phones.unit', 'utilities', 'utilities.utilityType',
            'utilities.utilityProvider', 'unit'
        ]);

        $staffUnits = $building->staff()->with('unit')->get()->groupBy('unit_id')->toArray();

        $staffSpecializations = Staff::getSpecializations();

        if (! empty($test)) {
            return view('registry.building.printout', compact('building', 'staffUnits', 'staffSpecializations'));
        }
        $pdf = \App::make('dompdf.wrapper');
        $pdf->loadView('registry.building.printout', compact('building', 'staffUnits', 'staffSpecializations'));

        $filename = 'ΑΚΙΝΗΤΟ_'.$building->id.'.pdf';

        return $pdf->download($filename);
    }
}
