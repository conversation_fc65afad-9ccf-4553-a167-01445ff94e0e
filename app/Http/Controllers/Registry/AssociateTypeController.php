<?php

namespace App\Http\Controllers\Registry;

use App\Http\Controllers\ReadyFormController;
use App\Models\Registry\AssociateType;

class AssociateTypeController extends ReadyFormController
{
    protected bool $withTrashed = true;

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Τύπος συσχετιζομένου', 'type' => 'text'],
    ];

    protected array $indexFields = ['name'];

    protected string $formTitle = 'Συσχετιζόμενοι Κτιρίων';

    protected array $validationRules = [
        'name' => 'required|min:3|max:255',
    ];

    protected string $bladeLayout = 'layouts.registry';

    protected array $permissions = [
        'index'  => 'registry.admin',
        'create' => 'registry.admin',
        'read'   => 'registry.admin',
        'update' => 'registry.admin',
        'delete' => 'registry.admin',
    ];

    public function __construct(AssociateType $associateType)
    {
        parent::__construct($associateType);
    }
}
