<?php

namespace App\Http\Controllers\Registry;

use App\Http\Controllers\ReadyFormController;
use App\Models\Registry\PhoneProvider;

class PhoneProviderController extends ReadyFormController
{
    protected bool $withTrashed = true;

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Ονομασία Παρόχου', 'type' => 'text'],
    ];

    protected array $indexFields = ['name'];

    protected string $formTitle = 'Πάροχοι Τηλεφωνίας';

    protected array $validationRules = [
        'name' => 'required|min:3|max:255',
    ];

    protected string $bladeLayout = 'layouts.registry';

    protected array $permissions = [
        'index'  => 'registry.admin',
        'create' => 'registry.admin',
        'read'   => 'registry.admin',
        'update' => 'registry.admin',
        'delete' => 'registry.admin',
    ];

    public function __construct(PhoneProvider $phoneProvider)
    {
        parent::__construct($phoneProvider);
    }
}
