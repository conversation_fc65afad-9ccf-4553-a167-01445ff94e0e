<?php

namespace App\Http\Controllers\Registry;

use App\Http\Controllers\Controller;
use App\Http\Requests\Registry\PhoneFormRequest;
use App\Models\Registry\Building;
use App\Models\Registry\Phone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PhoneController extends Controller
{
    public function store(PhoneFormRequest $request)
    {
        $this->authorize('create', Phone::class);

        $phone = Phone::create(array_merge($request->all(), ['unit_id' => Auth::user()->unit_id]));

        $phone->load(['unit']);

        return response()->json(['message' => 'O αριθμός τηλεφώνου αποθηκεύτηκε επιτυχώς.', 'phone' => $phone]);
    }

    public function update(PhoneFormRequest $request, Phone $phone)
    {
        $this->authorize('update', $phone);

        $phone->update($request->all());

        $phone->load(['unit']);

        return response()->json(['message' => 'Ο αριθμός τηλεφώνου ενημερώθηκε επιτυχώς.', 'phone' => $phone]);
    }

    public function destroy(Request $request, Phone $phone)
    {
        $this->authorize('delete', $phone);

        $phone->delete();

        return response()->json(['message' => 'Ο αριθμός τηλεφώνου διαγράφτηκε επιτυχώς.']);
    }

    public function search()
    {
        $this->authorize('registry.read');

        $filters = '';
        $relatedModels = collect(Building::getRelatedModels());

        return view('registry.phone.search', compact('filters', 'relatedModels'));
    }

    public function showSearchResults(Request $request)
    {
        $this->authorize('registry.read');

        $filters = collect($request->all())->forget('_token');

        $queryFilters = $filters->filter(function ($value, $key) {
            // Filter out the empty inputs
            return $value != '';
        });

        $phones = Phone::queryWithFilters($queryFilters)
            ->orderBy('building_id', 'asc')
            ->with('unit', 'building', 'building.ownershipType', 'building.buildingUsage', 'building.region', 'building.prefecture', 'building.rentals', 'building.associates', 'building.phones', 'building.phones.unit', 'building.utilities', 'building.unit', 'building.hostedUnits', 'building.staff', 'building.staff.unit')
            ->get();

        return response()->json(['message' => 'Ολοκλήρωση αναζήτησης.', 'resource' => $phones, 'filters' => $filters]);
    }
}
