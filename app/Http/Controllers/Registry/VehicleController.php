<?php

namespace App\Http\Controllers\Registry;

use Illuminate\Http\Request;
use App\Models\Registry\Vehicle;
use App\Models\Personnel\Employee;
use App\Http\Controllers\Controller;
use App\Models\Registry\VehicleType;
use App\Http\Requests\Registry\VehicleFormRequest;

class VehicleController extends Controller
{
    public function show(Vehicle $vehicle)
    {
        $this->authorize('read', $vehicle);

        $vehicle->load(['vehicleInsurances', 'employee', 'unit']);

        $vehicleTypes = VehicleType::orderBy('is_other')->orderBy('name')->get(['id', 'name', 'is_other']);

        return view('registry.vehicle.show', compact('vehicle', 'vehicleTypes'));
    }

    public function create()
    {
        $this->authorize('create', Vehicle::class);

        $vehicleTypes = VehicleType::orderBy('is_other')->orderBy('name')->get(['id', 'name', 'is_other']);
        // $employees = Employee::where('id','<',10000)->orderBy('fullname')->get(['id','fullname']);

        return view('registry.vehicle.create', compact('vehicleTypes'));
    }

    public function store(VehicleFormRequest $request)
    {
        $this->authorize('create', Vehicle::class);

        $vehicle = Vehicle::create(array_merge($request->all(), ['unit_id' => auth()->user()->unit_id]));

        $vehicle->load(['vehicleInsurances', 'employee', 'unit']);

        return response()->json(['message' => 'Το όχημα αποθηκεύτηκε επιτυχώς.', 'vehicle' => $vehicle]);
    }

    public function edit(Vehicle $vehicle)
    {
        $this->authorize('update', $vehicle);

        $vehicleTypes = VehicleType::orderBy('is_other')->orderBy('name')->get(['id', 'name', 'is_other']);
        $employees = Employee::orderBy('fullname')->get(['id', 'fullname']);

        $vehicle->load(['vehicleInsurances', 'employee', 'unit']);

        return view('registry.vehicle.edit', compact('vehicleTypes', 'employees', 'vehicle'));
    }

    public function update(VehicleFormRequest $request, Vehicle $vehicle)
    {
        $this->authorize('update', $vehicle);

        $vehicle->update($request->all());

        $vehicle->load(['vehicleInsurances', 'employee', 'unit']);

        return response()->json(['message' => 'Το όχημα ενημερώθηκε επιτυχώς.', 'vehicle' => $vehicle]);
    }

    public function destroy(Request $request, Vehicle $vehicle)
    {
        $this->authorize('delete', $vehicle);

        $vehicle->delete();

        return response()->json(['message' => 'To όχημα διαγράφτηκε επιτυχώς.']);
    }

    public function search()
    {
        $this->authorize('registry.read');

        $filters = '';
        $vehicleTypes = VehicleType::orderBy('is_other')->orderBy('name')->get(['id', 'name', 'is_other']);
        $employees = Employee::orderBy('fullname')->get(['id', 'fullname']);

        return view('registry.vehicle.search', compact('vehicleTypes', 'employees', 'filters'));
    }

    public function showSearchResults(Request $request)
    {
        $this->authorize('registry.read');

        $filters = collect($request->all())->forget('_token');

        $queryFilters = $filters->filter(function ($value, $key) {
            // Filter out the empty inputs
            return '' != $value;
        });

        $vehicles = Vehicle::queryWithFilters($queryFilters)
            ->orderBy('id', 'asc')
            ->with('vehicleInsurances', 'employee', 'vehicleType', 'unit')
            ->get();

        foreach ($vehicles as $vehicle) {
            $vehicle->employee_fullname = $vehicle->employee ? $vehicle->employee->fullname : '';

            $vehicle_insurance_amount = $vehicle->vehicleInsurances()
                                                ->validOn($request->input('valid_on'))
                                                ->sum('amount');

            $vehicle->vehicle_insurance_amount = number_format($vehicle_insurance_amount, 2, '.', '');
        }

        return response()->json([
            'message'  => 'Ολοκλήρωση αναζήτησης.',
            'resource' => $vehicles,
            'filters'  => $filters,
        ]);
    }

    public function printout(Vehicle $vehicle, $test = null)
    {
        $this->authorize('read', $vehicle);

        $vehicle->load(['vehicleInsurances', 'vehicleType', 'employee', 'unit']);

        if (!empty($test)) {
            return view('registry.vehicle.printout', compact('vehicle'));
        }

        $pdf = \App::make('dompdf.wrapper');
        $pdf->loadView('registry.vehicle.printout', compact('vehicle'));

        $filename = 'ΟΧΗΜΑ_'.$vehicle->id.'.pdf';

        return $pdf->download($filename);
    }
}
