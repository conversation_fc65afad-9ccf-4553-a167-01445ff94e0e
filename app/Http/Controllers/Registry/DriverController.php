<?php

namespace App\Http\Controllers\Registry;

use App\Http\Controllers\Controller;
use App\Models\Personnel\Employee;
use Illuminate\Http\Request;

class DriverController extends Controller
{
    /**
     * Ajax search Employee
     * Search for Employee based on keypresses on select2 form input.
     */
    public function search(Request $request)
    {
        $term = $request['q'];

        if ($term) {
            $term = '%'.$term.'%';

            $emloyees = Employee::with('unit')->where('compass_id', 'like', $term)->orWhere('fullname', 'like', $term)->get();

            $list = [];

            foreach ($emloyees as $employee) {
                $employeeText = $employee->surname.' '.$employee->name.', '.$employee->fathername;
                if (! empty($employee->unit_id)) {
                    $employeeText .= ' ('.$employee->unit->abbrv.')';
                }

                $list[] = ['text' => $employeeText, 'id' => $employee->id];
            }

            return $list;
        } else {
            return $term;
        }
    }
}
