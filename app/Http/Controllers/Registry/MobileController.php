<?php

namespace App\Http\Controllers\Registry;

use App\Http\Controllers\Controller;
use App\Http\Requests\Registry\MobileFormRequest;
use App\Models\Registry\Mobile;
use App\Models\Registry\MobileProvider;
use Illuminate\Http\Request;

class MobileController extends Controller
{
    public function show(Mobile $mobile)
    {
        $this->authorize('read', $mobile);

        $mobileProviders = MobileProvider::orderBy('name')->get(['id', 'name']);

        $mobile->load(['unit']);

        return view('registry.mobile.show', compact('mobile', 'mobileProviders'));
    }

    public function create()
    {
        $this->authorize('create', Mobile::class);

        $mobileProviders = MobileProvider::orderBy('name')->get(['id', 'name']);

        return view('registry.mobile.create', compact('mobileProviders'));
    }

    public function store(MobileFormRequest $request)
    {
        $this->authorize('create', Mobile::class);

        $mobile = Mobile::create(array_merge($request->all(), ['unit_id' => auth()->user()->unit_id]));

        $mobile->load(['unit']);

        return response()->json(['message' => 'Το τηλέφωνο αποθηκεύτηκε επιτυχώς.', 'mobile' => $mobile]);
    }

    public function edit(Mobile $mobile)
    {
        $this->authorize('update', $mobile);

        $mobileProviders = MobileProvider::orderBy('name')->get(['id', 'name']);

        $mobile->load(['unit']);

        return view('registry.mobile.edit', compact('mobile', 'mobileProviders'));
    }

    public function update(MobileFormRequest $request, Mobile $mobile)
    {
        $this->authorize('update', $mobile);

        $mobile->update($request->all());

        $mobile->load(['unit']);

        return response()->json(['message' => 'Το τηλέφωνο ενημερώθηκε επιτυχώς.', 'mobile' => $mobile]);
    }

    public function destroy(Request $request, Mobile $mobile)
    {
        $this->authorize('delete', $mobile);

        $mobile->delete();

        return response()->json(['message' => 'To τηλέφωνο διαγράφτηκε επιτυχώς.']);
    }

    /**
     * Show the form for searching for mobiles.
     */
    public function search()
    {
        $this->authorize('registry.read');

        $filters = '';
        $mobileProviders = MobileProvider::orderBy('id')->get(['id', 'name']);

        return view('registry.mobile.search', compact('mobileProviders', 'filters'));
    }

    /**
     * Mobile search results.
     * Returns search results from Mobiles based on search form filters.
     */
    public function showSearchResults(Request $request)
    {
        $this->authorize('registry.read');

        $filters = collect($request->all())->forget('_token');

        $queryFilters = $filters->filter(function ($value, $key) {
            // Filter out the empty inputs
            return $value != '';
        });

        $mobiles = Mobile::queryWithFilters($queryFilters)
            ->orderBy('id', 'asc')
            ->with('mobileProvider', 'unit')
            ->get();

        foreach ($mobiles as $mobile) {
            $mobile->mobileProvider_name = $mobile->mobileProvider ? $mobile->mobileProvider->name : '';
        }

        return response()->json(['message' => 'Ολοκλήρωση αναζήτησης.', 'resource' => $mobiles, 'filters' => $filters]);
    }

    public function printout(Mobile $mobile, $test = null)
    {
        $this->authorize('read', $mobile);

        $mobile->load(['mobileProvider', 'unit']);

        if (! empty($test)) {
            return view('registry.mobile.printout', compact('mobile'));
        }

        $pdf = \App::make('dompdf.wrapper');
        $pdf->loadView('registry.mobile.printout', compact('mobile'));

        $filename = 'ΚΙΝΤΗΛ_'.$mobile->id.'.pdf';

        return $pdf->download($filename);
    }
}
