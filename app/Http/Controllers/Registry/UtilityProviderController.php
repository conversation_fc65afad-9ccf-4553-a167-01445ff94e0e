<?php

namespace App\Http\Controllers\Registry;

use App\Http\Controllers\ReadyFormController;
use App\Models\Registry\UtilityProvider;

class UtilityProviderController extends ReadyFormController
{
    protected array $relationships = ['utilities', 'utilityType'];

    protected bool $withTrashed = true;

    protected array $formFields = [
        ['name' => 'utility_type_id', 'label' => 'Είδος Παροχής', 'type' => 'select', 'relationship' => 'utilityType'],
        ['name' => 'name', 'label' => 'Πάροχος', 'type' => 'text'],
    ];

    protected array $indexFields = ['utility_type_id', 'name'];

    protected string $formTitle = 'Εταιρείες Παροχών';

    protected array $validationRules = [
        'name' => 'required|min:3|max:255',
        'utility_type_id' => 'required',
    ];

    protected string $bladeLayout = 'layouts.registry';

    protected array $permissions = [
        'index'  => 'registry.admin',
        'create' => 'registry.admin',
        'read'   => 'registry.admin',
        'update' => 'registry.admin',
        'delete' => 'registry.admin',
    ];

    public function __construct(UtilityProvider $utilityProvider)
    {
        parent::__construct($utilityProvider);
    }
}
