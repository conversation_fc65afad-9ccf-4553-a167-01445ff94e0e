<?php

namespace App\Http\Controllers\Registry;

use Illuminate\Http\Request;
use App\Models\Registry\Associate;
use App\Models\Registry\Building;
use App\Http\Controllers\Controller;
use App\Http\Requests\Registry\AssociateFormRequest;

class AssociateController extends Controller
{

    public function store(AssociateFormRequest $request)
    {
        $building = Building::findOrFail($request->input('building_id'));
        $this->authorize('update', $building);

        $associate = Associate::create($request->all());

        return response()->json(['message' => 'O συσχετιζόμενος αποθηκεύτηκε επιτυχώς.', 'associate' => $associate]);
    }


    public function update(AssociateFormRequest $request, Associate $associate)
    {
        $building = Building::findOrFail($associate->building_id);
        $this->authorize('update', $building);

        $associate->update($request->all());

        return response()->json([ 'message' => 'Ο συσχετιζόμενος ενημερώθηκε επιτυχώς.', 'associate' => $associate ]);
    }


    public function destroy(Request $request, Associate $associate)
    {
        $building = Building::findOrFail($associate->building_id);
        $this->authorize('update', $building);

        $associate->delete();

        return response()->json([ 'message' => 'Ο συσχετιζόμενος διαγράφτηκε επιτυχώς.']);
    }
}
