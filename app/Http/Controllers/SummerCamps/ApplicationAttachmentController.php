<?php

namespace App\Http\Controllers\SummerCamps;

use App\Http\Controllers\Controller;
use App\Http\Requests\SummerCamps\ApplicationAttachmentRequest;
use App\Models\SummerCamps\Application;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ApplicationAttachmentController extends Controller
{
    public function index(Application $application)
    {
        if (!$application->belongsToUser(Auth::user()) && Auth::user()->cannot('summerCamps.admin')) {
            abort(403, 'Δεν έχετε δικαίωμα προβολής της αίτησης.');
        }

        $attachments = $application->getMedia('applications')
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            });

        return $attachments;
    }

    public function store(ApplicationAttachmentRequest $request, Application $application)
    {
        if (!$application->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (!$application->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $newAttachments = collect(request('attachments'))
            ->map(function ($attachment) use ($application) {
                return $application->addMedia($attachment)
                    ->usingFileName('document.'.$attachment->getClientOriginalExtension())
                    ->withCustomProperties([
                        'mime-type' => $attachment->getClientMimeType(),
                        'extension' => $attachment->getClientOriginalExtension(),
                    ])
                    ->toMediaCollection('applications', 'summer_camps_disk');
            })
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            });

        return $newAttachments;
    }

    public function destroy(Application $application, Media $attachment)
    {
        if (!$application->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (!$application->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $application->deleteMedia($attachment->id);

        return response()->json(['message' => 'Το αρχείο διαγράφηκε'], 200);
    }
}
