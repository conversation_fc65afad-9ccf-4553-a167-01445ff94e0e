<?php

namespace App\Http\Controllers\SummerCamps;

use App\Http\Controllers\Controller;
use App\Http\Requests\SummerCamps\ApplicationRequest;
use App\Models\SummerCamps\Application;
use App\Models\SummerCamps\EmploymentSector;
use App\Models\SummerCamps\EmploymentType;
use App\Models\SummerCamps\Season;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ApplicationController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        $seasons = Season::where('type', '=', 'main')
            ->with(['applications' => function ($query) use ($user) {
                $query->where('user_id', $user->id);
            }])
            ->orderBy('start_date')->get();

        return view('summerCamps.application.index', [
            'seasons' => $seasons,
        ]);
    }

    public function create(Request $request)
    {
        $seasonId = (int) $request->get('season_id');
        $user = Auth::user();

        $application = Application::where('season_id', $seasonId)
            ->where('user_id', $user->id)
            ->first();

        if (! $application) {
            $application = Application::create([
                'season_id' => $seasonId,
                'user_id' => $user->id,
                'name' => Str::before($user->name, ' '),
                'surname' => Str::after($user->name, ' '),
                'email_address' => $user->email,
            ]);
        }

        $application->load('applicationChildren');

        return view('summerCamps.application.edit', [
            'application' => $application,
            'employmentSectorOptions' => EmploymentSector::select(['id', 'name'])->get(),
            'employmentTypeOptions' => EmploymentType::select(['id', 'name'])->get(),
        ]);
    }

    public function show(Application $application)
    {
        if (! $application->belongsToUser(Auth::user()) && Auth::user()->cannot('summerCamps.admin')) {
            abort(403, 'Δεν έχετε δικαίωμα προβολής της αίτησης.');
        }

        $application->load(['employmentType', 'employmentSector', 'applicationChildren']);
        $application->attachments = $application->media
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            });

        return view('summerCamps.application.show', [
            'application' => $application,
            'updatePermission' => Auth::user()->id == $application->user_id && $application->canBeEdited(),
        ]);
    }

    public function edit(Application $application)
    {
        if (! $application->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (! $application->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $application->load('applicationChildren');
        $application->attachments = $application->media
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            });

        return view('summerCamps.application.edit', [
            'application' => $application,
            'employmentSectorOptions' => EmploymentSector::select(['id', 'name'])->get(),
            'employmentTypeOptions' => EmploymentType::select(['id', 'name'])->get(),
        ]);
    }

    public function update(Application $application, ApplicationRequest $request)
    {
        if (! $application->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (! $application->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $application->update($request->all());

        return response()->json(['message' => 'Success', 'data' => $application], 200);
    }

    public function destroy(Application $application)
    {
        if (! $application->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (! $application->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        try {
            $application->delete();
        } catch (\Throwable $e) {
            return response('Error Deleting', 422);
        }

        return response('Success', 200);
    }
}
