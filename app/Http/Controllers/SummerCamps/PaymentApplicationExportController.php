<?php

namespace App\Http\Controllers\SummerCamps;

use App\Http\Controllers\Controller;
use App\Models\SummerCamps\PaymentApplication;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class PaymentApplicationExportController extends Controller
{
    public function show(PaymentApplication $paymentApplication) {
        if (!$paymentApplication->belongsToUser(Auth::user()) && Auth::user()->cannot('summerCamps.admin')) {
            abort(403, 'Δεν έχετε δικαίωμα προβολής της αίτησης αποπληρωμής.');
        }

        $paymentApplication->load(['employmentType', 'employmentSector', 'paymentApplicationChildren']);
        $paymentApplication->attachments = $paymentApplication->media
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name . '.' . $file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            });

        $pdf = \App::make('dompdf.wrapper');

        $pdf->loadView('summerCamps.paymentApplication.export', [
            'season' => $paymentApplication->season,
            'paymentApplication' => $paymentApplication,
            'updatePermission' => Auth::user()->id == $paymentApplication->user_id,
        ]);

        $filename = 'Αίτηση Αποπληρωμής'.$paymentApplication->protocol.'.pdf';

        return $pdf->download($filename);
    }
}
