<?php

namespace App\Http\Controllers\SummerCamps;

use App\Http\Controllers\Controller;
use App\Models\SummerCamps\Application;

use App\Services\MediaLibrary\MediaStream;
use Illuminate\Support\Facades\Auth;


class ApplicationAttachmentZipController extends Controller
{
    public function show(Application $application)
    {
        if (!$application->belongsToUser(Auth::user()) && Auth::user()->cannot('summerCamps.admin')) {
            abort(403, 'Δεν έχετε δικαίωμα προβολής της αίτησης.');
        }

        // Let's get some media.
        $downloads = $application->getMedia('applications');

        // Download the files associated with the media in a streamed way.
        // No prob if your files are very large.
        return MediaStream::create("application-{$application->protocol}.zip")->addMedia($downloads);
    }
}
