<?php

namespace App\Http\Controllers\SummerCamps;

use App\Http\Controllers\Controller;
use App\Http\Requests\SummerCamps\PaymentApplicationRequest;
use App\Models\SummerCamps\Application;
use App\Models\SummerCamps\EmploymentSector;
use App\Models\SummerCamps\EmploymentType;
use App\Models\SummerCamps\PaymentApplication;
use App\Models\SummerCamps\Season;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class PaymentApplicationController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        $seasons = Season::where('type', 'payment')
            ->with(['paymentApplications' => function ($query) use ($user) {
                $query->where('user_id', $user->id);
            }])
            ->orderBy('start_date')->get();

        return view('summerCamps.paymentApplication.index', [
            'seasons' => $seasons,
        ]);
    }

    public function create(Request $request)
    {
        $seasonId = (int) $request->get('season_id');
        $season = Season::find($seasonId);
        $mainSeason = Season::where('type', 'main')
            ->where('start_date', '<', $season->start_date)
            ->where('start_date', '>=', Carbon::create($season->year))
            ->first();
        $user = Auth::user();

        $application = Application::where('user_id', $user->id)->ofSeason($mainSeason->id)->submitted()->latest()->first();
        if (empty($application)) {
            abort(422, 'Δεν έχετε υποβάλλει αίτηση συμμετοχής για το έτος '.$season->year);
        }

        $application->load('applicationChildren');

        $paymentApplication = PaymentApplication::where('season_id', $seasonId)
            ->where('user_id', $user->id)
            ->first();

        if (! $paymentApplication) {
            $paymentApplication = PaymentApplication::create([
                'season_id' => $seasonId,
                'user_id' => $user->id,
                'name' => Str::before($user->name, ' '),
                'surname' => Str::after($user->name, ' '),
                'email_address' => $user->email,
                'father_name' => $application->father_name,
                'employment_sector_id' => $application->employment_sector_id,
                'employment_type_id' => $application->employment_type_id,
                'position' => $application->position,
                'personal_phone' => $application->personal_phone,
                'mobile_phone' => $application->mobile_phone,
                'work_phone' => $application->work_phone,
            ]);
            $application->applicationChildren->each(function ($child) use ($paymentApplication) {
                $paymentApplication->paymentApplicationChildren()->create([
                    'full_name' => $child->full_name,
                    'has_disability' => $child->has_disability,
                    'summer_Camps' => $child->summer_camps,
                ]);
            });
            $paymentApplication->refresh();
        }

        $paymentApplication->load('paymentApplicationChildren');

        return view('summerCamps.paymentApplication.edit', [
            'paymentApplication' => $paymentApplication,
            'employmentSectorOptions' => EmploymentSector::select(['id', 'name'])->get(),
            'employmentTypeOptions' => EmploymentType::select(['id', 'name'])->get(),
        ]);
    }

    public function show(PaymentApplication $paymentApplication)
    {
        if (! $paymentApplication->belongsToUser(Auth::user()) && Auth::user()->cannot('summerCamps.admin')) {
            abort(403, 'Δεν έχετε δικαίωμα προβολής της αίτησης.');
        }

        $paymentApplication->load(['employmentType', 'employmentSector', 'paymentApplicationChildren']);
        $paymentApplication->attachments = $paymentApplication->media->filter(function (Media $file) {
            return $file->collection_name == 'payment_applications';
        })
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            })
            ->values();
        $paymentApplication->signedAttachments = $paymentApplication->media->filter(function (Media $file) {
            return $file->collection_name == 'signed_payment_applications';
        })
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            })
            ->values();

        return view('summerCamps.paymentApplication.show', [
            'paymentApplication' => $paymentApplication,
            'updatePermission' => Auth::user()->id == $paymentApplication->user_id && $paymentApplication->canBeEdited(),
        ]);
    }

    public function edit(PaymentApplication $paymentApplication)
    {
        if (! $paymentApplication->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (! $paymentApplication->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $paymentApplication->load('paymentApplicationChildren');
        $paymentApplication->attachments = $paymentApplication->media->filter(function (Media $file) {
            return $file->collection_name == 'payment_applications';
        })
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            })->values();

        return view('summerCamps.paymentApplication.edit', [
            'paymentApplication' => $paymentApplication,
            'employmentSectorOptions' => EmploymentSector::select(['id', 'name'])->get(),
            'employmentTypeOptions' => EmploymentType::select(['id', 'name'])->get(),
        ]);
    }

    public function update(PaymentApplication $paymentApplication, PaymentApplicationRequest $request)
    {
        if (! $paymentApplication->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (! $paymentApplication->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $paymentApplication->update($request->all());

        return response()->json(['message' => 'Success', 'data' => $paymentApplication], 200);
    }

    public function destroy(PaymentApplication $paymentApplication)
    {
        if (! $paymentApplication->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (! $paymentApplication->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        try {
            $paymentApplication->delete();
        } catch (\Throwable $e) {
            return response('Error Deleting', 422);
        }

        return response('Success', 200);
    }
}
