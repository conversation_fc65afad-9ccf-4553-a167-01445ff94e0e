<?php

namespace App\Http\Controllers\SummerCamps;

use App\Http\Controllers\Controller;
use App\Models\SummerCamps\PaymentApplication;
use App\Services\SummerCamps\Protocol\ProtocolNumberGenerator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Validator;

class SubmittedPaymentApplicationController extends Controller
{
    public function __construct(
        protected ProtocolNumberGenerator $protocolGenerator
    ) {}

    public function store(PaymentApplication $paymentApplication)
    {
        if (! $paymentApplication->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (! $paymentApplication->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $paymentApplication->load(['paymentApplicationChildren', 'media']);

        $validator = Validator::make($paymentApplication->toArray(), [
            'name' => 'required',
            'surname' => 'required',
            'father_name' => 'required',
            'employment_sector_id' => 'required',
            'employment_type_id' => 'required',
            'position' => 'required',
            'payroll_id' => 'required',
            'email_address' => 'required|email:rfc,strict',
            'payment_application_children' => 'required',
            'media' => 'required',
            'personal_phone' => function ($attribute, $value, $fail) use ($paymentApplication) {
                if (empty($paymentApplication->personal_phone.$paymentApplication->mobile_phone.$paymentApplication->work_phone)) {
                    $fail('Δεν έχετε καταχωρήσει κάποιο τηλέφωνο επικοινωνίας');
                }
            },
        ], [
            'name' => 'To πεδίο Όνομα είναι υποχρεωτικό',
            'surname' => 'To πεδίο Επώνυμο είναι υποχρεωτικό',
            'father_name' => 'To πεδίο Πατρώνυμο είναι υποχρεωτικό',
            'employment_sector_id' => 'To πεδίο Σχέση Εργασίας είναι υποχρεωτικό',
            'employment_type_id' => 'To πεδίο Τομέας είναι υποχρεωτικό',
            'position' => 'To πεδίο Υπηρεσία Οργανικής Θέσης είναι υποχρεωτικό',
            'payroll_id' => 'To πεδίο Αρ. Μητρώου Μισθοδοσίας είναι υποχρεωτικό',
            'email_address' => 'To πεδίο Email Επικοινωνίας είναι υποχρεωτικό',
            'payment_application_children' => 'Δεν έχετε καταχωρήσει τέκνα για φιλοξενία',
            'media' => 'Δεν έχετε επισυνάψει δικαιολογητικά',
        ]);

        if ($validator->fails()) {
            return response(['message' => 'Ελλειπή Στοιχεία', 'errors' => $validator->getMessageBag()], 422);
        }

        try {
            $paymentApplication->is_submitted = true;
            if (empty($paymentApplication->protocol)) {
                $paymentApplication->protocol = $this->protocolGenerator->generate();
            }
            $paymentApplication->submitted_at = now();
            $paymentApplication->save();
        } catch (\Throwable $e) {
            Log::error('Error when setting summer camps application protocol: '.$e->getMessage());

            return response($e->getMessage(), 422);
        }

        return $paymentApplication;
    }

    public function destroy(PaymentApplication $paymentApplication)
    {
        if (! $paymentApplication->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (! $paymentApplication->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        try {
            $paymentApplication->is_submitted = false;
            $paymentApplication->submitted_at = null;
            $paymentApplication->save();

            $paymentApplication->getMedia('signed_payment_applications')->each(function ($media) {
                $media->delete();
            });
        } catch (\Throwable $e) {
            Log::error('Error when revoking submitted payment application: '.$e->getMessage());

            return response('Error Withdrawing Application', 422);
        }

        return $paymentApplication;
    }
}
