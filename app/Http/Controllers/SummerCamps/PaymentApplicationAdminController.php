<?php

namespace App\Http\Controllers\SummerCamps;

use App\Http\Controllers\Controller;
use App\Models\SummerCamps\EmploymentSector;
use App\Models\SummerCamps\EmploymentType;
use App\Models\SummerCamps\PaymentApplication;
use App\Models\SummerCamps\Season;
use App\Presenters\SummerCamps\PaymentApplicationPresenter;

class PaymentApplicationAdminController extends Controller
{
    public function index(Season $season)
    {
        $this->authorize('summerCamps.admin');

        return view('summerCamps.paymentApplicationAdmin.show', [
            'season' => $season,
            'employmentSectorOptions' => EmploymentSector::select(['id', 'name'])->get(),
            'employmentTypeOptions' => EmploymentType::select(['id', 'name'])->get(),
        ]);
    }

    public function paymentApplications(Season $season)
    {
        $this->authorize('summerCamps.admin');

        $queryString = request()->query();

        $paymentApplications = PaymentApplication::ofSeason($season->id)
            ->with([
                'user:id,name,email',
                'season:id,name,description',
                'employmentSector:id,name',
                'employmentType:id,name',
                'paymentApplicationChildren:id,full_name',
            ])
            ->withCount('paymentApplicationChildren')
            ->filter($queryString)
            ->paginate((int) request()->query('limit', '10'));

        return response()->json([
            'message' => 'Success',
            'data' => PaymentApplicationPresenter::pagination($paymentApplications),
        ])->cookie('summer_camps_payment_applications_filters', json_encode($queryString), 20, null, null, false, false);
    }
}
