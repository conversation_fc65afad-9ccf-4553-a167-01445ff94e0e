<?php

namespace App\Http\Controllers\SummerCamps;

use App\Http\Controllers\Controller;
use App\Http\Requests\SummerCamps\PaymentApplicationAttachmentRequest;
use App\Models\SummerCamps\PaymentApplication;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class PaymentApplicationAttachmentController extends Controller
{
    public function index(PaymentApplication $paymentApplication)
    {
        if (!$paymentApplication->belongsToUser(Auth::user()) && Auth::user()->cannot('summerCamps.admin')) {
            abort(403, 'Δεν έχετε δικαίωμα προβολής της αίτησης.');
        }

        $attachments = $paymentApplication->getMedia('payment_applications')
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            });

        return $attachments;
    }

    public function store(PaymentApplicationAttachmentRequest $request, PaymentApplication $paymentApplication)
    {
        if (!$paymentApplication->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (!$paymentApplication->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $newAttachments = collect(request('attachments'))
            ->map(function ($attachment) use ($paymentApplication) {
                return $paymentApplication->addMedia($attachment)
                    ->usingFileName('document.'.$attachment->getClientOriginalExtension())
                    ->withCustomProperties([
                        'mime-type' => $attachment->getClientMimeType(),
                        'extension' => $attachment->getClientOriginalExtension(),
                    ])
                    ->toMediaCollection('payment_applications', 'summer_camps_disk');
            })
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            });

        return $newAttachments;
    }

    public function destroy(PaymentApplication $paymentApplication, Media $attachment)
    {
        if (!$paymentApplication->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (!$paymentApplication->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $paymentApplication->deleteMedia($attachment->id);

        return response()->json(['message' => 'Το αρχείο διαγράφηκε'], 200);
    }
}
