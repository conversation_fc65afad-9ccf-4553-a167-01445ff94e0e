<?php

namespace App\Http\Controllers\SummerCamps;

use App\Http\Controllers\Controller;
use App\Models\SummerCamps\Application;
use App\Services\SummerCamps\Protocol\ProtocolNumberGenerator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Validator;

class SubmittedApplicationController extends Controller
{
    public function __construct(
        protected ProtocolNumberGenerator $protocolGenerator
    ) {}

    public function store(Application $application)
    {
        if (!$application->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (!$application->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $application->load(['applicationChildren','media']);

        $validator = Validator::make($application->toArray(), [
            'name' => 'required',
            'surname' => 'required',
            'father_name' => 'required',
            'employment_sector_id' => 'required',
            'employment_type_id' => 'required',
            'position' => 'required',
            'afm' => 'required|afm',
            'doy' => 'required',
            'email_address' => 'required|email:rfc,strict',
            'application_children' => 'required',
            'media' => 'required',
            'personal_phone' => function ($attribute, $value, $fail) use ($application) {
                if (empty($application->personal_phone . $application->mobile_phone . $application->work_phone)) {
                    $fail('Δεν έχετε καταχωρήσει κάποιο τηλέφωνο επικοινωνίας');
                }
            }
        ], [
            'name' => 'To πεδίο Όνομα είναι υποχρεωτικό',
            'surname' => 'To πεδίο Επώνυμο είναι υποχρεωτικό',
            'father_name' => 'To πεδίο Πατρώνυμο είναι υποχρεωτικό',
            'employment_sector_id' => 'To πεδίο Σχέση Εργασίας είναι υποχρεωτικό',
            'employment_type_id' => 'To πεδίο Τομέας είναι υποχρεωτικό',
            'position' => 'To πεδίο Υπηρεσία Οργανικής Θέσης είναι υποχρεωτικό',
            'afm' => 'To πεδίο ΑΦΜ είναι υποχρεωτικό και πρέπει να είναι έγκυρο ΑΦΜ',
            'doy' => 'To πεδίο Αρμόδια Δ.Ο.Υ. είναι υποχρεωτικό',
            'email_address' => 'To πεδίο Email Επικοινωνίας είναι υποχρεωτικό',
            'application_children' => 'Δεν έχετε καταχωρήσει τέκνα για φιλοξενία',
            'media' => 'Δεν έχετε επισυνάψει δικαιολογητικά',
        ]);

        if ($validator->fails()) {
            return response(['message' => 'Ελλειπή Στοιχεία', 'errors' => $validator->getMessageBag()], 422);
        }

        try {
            $application->is_submitted = true;
            if (empty($application->protocol)) {
                $application->protocol = $this->protocolGenerator->generate();
            }
            $application->submitted_at = now();
            $application->save();
        } catch (\Throwable $e) {
            Log::error('Error when setting summer camps application protocol: '.$e->getMessage());

            return response($e->getMessage(), 422);
        }

        return $application;
    }

    public function destroy(Application $application)
    {
        if (!$application->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (!$application->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        try {
            $application->is_submitted = false;
            $application->submitted_at = null;
            $application->save();
        } catch (\Throwable $e) {
            Log::error('Error when setting summer camps application protocol: ' . $e->getMessage());

            return response('Error Withdrawing Application', 422);
        }

        return $application;
    }
}
