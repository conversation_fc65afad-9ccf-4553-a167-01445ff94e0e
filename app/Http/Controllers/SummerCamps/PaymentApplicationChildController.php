<?php

namespace App\Http\Controllers\SummerCamps;

use App\Http\Controllers\Controller;
use App\Http\Requests\SummerCamps\PaymentApplicationChildRequest;
use App\Models\SummerCamps\PaymentApplication;
use App\Models\SummerCamps\PaymentApplicationChild;
use Illuminate\Support\Facades\Auth;

class PaymentApplicationChildController extends Controller
{
    public function store(PaymentApplication $paymentApplication, PaymentApplicationChildRequest $request)
    {
        if (! $paymentApplication->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (! $paymentApplication->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $paymentApplicationChild = PaymentApplicationChild::create([
            'payment_application_id' => $paymentApplication->id,
            'full_name' => $request->get('full_name'),
            'has_disability' => $request->get('has_disability', null),
            'summer_camps' => $request->get('summer_camps'),
        ]);

        return response()->json(['message' => 'Success', 'data' => $paymentApplicationChild], 200);
    }

    public function update(PaymentApplication $paymentApplication, PaymentApplicationChild $paymentApplicationChild, PaymentApplicationChildRequest $request)
    {
        if (! $paymentApplication->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (! $paymentApplication->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        $paymentApplicationChild->update([
            'payment_application_id' => $paymentApplication->id,
            'full_name' => $request->get('full_name'),
            'has_disability' => $request->get('has_disability', null),
            'summer_camps' => $request->get('summer_camps'),
        ]);

        return response()->json(['message' => 'Success', 'data' => $paymentApplicationChild], 200);
    }

    public function destroy(PaymentApplication $paymentApplication, PaymentApplicationChild $paymentApplicationChild)
    {
        if (! $paymentApplication->belongsToUser(Auth::user())) {
            abort(403, 'Δεν έχετε δικαίωμα επεξεργασίας της αίτησης.');
        }

        if (! $paymentApplication->canBeEdited()) {
            abort(403, 'Η προθεσμία υποβολής έχει λήξει.');
        }

        try {
            $paymentApplicationChild->delete();
        } catch (\Throwable $e) {
            return response('Error Deleting', 422);
        }

        return response('Success', 200);
    }
}
