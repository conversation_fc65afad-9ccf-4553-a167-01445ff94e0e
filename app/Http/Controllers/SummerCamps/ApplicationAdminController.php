<?php

namespace App\Http\Controllers\SummerCamps;

use App\Http\Controllers\Controller;
use App\Models\SummerCamps\Application;
use App\Models\SummerCamps\ApplicationChild;
use App\Models\SummerCamps\EmploymentSector;
use App\Models\SummerCamps\EmploymentType;
use App\Models\SummerCamps\Season;
use App\Presenters\SummerCamps\ApplicationPresenter;
use Illuminate\Database\Eloquent\Builder;

class ApplicationAdminController extends Controller
{
    public function index(Season $season)
    {
        $this->authorize('summerCamps.admin');

        //        $season = Season::orderByDesc('start_date')->where('type', 'main')->first();

        $totalDays = ApplicationChild::whereHas('application', function (Builder $query) use ($season) {
            $query->where('season_id', '=', $season->id)->where('is_submitted', true);
        })->sum('days');

        return view('summerCamps.applicationAdmin.show', [
            'season' => $season,
            'totalDays' => $totalDays,
            'employmentSectorOptions' => EmploymentSector::select(['id', 'name'])->get(),
            'employmentTypeOptions' => EmploymentType::select(['id', 'name'])->get(),
        ]);
    }

    public function applications(Season $season)
    {
        $this->authorize('summerCamps.admin');

        $queryString = request()->query();

        $applications = Application::ofSeason($season->id)
            ->with([
                'user:id,name,email',
                'season:id,name,description',
                'employmentSector:id,name',
                'employmentType:id,name',
                'applicationChildren:id,full_name,days',
            ])
            ->withCount('applicationChildren')
            ->filter($queryString)
            ->paginate((int) request()->query('limit', '10'));

        return response()->json([
            'message' => 'Success!',
            'data' => ApplicationPresenter::pagination($applications),
        ])->cookie('summer_camps_applications_filters', json_encode($queryString), 20, null, null, false, false);
    }
}
