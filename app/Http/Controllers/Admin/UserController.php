<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\User;
use App\Presenters\Admin\ActiveUserListPresenter;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class UserController extends Controller
{
    public function index()
    {
        $activeFiltersCookieName = 'apptree_admin_filters_users_active';

        if (request()->query()) {
            $filters = request()->query();
        } elseif (request()->hasCookie($activeFiltersCookieName)) {
            $filters = json_decode(request()->cookie($activeFiltersCookieName), true);
        } else {
            $filters = [];

        }

        $activeUsers = User::with(['roles', 'primaryUnit'])
            ->filter($filters)
            ->paginate(isset($filters['limit']) ? (int) $filters['limit'] : 10);

        if (request()->wantsJson()) {
            return response()->json([
                'data' => ActiveUserListPresenter::pagination($activeUsers),
            ])->cookie($activeFiltersCookieName, json_encode(request()->query()), 15, null, null, false, false);
        }

        return view('admin.user.index', [
            'paginatedUsers' => ActiveUserListPresenter::pagination($activeUsers),
        ]);
    }

    public function create()
    {
        return view('admin.user.create');
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'required|max:255',
            'username' => 'required|max:255|unique:users',
            'email' => 'required|email|max:255|unique:users',
            'password' => 'required|confirmed|min:6',
        ]);

        $user = User::create([
            'name' => trim($request['name']),
            'username' => trim($request['username']),
            'email' => trim($request['email']),
            'password' => bcrypt($request['password']),
        ]);

        flash()->success('Επιτυχία!', 'Ο νέος χρήστης καταχωρήθηκε.');

        return redirect()->route('admin.user.edit', $user);
    }

    public function edit(User $user)
    {
        $roles = Role::withAppName()
            ->orderBy('app_id')
            ->orderByRaw("CASE
                            WHEN roles.name LIKE '%.subscriber%' THEN 1
                            WHEN roles.name LIKE '%.editor%' THEN 2
                            WHEN roles.name LIKE '%.admin%' THEN 3
                            ELSE 4
                          END")
            ->get()
            ->groupBy('app_id')
            ->map(function (Collection $appRoles, $groupKey) {
                $roleGroups = $appRoles->groupBy(function ($role) {
                    $nameParts = explode('.', $role->name);
                    if (isset($nameParts[1])) {
                        // Extract 'subscriber', 'editor', or 'admin' from the second part
                        preg_match('/(subscriber|editor|admin)/', $nameParts[1], $matches);
                        return $matches[0] ?? '';
                    }
                    return '';
                })->map(function ($roles, $roleType) {
                    return [
                        'type' => $roleType,
                        'roles' => $roles,
                    ];
                })->values()->toArray();

                return [
                    'app' => [
                        'id' => $groupKey,
                        'name' => $appRoles->first()->app_name,
                    ],
                    'roleGroup' => $roleGroups,
                ];
            })
            ->values();

        $user = $user->load('roles');



//        dd($roles->toArray());

        return view('admin.user.edit', [
            'user' => $user->load('roles'),
            'roles' => $roles,
            'primaryUnit' => $user->primaryUnit()->get(),
            'secondaryUnits' => $user->secondaryUnits()->get(),
        ]);
    }

    public function update(Request $request, User $user)
    {
        $this->validate($request, [
            'name' => 'required|max:255',
            'username' => "required|max:255|unique:users,username,{$user->id},id",
            'email' => "required|max:255|unique:users,email,{$user->id},id",
            'primary_unit_id' => 'required|array|max:1',
            'secondary_unit_id' => 'array',
        ]);

        $user->update([
            'name' => $request->input('name'),
            'username' => $request->input('username'),
            'email' => $request->input('email'),
            'primary_unit_id' => (int) $request->input('primary_unit_id')[0],
            'unit_id' => (int) $request->input('primary_unit_id')[0],
        ]);

        $user->attachPrimaryUnit($request->input('primary_unit_id')[0]);

        $user->secondaryUnits()->sync($request->input('secondary_unit_id', []));

        $user->roles()->sync($request->input('roles', []));

        flash()->success('Επιτυχία!', 'Οι αλλαγές αποθηκεύτηκαν.');

        return redirect()->route('admin.user.edit', $user);
    }

    public function destroy(Request $request, User $user)
    {
        $user->delete();

        flash()->success('Επιτυχία!', 'O χρήστης επενργοποιήθηκε.');

        return redirect()->route('admin.user.index');
    }

    public function restore($user_id)
    {
        User::withTrashed()->where('id', $user_id)->restore();

        flash()->success('Επιτυχία!', 'O χρήστης επανήλθε ως ενεργός.');

        return redirect()->route('admin.user.index');
    }

    public function forceDestroy($user_id)
    {
        User::onlyTrashed()->where('id', $user_id)->forceDelete();

        flash()->success('Επιτυχία!', 'O χρήστης διαγράφτηκε οριστικά.');

        return redirect()->route('admin.user.index');
    }
}
