<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Organogram;
use App\Models\Prefecture;
use App\Models\Unit;
use App\Models\UnitType;
use App\Presenters\UnitTreePresenter;
use App\Services\Shared\UnitTreeMaker;

class UnitController extends Controller
{
    protected UnitTreeMaker $unitTreeMaker;

    public function __construct(UnitTreeMaker $unitTreeMaker)
    {
        $this->unitTreeMaker = $unitTreeMaker;
    }

    public function index()
    {
        // TODO: Add validation

        if (request()->ajax()) {
            $filters = collect(request()->query('filters'));
            $selectedOrganogramId = $filters->firstWhere('fieldName', 'selectedOrganogramId')['value'];
            $showDepartments = filter_var($filters->firstWhere('fieldName', 'showDepartments')['value'], FILTER_VALIDATE_BOOLEAN);
            $authorizeFor = $filters->firstWhere('fieldName', 'authorizeFor')['value'];

            return response()->json(
                UnitTreePresenter::make(Unit::query())->getTree($selectedOrganogramId, $showDepartments, $authorizeFor)
            );
        }

        $units = UnitTreePresenter::make(Unit::query())->getTree(Organogram::active()->first()->id, true);

        $deleteOptions = Unit::ofOrganogram(Organogram::active()->first())
            ->whereNotIn('unit_type_id', [70, 80])
            ->get()->map->only(['id', 'name'])->sortBy('order')->values()->all();

        return view('admin.unit.index', ['units' => $units, 'deleteOptions' => $deleteOptions]);
    }

    public function create()
    {
        $options = [
            'unitTypes' =>  UnitType::all()->map->only(['id', 'name'])->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
            'organograms' =>  Organogram::all()->sortByDesc('started_at')->map->only(['id', 'name'])->values()->all(),
            'prefectures' =>  Prefecture::all()->map->only(['id', 'name'])->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
            'units' =>  Unit::ofOrganogram(Organogram::active()->first())
                ->whereNotIn('unit_type_id', [70, 80])
                ->get()->map->only(['id', 'name'])->sortBy('order')->values()->all(),
        ];

        return view('admin.unit.create', [
            'options' => $options,
        ]);
    }

    public function store()
    {
        request()->validate([
            'name' => 'required',
            'abbrv' => 'required',
            'order' => 'required',
            'unit_type_id' => 'required',
            'parent_id' => 'required',
            'compass_id' => 'required',
        ]);

        if (request()->has('version')) {
            abort_if(
                request()->isNotFilled('version.name') || request()->isNotFilled('version.date'),
                422,
                'You have to provide organogram data'
            );

            $newVersion = $this->unitTreeMaker->replicateWithVersion(
                Unit::minister()->orderByDesc('organogram_id')->first(),
                request('version')
            );

            Unit::create([
                'name' => request('name'),
                'abbrv' => request('abbrv'),
                'order' => request('order'),
                'email' => request('email'),
                'unit_type_id' => request('unit_type_id'),
                'organogram_id' => $newVersion->id,
                'prefecture_id' => request('prefecture_id'),
                'parent_id' => request('parent_id'),
                'compass_id' => request('compass_id'),
                'started_at' => $newVersion->started_at,
            ]);

            return response()->json(['message' => 'Unit created and new version created']);
        }

        Unit::create(request()->except('version'));

        return response()->json(['message' => 'Unit updated']);
    }

    public function edit(Unit $unit)
    {
        $options = [
            'unitTypes' =>  UnitType::all()->map->only(['id', 'name'])->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
            'organograms' =>  Organogram::all()->sortByDesc('started_at')->map->only(['id', 'name'])->values()->all(),
            'prefectures' =>  Prefecture::all()->map->only(['id', 'name'])->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
            'units' =>  Unit::ofOrganogram($unit->organogram()->first())
                ->whereNotIn('unit_type_id', [70, 80])
                ->get()->map->only(['id', 'name'])->sortBy('order')->values()->all(),
        ];

        return view('admin.unit.edit', [
            'unit' => $unit,
            'options' => $options,
        ]);
    }

    public function update(Unit $unit)
    {
        request()->validate([
            'name' => 'required',
            'abbrv' => 'required',
            'order' => 'required',
            'unit_type_id' => 'required',
            'parent_id' => 'required',
            'compass_id' => 'required',
        ]);

        if (request()->has('version')) {
            abort_if(
                request()->isNotFilled('version.name') || request()->isNotFilled('version.date'),
                422,
                'You have to provide organogram data'
            );

            /** @var Unit $rootAncestor */
            $rootAncestor = $unit->rootAncestor;
            $newVersion = $this->unitTreeMaker->replicateWithVersion(
                $rootAncestor,
                request('version')
            );

            $unit->update([
                'name' => request('name'),
                'abbrv' => request('abbrv'),
                'order' => request('order'),
                'email' => request('email'),
                'unit_type_id' => request('unit_type_id'),
                'prefecture_id' => request('prefecture_id'),
                'parent_id' => request('parent_id'),
                'compass_id' => request('compass_id'),
                'started_at' => $newVersion->started_at,
            ]);

            $unit->closestRelatedUnit->update(['ended_at' => $newVersion->started_at]);

            return response()->json(['message' => 'Unit updated and new version created']);
        }

        $unit->update(request()->except('version'));

        return response()->json(['message' => 'Unit updated']);
    }

    public function destroy(Unit $unit)
    {
        if (request()->has('version')) {
            abort_if(
                request()->isNotFilled('version.name') || request()->isNotFilled('version.date'),
                422,
                'You have to provide organogram data'
            );

            /** @var Unit $rootAncestor */
            $rootAncestor = $unit->rootAncestor;
            $newVersion = $this->unitTreeMaker->replicateWithVersion(
                $rootAncestor,
                request('version'),
                $unit->id,
                request('new_parent_id')
            );

            $unit->update([
                'ended_at' => $newVersion->started_at,
            ]);

            return response()->json(['message' => 'Unit deleted with version']);
        }

        $unit->delete();

        return response()->json(['message' => 'Unit deleted']);
    }
}
