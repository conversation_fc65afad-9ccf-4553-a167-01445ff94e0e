<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Permission;
use Illuminate\Http\Request;

/**
 * @resource Admin\Permission
 *
 * Resource Controller for User Role Permissions.
 */
class PermissionController extends Controller
{
    /**
     * Permission index
     * Display a listing of the resource.
     */
    public function index()
    {
        $permissions = Permission::orderBy('name', 'asc')->get();

        return view('admin/permission/index', compact('permissions'));
    }

    /**
     * Create Permission
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin/permission/create');
    }

    /**
     * Store Permission
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'required|unique:permissions|max:255',
            'description' => 'max:255',
        ]);

        Permission::create($request->all());

        flash()->success('Επιτυχία!', 'Το νέο δικαίωμα χρήσης καταχωρήθηκε.');

        return redirect('/admin/permission');
    }

    /**
     * Edit Permission
     * Show the form for editing the specified resource.
     */
    public function edit(Permission $permission)
    {
        return view('admin/permission/edit', compact('permission'));
    }

    /**
     * Update Permission
     * Update the specified resource in storage.
     */
    public function update(Request $request, Permission $permission)
    {
        $this->validate($request, [
            'name' => "required|max:255|unique:permissions,name,{$permission->id},id",
            'description' => 'max:255',
        ]);

        $permission->update($request->all());

        flash()->success('Επιτυχία!', 'Οι αλλαγές αποθηκεύτηκαν.');

        return redirect('/admin/permission');
    }

    /**
     * Delete Permission
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, Permission $permission)
    {
        $permission->delete();

        flash()->success('Επιτυχία!', 'Το δικαίωμα χρήσης διαγράφηκε.');

        return redirect('/admin/permission');
    }
}
