<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class ImpersonatedUserController extends Controller
{
    public function store()
    {
        $userToIMpersonate = User::find(request('user_id'));

        if (! Auth::user()->startImpersonating($userToIMpersonate)) {
            flash()->error('Error!', 'Impersonate disabled for this user.');
        }

        flash()->success('Done!', "Now you are user $userToIMpersonate->username !");

        return redirect()->route('home');
    }

    public function destroy(User $user)
    {
        $user->stopImpersonating();

        flash()->success('Done!', 'Welcome back!');

        return redirect()->route('home');
    }
}
