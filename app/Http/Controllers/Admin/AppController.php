<?php

namespace App\Http\Controllers\Admin;

use App\Models\App;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class AppController extends Controller
{
    public function index()
    {
        return view('admin.app.index', ['apps' => App::all()]);
    }


    public function create()
    {
        return view('admin.app.create');
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'required|string|max:255',
            'abbrv' => 'required|string|max:255|unique:apps',
            'icon' => 'required|string|starts_with:fa-|unique:apps',
            'color' => 'required|string|starts_with:#|size:7',
        ], [
            'icon.starts_with' => 'The :attribute must start with fa- .',
            'color.starts_with' => 'The :attribute must start with # .',
        ]);
        App::create($request->except('_token'));

        flash()->success('Επιτυχία!', 'Η εφαρμογή καταχωρήθηκε.');

        return redirect()->route('admin.apps.index');
    }

    public function edit(App $app)
    {
        return view('admin.app.edit', ['apptreeApp' => $app]);
    }

    public function update(Request $request, App $app)
    {
        $this->validate($request, [
            'name' => 'required|string',
            'abbrv' => 'required|string',
            'icon' => 'required|string|starts_with:fa-',
            'color' => 'required|string|starts_with:#|size:7',
        ], [
            'icon.starts_with' => 'The :attribute must start with fa- .',
            'color.starts_with' => 'The :attribute must start with # .',
        ]);

        $app->update($request->except('_token'));

        flash()->success('Επιτυχία!', 'Η εφαρμογή ενημερώθηκε.');

        return redirect()->route('admin.apps.index');
    }
}
