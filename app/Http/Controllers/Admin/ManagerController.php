<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\RoleManagement\Manager;
use Illuminate\Http\Request;

class ManagerController extends Controller
{
    public function index()
    {
        return view('admin.manager.index', [
            'managers' => Manager::with([
                'managedRoles',
                'unit',
            ])->get(),
        ]);
    }

    public function edit(Manager $manager)
    {
        return view('admin.manager.edit', [
            'manager' => $manager->load('managedRoles', 'unit'),
            'managedRoles' => Role::pluck('name', 'id'),
        ]);
    }

    public function update(Request $request, Manager $manager)
    {
        $manager->managedRoles()->sync($request->managedRoles);

        flash()->success('Επιτυχία!', 'Οι διαχειρίσιμοι ρόλοι ενημερώθηκαν');

        return redirect()->route('admin.managers.index');
    }
}
