<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Unit;
use App\Models\User;

class UserSecondaryUnitController extends Controller
{
    public function create(User $user)
    {
        return view('admin.user.secondary-unit.create', [
            'user' => $user,
        ]);
    }

    public function store(User $user)
    {
        request()->validate([
            'secondary_unit' => 'required',
        ]);

        $user->units()->attach(request('secondary_unit'));

        flash()->success('Επιτυχία!', 'Η νέα Υπηρεσία προστέθηκε');

        return redirect()->route('admin.user.edit', $user);
    }

    public function destroy(User $user, Unit $unit)
    {
        $user->detachSecondaryUnit($unit);

        flash()->success('Επιτυχία!', 'Η υπάρχουσα Υπηρεσία αφαιρέθηκε');

        return redirect()->route('admin.user.edit', $user);
    }
}
