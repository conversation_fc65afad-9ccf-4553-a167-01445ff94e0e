<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Controller;

class RouterController extends Controller
{
    public function index()
    {

        $routeCollection = Route::getRoutes();
        $routes = $routeCollection->getRoutes();
        $res = [];
        $labels = [
            'GET' => 'label-primary',
            'POST' => 'label-warning',
            'PUT' => 'label-success',
            'PATCH' => 'label-success',
            'DELETE' => 'label-danger',
        ];

        foreach ($routes as $r) {
            $name = $r->getName();
            $uri = $r->uri();
            $method = $r->methods[0];
            $controller = substr($r->getActionName(), 21);
            $label = $labels[$method];

            $route = [
                'name' => $name,
                'uri' => $uri,
                'method' => $method,
                'controller' => $controller,
                'label' => $label,
            ];

            array_push($res, $route);
            //            $res->push($route);
        }

        return view('admin.router.index', [
            'routes' => $res,
        ]);
    }
}
