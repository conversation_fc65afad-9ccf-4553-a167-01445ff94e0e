<?php

namespace App\Http\Controllers\Personnel;

use App\Http\Controllers\Controller;
use App\Models\Personnel\Position;
use App\Models\Personnel\Specialization;
use App\Models\Unit;
use Illuminate\Http\Request;

/**
 * @resource Personnel\Position
 *
 * This is the main resource of the personnel app.
 */
class PositionController extends Controller
{
    /**
     * Position search form
     * Shows a form with search filters for Personnel Position.
     */
    public function index()
    {
        return view('personnel.search')->with([
            'resultRoute' => 'personnel.position.searchresults',
            'title' => 'Αναζήτηση Οργανικών Θέσεων Προσωπικού',
        ]);
    }

    /**
     * Create Position
     * Show the form for creating a new Position.
     */
    public function create()
    {
        // Only users with permission to create personnel data can add positions
        $this->authorize('personnel.create');

        return view('personnel/position/create');
    }

    /**
     * Store Position
     * Store a newly created Position.
     */
    public function store(Request $request)
    {
        // Only users with permission to create personnel data can add positions
        $this->authorize('personnel.create');

        // Check and prepare department_id to use in validation
        $department_id = $request['department'] ? $request['department'] : 'NULL';

        $this->validate($request, [
            'unit_id' => 'required',
            'occupation' => 'required',
            'specialization' => 'required',
            'org_nr' => 'integer|min:0',
        ]);

        $unit = Unit::find($request['unit_id'][0]);
        if ($unit->isDepartment()) {
            $department = $unit;
            $unit = Unit::find($unit->parent_id);
        }

        $duplicate = Position::where('unit_id', $unit->id)->where('specialization_id', $request['specialization'])->where('occupation_id', $request['occupation']);
        if (isset($department)) {
            $duplicate = $duplicate->where('department_id', $department->id);
        } else {
            $duplicate = $duplicate->whereNull('department_id');
        }
        if ($duplicate->exists()) {
            flash()->overlay('Σφάλμα!', 'Υπάρχει ήδη καταχώρηση θέσης με τα στοιχεία που δώσατε.', 'error');

            return redirect()->route('personnel.position.create');
        }

        $position = new Position;
        $position->unit_id = $unit->id;
        $position->compass_unit_id = $unit->compass_id;
        if (isset($department)) {
            $position->department_id = $department->id;
            $position->compass_department_id = $department->compass_id;
        }
        $position->occupation_id = $request['occupation'];
        $specialization = Specialization::find($request['specialization']);
        $position->specialization_id = $specialization->id;
        $position->specialization_parent_id = $specialization->specialization_parent_id;
        $position->compass_specialization_id = $specialization->compass_id;
        $position->compass_specialization_parent_id = $specialization->compass_specialization_parent_id;
        $position->org_nr = $request['org_nr'] ? $request['org_nr'] : 0;
        $position->save();

        flash()->overlay('Επιτυχία!', 'Οι θέσεις αποθηκεύτηκαν (ID:'.$position->id.' ).');

        // Return to create form
        return redirect()->route('personnel.position.create', $position);
    }

    /**
     * Update Position (ajax)
     * Updates a row in positions table based on an ajax request.
     */
    public function update(Request $request, Position $position)
    {
        // Only users with permission to update personnel data can update position numbers
        $this->authorize('personnel.update');

        if ($request->ajax()) {
            $position->org_nr = $request['org'];

            if (($request->filled('desm')) && ($request['desm'] != '')) {
                $position->desm_nr = $request['desm'];
            }

            $position->update();

            return 'OK';
        }
    }

    /**
     * Delete Position
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, Position $position)
    {
        $this->authorize('personnel.delete');

        if ($position->epet_yphr_nr > 0 || $position->epet_pros_nr > 0 || $position->epet_diath_nr > 0) {
            flash()->overlay('Σφάλμα!', 'Δεν μπορεί να γίνει διαγραφή της θέσης διότι υπάρχουν ενεργοί υπάλληλοι.', 'error');

            return redirect()->route('personnel.position.index');
        }

        $position->delete();

        flash()->success('Επιτυχία!', 'Η εγγραφή θέσεων διαγράφηκε.');

        return redirect()->route('personnel.position.index');
    }

    /**
     * List Positions
     * Returns search results from positions table.
     */
    public function showSearchResults(Request $request)
    {
        $position = new Position;
        $positions = $position->queryPosition($request)
            ->orderBy('compass_unit_id', 'asc')->orderBy('compass_department_id', 'asc')->orderBy('compass_specialization_id', 'asc')
            ->with('unit', 'department', 'occupation', 'specialization.occupation', 'specialization.specializationParent')
            ->get();

        return view('personnel/position/searchresults')->with('positions', $positions);
    }

    /**
     * List Employee of Position (modal)
     * Returns the Employees of a specific Position to be shown in a modal window.
     */
    public function employees(Position $position)
    {
        return view('personnel/position/employeesModal', compact('position'), $position->employees());
    }

    /**
     * List Employee of Position (pdf)
     * Returns the Employees of a specific Position as a pdf file.
     */
    public function employeesPdf(Position $position)
    {
        $pdf = \App::make('dompdf.wrapper');
        $pdf->loadView('personnel.position.employeesPDF', compact('position'), $position->employees());

        return $pdf->download('personnel'.$position->id.'.pdf');
    }

    /**
     * Overfilled Position
     * Returns the Positions that have more Employees than position availability (or negative unfilled).
     */
    public function overfilled()
    {
        $position = new Position;
        $positions = $position->overfilled()
            ->orderBy('compass_unit_id', 'asc')->orderBy('compass_department_id', 'asc')->orderBy('compass_specialization_id', 'asc')
            ->with('unit', 'department', 'specialization', 'specialization.specializationParent')
            ->get();

        return view('personnel/position/overfilled')->with('positions', $positions);
    }

    /**
     * List Position aggregates by Specialization (pdf)
     * Returns a PDF file with tables of total positions and employees by specialization.
     */
    public function totalsBySpecializationPdf(Request $request)
    {
        $position = new Position;
        $positions = $position->queryPosition($request)
            ->join('specializations', 'positions.specialization_id', '=', 'specializations.id')
            ->groupBy('positions.occupation_id', 'positions.specialization_id')
            ->orderBy('positions.occupation_id')
//            ->orderBy('specializations.specialization_type_id')
            ->orderBy('specializations.compass_id')
            ->get(['positions.occupation_id',
                'positions.specialization_id',
                \DB::raw('sum(org_nr) as org_nr'),
                \DB::raw('sum(desm_nr) as desm_nr'),
                \DB::raw('sum(epet_pros_nr) as epet_pros_nr'),
                \DB::raw('sum(epet_yphr_nr) as epet_yphr_nr'),
                \DB::raw('sum(epet_diath_nr) as epet_diath_nr'),
            ]);

        $specializationNames = Specialization::select('id', 'fullname', 'compass_id')
            ->get()
            ->keyBy('id')
            ->toArray();

        // return \View::make('personnel.position.totalsBySpecializationPDF', compact('positions', 'specializationNames'));

        $pdf = \App::make('dompdf.wrapper');
        $snappy = false;
        $pdf->loadView('personnel.position.totalsBySpecializationPDF', compact('positions', 'specializationNames', 'snappy'));

        return $pdf->download('positionsBySpecialization.pdf');
    }

    /**
     * List Position aggregates by Unit and Specialization (pdf)
     * Returns a PDF file with tables of total positions and employees by Unit and specialization.
     */
    public function totalsByUnitPdf(Request $request)
    {
        $position = new Position;
        $positions = $position->queryPosition($request)
            ->join('specializations', 'positions.specialization_id', '=', 'specializations.id')
            ->join('laravel.units', 'positions.unit_id', '=', 'laravel.units.id')
            ->groupBy('unit_id', 'laravel.units.compass_id', 'positions.occupation_id', 'positions.specialization_id')
            ->orderBy('laravel.units.compass_id')
            ->orderBy('positions.occupation_id')
            ->orderBy('positions.compass_specialization_id')
            ->get(['unit_id', 'laravel.units.compass_id', 'positions.occupation_id', 'positions.specialization_id',
                \DB::raw('sum(org_nr) as org_nr'),
                \DB::raw('sum(desm_nr) as desm_nr'),
                \DB::raw('sum(epet_pros_nr) as epet_pros_nr'),
                \DB::raw('sum(epet_yphr_nr) as epet_yphr_nr'),
                \DB::raw('sum(epet_diath_nr) as epet_diath_nr'),
            ]);

        $specializationNames = Specialization::select('id', 'fullname', 'compass_id')
            ->get()
            ->keyBy('id')
            ->toArray();

        $unitNames = Unit::pluck('name', 'id');

        // return \View::make('personnel.position.totalsByUnitPDF', compact('positions', 'specializationNames', 'unitNames'));

        $pdf = \App::make('dompdf.wrapper');
        $snappy = false;
        $pdf->loadView('personnel.position.totalsByUnitPDF', compact('positions', 'specializationNames', 'unitNames', 'snappy'));

        return $pdf->download('positionsByUnit.pdf');
    }
}
