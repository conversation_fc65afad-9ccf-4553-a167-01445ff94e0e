<?php

namespace App\Http\Controllers\Personnel;

use App\Http\Controllers\Controller;
use App\Models\Personnel\Assignment;
use Illuminate\Http\Request;

class AssignmentController extends Controller
{
    public function index()
    {
        return view('personnel.search')->with([
            'resultRoute' => 'personnel.assignment.searchresults',
            'title' => 'Προβολή Σύνθεσης Υπηρεσιών',
        ]);
    }

    /**
     * List Assignment
     * Returns search results from Assignments.
     */
    public function showSearchResults(Request $request)
    {
        $assignment = new Assignment;
        $assignments = $assignment->queryAssignment($request)
            ->orderBy('print_unit_id', 'asc')
            ->orderBy('specialization_id', 'asc')
            ->orderBy('occupation_id')
            ->orderBy('flg', 'asc')
            ->orderBy('place', 'asc')
            ->orderBy('fullname', 'asc')
            ->with(
                'printUnit',
                'specialization',
                'specialization.specializationParent',
                'employee.ranks',
                'occupation'
            )
            ->get();

        return view('personnel.assignment.searchresults')->with('assignments', $assignments);
    }

    /**
     * PDF of Assignment Listing
     * Downloads pdf with Assignments.
     */
    public function assignmentsPDF(Request $request)
    {
        $assignment = new Assignment;
        $assignments = $assignment->queryAssignment($request)
            ->select('assignments.*')
            ->join('specializations', 'assignments.specialization_id', '=', 'specializations.id')
            ->orderBy('assignments.compass_unit_id', 'asc')
            ->orderBy('specializations.occupation_id')
            ->orderBy('specializations.specialization_type_id')
            ->orderBy('specializations.compass_id')
            ->orderBy('assignments.flg', 'asc')
            ->orderBy('assignments.place', 'asc')
            ->orderBy('assignments.fullname', 'asc')
            ->with(
                'printUnit',
                'specialization.occupation',
                'specialization.specializationParent',
                'employee.ranks'
            )
            ->get();

        $pdf = \App::make('dompdf.wrapper');
        $snappy = false;
        $pdf->loadView('personnel.assignment.exportPDF', compact('assignments', 'snappy'));

        return $pdf->download('assignments.pdf');
    }
}
