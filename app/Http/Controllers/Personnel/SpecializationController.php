<?php

namespace App\Http\Controllers\Personnel;

// use App\Http\Requests;
// use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Personnel\Hroffice;
use App\Models\Personnel\Occupation;
use App\Models\Personnel\Specialization;

/**
 * @resource Personnel\Specialization
 */
class SpecializationController extends Controller
{
    /**
     * Fine Specialization by Occupation (json)
     * Get the options for a select dropdown of Specializations by Occupation as json response.
     */
    public function getSelectOptionsByOccupation(Occupation $occupation)
    {
        $specializations = $occupation->specializations()->orderBy('compass_id', 'asc')->get();

        return $specializations;
    }

    /**
     * Find Specialization by Hroffice (json)
     * Get the options for a select dropdown of Specializations by Office as json response.
     */
    public function getSelectOptionsByHroffice(Hroffice $hroffice)
    {
        $specializations = $hroffice->specializations()->orderBy('compass_id', 'asc')->get();

        return $specializations;
    }
}
