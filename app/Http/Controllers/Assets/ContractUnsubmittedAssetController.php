<?php

namespace App\Http\Controllers\Assets;

use App\Http\Controllers\Controller;
use App\Models\Assets\Asset;
use App\Models\Assets\Contract;
use App\Presenters\Assets\AssetListPresenter;
use Illuminate\Http\JsonResponse;

class ContractUnsubmittedAssetController extends Controller
{
    /**
     * List unsubmitted assets for a specific contract
     */
    public function index(Contract $contract): JsonResponse
    {
        $this->authorize('assets.read');

        $queryString = request()->query();
        $perPage = (int) request()->query('limit', 5);

        $assets = Asset::with([
            'assetCategory',
            'contract',
            'unit',
        ])
            ->ofContract($contract)
            ->unsubmitted()
            ->filter($queryString)
            ->paginate($perPage);

        $cookieKey = "contract_{$contract->id}_unsubmitted_assets";

        return response()->json([
            'message' => 'Contract Unsubmitted Assets',
            'data' => AssetListPresenter::pagination($assets),
        ])->cookie($cookieKey, json_encode($queryString), 15, null, null, false, false);
    }
}
