<?php

namespace App\Http\Controllers\Assets;

use App\Http\Controllers\Controller;
use App\Models\Assets\Asset;
use App\Presenters\Assets\AssetListPresenter;
use Illuminate\Http\JsonResponse;

class UnsubmittedAssetController extends Controller
{
    /**
     * List unsubmitted assets
     */
    public function index(): JsonResponse
    {
        $queryString = request()->query();
        $perPage = (int) request()->query('limit', 5);

        $assets = Asset::with([
            'assetCategory',
            'contract',
            'unit',
        ])
            ->unsubmitted()
            ->filter($queryString)
            ->paginate($perPage);

        return response()->json([
            'message' => 'Unsubmitted Assets',
            'data' => AssetListPresenter::pagination($assets),
        ])->cookie('assets_unsubmitted_assets', json_encode($queryString), 15, null, null, false, false);
    }

    // Destroy method removed - handled by AssetController

    // View method removed - handled by AssetController::index
}
