<?php

namespace App\Http\Controllers\Assets;

use App\Actions\Assets\SubmitAssetAction;
use App\Actions\Assets\WithdrawAssetAction;
use App\Http\Requests\Assets\SubmitAssetRequest;
use App\Http\Controllers\Controller;
use App\Models\Assets\Asset;
use App\Presenters\Assets\AssetListPresenter;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response as StatusCode;

class SubmittedAssetController extends Controller
{
    /**
     * List submitted assets
     */
    public function index(): JsonResponse
    {
        $this->authorize('assets.read');

        $queryString = request()->query();
        $perPage = (int) request()->query('limit', 5);

        $assets = Asset::with([
            'assetCategory',
            'contract',
            'unit',
        ])
            ->submitted()
            ->filter($queryString)
            ->paginate($perPage);

        return response()->json([
            'message' => 'Submitted Assets',
            'data' => AssetListPresenter::pagination($assets),
        ])->cookie('assets_submitted_assets', json_encode($queryString), 15, null, null, false, false);
    }

    /**
     * Submit an existing asset
     */
    public function store(SubmitAssetRequest $request, SubmitAssetAction $submitAction): JsonResponse
    {
        $asset = Asset::find($request->asset_id);

        if (!$asset) {
            return response()->json([
                'message' => 'Asset not found',
            ], StatusCode::HTTP_NOT_FOUND);
        }

        $this->authorize('update', $asset);

        if ($asset->submitted_at !== null) {
            return response()->json([
                'message' => 'The asset is already submitted.',
            ], StatusCode::HTTP_UNPROCESSABLE_ENTITY);
        }

        $submitAction->handle($asset);

        return response()->json([
            'message' => 'Το περιουσιακό στοιχείο υποβλήθηκε επιτυχώς!',
        ]);
    }

    /**
     * Withdraw a submitted asset
     */
    public function destroy(Asset $asset, WithdrawAssetAction $withdrawAction): JsonResponse
    {
        $this->authorize('update', $asset);

        if ($asset->submitted_at === null) {
            return response()->json([
                'message' => 'Only submitted assets can be withdrawn.',
            ], StatusCode::HTTP_UNPROCESSABLE_ENTITY);
        }

        $withdrawAction->handle($asset);

        return response()->json([
            'message' => 'Η υποβολή του περιουσιακού στοιχείου αποσύρθηκε επιτυχώς!',
        ]);
    }

    // View method removed - handled by AssetController::index
}
