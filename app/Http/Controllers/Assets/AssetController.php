<?php

namespace App\Http\Controllers\Assets;

use App\Actions\Assets\Common\GetAuthUserAction;
use App\Actions\Assets\CreateAssetAction;
use App\Actions\Assets\GetFilterOptionsAction;
use App\Actions\Assets\GetFormOptionsAction;
use App\Actions\Assets\UpdateAssetAction;

use App\Http\Controllers\Controller;
use App\Http\Requests\Assets\CreateAssetRequest;
use App\Http\Requests\Assets\UpdateAssetRequest;
use App\Models\Assets\Asset;
use App\Presenters\Assets\AssetFormPresenter;
use App\Presenters\Assets\AssetDetailPresenter;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AssetController extends Controller
{
    /**
     * Display asset listing page
     */
    public function index(GetFilterOptionsAction $filterOptionsAction): View
    {
        $this->authorize('assets.read');

        return view('assets.asset.index', [
            'filterOptions' => $filterOptionsAction->handle(),
        ]);
    }

    /**
     * Display asset creation form
     */
    public function create(GetFormOptionsAction $formOptionsAction, GetAuthUserAction $getAuthUserAction): View
    {
        $this->authorize('create', Asset::class);

        return view('assets.asset.create', [
            'formOptions' => $formOptionsAction->handle(),
            'user' => $getAuthUserAction->handle(),
        ]);
    }

    /**
     * Create a new asset
     */
    public function store(CreateAssetRequest $request, CreateAssetAction $createAction): JsonResponse
    {
        $this->authorize('create', Asset::class);

        $assetDto = $request->toDto();
        $user = Auth::user();
        $asset = $createAction->handle($assetDto, $user);

        return response()->json([
            'message' => 'Το περιουσιακό στοιχείο δημιουργήθηκε επιτυχώς!',
            'data' => $asset,
        ]);
    }

    /**
     * Display asset details
     */
    public function show(Asset $asset): View
    {
        $this->authorize('read', $asset);

        $asset->load(['assetCategory', 'contract', 'user', 'unit']);

        return view('assets.asset.show', [
            'asset' => AssetDetailPresenter::make($asset)->toArray(),
        ]);
    }

    /**
     * Display asset edit form
     */
    public function edit(Asset $asset, GetFormOptionsAction $formOptionsAction, GetAuthUserAction $getAuthUserAction): View
    {
        $this->authorize('update', $asset);

        return view('assets.asset.edit', [
            'asset' => AssetFormPresenter::transform($asset),
            'formOptions' => $formOptionsAction->handle(),
            'user' => $getAuthUserAction->handle(),
        ]);
    }

    /**
     * Update an existing asset
     */
    public function update(UpdateAssetRequest $request, Asset $asset, UpdateAssetAction $updateAction): JsonResponse
    {
        // Create a DTO from the request
        $assetDto = $request->toDto();
        $user = Auth::user();

        // Pass the DTO, asset model, and user to the action
        $updatedAsset = $updateAction->handle($assetDto, $asset, $user);

        return response()->json([
            'message' => 'Το περιουσιακό στοιχείο ενημερώθηκε επιτυχώς!',
            'data' => $updatedAsset,
        ]);
    }

    /**
     * Delete an asset
     */
    public function destroy(Asset $asset): JsonResponse
    {
        $this->authorize('delete', $asset);

        $asset->delete();

        return response()->json([
            'message' => 'Το περιουσιακό στοιχείο διαγράφτηκε επιτυχώς!',
        ]);
    }
}
