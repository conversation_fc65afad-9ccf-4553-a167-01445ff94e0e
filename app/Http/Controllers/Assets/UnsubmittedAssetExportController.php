<?php

namespace App\Http\Controllers\Assets;

use App\Exports\Assets\UnsubmittedAssetsExport;
use App\Http\Controllers\Controller;
use Exception;
use Maatwebsite\Excel\Facades\Excel;

class UnsubmittedAssetExportController extends Controller
{
    public function store()
    {
        $this->authorize('assets.read');

        try {
            return Excel::download(new UnsubmittedAssetsExport(request()->all()), 'unsubmitted-assets.xlsx');
        } catch (Exception $e) {
            debug($e);
            return response()->json(['message' => 'An error occurred while creating the export file'], 422);
        }
    }
}
