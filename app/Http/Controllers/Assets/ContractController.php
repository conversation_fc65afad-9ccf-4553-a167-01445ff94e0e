<?php

namespace App\Http\Controllers\Assets;

use App\Actions\Assets\CreateContractAction;
use App\Actions\Assets\GetFilterOptionsAction;
use App\Actions\Assets\UpdateContractAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Assets\CreateContractRequest;
use App\Http\Requests\Assets\UpdateContractRequest;
use App\Models\Assets\Contract;
use App\Presenters\Assets\ContractFormPresenter;
use App\Presenters\Assets\ContractDetailPresenter;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class ContractController extends Controller
{
    /**
     * Display a listing of contracts
     */
    public function index(GetFilterOptionsAction $filterOptionsAction): View
    {
        return view('assets.contract.index', [
            'filterOptions' => $filterOptionsAction->handle(),
        ]);
    }

    /**
     * Show the form for creating a new contract
     */
    public function create(): View
    {
        return view('assets.contract.create');
    }

    /**
     * Store a newly created contract
     */
    public function store(CreateContractRequest $request, CreateContractAction $createAction): JsonResponse
    {
        $contractDto = $request->toDto();
        $contract = $createAction->handle($contractDto);

        return response()->json([
            'message' => 'Η σύμβαση δημιουργήθηκε επιτυχώς!',
            'data' => $contract->present(ContractDetailPresenter::class),
        ]);
    }

    /**
     * Display the specified contract
     */
    public function show(Contract $contract): View
    {
        $contract->loadCount('assets');

        return view('assets.contract.show', [
            'contract' => $contract->present(ContractDetailPresenter::class),
        ]);
    }

    /**
     * Show the form for editing the specified contract
     */
    public function edit(Contract $contract): View
    {
        return view('assets.contract.edit', [
            'contract' => $contract->present(ContractFormPresenter::class),
        ]);
    }

    /**
     * Update the specified contract
     */
    public function update(UpdateContractRequest $request, Contract $contract, UpdateContractAction $updateAction): JsonResponse
    {
        $contractDto = $request->toDto();
        $contract = $updateAction->handle($contractDto, $contract);

        return response()->json([
            'message' => 'Η σύμβαση ενημερώθηκε επιτυχώς!',
            'data' => $contract->present(ContractDetailPresenter::class),
        ]);
    }

    /**
     * Remove the specified contract
     */
    public function destroy(Contract $contract): JsonResponse
    {
        // Check if the contract has assets
        if ($contract->assets()->count() > 0) {
            return response()->json([
                'message' => 'Η σύμβαση δεν μπορεί να διαγραφεί επειδή έχει συνδεδεμένα περιουσιακά στοιχεία.',
            ], 422);
        }

        $contract->delete();

        return response()->json([
            'message' => 'Η σύμβαση διαγράφηκε επιτυχώς!',
        ]);
    }
}
