<?php

namespace App\Http\Controllers\Assets;

use App\Http\Controllers\Controller;
use App\Models\Assets\Contract;
use App\Presenters\Assets\ContractListPresenter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ContractListController extends Controller
{
    /**
     * List contracts
     */
    public function index(Request $request): JsonResponse
    {
        $queryString = $request->query();
        $perPage = (int) $request->query('limit', 5);

        $contracts = Contract::withCount('assets')
            ->filter($queryString)
            ->paginate($perPage);

        return response()->json([
            'message' => 'Contracts',
            'data' => ContractListPresenter::pagination($contracts),
        ])->cookie('assets_contracts', json_encode($queryString), 15, null, null, false, false);
    }
}
