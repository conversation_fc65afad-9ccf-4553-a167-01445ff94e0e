<?php

namespace App\Http\Controllers\Assets;

use App\Actions\Assets\CreateAssetAction;
use App\Actions\Assets\GetFormOptionsAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Assets\CreateAssetRequest;
use App\Models\Assets\Contract;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class ContractAssetController extends Controller
{
    /**
     * Display asset creation form for a specific contract
     */
    public function create(Contract $contract, GetFormOptionsAction $formOptionsAction): View
    {
        $formOptions = $formOptionsAction->handle();

        return view('assets.asset.create', [
            'formOptions' => $formOptions,
            'contract' => [
                'id' => $contract->id,
                'contract_number' => $contract->contract_number,
            ],
        ]);
    }

    /**
     * Create a new asset for a specific contract
     */
    public function store(CreateAssetRequest $request, Contract $contract, CreateAssetAction $createAction): JsonResponse
    {
        // Ensure the contract_id in the request matches the route parameter
        if ((int)$request->input('contract_id') !== $contract->id) {
            return response()->json([
                'message' => 'Contract ID mismatch',
            ], 422);
        }

        $assetDto = $request->toDto();
        $user = Auth::user();
        $asset = $createAction->handle($assetDto, $user);

        return response()->json([
            'message' => 'Το περιουσιακό στοιχείο δημιουργήθηκε επιτυχώς!',
            'data' => $asset,
        ]);
    }
}
