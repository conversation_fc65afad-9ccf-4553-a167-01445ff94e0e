<?php

namespace App\Http\Controllers\RoleManagement;

use App\Http\Controllers\Controller;
use App\Models\RoleManagement\Manager;
use App\Models\User;

class UserController extends Controller
{
    public function index()
    {
        abort_if(auth()->user()->isAdmin(), 422, 'You have to impersonate');

        $manager = Manager::findOrFail(auth()->user()->id);

        $users = User::ofUnit($manager->primaryUnit)
            ->with(['roles.app'])
            ->orderBy('name')
            ->get();

        return view('roleManagement.user.index', [
            'users' => $users,
            'managedRoles' => $manager->managedRoles()->pluck('id'),
        ]);
    }

    public function edit(User $user)
    {
        $manager = Manager::findOrFail(auth()->user()->id);

        abort_if($user->primary_unit_id !== $manager->primary_unit_id, 422, 'Ο χρήστης δεν ανήκει στην υπηρεσία σας');

        return view('roleManagement.user.edit', [
            'user' => $user->load('roles.app'),
            'manager' => $manager->loadManagedAppsAndManagedRoles(),
        ]);
    }

    public function update(User $user)
    {
        $user->roles()->sync(array_values(request()->all()));

        return response()->json(['message' => 'Οι ρόλοι χορηγήθηκαν']);
    }
}
