<?php

namespace App\Http\Controllers\Educational;

use App\Http\Controllers\ReadyFormController;
use App\Models\Educational\Period;

class PeriodController extends ReadyFormController
{
    protected array $indexFields = [
        'name',
    ];

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Έτος', 'type' => 'text'],
    ];

    protected string $formTitle = 'Έτος';

    protected array $validationRules = [
        'name' => 'required|min:3',
    ];

    protected string $bladeLayout = 'layouts.educational';

    protected array $permissions = [
        'index'  => 'educational.admin',
        'create' => 'educational.admin',
        'read'   => 'educational.admin',
        'update' => 'educational.admin',
        'delete' => 'educational.admin',
    ];

    public function __construct(Period $period)
    {
        parent::__construct($period);
    }
}
