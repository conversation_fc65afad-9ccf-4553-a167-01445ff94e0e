<?php

namespace App\Http\Controllers\Educational;

use App\Http\Controllers\ReadyFormController;
use App\Models\Educational\Type;

class TypeController extends ReadyFormController
{
    protected array $permissions = [
        'index'  => 'educational.admin',
        'create' => 'admin',
        'read'   => 'educational.admin',
        'update' => 'educational.admin',
        'delete' => 'admin',
    ];

    protected array $indexFields = [
        'name',
    ];

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Όνομα', 'type' => 'text'],
    ];

    protected string $formTitle = 'Είδος Δράσης';

    protected array $validationRules = [
        'name' => 'required|min:3',
    ];

    protected string $bladeLayout = 'layouts.educational';

    public function __construct(Type $type)
    {
        parent::__construct($type);
    }
}
