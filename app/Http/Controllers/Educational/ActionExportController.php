<?php

namespace App\Http\Controllers\Educational;

use App\Exports\Educational\ActionsExport;
use App\Http\Controllers\Controller;
use Exception;
use Maatwebsite\Excel\Facades\Excel;

class ActionExportController extends Controller
{
    public function store()
    {
        try {
            return Excel::download(new ActionsExport(request()->all()), 'actions.xlsx'); // TODO consider better using request('filters')
        } catch (Exception $e) {
            debug($e);
            return response()->json(['message' => 'Παρουσιάστηκε πρόβλημα κατά την δημιουργία του εξαγόμενου αρχείου'], 422);
        }
    }
}
