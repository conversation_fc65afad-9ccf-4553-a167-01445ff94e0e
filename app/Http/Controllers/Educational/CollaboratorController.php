<?php

namespace App\Http\Controllers\Educational;

use App\Http\Controllers\ReadyFormController;
use App\Models\Educational\Collaborator;

class CollaboratorController extends ReadyFormController
{
    protected array $indexFields = [
        'name',
        'collaborator_type_id',
    ];

    protected array $relationships = ['collaboratorType'];

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Όνομα', 'type' => 'text'],
        ['name' => 'collaborator_type_id', 'label' => 'Κατηγορία', 'type' => 'select', 'relationship' => 'collaboratorType'],
    ];

    protected string $formTitle = 'Συνεργαζόμενοι Φορείς';

    protected array $validationRules = [
        'name' => 'required|min:10',
        'collaborator_type_id' => 'required',
    ];

    protected string $bladeLayout = 'layouts.educational';

    protected array $permissions = [
        'index'  => 'educational.admin',
        'create' => 'educational.admin',
        'read'   => 'educational.admin',
        'update' => 'educational.admin',
        'delete' => 'educational.admin',
    ];

    public function __construct(Collaborator $collaborator)
    {
        parent::__construct($collaborator);
    }
}
