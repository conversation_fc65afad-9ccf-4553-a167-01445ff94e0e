<?php

namespace App\Http\Controllers\Educational;

use App\Http\Controllers\Controller;
use App\Models\Educational\Action;
use App\Presenters\Educational\ActionIndexPresenter;
use App\Rules\Educational\AtLeastOneAttachment;
use App\Rules\Educational\AttributeExistsInModel;
use App\Rules\Educational\RelatedAttachedToModel;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response as StatusCode;

class SubmittedActionController extends Controller
{
    public function index(): JsonResponse
    {
        $queryString = request()->query();

        $actions = Action::with([
            'period',
            'unit',
            'type',
            'locations',
            'targets.targetType',
        ])
            ->submitted()
            ->filter($queryString)
            ->paginate((int) request()->query('limit', '10'));

        return response()->json([
            'message' => 'Submitted Actions',
            'data' => ActionIndexPresenter::pagination($actions),
        ])->cookie('educational_submitted_actions', json_encode($queryString), 15, null, null, false, false);
    }

    public function store(Request $request)
    {
        $action = Action::find($request->action_id);

        $validator = Validator::make($request->all(), [
            'period_id' => new AttributeExistsInModel($action),
            'title' => new AttributeExistsInModel($action),
            'involvements' => new RelatedAttachedToModel($action),
            'ongoing' => new AttributeExistsInModel($action),
            'is_digital' => new AttributeExistsInModel($action),
            'context_id' => new AttributeExistsInModel($action),
            'started_at' => new AttributeExistsInModel($action),
            'ended_at' => new AttributeExistsInModel($action),
            'duration_id' => new AttributeExistsInModel($action),
            'frequency'=> new AttributeExistsInModel($action),
            'description'=> new AttributeExistsInModel($action),
            'contributors'=> new AttributeExistsInModel($action),
            'locations'=> new RelatedAttachedToModel($action),
            'targets'=> new RelatedAttachedToModel($action),
            'target_types'=> new RelatedAttachedToModel($action),
            'collaborators'=> new RelatedAttachedToModel($action),
            'funds'=> new RelatedAttachedToModel($action),
            'tools'=> new RelatedAttachedToModel($action),
            'assessments'=> new RelatedAttachedToModel($action),
            'evaluation'=> new AttributeExistsInModel($action),
//            'link'=> new AttributeExistsInModel($action),
            'disseminations'=> new RelatedAttachedToModel($action),
            'attachments'=> new AtLeastOneAttachment($action),
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                'message' => 'Υπάρχουν σφάλματα στα υποβαλλόμενα στοιχεία. Θα μεταβείτε στην καρτέλα επεξεργασίας δράσης για να τα διορθώσετε.',
                'errors' => $validator->getMessageBag(), ],
                StatusCode::HTTP_UNPROCESSABLE_ENTITY
            );
        }

//        throw new Exception('Δεν μπόρεσε να υποβληθεί η δράση.', StatusCode::HTTP_INTERNAL_SERVER_ERROR);

        $action = Action::find($request->action_id);
        $action->submit();

        return response()->json(['message' => 'Η Δράση σας έχει υποβληθεί!']);
    }

    public function destroy(Action $action)
    {
        // TODO - validation

//        throw new Exception('Δεν μπόρεσε να υποβληθεί η δράση.', StatusCode::HTTP_INTERNAL_SERVER_ERROR);

        $action->withdraw();

        return response()->json(['message' => 'Η υποβολή της δράσης αναιρέθηκε επιτυχώς!']);
    }
}
