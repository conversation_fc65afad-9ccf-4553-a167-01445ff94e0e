<?php

namespace App\Http\Controllers\Educational;

use App\Http\Controllers\Controller;
use App\Models\Educational\Action;
use App\Presenters\Educational\ActionIndexPresenter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UnsubmittedActionController extends Controller
{
    public function index(): JsonResponse
    {
        $queryString = request()->query();

        $actions = Action::with([
            'period',
            'unit',
            'type',
            'locations',
            'targets.targetType',
        ])
            ->unsubmitted()
            ->filter(request()->all())
            ->paginate((int) request()->query('limit', '10'));

        return response()->json([
            'message' => 'Unsubmitted Actions',
            'data' => ActionIndexPresenter::pagination($actions),
        ])->cookie('educational_unsubmitted_actions', json_encode($queryString), 15, null, null, false, false);
    }
}
