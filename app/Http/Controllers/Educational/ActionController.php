<?php

namespace App\Http\Controllers\Educational;

use App\Http\Controllers\Controller;
use App\Http\Requests\Educational\ActionCreateRequest;
use App\Http\Requests\Educational\ActionUpdateRequest;
use App\Models\Educational\Action;
use App\Models\Educational\Assessment;
use App\Models\Educational\CollaboratorType;
use App\Models\Educational\Context;
use App\Models\Educational\Duration;
use App\Models\Educational\Fund;
use App\Models\Educational\Involvement;
use App\Models\Educational\Location;
use App\Models\Educational\LocationType;
use App\Models\Educational\Period;
use App\Models\Educational\Target;
use App\Models\Educational\TargetType;
use App\Models\Educational\Tool;
use App\Models\Educational\Type;
use App\Models\Region;
use App\Models\Unit;
use App\Presenters\Educational\ActionEditPresenter;
use App\Presenters\Educational\ActionShowPresenter;
use App\Services\Educational\ActionFormService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Symfony\Component\HttpFoundation\Response as StatusCode;

class ActionController extends Controller
{
    protected ActionFormService $actionFormService;

    public function __construct(ActionFormService $actionFormService)
    {
        $this->actionFormService = $actionFormService;
        $this->middleware('permission:educational.update')->only(['create', 'store', 'edit', 'update']);
    }

    public function index(): View
    {
        return view('educational.action.index', [
            'filterOptions' => [
                'periods' => Period::all()->map->only(['id', 'name'])->sortByDesc('name')->values()->all(),
                'units' => Unit::active()->relatedToEducationalApp()->get()->map
                    ->only(['id', 'name'])->sortByGreek('name')->values()->all(),
                'types' => Type::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'involvements' => Involvement::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'ongoing' => [['id' => 1, 'name' => 'ΝΑΙ'], ['id' => 0, 'name' => 'ΟΧΙ']],
                'regions' => Region::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'contexts' => Context::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'durations' => Duration::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'locationTypes' => LocationType::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'locations' => Location::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'targetTypes' => TargetType::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'targets' => Target::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'collaboratorTypes' => CollaboratorType::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'funds' => Fund::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'tools' => Tool::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
                'assessments' => Assessment::all()->map->only(['id', 'name'])
                    ->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
            ],
        ]);
    }

    public function create(): View
    {
        return view('educational.action.create', [
            'user' => [
                'id' => auth()->user()->id,
                'unit_id' => auth()->user()->unit_id,
                'can' => [
                    'admin' => auth()->user()->can('educational.admin'),
                ],
            ],
            'typeOptions' => Type::query()
                ->orderBy('name')
                ->get()
                ->map->only(['id', 'name']),
            'unitOptions' => Unit::active()->relatedToEducationalApp()->get()->map
                ->only(['id', 'name'])->sortBy('name', SORT_LOCALE_STRING)->values()->all(),
        ]);
    }

    public function store(ActionCreateRequest $request): JsonResponse
    {
        try {
            $newAction = $this->actionFormService->create($request->validated());
        } catch (Exception $e) {
            return response()->json(['message' => 'Παρουσιάστηκε σφάλμα κατά την καταχώριση'], 503);
        }

        return response()->json([
            'message' => 'Η νέα καρτέλα τεκμηρίωσης της δράσης δημιουργήθηκε επιτυχώς. Μπορείτε να μεταβείτε στην καρτέλα και να προχωρήσετε στη συμπλήρωσή της.',
            'data' => $newAction,
        ], StatusCode::HTTP_CREATED);
    }

    public function show(Action $action): View
    {
        return view('educational.action.show', [
            'action' => ActionShowPresenter::make(
                $action->load([
                    'period',
                    'unit',
                    'type',
                    'involvements',
                    'context',
                    'duration',
                    'locations.locationType',
                    'targets.targetType',
                    'targetTypes',
                    'collaborators.collaboratorType',
                    'funds',
                    'tools',
                    'assessments',
                ])
            )->toArray(),
            'requiredFields' => Action::getRequiredFieldsForType($action->type_id),
        ]);
    }

    public function edit(Action $action): View
    {
        return view('educational.action.edit', [
            'action' => ActionEditPresenter::make(
                $action->load([
                    'period',
                    'unit',
                    'type',
                    'involvements',
                    'context',
                    'duration',
                    'locations.locationType',
                    'targets',
                    'targetTypes',
                    'collaborators',
                    'funds',
                    'tools',
                    'assessments',
                    'media',
                ])
            )->toArray(),
            'requiredFields' => Action::getRequiredFieldsForType($action->type_id),
        ]);
    }

    public function update(ActionUpdateRequest $request, Action $action): JsonResponse
    {
        try {
            $updatedAction = $this->actionFormService->save($request->all(), $action);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['message' => 'Παρουσιάστηκε σφάλμα κατά την ενημέρωση'], 503);
        }

        return response()->json([
            'message' => 'Η δράση αποθηκεύτηκε επιτυχώς στις '.$updatedAction->updated_at->format('d/m/Y H:i'),
        ]);
    }

    public function destroy(Action $action): JsonResponse
    {
        try {
            $this->actionFormService->destroy($action);
        } catch (Exception $e) {
            logger($e->getMessage());

            return response()->json(['message' => $e->getMessage()], 503);
        }

        return response()->json(['message' => 'Η δράση διαγράφηκε επιτυχώς'], 200);
    }
}
