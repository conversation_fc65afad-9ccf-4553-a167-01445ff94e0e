<?php

namespace App\Http\Controllers\Educational;

use App\Http\Controllers\Controller;
use App\Http\Requests\Educational\ActionAttachmentRequest;
use App\Models\Educational\Action;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ActionAttachmentController extends Controller
{
    public function index(Action $action)
    {
        $attachments = $action->getMedia('actions')
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            });

        return $attachments;
    }

    public function store(ActionAttachmentRequest $request, Action $action)
    {
        $newAttachments = collect(request('attachments'))
            ->map(function ($attachment) use ($action) {
                return $action->addMedia($attachment)
                    ->usingFileName('document.'.$attachment->getClientOriginalExtension())
                    ->withCustomProperties([
                        'mime-type' => $attachment->getClientMimeType(),
                        'extension' => $attachment->getClientOriginalExtension(),
                    ])
                    ->toMediaCollection('actions', 'educational_disk');
            })
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            });

        return $newAttachments;
    }

    public function destroy(Action $action, Media $attachment)
    {
        $action->deleteMedia($attachment->id);

        return response()->json(['message' => 'Το αρχείο διαγράφηκε'], 200);
    }
}
