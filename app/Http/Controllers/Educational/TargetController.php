<?php

namespace App\Http\Controllers\Educational;

use App\Http\Controllers\ReadyFormController;
use App\Models\Educational\Target;

class TargetController extends ReadyFormController
{
    protected array $permissions = [
        'index'  => 'educational.admin',
        'create' => 'educational.admin',
        'read'   => 'educational.admin',
        'update' => 'educational.admin',
        'delete' => 'educational.admin',
    ];

    protected array $indexFields = [
        'name',
        'target_type_id',
    ];

    protected array $relationships = ['targetType'];

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Όνομα', 'type' => 'text'],
        ['name' => 'target_type_id', 'label' => 'Κατηγορία', 'type' => 'select', 'relationship' => 'targetType'],
    ];

    protected string $formTitle = 'Ομάδες Κοινού';

    protected array $validationRules = [
        'name' => 'required|min:3',
        'target_type_id' => 'required',
    ];

    protected string $bladeLayout = 'layouts.educational';

    public function __construct(Target $target)
    {
        parent::__construct($target);
    }
}
