<?php

namespace App\Http\Controllers\Educational;

use App\Http\Controllers\ReadyFormController;
use App\Models\Educational\Involvement;

class InvolvementController extends ReadyFormController
{
    protected array $indexFields = [
        'name',
    ];

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Όνομα', 'type' => 'text'],
    ];

    protected string $formTitle = 'Στάδια Σχεδιασμού - Υλοποίησης';

    protected array $validationRules = [
        'name' => 'required|min:3',
    ];

    protected string $bladeLayout = 'layouts.educational';

    protected array $permissions = [
        'index'  => 'educational.admin',
        'create' => 'educational.admin',
        'read'   => 'educational.admin',
        'update' => 'educational.admin',
        'delete' => 'educational.admin',
    ];

    public function __construct(Involvement $involvement)
    {
        parent::__construct($involvement);
    }
}
