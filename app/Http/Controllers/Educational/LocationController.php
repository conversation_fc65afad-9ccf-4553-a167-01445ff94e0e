<?php

namespace App\Http\Controllers\Educational;

use App\Http\Controllers\ReadyFormController;
use App\Models\Educational\Location;

class LocationController extends ReadyFormController
{
    protected array $indexFields = [
        'name',
        'location_type_id',
    ];

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Όνομα', 'type' => 'text'],
        ['name' => 'location_type_id', 'label' => 'Κατηγορία', 'type' => 'select', 'relationship' => 'locationType'],
    ];

    protected string $formTitle = 'Χώροι Διεξαγωγής';

    protected array $validationRules = [
        'name' => 'required|min:3',
        'location_type_id' => 'required',
    ];

    protected string $bladeLayout = 'layouts.educational';

    protected array $permissions = [
        'index'  => 'educational.admin',
        'create' => 'educational.admin',
        'read'   => 'educational.admin',
        'update' => 'educational.admin',
        'delete' => 'educational.admin',
    ];

    protected array $relationships = ['locationType'];

    public function __construct(Location $location)
    {
        parent::__construct($location);
    }
}
