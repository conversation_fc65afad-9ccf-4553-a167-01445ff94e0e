<?php

namespace App\Http\Controllers;

use App\Http\Requests;
use Illuminate\Http\Request;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * @resource Department
 */
class MediaDownloadsController extends Controller
{

    public function download($mediahash) {
        // Get id from hash
        $id = simpleHash($mediahash);

        $media = Media::findOrFail($id);

        return response()->download($media->getPath(), $media->name . '.' . $media->getCustomProperty('extension'));
    }
}
