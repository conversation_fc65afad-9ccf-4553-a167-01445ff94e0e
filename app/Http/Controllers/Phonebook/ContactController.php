<?php

namespace App\Http\Controllers\Phonebook;

use App\Http\Controllers\Controller;
use App\Models\Phonebook\EmployeeContact;
use App\Models\Phonebook\ServiceContact;
use App\Models\Unit;
use App\Presenters\Phonebook\EmployeeContactListingPresenter;
use App\Presenters\Phonebook\ServiceContactListingPresenter;
use Auth;

class ContactController extends Controller
{
    public function index()
    {
        if (request()->ajax()) {
            $serviceContacts = ServiceContact::ofUnit(request('unit_id'))->get();

            $employeeContacts = EmployeeContact::ofUnit(request('unit_id'))->get()
                ->sortBy(function ($employeeContact) {
                    return [
                        $employeeContact->ranks->first()->compass_id ?? 'Ω',
                        $employeeContact->surname,
                    ];
                })->values();

            // TODO
            //   Αναζήτηση επιπλέον εργαζομένων που δε υπηρετούν στην τρέχουσα διεύθυνση
            //   οι οποίοι εκτελούν χρέη προισταμένου στην τρέχουσα διεύθυνση αυτή

            return response()->json([
                'employees' => EmployeeContactListingPresenter::collection($employeeContacts),
                'services' => ServiceContactListingPresenter::collection($serviceContacts),
            ]);
        }

        return view('phonebook.contact.index', [
            'authUserUnit' => Auth::check() ? Unit::where('id', auth()->user()->unit_id)->get() : [],
        ]);
    }
}
