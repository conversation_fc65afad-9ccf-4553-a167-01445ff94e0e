<?php

namespace App\Http\Controllers\Phonebook;

use App\Http\Controllers\Controller;
use App\Http\Requests\Phonebook\EditServiceContactRequest;
use App\Models\Phonebook\ServiceContact;
use App\Models\Phonebook\Telephone;
use App\Presenters\Phonebook\ServiceContactEditPresenter;
use App\Presenters\Phonebook\ServiceContactListingPresenter;
use Illuminate\Http\Request;

class ServiceContactController extends Controller
{
    public function store(EditServiceContactRequest $request)
    {
        $this->authorize('phonebook.store', ServiceContact::class);

        if ($request->ajax()) {
            $serviceContact = ServiceContact::create([
                'description' => request('description'),
                'tel' => request('telephone'),
                'info' => request('info'),
                'unit_id' => request('unit_id'),
            ]);

            return response()->json(ServiceContactListingPresenter::make($serviceContact));
        }
    }

    public function edit(Request $request, ServiceContact $serviceContact)
    {
        $this->authorize($serviceContact);

        if ($request->ajax()) {
            return response()->json(ServiceContactEditPresenter::make($serviceContact));
        }
    }

    public function update(EditServiceContactRequest $request, Telephone $serviceContact)
    {
        $this->authorize($serviceContact);

        if ($request->ajax()) {
            $serviceContact->update([
                'description' => request('description'),
                'tel' => request('telephone'),
                'info' => request('info'),
                'unit_id' => request('unit_id'),
            ]);

            return response()->json(ServiceContactListingPresenter::make($serviceContact));
        }
    }

    public function destroy(Request $request, ServiceContact $serviceContact)
    {
        $this->authorize($serviceContact);

        if ($request->ajax()) {
            $serviceContact->delete();

            return response()->json(['message' => 'Η επαφή διαγράφηκε επιτυχώς']);
        }
    }
}
