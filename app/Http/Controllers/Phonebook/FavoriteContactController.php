<?php

namespace App\Http\Controllers\Phonebook;

use App\Http\Controllers\Controller;
use App\Models\Phonebook\EmployeeContact;
use App\Presenters\Phonebook\EmployeeContactListingPresenter;

class FavoriteContactController extends Controller
{
    public function index()
    {
        $favoriteContacts = EmployeeContact::favoritedByUser(auth()->id())->get();

        return view('phonebook.favorite-contact.index', [
            'favoriteContacts' => EmployeeContactListingPresenter::collection($favoriteContacts),
        ]);
    }

    public function store()
    {
        $favoriteContact = EmployeeContact::find(request('id'));

        $favoriteContact->users()->attach(auth()->id());

        return response()->json(EmployeeContactListingPresenter::make($favoriteContact->fresh()));
    }

    public function destroy(EmployeeContact $favoriteContact)
    {
        $favoriteContact->users()->detach(auth()->id());

        return response()->json(EmployeeContactListingPresenter::make($favoriteContact->fresh()));
    }
}
