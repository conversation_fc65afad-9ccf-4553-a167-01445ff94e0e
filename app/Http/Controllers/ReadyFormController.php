<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Validator;
use ReflectionClass;

class ReadyFormController extends Controller
{
    // The model that we use
    private Model $model;

    protected array $relationships = [];

    // If we want to handle deleted resources
    protected bool $withTrashed = false;

    // The fields shown on the index page
    protected array $indexFields = ['name'];

    // The fields shown in forms
    protected array $formFields = [];

    // The form's title (model name or description)
    protected string $formTitle = '';

    // Validation rules
    protected array $validationRules = [];

    // Validation messages
    protected array $validationMessages = [];

    // Validation attribute names
    protected array $validationAttributes = [];

    /** @var string The resource base url (used to create the urls in forms) */
    protected string $baseUrl = '';

    // The blade layout that we extend
    protected string $bladeLayout = '';

    // Permission names to use
    protected array $permissions = [];

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    public function index(Request $request)
    {
        $this->authorizeAction($request, 'index');

        if ($this->withTrashed) {
            if (in_array(SoftDeletes::class, class_uses($this->model))) {
                $entities = $this->model->withTrashed()->get();
            } else {
                throw new \RuntimeException('Model does not support soft deletes.');
            }
        } else {
            $entities = $this->model->all();
        }

        $this->loadModelRelationships($entities);

        $fields = $this->getIndexFields();
        $title = $this->getFormTitle();
        $route = $this->getBaseUrl($request);
        $withTrashed = $this->withTrashed;
        $bladeLayout = $this->getBaseLayout($request);

        $createPermission = $this->getPermissionForAction($request, 'create');
        $updatePermission = $this->getPermissionForAction($request, 'update');
        $deletePermission = $this->getPermissionForAction($request, 'delete');

        return view('readyform.index', [
            'entities' => $entities,
            'fields' => $fields,
            'title' => $title,
            'route' => $route,
            'withTrashed' => $withTrashed,
            'bladeLayout' => $bladeLayout,
            'createPermission' => $createPermission,
            'updatePermission' => $updatePermission,
            'deletePermission' => $deletePermission,
        ]);
    }

    public function create(Request $request)
    {
        $this->authorizeAction($request, 'create');

        $entity = $this->model;

        $relationshipOptions = $this->relationships ? $this->getModelRelationshipData() : [];

        $fields = $this->getFormFields();
        $title = $this->getFormTitle();
        $route = $this->getBaseUrl($request);
        $bladeLayout = $this->bladeLayout;

        return view('readyform.create', [
            'entity' => $entity,
            'fields' => $fields,
            'title' => $title,
            'route' => $route,
            'bladeLayout' => $bladeLayout,
            'relationshipOptions' => $relationshipOptions,
        ]);
    }

    public function store(Request $request)
    {
        $this->authorizeAction($request, 'create');
        $this->validateAction($request);

        $entity = $this->model->create($request->all());

        $this->syncModelRelationships($entity, $request);

        flash()->success('Επιτυχία!', 'Δημιουργήθηκε νέα εγγραφή.');

        return redirect(route($this->getBaseUrl($request) . '.index'));
    }

    public function show(Request $request, $id)
    {
        $this->authorizeAction($request, 'read');

        $entity = $this->model->findOrFail($id);

        $this->loadModelRelationships($entity);

        $fields = $this->getFormFields();
        $title = $this->getFormTitle();
        $route = $this->getBaseUrl($request);
        $bladeLayout = $this->bladeLayout;

        $updatePermission = $this->getPermissionForAction($request, 'update');
        $deletePermission = $this->getPermissionForAction($request, 'delete');

        return view('readyform.show', [
            'entity' => $entity,
            'fields' => $fields,
            'title' => $title,
            'route' => $route,
            'bladeLayout' => $bladeLayout,
            'updatePermission' => $updatePermission,
            'deletePermission' => $deletePermission,
        ]);
    }

    public function edit(Request $request, $id)
    {
        $this->authorizeAction($request, 'update');

        $entity = $this->model->findOrFail($id);

        $this->loadModelRelationships($entity);

        $relationshipOptions = $this->relationships ? $this->getModelRelationshipData() : [];

        $fields = $this->getFormFields();
        $title = $this->getFormTitle();
        $route = $this->getBaseUrl($request);
        $bladeLayout = $this->bladeLayout;

        return view('readyform.edit', [
            'entity' => $entity,
            'fields' => $fields,
            'title' => $title,
            'route' => $route,
            'bladeLayout' => $bladeLayout,
            'relationshipOptions' => $relationshipOptions,
        ]);
    }

    public function update(Request $request, $id)
    {
        $this->authorizeAction($request, 'update');
        $this->validateAction($request);

        $entity = $this->model->findOrFail($id);

        //Handle checkboxes
        foreach ($this->getFormFields() as $field) {
            if ($field['type'] == 'checkbox') {
                $request["{$field['name']}"] = ($request["{$field['name']}"]) ? true : false;
            }
        }

        $entity->update($request->all());

        $this->syncModelRelationships($entity, $request);

        flash()->success('Επιτυχία!', 'Η εγγραφή αποθηκεύτηκε.');

        return redirect(route($this->getBaseUrl($request) . '.show', $id));
    }

    public function destroy(Request $request, $id)
    {
        $this->authorizeAction($request, 'delete');

        $entity = $this->model->findOrFail($id);

        $entity->delete();

        flash()->success('Επιτυχία!', 'Η εγγραφή διαγράφηκε.');

        return redirect(route($this->getBaseUrl($request) . '.index'));
    }

    public function restore(Request $request, $id)
    {
        $this->authorizeAction($request, 'update');

        if (!in_array(SoftDeletes::class, class_uses($this->model))) {
            throw new \RuntimeException('Model does not support soft deletes.');
        }

        $entity = $this->model->withTrashed()->findOrFail($id);

        if (!$entity->trashed()) {
            throw new \RuntimeException('Record is not deleted.');
        }

        $entity->restore();

        flash()->success('Επιτυχία!', 'H εγγραφή ενεργοποιήθηκε.');

        return redirect(route($this->getBaseUrl($request) . '.index'));
    }

    /**
     * Get the array of fields that we need to present in the forms.
     */
    private function getFormFields(): array
    {
        // No fields declared. We have a table with only a name field.
        if (count($this->formFields) === 0) {
            array_push($this->formFields, ['name' => 'name', 'label' => 'Ονομασία', 'type' => 'text']);

            return $this->formFields;
        }

        foreach ($this->formFields as $key => $field) {
            if (Arr::has($field, 'relationship') && !Arr::has($field, 'relFieldName')) {
                // set default name of related table main field
                $this->formFields[$key]['relFieldName'] = 'name';
            }
        }

        return $this->formFields;
    }

    /**
     * Get the array of fields that we need to present in the resource index.
     */
    private function getIndexFields(): array
    {
        // We only want the fields that are declared to use for index pages.
        // So we restrict the formFields to those declared in the indexFields
        $this->formFields = Arr::where($this->getFormFields(), function ($value) {
            return in_array($value['name'], $this->indexFields, true);
        });

        return $this->getFormFields();
    }

    /**
     * Get an array of collections of related data.
     */
    private function getModelRelationshipData(): array
    {
        $relationshipData = [];

        foreach ($this->relationships as $relationship) {
            $relationshipField = Arr::first(
                Arr::where($this->getFormFields(), function ($field) use ($relationship) {
                    return Arr::has($field, 'relationship') && ($relationship === $field['relationship']);
                })
            );

            throw_if($relationshipField === null, new Exception('Relationship field could not be found'));

            $relationshipData["$relationship"] = $this->model->$relationship()
                ->getRelated()
                ->all()
                ->pluck($relationshipField['relFieldName'], 'id');
        }

        return $relationshipData;
    }

    /**
     * Sync any BelongsToMany Relationships.
     */
    private function syncModelRelationships(Model $model, Request $request): void
    {
        $relationships = $this->relationships;

        foreach ($relationships as $relationship) {
            if (get_class($model->$relationship()) === 'Illuminate\Database\Eloquent\Relations\BelongsToMany') {
                $model->$relationship()->sync($request->input($relationship, []));
            }
        }
    }

    /**
     * Eager load all the BelongsTo and BelongsToMany relationships.
     */
    private function loadModelRelationships($entities)
    {
        $relationships = $this->relationships;

        foreach ($relationships as $relationship) {
            $entities->load($relationship);
        }
    }

    private function getBaseUrl(Request $request): string
    {
        if ($this->baseUrl) {
            return $this->baseUrl;
        }

        $routeName = $request->route()->getName();

        return substr($routeName, 0, strrpos($routeName, '.'));
    }

    private function getBaseLayout(Request $request): string
    {
        if ($this->bladeLayout) {
            return $this->bladeLayout;
        }

        return 'layouts.' . $this->getApplicationName($request);
    }

    /**
     * Get the title of the resource to use in form headers.
     */
    private function getFormTitle(): string
    {
        if ($this->formTitle) {
            return $this->formTitle;
        }

        // No title defined. We return the model name.
        return (new ReflectionClass($this->model))->getShortName();
    }

    private function authorizeAction(Request $request, string $action): void
    {
        switch ($action) {
            case 'index':
                $this->authorize($this->getPermissionForAction($request, 'index'), get_class($this->model));
                break;
            case 'create':
                $this->authorize($this->getPermissionForAction($request, 'create'), get_class($this->model));
                break;
            case 'read':
                $this->authorize($this->getPermissionForAction($request, 'read'), $this->model);
                break;
            case 'update':
                $this->authorize($this->getPermissionForAction($request, 'update'), $this->model);
                break;
            case 'delete':
                $this->authorize($this->getPermissionForAction($request, 'delete'), $this->model);
                break;
        }
    }

    // Get a permission for specific action
    private function getPermissionForAction(Request $request, string $action): string
    {
        if (array_key_exists($action, $this->permissions)) {
            return $this->permissions[$action];
        }

        return $this->getApplicationName($request) . '.admin';
    }

    private function validateAction(Request $request): void
    {
        Validator::make(
            $request->all(),
            $this->getValidationRules(),
            $this->getValidationMessages(),
            $this->getValidationAttributes()
        )->validate();
    }

    private function getValidationRules(): array
    {
        return $this->validationRules;
    }

    private function getValidationMessages(): array
    {
        return $this->validationMessages;
    }

    private function getValidationAttributes(): array
    {
        if ($this->validationAttributes) {
            return $this->validationAttributes;
        }

        $attributes = [];
        foreach ($this->getFormFields() as $field) {
            $attributes[$field['name']] = $field['label'];
        }

        return $attributes;
    }

    private function getApplicationName(Request $request): string
    {
        return $request->segment(1);
    }
}
