<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\InvalidationDescription;
use App\Models\Contractuals\Language;
use App\Models\Contractuals\LanguageLevel;
use App\Presenters\Contractuals\ApplicationEditPresenter;
use App\Presenters\Contractuals\ApplicationPresenter;
use App\Presenters\Contractuals\ApplicationRatingPresenter;
use App\Presenters\Contractuals\ContestStatePresenter;
use App\Presenters\Contractuals\PositionRatingPresenter;
use App\Presenters\Contractuals\QualificationPresenter;
use App\Services\Contractuals\ApplicationFormService;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Symfony\Component\HttpFoundation\Response;

class ApplicationController extends Controller
{
    public function __construct(
        readonly protected ApplicationFormService $applicationFormService
    ) {}

    public function show(Contest $contest, Application $application): View
    {
        if ($contest->type_id == 2) {
            try {
                $response = Http::withHeaders([
                    config('public_contractuals.api.header') => config('public_contractuals.api.key'),
                ])
                    ->get(config('public_contractuals.url')."/api/backoffice/applications/{$application->public_application_id}/attachments/")
                    ->throw();
                $attachmentData = $response->collect()->toArray();
            } catch (RequestException $e) {
                Log::error('FO_API: Unable to fetch attachments of application', [
                    'app' => 'contractuals',
                    'application_id' => $application->id,
                    'public_application_id' => $application->public_application_id,
                    'response_status' => $e->response->status(),
                    'response_body' => $e->response->body(),
                ]);
                $attachmentError = 'Παρουσιάστηκε πρόβλημα κατά την άντληση των συνημμένων από το Front Office.
                Παρακαλούμε κάντε refresh την σελίδα και αν το πρόβλημα παραμένει επικοινωνήστε με τον
                 διαχειριστή της εφαρμογής.';
            }
        }

        $application->load([
            'qualifications',
            'applicationRatings' => function ($q) {
                $q->orderByDesc('evaluation_number')
                    ->take(1)
                    ->with([
                        'evaluator',
                        'evaluatorUnit',
                        'positionRatings' => function ($q) {
                            $q
                                ->withUnitName()
                                ->withSpecializationName()
                                ->withPositionCode()
                                ->withPositionLocation()
                                ->orderBy('position_order');
                        },
                    ]);
            },
        ]);

        return view('contractuals.application.show', [
            'application' => ApplicationPresenter::make($application)->toArray(),
            'contest' => ContestStatePresenter::make($contest)->toArray(),
            'qualifications' => QualificationPresenter::collection($application->qualifications),
            'applicationRating' => ApplicationRatingPresenter::make($application->applicationRatings->first())->toArray(),
            'positionRatings' => PositionRatingPresenter::collection($application->applicationRatings->first()->positionRatings),
            'attachmentData' => $attachmentData ?? null,
            'attachmentError' => $attachmentError ?? null,
            'employablePosition' => $application->employablePosition()?->load(['unit', 'specialization'])->toArray(),
        ]);
    }

    public function edit(Contest $contest, Application $application): View
    {
        // TODO abort if finalized
        abort_if(
            $application->isLocked(),
            Response::HTTP_FORBIDDEN,
            'Η αίτηση είναι κλειδωμένη και δεν μπορεί να γίνει επεξεργασία.'
        );

        abort_if(
            $contest->isRestricted() && Auth::user()->cannot('contractuals.admin'),
            Response::HTTP_FORBIDDEN,
            'Η επεξεργασία των αιτήσεων του διαγωνισμού είναι κλειδωμένη από την κεντρική επιτροπή.'
        );

        return view('contractuals.application.edit', [
            'application' => ApplicationEditPresenter::make($application->load([
                'positions' => function ($query) {
                    $query
                        ->withSpecializationName()
                        ->withUnitName();
                },
                'degrees',
                'postgraduates',
                'doctorates',
                'greekLanguages',
                'languageSkills',
                'computerSkills',
                'experiences',
                'unemployments',
                'multiChildFamilies',
                'threeChildFamilies',
                'singleParentFamilies',
                'minors',
                'disabilities',
                'familyDisabilities',
            ]))->toArray(),
            'languages' => Language::all(),
            'languageLevels' => LanguageLevel::all(),
            'invalidationDescriptions' => InvalidationDescription::all(),
        ]);
    }

    public function update(Contest $contest, Application $application): JsonResponse
    {
        abort_if(
            $application->isLocked(),
            Response::HTTP_FORBIDDEN,
            'Η αίτηση είναι κλειδωμένη και δεν μπορεί να αλλαχτεί.'
        );

        abort_if(
            $contest->isRestricted() && Auth::user()->cannot('contractuals.admin'),
            Response::HTTP_FORBIDDEN,
            'Η επεξεργασία των αιτήσεων του διαγωνισμού είναι κλειδωμένη από την κεντρική επιτροπή.'
        );

        $this->validate(
            request(),
            [
                'invalidation_description_id' => 'exclude_unless:invalidated,true|required',
            ],
            [],
            [
                'invalidation_description_id' => 'Αιτιολογία Απόρριψης',
            ]
        );

        // TODO implement optimistic locking
        //        if (request()->has('t')) {
        //            $timestamp = Carbon::parse(request()->query('t'))->timestamp;
        //            abort_if($timestamp !== $application->updated_at->timestamp, 409, 'Η αίτηση έχει ήδη αλλάξει.');
        //        }

        try {
            $updatedApplication = $this->applicationFormService->update($application, request()->all());
        } catch (\Exception $e) {
            Log::error($e->getMessage(), ['app' => 'contractuals', 'application_id' => $application->id]);

            return response()->json([
                'message' => 'Η αίτηση δεν μπόρεσε να αποθηκευτεί προσωρινά. Παρακαλούμε προσπαθήστε ξανά.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json([
            'message' => 'Η αίτηση αποθηκεύτηκε προσωρινά.',
            'data' => ApplicationEditPresenter::make($updatedApplication->load([
                'positions' => function ($query) {
                    $query->withSpecializationName()->withUnitName();
                },
                'degrees',
                'postgraduates',
                'doctorates',
                'greekLanguages',
                'languageSkills',
                'computerSkills',
                'experiences',
                'unemployments',
                'multiChildFamilies',
                'threeChildFamilies',
                'singleParentFamilies',
                'minors',
                'disabilities',
                'familyDisabilities',
            ]))->toArray(),
        ]);
    }
}
