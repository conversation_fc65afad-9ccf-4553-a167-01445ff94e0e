<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Application;

class ApplicationPositionCollectionController extends Controller
{
    public function update(Contest $contest, Application $application)
    {
        $applicationPositionCollection = $application->positions()->get();
        
        return response()->json([
            'message' => 'Application position collection updated',
            'data' => $applicationPositionCollection
        ]);
    }
}
