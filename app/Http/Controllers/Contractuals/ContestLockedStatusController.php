<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Contest;

class ContestLockedStatusController extends Controller
{
    public function update(Contest $contest)
    {
        /*TODO
        maybe we can be more flexible with allowing the editing of a contest
        that already has applications. For example we can prohibit editing
        only if the contest has *evaluated* applications
        */
        abort_if(
            $contest->hasApplications(),
            422,
            'Cannnot unlock contest because it already contains applications. Please delete applications'
        );
        abort_if(!$contest->hasPositions(), 422, 'Cannot lock contest because we have not added any position');

        $contest->isLocked()
            ? $contest->unlock()
            : $contest->lock();

        return $contest;
    }
}
