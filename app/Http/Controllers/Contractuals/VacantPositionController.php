<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Position;

class VacantPositionController extends Controller
{
    public function index(Contest $contest, Calculation $calculation)
    {
        // Get stats about unfilled positions
        $unfilledPositions = Position::where('contest_id', $contest->id)
            ->select(['id', 'code', 'location', 'amount'])
            ->withEmploymentAggregates($calculation->id)
            ->get()
            ->map(function ($position) {
                $employeableCount = $position->employable_count - $position->declined_count;
                $toBeFilledCount = $employeableCount - $position->accepted_count;
                $vacantCount = $position->amount - $employeableCount;

                return [
                    'id' => $position->id,
                    'code' => $position->code,
                    'location' => $position->location,
                    'vacant' => $vacantCount,
                    'toBeFilled' => $toBeFilledCount,
                ];
            })
            ->filter(function ($position) {
                return $position['vacant'] > 0 || $position['toBeFilled'] > 0;
            });

        return view('contractuals.vacant-positions', [
            'contest' => $contest,
            'calculation' => $calculation,
            'unfilledPositions' => $unfilledPositions,
        ]);
    }
}
