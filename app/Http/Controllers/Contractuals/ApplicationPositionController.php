<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Position;
use App\Models\Contractuals\Application;
use App\Presenters\Contractuals\ApplicationPositionPresenter;

class ApplicationPositionController extends Controller
{
    public function update(Contest $contest, Application $application, int $positionId)
    {
        $this->validate(request(), [
            'locality' => 'required|boolean',
        ]);
        
        $application->positions()->updateExistingPivot($positionId, ['locality' => request()->boolean('locality')]);

        return response()->json([
            'message' => 'Επιτυχία',
            'data' => ApplicationPositionPresenter::make(
                $application->positions()
                ->withSpecializationName()
                ->withUnitName()
                ->where('position_id', $positionId)
                ->first()
            )->toArray(),
        ]);
    }
}
