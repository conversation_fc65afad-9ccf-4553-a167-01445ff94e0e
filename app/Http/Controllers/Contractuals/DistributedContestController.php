<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Jobs\Contractuals\DistributeContestJob;
use App\Models\Contractuals\Contest;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class DistributedContestController extends Controller
{
    public function store()
    {
        request()->validate([
            'contest_id' => 'required',
            'n' => 'required|numeric|min:1',
            'limit' => 'required|numeric|min:0',
        ]);

        $contest = Contest::find(request('contest_id'));

        abort_if(
            ! $contest->hasAllApplicationsRatedOrAutoRated(),
            Response::HTTP_FORBIDDEN,
            'Οι αιτήσεις διαγωνισμού δεν μπορούν να διαμοιραστούν γιατί υπάρχουν εκκρεμείς αιτήσεις (υπό έλεγχο).'
        );
        abort_if(
            $contest->isRanked(),
            Response::HTTP_FORBIDDEN,
            'Οι αιτήσεις διαγωνισμού δεν μπορούν να διαμοιραστούν γιατί οι τελικοί πίνακες έχουν υπολογισθεί'
        );

        try {
            DistributeContestJob::dispatch($contest->id, request()->get('n', 2), request()->get('limit', 10), $contest->notifications_email ?? '');
        } catch (\Throwable $e) {
            Log::error($e->getMessage());

            return response()->json([
                'message' => 'Ο διαμοιρασμός των αιτήσεων του διαγωνισμού δεν μπόρεσε να ξεκινήσει.',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        // TODO - Consider returng contest (is_calculating) in order to lock specific actions in the front-end
        return response()->json([
            'message' => 'Ο διαμοιρασμός των αιτήσεων έχει ξεκινήσει. Θα ενημερωθεί όταν ο διαμοιρασμός ολοκληρωθεί.',
        ], Response::HTTP_ACCEPTED);
    }
}
