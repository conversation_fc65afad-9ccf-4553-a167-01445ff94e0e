<?php

namespace App\Http\Controllers\Contractuals;

use App\Exports\Contractuals\RejectedRankingsExport2190;
use App\Exports\Contractuals\RejectedRankingsExport4765;
use App\Http\Controllers\Controller;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Position;

class RankedPositionRejectedExportController extends Controller
{
    public function show(Contest $contest, Position $rankedPosition)
    {
        $calculation = request()->has('calculation')
            ? $contest->calculations()->findOrFail(request()->query('calculation'))
            : $contest->getLatestCalculation();

        if ($contest->type_id === 1) {
            return (new RejectedRankingsExport2190())
                ->forPosition($rankedPosition->id)
                ->forCalculation($calculation->id)
                ->download('aporiptaioi.xlsx');
        }

        return (new RejectedRankingsExport4765(
            position: $rankedPosition,
            calculationId: $calculation->id,
            forPublication: request('publication')
        ))->download('ΠΙΝΑΚΕΣ AΠΟΡΡΙΠΤΕΩΝ - ΘΕΣΗ '.$rankedPosition->code.'.xlsx');
    }
}
