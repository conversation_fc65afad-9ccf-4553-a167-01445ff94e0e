<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Http\Requests\Contractuals\PositionFormRequest;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Position;
use App\Presenters\Contractuals\ContestShowPresenter;
use App\Presenters\Contractuals\PositionPresenter;
use Illuminate\Http\Request;

class PositionController extends Controller
{
    public function index(Contest $contest)
    {
        $this->authorize('contractuals.read');

        $contest->load([
            'units:id,name',
        ]);

        $positions = Position::where('contest_id', $contest->id)
            ->when(auth()->user()->cannot('contractuals.admin'), function ($q) {
                $q->where('unit_id', auth()->user()->unit_id);
            })
            ->with([
                'unit:id,name,abbrv',
                'specialization:id,name',
            ])
            ->withUnitName()
            ->withSpecializationName()
            ->get();

        return view('contractuals.position.index', [
            'contest' => ContestShowPresenter::make($contest)->toArray(),
            'positions' => PositionPresenter::collection($positions),
        ]);
    }

    public function create(Contest $contest)
    {
        $this->authorize('contractuals.create');

        $auxiliaryLevels = Position::AUXILIARY_LEVELS;

        return view('contractuals.position.create', compact('contest', 'auxiliaryLevels'));
    }

    public function store(PositionFormRequest $request)
    {
        $this->authorize('contractuals.create');

        $position = Position::create([
            'contest_id' => $request['contest_id'],
            'specialization_id' => $request['specialization_id'],
            'amount' => $request['amount'],
            'code' => $request['code'],
            'location' => $request['location'],
            'unit_id' => $request['unit_id'],
            'has_locality' => (bool) $request['has_locality'],
        ]);

        if ($request->input('requirements')) {
            foreach ($request->input('requirements') as $auxiliary_level => $requirements) {
                $reqs = collect($requirements)
                    ->mapWithKeys(function ($requirement) use ($auxiliary_level) {
                        return [$requirement => ['auxiliary_level' => $auxiliary_level]];
                    });
                $position->requirements()->attach($reqs->toArray());
            }
        }

        flash()->success('Επιτυχία!', 'Η Θέση / Ειδικότητα αποθηκεύτηκε.');

        return redirect()->route('contractuals.positions.index', $position->contest_id);
    }

    public function edit(Contest $contest, Position $position)
    {
        $this->authorize('contractuals.create');

        $auxiliaryLevels = Position::AUXILIARY_LEVELS;

        return view('contractuals.position.edit', compact('contest', 'position', 'auxiliaryLevels'));
    }

    public function show(Contest $contest, Position $position)
    {
        $this->authorize('contractuals.read');

        $auxiliaryLevels = Position::AUXILIARY_LEVELS;

        return view('contractuals.position.show', compact('contest', 'position', 'auxiliaryLevels'));
    }

    public function update(PositionFormRequest $request, Contest $contest, Position $position)
    {
        $this->authorize('contractuals.create');

        info('locality'.$request['has_locality']);
        $position->update([
            'contest_id' => $request['contest_id'],
            'specialization_id' => $request['specialization_id'],
            'amount' => $request['amount'],
            'code' => $request['code'],
            'location' => $request['location'],
            'unit_id' => $request['unit_id'],
            'has_locality' => (bool) $request['has_locality'],
        ]);

        $position->requirements()->detach();

        if ($request->input('requirements')) {
            foreach ($request->input('requirements') as $auxiliary_level => $requirements) {
                $reqs = collect($requirements)
                    ->mapWithKeys(function ($requirement) use ($auxiliary_level) {
                        return [$requirement => ['auxiliary_level' => $auxiliary_level]];
                    });
                $position->requirements()->attach($reqs->toArray());
            }
        }

        flash()->success('Επιτυχία!', 'Η Θέση / Ειδικότητα αποθηκεύτηκε.');

        return redirect()->route('contractuals.positions.index', $position->contest_id);
    }

    public function destroy(Request $request, Contest $contest, Position $position)
    {
        $this->authorize('contractuals.create');

        if (\count($position->applications)) {
            flash()->overlay('Αποτυχία!', 'Η Θέση / Ειδικότητα δεν μπορεί να διαγραφεί καθώς έχει καταχωρημένες αιτήσεις.', 'error');
        } else {
            $position->delete();
            flash()->success('Επιτυχία!', 'Η Θέση / Ειδικότητα διαγράφηκε.');
        }

        return redirect()->route('contractuals.contests.show', $position->contest_id);
    }
}
