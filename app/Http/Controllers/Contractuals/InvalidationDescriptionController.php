<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\ReadyFormController;
use App\Models\Contractuals\InvalidationDescription;
use App\Models\Contractuals\Language;

class InvalidationDescriptionController extends ReadyFormController
{
    protected array $permissions = [
        'index'  => 'contractuals.admin',
        'create' => 'contractuals.admin',
        'read'   => 'contractuals.admin',
        'update' => 'admin',
        'delete' => 'contractuals.admin',
    ];

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Λόγος Απόρριψης', 'type' => 'text'],
           ];

    protected string $formTitle = 'Λόγοι Απόρριψης';

    protected array $validationRules = [
        'name' => 'required|min:3',
    ];

    protected string $bladeLayout = 'layouts.contractuals';

    public function __construct(InvalidationDescription $invalidationDescription)
    {
        $this->middleware('permission:contractuals.admin');

        parent::__construct($invalidationDescription);
    }
}
