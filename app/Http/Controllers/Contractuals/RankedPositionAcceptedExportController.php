<?php

namespace App\Http\Controllers\Contractuals;

use App\Exports\Contractuals\AcceptedRankingsExport4765;
use App\Exports\Contractuals\RankingsExport2190;
use App\Http\Controllers\Controller;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Position;

class RankedPositionAcceptedExportController extends Controller
{
    public function show(Contest $contest, Position $rankedPosition)
    {
        $calculation = request()->has('calculation')
            ? $contest->calculations()->findOrFail(request()->query('calculation'))
            : $contest->getLatestCalculation();

        if ($contest->type_id === 1) {
            return (new RankingsExport2190())
                ->accepted()
                ->forPosition($rankedPosition->id)
                ->forCalculation($calculation->id)
                ->download('pinakas.xlsx');
        }

        return (new AcceptedRankingsExport4765(
            position: $rankedPosition,
            calculationId: $calculation->id,
            forPublication: request('publication'),
        ))->download('ΠΙΝΑΚΕΣ ΕΠΙΤΥΧΟΝΤΩΝ - ΘΕΣΗ '.$rankedPosition->code.'.xlsx');
    }
}
