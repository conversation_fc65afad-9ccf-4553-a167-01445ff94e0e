<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;

class ApplicationLockedStatusController extends Controller
{
    public function update(Contest $contest, Application $application)
    {
        abort_if($application->hasEvaluations(), 422, 'Application cannot been unlocked because its evaluations have been initialized');

        $application->isLocked()
            ? $application->unlock()
            : $application->lock();

        return $application;
    }
}
