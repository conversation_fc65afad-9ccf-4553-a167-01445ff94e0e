<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\PublicApplication;
use App\Models\Prefecture;
use App\Models\Region;
use App\Models\Unit;
use App\Services\Contractuals\Statistics\LeastPopularUnits;
use App\Services\Contractuals\Statistics\MostPopularUnits;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class FrontOfficeStatisticsController extends Controller
{
    public function index()
    {
        $contest = request('contest_id') ? Contest::find(request('contest_id')) : Contest::latest('start_date')->first();
        $contestId = $contest->id;

        try {
            $responseA = Http::withHeaders([
                config('public_contractuals.api.header') => config('public_contractuals.api.key'),
            ])
                ->get(config('public_contractuals.url').'/api/backoffice/online-users')
                ->throw();
        } catch (\Throwable $e) {
            abort(422, 'Unable to fetch online users');
        }

        $onlineUsers = DB::connection('mysql_contractuals')
            ->table('online_users')
            ->whereDate('logged_at', '>=', $contest->start_date)
            ->whereDate('logged_at', '<=', $contest->end_date)
            ->select('logged_at as x', 'users as y')->get();

        $periodData = collect();
        $dailySubmittedApplicationData = collect();
        $dailyTotalApplicationData = collect();
        $accumulatedSubmittedApplicationData = collect();
        $accumulatedTotalApplicationData = collect();

        $period = CarbonPeriod::create($contest->start_date->format('Y-m-d'), $contest->end_date->format('Y-m-d'));
        foreach ($period as $date) {
            $dailySubmittedApplications = PublicApplication::ofContest($contestId)
                ->whereDate('submitted_at', '=', $date->format('Y-m-d'))
                ->count();
            $dailySubmittedApplicationData->push($dailySubmittedApplications);

            $dailyTotalApplications = PublicApplication::ofContest($contestId)
                ->whereDate('created_at', '=', $date->format('Y-m-d'))
                ->count();
            $dailyTotalApplicationData->push($dailyTotalApplications);

            $accumulatedSubmittedApplications = PublicApplication::ofContest($contestId)
                ->whereDate('submitted_at', '<=', $date)
                ->count();
            if ($date->isAfter(now()->startOfDay())) {
                $accumulatedSubmittedApplicationData->push(0);
            } else {
                $accumulatedSubmittedApplicationData->push($accumulatedSubmittedApplications);
            }

            $accumulatedTotalApplications = PublicApplication::ofContest($contestId)
                ->whereDate('created_at', '<=', $date)
                ->count();
            if ($date->isAfter(now()->startOfDay())) {
                $accumulatedTotalApplicationData->push(0);
            } else {
                $accumulatedTotalApplicationData->push($accumulatedTotalApplications);
            }

            $periodData->push($date->format('d-m-Y'));
        }

        // Number of Applications by specialization type
        $peApplications = PublicApplication::ofContest($contestId)->PE()->submitted()->count();
        $teApplications = PublicApplication::ofContest($contestId)->TE()->submitted()->count();
        $deApplications = PublicApplication::ofContest($contestId)->DE()->submitted()->count();
        $yeApplications = PublicApplication::ofContest($contestId)->YE()->submitted()->count();

        // Least popular units
        $leastPopularUnitData = LeastPopularUnits::get($contestId);

        // Most popular units
        $mostPopularUnitData = MostPopularUnits::get($contestId);

        // Get amount of applications per geographic region.
        $applicationsPerPosition = DB::connection('mysql_public_contractuals')->table('application_position as ap')
            ->join('applications as a', 'a.id', '=', 'ap.application_id')
            ->selectRaw('position_id, count(*) as applications')
            ->where('a.is_submitted', '=', true)
            ->where('a.contest_id', '=', $contestId)
            ->groupBy('position_id')
            ->get();

        $prefectureData = Prefecture::whereNotNull('geoid')->get()
            ->map(function ($prefecture) use ($contestId, $applicationsPerPosition) {
                // Get all units of this prefecture
                $unitIds = Unit::where('prefecture_id', $prefecture->id)->pluck('id');
                // Get all positions of these units
                $positionIds = Contest::find($contestId)->positions()->whereIn('unit_id', $unitIds)->pluck('id');
                // Get total applications for
                $totalApplications = $applicationsPerPosition->whereIn('position_id', $positionIds)->sum('applications');

                return [
                    'id' => $prefecture->geoid,
                    'name' => "Νομός {$prefecture->name}",
                    'value' => $totalApplications,
                ];
            });

        return view('contractuals.statistics.front-office', [
            'contestId' => $contestId,
            'contests' => Contest::orderByDesc('start_date')->get(['id', 'name', 'start_date', 'end_date']),
            'totalUsers' => $responseA->json('total_users'),
            'currentOnlineUsers' => $responseA->json('online_users'),
            'onlineUsers' => $onlineUsers,
            'totalApplications' => PublicApplication::ofContest($contestId)->count(),
            'submittedApplications' => PublicApplication::ofContest($contestId)->submitted()->count(),
            'dailySubmittedApplicationData' => $dailySubmittedApplicationData,
            'dailyTotalApplicationData' => $dailyTotalApplicationData,
            'accumulatedSubmittedApplicationData' => $accumulatedSubmittedApplicationData,
            'accumulatedTotalApplicationData' => $accumulatedTotalApplicationData,
            'period' => $periodData,
            'peApplications' => $peApplications,
            'teApplications' => $teApplications,
            'deApplications' => $deApplications,
            'yeApplications' => $yeApplications,
            'prefectureData' => $prefectureData,
            'mostPopularUnitData' => $mostPopularUnitData,
            'leastPopularUnitData' => $leastPopularUnitData,
        ]);
    }
}
