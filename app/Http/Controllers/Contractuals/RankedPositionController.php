<?php

namespace App\Http\Controllers\Contractuals;

use App\Exports\Contractuals\AcceptedRankingsExport4765;
use App\Exports\Contractuals\RankingsExport2190;
use App\Exports\Contractuals\RejectedRankingsExport2190;
use App\Exports\Contractuals\RejectedRankingsExport4765;
use App\Http\Controllers\Controller;
use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Position;
use App\Models\Contractuals\Ranking;
use App\Models\Contractuals\Specialization;
use App\Presenters\Contractuals\CalculationPresenter;
use App\Presenters\Contractuals\ContestShowPresenter;
use App\Presenters\Contractuals\PositionPresenter;
use App\Presenters\Contractuals\RankedPositionPresenter;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Symfony\Component\HttpFoundation\Response;

class RankedPositionController extends Controller
{
    public function index(Contest $contest): JsonResponse|View
    {
        abort_if(
            ! $contest->hasRankings(),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            'Δεν έχουν υπολογιστεί πίνακες κατάταξης για αυτόν τον διαγωνισμό'
        );

        // Since the contest is ranked (i.e. contest has rankings), there should be at least one calculation
        $calculation = request()->has('calculation')
            ? $contest->calculations()->findOrFail(request()->query('calculation'))
            : $contest->getLatestCalculation();

        if (request()->expectsJson()) {
            $queryString = request()->query();

            // FIXME: Use multitenancy to get the positions
            $positions = Position::ofContest($contest->id)
                ->when(auth()->user()->cannot('contractuals.admin'), function ($q) {
                    $q->where('unit_id', auth()->user()->unit_id);
                })
                ->withEmploymentAggregates($calculation->id)
                ->withSpecializationName()
                ->withUnitName()
                ->filter($queryString)
                ->paginate((int) request()->query('limit', '10'));

            return response()->json([
                'message' => 'Welcome to Auto Rated Application Controller!',
                'data' => PositionPresenter::pagination($positions),
            ])->cookie('contractuals_ranked_positions_filters', json_encode($queryString), 20, null, null, false, false);
        }

        // Get exports
        $exports = $calculation->getMedia('exports')
            ->map(function (Media $file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name.'.'.$file->getCustomProperty('extension'),
                    'url' => $file->getUrl(),
                ];
            })
            ->toArray();

        return view('contractuals.ranked-position.index', [
            'contest' => ContestShowPresenter::make($contest),
            'calculation' => CalculationPresenter::make($calculation),
            'isCreatingExports' => Cache::has('creating_contractuals_exports'),
            'exports' => $exports,
            'stats' => [
                'totalPositions' => (int) $contest->positions()->sum('amount'),
                'totalEmployables' => Ranking::ofCalculation($calculation->id)->employable()->count(),
                'totalAccepted' => Ranking::ofCalculation($calculation->id)->accepted()->distinct('application_id')->count(),
                'totalDeclined' => Ranking::ofCalculation($calculation->id)->declined()->distinct('application_id')->count(),
                'totalRejected' => Ranking::ofCalculation($calculation->id)->rejected()->distinct('application_id')->count(),
            ],
            'filters' => [
                'specializations' => Specialization::all()->map->only(['id', 'name']),
                'units' => $contest->units->map->only(['id', 'name']),
            ],
        ]);
    }

    public function show(Contest $contest, Position $rankedPosition)
    {
        // TODO: abort if contest has no rankings

        $calculation = request()->has('calculation')
            ? $contest->calculations()->findOrFail(request()->query('calculation'))
            : $contest->getLatestCalculation();

        $rankedPosition->load([
            'succeededRankings' => function ($query) use ($calculation) {
                $query->whereBelongsTo($calculation)
                    ->withEmployableDescription()
                    ->with(['application', 'positionRating.applicationRating']);
            },
            'rejectedRankings' => function ($query) use ($calculation) {
                $query->whereBelongsTo($calculation)
                    ->with(['application', 'positionRating.applicationRating']);
            },
        ]);

//        $calculationData = $contest->calculations->map(function ($calculation) {
//            return [
//                'id' => $calculation->id,
//                'description' => "$calculation->description    ($calculation->run_at)",
//                'distribution_run' => $calculation->distribution_run,
//            ];
//        });

        return view('contractuals.ranked-position.show', [
            'rankedPosition' => RankedPositionPresenter::make($rankedPosition)->toArray(),
            'calculation' => CalculationPresenter::make($calculation)->toArray(),
            'contest' => ContestShowPresenter::make($contest)->toArray(),
        ]);
    }

//    public function exportAccepted(Position $rankedPosition)
//    {
//        if ($rankedPosition->contest->type_id === 1) {
//            return (new RankingsExport2190())->accepted()->forPosition($rankedPosition->id)->forCalculation(request('calculation_id'))->download('pinakas.xlsx');
//        } elseif ($rankedPosition->contest->type_id === 2) {
//            return (new AcceptedRankingsExport4765(
//                position: $rankedPosition,
//                calculationId: request('calculation_id'),
//                forPublication: request('publication'),
//            ))->download('ΠΙΝΑΚΕΣ ΕΠΙΤΥΧΟΝΤΩΝ - ΘΕΣΗ '.$rankedPosition->code.'.xlsx');
//        }
//    }

//    public function exportRejected(Position $rankedPosition)
//    {
//        if ($rankedPosition->contest->type_id === 1) {
//            return (new RejectedRankingsExport2190())->forPosition($rankedPosition->id)->forCalculation(request('calculation_id'))->download('aporiptaioi.xlsx');
//        } elseif ($rankedPosition->contest->type_id === 2) {
//            return (new RejectedRankingsExport4765(
//                position: $rankedPosition,
//                calculationId: request('calculation_id'),
//                forPublication: request('publication')
//            ))->download('ΠΙΝΑΚΕΣ AΠΟΡΡΙΠΤΕΩΝ - ΘΕΣΗ '.$rankedPosition->code.'.xlsx');
//        }
//    }
}
