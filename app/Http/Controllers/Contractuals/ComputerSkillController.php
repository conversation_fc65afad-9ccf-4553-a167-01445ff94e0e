<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\ComputerSkill;
use App\Models\Contractuals\Contest;

class ComputerSkillController extends Controller
{
    public function store(Contest $contest, Application $application)
    {
        $validated = request()->validate([
            'name' => 'required|string',
        ]);

        $doctorate = $application->computerSkills()->create($validated);

        return response()->json([
            'message' => 'Computer skill created successfully',
            'data' => $doctorate,
        ]);
    }

    public function update(Contest $contest, Application $application, ComputerSkill $computerSkill)
    {
        $validated = request()->validate([
            'name' => 'required|string',
        ]);

        $computerSkill->update($validated);

        return response()->json([
            'message' => 'Computer skill updated successfully',
            'data' => $computerSkill,
        ]);
    }

    public function destroy(Contest $contest, Application $application, ComputerSkill $computerSkill)
    {
        $computerSkill->delete();
        $application->computerSkills()->detach($computerSkill->id);

        return response()->json([
            'message' => 'Computer skill deleted successfully',
        ]);
    }
}
