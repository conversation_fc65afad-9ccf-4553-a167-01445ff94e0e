<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;
use App\Presenters\Contractuals\ApplicationPresenter;
use Illuminate\Contracts\Pagination\Paginator;

class AutoRatedApplicationController extends Controller
{
    public function index(Contest $contest)
    {
        $queryString = request()->query();

        $autoRatedApplications = Application::query()
            ->withScore()
            ->withValidator()
            ->withRater()
            ->withInvalidationDescription()
            ->ofContest($contest->id)
            ->when(request()->user()->cannot('contractuals.adminUnit') && ! $contest->isRanked(), function ($query) { // TODO: Improve multitenancy
                return $query->evaluatedByUnit((string) request()->user()->unit_id);
            })
            ->autoRated()
            ->notRejected()
            ->filter($queryString)
            ->paginate((int) request()->query('limit', '10'));

        if (! $autoRatedApplications instanceof Paginator) {
            $autoRatedApplications = $autoRatedApplications->paginate(10);
        }

        return response()->json([
            'message' => 'Welcome to Auto Rated Application Controller!',
            'data' => ApplicationPresenter::pagination($autoRatedApplications),
        ])->cookie('contractuals_auto_rated_applications_filters', json_encode($queryString), 20, null, null, false, false);
    }
}
