<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Contest;
use Illuminate\Http\Request;

class ContestRestrictedStatusController extends Controller
{
    public function update(Contest $contest)
    {
//        abort(404, 'Not implemented');
        $contest->isRestricted()
            ? $contest->unrestrict()
            : $contest->restrict();

        return $contest;
    }
}
