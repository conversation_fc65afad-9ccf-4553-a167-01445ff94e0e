<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\ReadyFormController;
use App\Models\Contractuals\Language;

class LanguageController extends ReadyFormController
{
    protected array $permissions = [
        'index'  => 'contractuals.admin',
        'create' => 'contractuals.admin',
        'read'   => 'contractuals.admin',
        'update' => 'contractuals.admin',
        'delete' => 'contractuals.admin',
    ];

    protected array $formFields = [
        ['name' => 'name', 'label' => 'Γλώσσα', 'type' => 'text'],
        ['name' => 'code', 'label' => 'Κωδικός Γλώσσας', 'type' => 'text'],
    ];

    protected string $formTitle = 'Ξένες Γλώσσες';

    protected array $validationRules = [
        'name' => 'required|min:3',
    ];

    protected string $bladeLayout = 'layouts.contractuals';

    public function __construct(Language $language)
    {
        $this->middleware('permission:contractuals.admin');

        parent::__construct($language);
    }
}
