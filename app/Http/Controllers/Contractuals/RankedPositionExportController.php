<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Jobs\Contractuals\CreateAcceptedRankingsExportJob;
use App\Jobs\Contractuals\CreateRejectedRankingsExportJob;
use App\Models\Contractuals\Contest;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class RankedPositionExportController extends Controller
{
    public function store(Contest $contest)
    {
        abort_if(
            ! $contest->isRanked(),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            'The contest is not ranked/has not active calculation'
        );

        // Creating total exports is **only** allowed for latest calculation
        $calculation = $contest->getLatestCalculation();

        abort_if(
            $calculation->isDistributionRun(),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            'The export can only be created for final calculation runs.'
        );

        try {
            // First we clear the previous exports
            $calculation->getMedia('exports')->each(function ($media) {
                $media->delete();
            });

            // TODO send email when chain is done
            Bus::chain([
                new CreateAcceptedRankingsExportJob($calculation, $contest->notifications_email ?? ''),
                new CreateRejectedRankingsExportJob($calculation, $contest->notifications_email ?? ''),
                new CreateAcceptedRankingsExportJob($calculation, $contest->notifications_email ?? '', true),
                new CreateRejectedRankingsExportJob($calculation, $contest->notifications_email ?? '', true),
            ])->catch(function (Throwable $e) {
                Log::error($e->getMessage());
                // TODO - Consider adding a notification or clearing cache
            })->dispatch();

            return response()->json(
                ['message' => 'H δημιουργία των xls των τελικών πινάκων έχει ξεκινήσει.'],
                200
            );

//            return redirect()->route('contractuals.positions.index', $calculation->contest)
//                ->with('success', 'H δημιουργία των xls των τελικών πινάκων έχει ξεκινήσει.');
        } catch (Throwable $e) {
            Log::error($e->getMessage());

            return response()->json(
                ['message' => $e->getMessage()],
                200
            );
        }
    }
}
