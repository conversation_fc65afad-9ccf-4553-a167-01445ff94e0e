<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\LanguageSkill;

class LanguageSkillController extends Controller
{
    public function store(Contest $contest, Application $application)
    {
        $validated = request()->validate([
            'name' => 'required|string',
            'language_id' => 'required|integer',
            'language_level_id' => 'required|integer',
        ]);

        $doctorate = $application->languageSkills()->create($validated);

        return response()->json([
            'message' => 'Language skill created successfully',
            'data' => $doctorate,
        ]);
    }

    public function update(Contest $contest, Application $application, LanguageSkill $languageSkill)
    {
        $validated = request()->validate([
            'name' => 'required|string',
            'language_id' => 'required|integer',
            'language_level_id' => 'required|integer',
        ]);

        $languageSkill->update($validated);

        return response()->json([
            'message' => 'Language skill updated successfully',
            'data' => $languageSkill,
        ]);
    }

    public function destroy(Contest $contest, Application $application, LanguageSkill $languageSkill)
    {
        $languageSkill->delete();
        $application->languageSkills()->detach($languageSkill->id);

        return response()->json([
            'message' => 'Language skill deleted successfully',
        ]);
    }
}
