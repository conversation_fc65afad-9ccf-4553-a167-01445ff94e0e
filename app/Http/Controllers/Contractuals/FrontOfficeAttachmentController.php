<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FrontOfficeAttachmentController extends Controller
{
    public function show($id, $attachmentId)
    {
        try {
            $response = $this->getFileStreamFromFrontOffice("/api/backoffice/applications/$id/attachments/$attachmentId");
        } catch (RequestException $e) {
            Log::error('FO_API: Unable to fetch application attachment', [
                'app' => 'contractuals',
                'public_application_id' => $id,
                'attachment_id' => $attachmentId,
                'response_status' => $e->response->status(),
                'response_body' => $e->response->body(),
            ]);
            abort(422, 'Unable to fetch application attachment.');
        }

        return getFileFromResponse($response);
    }

    public function zip($id)
    {
        try {
            $response = $this->getFileStreamFromFrontOffice("/api/backoffice/applications/$id/zip");
        } catch (RequestException $e) {
            Log::error('FO_API: Unable to fetch application attachments zip', [
                'app' => 'contractuals',
                'public_application_id' => $id,
                'response_status' => $e->response->status(),
                'response_body' => $e->response->body(),
            ]);
            abort(422, 'Unable to fetch application attachments zip.');
        }

        return getFileFromResponse($response);
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     */
    protected function getFileStreamFromFrontOffice($apiUrl)
    {
        return Http::withHeaders([
            config('public_contractuals.api.header') => config('public_contractuals.api.key'),
        ])->get(config('public_contractuals.url').$apiUrl, ['stream' => true])
            ->throw();
    }
}
