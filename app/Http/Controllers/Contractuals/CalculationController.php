<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Contest;
use App\Presenters\Contractuals\CalculationPresenter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class CalculationController extends Controller
{
    public function index(Contest $contest): JsonResponse
    {
        $calculations = $contest->calculations()
            ->notForDistribution()
            ->orderByDesc('run_at')
            ->get();

        return response()->json(CalculationPresenter::collection($calculations), Response::HTTP_OK);
    }
}
