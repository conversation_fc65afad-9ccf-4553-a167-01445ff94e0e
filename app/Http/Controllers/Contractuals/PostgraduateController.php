<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Postgraduate;

class PostgraduateController extends Controller
{
    public function store(Contest $contest, Application $application)
    {
        $validated = request()->validate([
            'name' => 'required|string',
            'is_integrated' => 'required|boolean',
        ]);

        $postgraduate = $application->postgraduates()->create($validated);

        return response()->json([
            'message' => 'Degree created successfully',
            'data' => $postgraduate,
        ]);
    }

    public function update(Contest $contest, Application $application, Postgraduate $postgraduate)
    {
        $validated = request()->validate([
            'name' => 'required|string',
            'is_integrated' => 'required|boolean',
        ]);

        $postgraduate->update($validated);

        return response()->json([
            'message' => 'Postgraduate updated successfully',
            'data' => $postgraduate,
        ]);
    }

    public function destroy(Contest $contest, Application $application, Postgraduate $postgraduate)
    {
        $postgraduate->delete();
        $application->postgraduates()->detach($postgraduate->id);

        return response()->json([
            'message' => 'Postgraduate deleted successfully',
        ]);
    }
}
