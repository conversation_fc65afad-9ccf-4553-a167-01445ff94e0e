<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Jobs\Contractuals\RankContestJob;
use App\Models\Contractuals\Contest;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class RankedContestController extends Controller
{
    public function store()
    {
        request()->validate([
            'contest_id' => 'required',
            'description' => 'required|string',
            'hire_runners_up' => 'boolean',
        ]);

        $contest = Contest::find(request('contest_id'));
        $calculationDescription = request('description');
        $isHirringRunnerUps = request()->boolean('hire_runners_up');

        abort_if(
            ! $contest->isRated(),
            Response::HTTP_FORBIDDEN,
            'Οι τελικοί πίνακες του διαγωνισμού δεν μπορούν να δημιουργηθούν γιατί υπάρχουν εκκρεμείς αιτήσεις (υπό έλεγχο).'
        );

        abort_if(
            $contest->isRanked(),
            Response::HTTP_FORBIDDEN,
            'Οι τελικοί πίνακες του διαγωνισμού έχουν ήδη δημιουργηθεί.'
        );

        abort_if(
            Cache::has('calculating_contest'),
            Response::HTTP_FORBIDDEN,
            'Οι τελικοί πίνακες του διαγωνισμού υπολογίζονται, παρακαλώ περιμένετε μέχρι να ολοκληρωθεί ο υπολογισμός'
        );

        try {
            RankContestJob::dispatch($contest->id, $calculationDescription, $isHirringRunnerUps, $contest->notifications_email ?? '');
        } catch (\Throwable $e) {
            Log::error($e->getMessage());

            return response()->json(
                ['message' => $e->getMessage()],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }

        // TODO - Consider returng contest (is_calculating) in order to lock specific actions in the front-end
        return response()->json([
            'message' => 'Οι αιτήσεις έχουν υποβληθεί για βαθμολόγηση. Θα ενημερωθείτε με την επιτυχή κατάσταση του υπολογισμού',
        ], Response::HTTP_ACCEPTED);
    }
}
