<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;
use App\Presenters\Contractuals\ApplicationPresenter;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Http\JsonResponse;

class UnderEvaluationApplicationController extends Controller
{
    public function index(Contest $contest): JsonResponse
    {
        $queryString = request()->query();

        $underEvaluationApplications = Application::query()
            ->withScore()
            ->withValidator()
            ->withRater()
            ->withInvalidationDescription()
            ->ofContest($contest->id)
            ->when(request()->user()->cannot('contractuals.adminUnit') && ! $contest->isRanked(), function ($query) { // TODO: Improve multitenancy
                return $query->evaluatedByUnit((string) request()->user()->unit_id);
            })
            ->notRated()
            ->filter($queryString)
            ->paginate((int) request()->query('limit', '10'));

        return response()->json([
            'message' => 'Welcome to Auto Rated Application Controller!',
            'data' => ApplicationPresenter::pagination($underEvaluationApplications),
        ])->cookie('contractuals_under_evaluation_applications_filters', json_encode($queryString), 20, null, null, false, false);
    }
}
