<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\ReadyFormController;
use App\Models\Contractuals\Specialization;

class SpecializationController extends ReadyFormController
{
    protected array $relationships = ['specializationType'];

    protected array $formFields = [
        ['name' => 'specialization_type_id', 'label' => 'Tύπος Ειδικότητας', 'type' => 'select', 'relationship' => 'specializationType'],
        ['name' => 'name', 'label' => 'Ειδικότητα', 'type' => 'text'],
        ['name' => 'shortname', 'label' => 'Συντομογραφία', 'type' => 'text'],
    ];

    protected array $indexFields = ['specialization_type_id', 'name'];

    protected string $formTitle = 'Ειδικότητες';

    protected bool $withTrashed = true;

    protected array $validationRules = [
        'name' => 'required|min:4',
        'specialization_type_id' => 'required|exists:mysql_contractuals.specialization_types,id',
    ];

    protected string $bladeLayout = 'layouts.contractuals';

    protected array $permissions = [
        'index'  => 'contractuals.admin',
        'create' => 'contractuals.admin',
        'read'   => 'contractuals.admin',
        'update' => 'contractuals.admin',
        'delete' => 'contractuals.admin',
    ];

    public function __construct(Specialization $specialization)
    {
        $this->middleware('permission:contractuals.admin');

        parent::__construct($specialization);
    }
}
