<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\ContestType;
use App\Models\Organogram;
use App\Models\Unit;
use App\Presenters\Contractuals\CalculationPresenter;
use App\Presenters\Contractuals\ContestEditPresenter;
use App\Presenters\Contractuals\ContestShowPresenter;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ContestController extends Controller
{
    public function index()
    {
        // The full index is only for admins
        $this->authorize('contractuals.read');

        $activeContests = Contest::notRanked()->with('positions', 'applications')->orderBy('start_date', 'desc')->get();

        $completedContests = Contest::ranked()->with('positions', 'applications')->orderBy('start_date', 'desc')->get();

        return view('contractuals.contest.index', compact('activeContests', 'completedContests'));
    }

    public function create()
    {
        $this->authorize('contractuals.adminUnit');

        return view('contractuals.contest.create', [
            'menuOptions' => [
                'contestTypes' => ContestType::all()->map->only(['id', 'name'])
                    ->sortByDesc('name', SORT_LOCALE_STRING)->values()->all(),
                'units' => Unit::active()->ofOrganogram(Organogram::active()->first())
                    ->withDepartments(false)
                    ->when(auth()->user()->cannot('contractuals.admin'), fn ($q) => $q->where('id', auth()->user()->unit_id))
                    ->orderBy('order')
                    ->get()
                    ->map->only(['id', 'name'])
                    ->values()
                    ->all(),
            ],
        ]);
    }

    public function store()
    {
        $this->authorize('contractuals.adminUnit');

        request()->validate([
            'type_id' => 'required',
            'protocol_number' => 'required',
            'protocol_date' => 'required',
            'name' => 'required|min:3',
            'ada' => 'required|min:3',
            'start_date' => 'required',
            'end_date' => 'required|after:start_date',
            'organization' => 'required',
            'contract_duration' => 'required',
            'undersigned_title_first_row' => 'required',
            'undersigned_name' => 'required',
            'notifications_email' => 'required|email',
        ]);

        $contest = Contest::create(request()->except('units'));
        $contest->units()->attach(request('units'));

        return response()->json(['message' => 'Ο Διαγωνισμός καταχωρήθηκε επιτυχώς']);
    }

    public function show(Contest $contest)
    {
        $this->authorize('contractuals.read');

        if (! $contest->isLocked()) {
            $contest->load([
                'units:id,name',
                'positions:id,contest_id,unit_id,specialization_id,code,amount,location,has_locality',
                'positions.unit:id,name,abbrv',
                'positions.specialization:id,name',
            ]);
        }

        $latestCalculation = $contest->calculations()->latest('run_at')->first();

        return view('contractuals.contest.show', [
            'contest' => ContestShowPresenter::make($contest)->toArray(),
            'contestHasApplications' => $contest->hasApplications(),
            'contestIsCalculating' => Cache::has('calculating_contest'),
            'latestCalculation' => $latestCalculation ? CalculationPresenter::make($latestCalculation)->toArray() : null,
            'applicationStats' => [
                'totalApplications' => $contest->applications()->count(),
                'totalRatedApplications' => $contest->applications()->rated()->notAutoRated()->count(),
                'totalUnderRatingApplications' => $contest->applications()->notRated()->count(), // ΥΠΟ ΕΛΕΓΧΟ
                'totalDistributedApplications' => $contest->applications()->autoRated()->notRejected()->distributed()->count(),
                'totalNonDistributedApplications' => $contest->applications()->autoRated()->notRejected()->notDistributed()->count(),
                'totalAutoRejectedApplications' => $contest->applications()->autoRated()->rejected()->count(),
            ],
            'unitOptions' => $contest->units
                ->sortBy(fn (Unit $unit) => $unit->abbrv)
                ->values()
                ->map(fn (Unit $unit) => [
                    'id' => $unit->id,
                    'name' => $unit->abbrv,
                ]),
        ]);
    }

    public function edit(Contest $contest)
    {
        return view('contractuals.contest.edit', [
            'contest' => ContestEditPresenter::make(
                $contest->load('units')
            )->toArray(),
            'menuOptions' => [
                'contestTypes' => ContestType::all()->map->only(['id', 'name'])
                    ->sortByDesc('name', SORT_LOCALE_STRING)->values()->all(),
                'units' => Unit::active()
                    ->ofOrganogram(Organogram::orderBy('id', 'desc')->first())
                    ->withDepartments(false)
                    ->when(auth()->user()->cannot('contractuals.admin'), fn ($q) => $q->where('id', auth()->user()->unit_id))
                    ->orderBy('order')
                    ->get()->map->only(['id', 'name'])
                    ->values()
                    ->all(),
            ],
        ]);
    }

    public function update(Contest $contest)
    {
        abort_if(
            $contest->hasApplications(),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            'Η επεξεργασία του διαγωνισμού είναι αδύνατη διότι υπάρχουν '
        );

        request()->validate([
            'type_id' => 'required',
            'protocol_number' => 'required',
            'protocol_date' => 'required',
            'name' => 'required|min:3',
            'ada' => 'required|min:3',
            'start_date' => 'required',
            'end_date' => 'required|after:start_date',
            'organization' => 'required',
            'contract_duration' => 'required',
            'undersigned_title_first_row' => 'required',
            'undersigned_name' => 'required',
            'notifications_email' => 'required|email',
        ]);

        $contest->update(request()->except('units'));
        $contest->units()->sync(request('units'));

        return response()->json(['message' => 'Ο Διαγωνισμός ενημερώθηκε επιτυχώς']);
    }

    public function destroy(Contest $contest)
    {
        abort_if(
            $contest->hasApplications(),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            'Η διαγραφή του Διαγωνισμού είναι αδύνατη διότι έχει ήδη καταχωρημένες Αιτήσεις'
        );

        abort_if(
            $contest->isLocked(),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            'Η διαγραφή του Διαγωνισμού είναι αδύνατη διότι είναι κλειδωμένος'
        );

        DB::connection('mysql_contractuals')->beginTransaction();

        try {
            $contest->units()->detach();

            $contest->positions->each(function ($position) {
                $position->requirements()->detach();
                $position->delete();
            });

            $contest->delete();

            DB::connection('mysql_contractuals')->commit();

            return response()->json(['message' => 'Ο Διαγωνισμός διαγράφηκε επιτυχώς']);
        } catch (\Throwable $e) {
            DB::connection('mysql_contractuals')->rollBack();
            Log::error($e->getMessage());

            return response()->json([
                'message' => 'Παρουσιάστηκε σφάλμα κατά την διαγραφή του διαγωνισμού',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
