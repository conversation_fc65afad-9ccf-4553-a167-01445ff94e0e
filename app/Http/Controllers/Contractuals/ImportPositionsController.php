<?php

namespace App\Http\Controllers\Contractuals;

use App\Http\Controllers\Controller;
use App\Imports\Contractuals\PositionsImport;
use App\Models\Contractuals\Contest;

class ImportPositionsController extends Controller
{
    public function create(Contest $contest)
    {
        return view('contractuals.import-positions.create', ['contest' => $contest]);
    }

    public function store(Contest $contest)
    {
        $this->validate(request(), [
            'csv_import_file' => 'required|file'
        ]);

        $contest->positions()->forceDelete();

        (new PositionsImport($contest))->import(request()->file('csv_import_file'));

        $contest->positions()->each(function($position) {
            if ($position->specialization_id === 4) {
                // ΤΕ ΛΟΓΙΣΤΕΣ
                $position->requirements()->attach([
                    6 => ['auxiliary_level' => 0],
                    7 => ['auxiliary_level' => 0],
                ]);
            } elseif ($position->specialization_id === 1) {
                // ΔΕ ΗΜ ΦΥΛΑΚΕΣ
                $position->requirements()->attach([
                    1 => ['auxiliary_level' => 0],
                    4 => ['auxiliary_level' => 0],
                ]);
                $position->requirements()->attach([
                    2 => ['auxiliary_level' => 1],
                    4 => ['auxiliary_level' => 1],
                ]);
                $position->requirements()->attach([
                    3 => ['auxiliary_level' => 2],
                    4 => ['auxiliary_level' => 2],
                ]);
            } elseif ($position->specialization_id === 2) {
                // ΔΕ ΝΥΧΤΟΦΥΛΑΚΕΣ
                $position->requirements()->attach([
                    1 => ['auxiliary_level' => 0],
                ]);
                $position->requirements()->attach([
                    2 => ['auxiliary_level' => 1],
                ]);
                $position->requirements()->attach([
                    3 => ['auxiliary_level' => 2],
                ]);
            } elseif ($position->specialization_id === 3) {
                $position->requirements()->attach([
                    3 => ['auxiliary_level' => 0],
                ]);
            }
        });

        $unitIds = $contest->positions->pluck('unit_id')->unique()->values();
        $contest->units()->sync($unitIds);

        return redirect()->route('contractuals.contests.show', ['contest' => $contest->id]);
    }

}
