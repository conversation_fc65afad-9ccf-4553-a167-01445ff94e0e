<?php

namespace App\Actions\Assets;

use App\Dtos\Asset\AssetDto;
use App\Models\Assets\Asset;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class CreateAssetAction
{
    /**
     * Create a new asset
     */
    public function handle(AssetDto $dto, User $user): Asset
    {
        return DB::connection('mysql_assets')->transaction(function () use ($dto, $user) {
            $assetData = [
                'contract_id' => $dto->contractId,
                'asset_category_id' => $dto->assetCategoryId,
                'serial_number' => $dto->serialNumber,
                'quantity' => $dto->quantity,
                'date_of_receipt' => $dto->dateOfReceipt,
                'location' => $dto->location,
                'acquisition_cost_in_cents' => $dto->acquisitionCostInCents,
                'user_id' => $user->id,
            ];

            // If unit_id is provided (admin user), use it
            // Otherwise, use the user's unit_id
            if ($dto->unitId) {
                $assetData['unit_id'] = $dto->unitId;
            } else {
                $assetData['unit_id'] = $user->unit_id;
            }

            return Asset::create($assetData);
        });
    }
}
