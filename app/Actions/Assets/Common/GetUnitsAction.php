<?php

namespace App\Actions\Assets\Common;

use App\Models\Unit;

class GetUnitsAction
{
    /**
     * Get active units for dropdown
     */
    public function handle(): array
    {
        return Unit::active()
            ->withDepartments(false)
            ->orderBy('name')
            ->get()
            ->map(function ($unit) {
                return [
                    'id' => $unit->id,
                    'name' => $unit->name,
                ];
            })
            ->toArray();
    }
}
