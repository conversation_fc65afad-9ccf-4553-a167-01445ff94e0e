<?php

namespace App\Actions\Assets;

use App\Dtos\Assets\ContractDto;
use App\Models\Assets\Contract;
use Illuminate\Support\Facades\DB;

class UpdateContractAction
{
    /**
     * Update an existing contract
     */
    public function handle(ContractDto $dto, Contract $contract): Contract
    {
        return DB::connection('mysql_assets')->transaction(function () use ($dto, $contract) {
            $contract->update([
                'contract_number' => $dto->contractNumber,
            ]);

            return $contract->fresh();
        });
    }
}
