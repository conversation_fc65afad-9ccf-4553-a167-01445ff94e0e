<?php

namespace App\Actions\Assets;

use App\Actions\Assets\Common\GetAssetCategoriesAction;
use App\Actions\Assets\Common\GetContractsAction;

class GetFilterOptionsAction
{
    /**
     * Create a new GetFilterOptionsAction instance
     */
    public function __construct(
        private readonly GetAssetCategoriesAction $getAssetCategoriesAction,
        private readonly GetContractsAction $getContractsAction,
    ) {
    }

    /**
     * Get filter options for asset listings
     */
    public function handle(?string $type = null): array
    {
        $filterOptions = [
            'assetCategories' => $this->getAssetCategoriesAction->handle(),
            'contracts' => $this->getContractsAction->handle(),
            'statuses' => $this->getStatuses(),
        ];

        if ($type === 'submitted') {
            $filterOptions['submissionDates'] = $this->getSubmissionDates();
        }

        return $filterOptions;
    }



    /**
     * Get submission dates for filter options.
     */
    private function getSubmissionDates(): array
    {
        // TODO: Implement actual logic to get submission dates
        return [];
    }

    /**
     * Get asset statuses for filter options.
     */
    private function getStatuses(): array
    {
        return [
            ['id' => 'submitted', 'name' => 'Submitted'],
            ['id' => 'unsubmitted', 'name' => 'Unsubmitted'],
        ];
    }
}
