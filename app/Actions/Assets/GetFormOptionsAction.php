<?php

namespace App\Actions\Assets;

use App\Actions\Assets\Common\GetAssetCategoriesAction;
use App\Actions\Assets\Common\GetContractsAction;
use App\Actions\Assets\Common\GetUnitsAction;

class GetFormOptionsAction
{
    /**
     * Create a new GetFormOptionsAction instance
     */
    public function __construct(
        private readonly GetAssetCategoriesAction $getAssetCategoriesAction,
        private readonly GetContractsAction $getContractsAction,
        private readonly GetUnitsAction $getUnitsAction,
    ) {
    }

    /**
     * Get options for asset forms
     */
    public function handle(): array
    {
        return [
            'assetCategories' => $this->getAssetCategoriesAction->handle(),
            'contracts' => $this->getContractsAction->handle(),
            'units' => $this->getUnitsAction->handle(),
        ];
    }
}
