<?php

namespace App\Services\Shared;

use App\Models\Organogram;
use App\Models\Unit;

class UnitTreeMaker
{
    public function replicateWithVersion(Unit $root, array $version, ?int $exceptId = null, ?int $newParentId = null): Organogram
    {
        $newVersion = $this->createNewVersion($version['name'], $version['date']);

        $this->replicateTree($root, $newVersion, null, $exceptId, $newParentId);

        return $newVersion;
    }

    private function createNewVersion(string $name, string $date): Organogram
    {
        Organogram::active()->first()->update(['ended_at' => $date]);

        return Organogram::create([
            'name' => $name,
            'started_at' => $date,
        ]);
    }

    private function replicateTree(Unit $unit, Organogram $organogram, ?int $parentId = null, ?int $excludedUnitId = null, ?int $newParentId = null): void
    {
        if ($unit->id !== $excludedUnitId) {
            $clone = $this->replicateUnit($unit, $parentId);
            $unit->update(['organogram_id' => $organogram->id]);
            /** @var Unit $child */
            foreach ($unit->children as $child) {
                $this->replicateTree($child, $organogram, $clone->id, $excludedUnitId, $newParentId);
            }
        } else {
            // $excludedUnitId and $newParentId are used in case we deleting a unit with version
            $unit->update(['parent_id' => $parentId]);
            if ($newParentId) {
                /** @var Unit $child */
                foreach ($unit->children as $child) {
                    $this->replicateTree($child, $organogram, $unit->id);
                    $child->update(['parent_id' => $newParentId]);
                }
            }
        }
    }

    private function replicateUnit(Unit $unit, ?int $parentId = null): Unit
    {
        $clone = $unit->replicate(['depth', 'path']);
        $clone->reference_id = $unit->id;
        $clone->compass_id = null;
        $clone->parent_id = $parentId;
        $clone->save();

        return $clone;
    }
}
