<?php

namespace App\Services\Shared\MediaLibrary;

use <PERSON><PERSON>\MediaLibrary\MediaCollections\Models\Media;
use Spatie\MediaLibrary\Support\PathGenerator\PathGenerator;

class CustomPathGenerator implements PathGenerator
{
    /*
     * Get the path for the given media, relative to the root storage path.
     */
    public function getPath(Media $media): string
    {
        return $media->collection_name.'/'.$media->id.'/';
    }

    /*
     * Get the path for conversions of the given media, relative to the root storage path.

     * @return string
     */
    public function getPathForConversions(Media $media) : string
    {
        return $media->collection_name . '/' . $media->id . '/conversions/';
    }

    /*
     * Get the path for responsive images of the given media, relative to the root storage path.
     *
     * @return string
     */
    public function getPathForResponsiveImages(Media $media): string{
        return $media->collection_name . '/' . $media->id . '/responsive-images/';
    }
}
