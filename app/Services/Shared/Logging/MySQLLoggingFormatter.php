<?php

namespace App\Services\Shared\Logging;

use Illuminate\Support\Carbon;
use Monolog\Formatter\NormalizerFormatter;
use Monolog\LogRecord;

class MySQLLoggingFormatter extends NormalizerFormatter
{
    public function __construct()
    {
        parent::__construct();
    }

    public function format(LogRecord $record)
    {
        $record = parent::format($record);

        return $this->convertToDatabase($record);
    }

    /**
     * Convert a log message into an appropriate form in order to
     * be stored in the MySQL Database using Log model.
     * @param  array  $record
     *
     * @return mixed
     */
    protected function convertToDataBase(array $record): mixed
    {
        $field = $record['extra'];
        $field['message'] = $record['message'];
        $field['context'] = json_encode($record['context']);
        $field['level'] = strtolower($record['level_name']);
        $field['channel'] = $record['channel'];
        $field['timestamp'] = Carbon::parse($record['datetime']);

        return $field;
    }
}
