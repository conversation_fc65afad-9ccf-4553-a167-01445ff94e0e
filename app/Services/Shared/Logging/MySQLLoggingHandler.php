<?php

namespace App\Services\Shared\Logging;

use App\Models\Log;
use Monolog\Formatter\FormatterInterface;
use Monolog\Handler\AbstractProcessingHandler;
use Monolog\Level;
use Monolog\LogRecord;

class MySQLLoggingHandler extends AbstractProcessingHandler
{
    public function __construct($level = Level::Debug, bool $bubble = true)
    {
        parent::__construct($level, $bubble);
    }

    protected function getDefaultFormatter(): FormatterInterface
    {
        return new MySQLLoggingFormatter;
    }

    protected function write(LogRecord $record): void
    {
        $log = new Log;
        $log->fill($record['formatted']);
        $log->save();
    }
}
