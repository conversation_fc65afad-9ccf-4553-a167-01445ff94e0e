<?php

namespace App\Services\Shared\Logging;

use Monolog\Logger;

class MySQLCustomLogger
{
    public function __invoke(array $config): Logger
    {
        $logger = new Logger('database_channel');
        $handler = new MySQLLoggingHandler($config['level']);
        $processor = new MySQLLogProcessor();
        $logger->pushHandler($handler);
        $logger->pushProcessor($processor);

        return $logger;
    }
}
