<?php

namespace App\Services\Shared\Logging;

use Illuminate\Http\Request;
use Monolog\LogRecord;

/**
 * This class extend the default fields for the log entry with some extra information.
 */
class MySQLLogProcessor
{
    public function __invoke(LogRecord $record): LogRecord
    {
        $record['extra'] = [
            'user_id' => auth()->user() ? auth()->user()->id : null,
            'remote_addr' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->url(),
        ];

        return $record;
    }
}
