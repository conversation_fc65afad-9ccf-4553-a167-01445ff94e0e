<?php

namespace App\Services\Educational;

use App\Models\Educational\Action;
use App\Models\Educational\Collaborator;
use App\Models\Educational\Location;
use App\Models\Educational\Target;
use App\Models\Unit;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ActionFormService
{
    public function create(array $data): Action
    {
        DB::connection('mysql_educational')->beginTransaction();
        $unit = Unit::find($data['unit_id']);
        try {
            $action = Action::create([
                'type_id' => $data['type_id'],
                'unit_id' => $data['unit_id'],
                'region_id' => $unit?->prefecture?->region?->id ?? null,
                'user_id' => auth()->user()?->id ?? null
            ]);

            DB::connection('mysql_educational')->commit();
        } catch (Exception $e) {
            DB::connection('mysql_educational')->rollBack();
            throw $e;
        }

        return $action;
    }

    public function save(array $data, Action $action): Action
    {
        DB::connection('mysql_educational')->beginTransaction();
        try {
            $action->update([
                'period_id' => $data['period_id'],
                'ongoing' => $data['ongoing'],
                'is_digital' => $data['is_digital'],
                'title' => $data['title'],
                'context_id' => $data['context_id'],
                'started_at' => $data['started_at'],
                'ended_at' => $data['ended_at'],
                'duration_id' => $data['duration_id'],
                'frequency' => $data['frequency'],
                'description' => $data['description'],
                'contributors' => $data['contributors'],
                'evaluation' => $data['evaluation'],
                'link' => $data['link'],
                'user_id' => auth()->user()?->id ?? null
            ]);

            $action->involvements()->sync($data['involvements']);
            $action->tools()->sync($data['tools']);
            $action->funds()->sync($data['funds']);
            $action->assessments()->sync($data['assessments']);
            $action->targets()->sync($this->handleFormData($data['targets'], Target::class));
            $action->locations()->sync($this->handleFormData($data['locations'], Location::class));
            $action->collaborators()->sync($this->handleFormData($data['collaborators'], Collaborator::class));
            $action->targetTypes()->sync(
                collect($data['target_types'])->mapWithKeys(function ($amount, $targetTypeId) {
                    return [$targetTypeId => ['amount' => $amount]];
                })->toArray()
            );
            $action->disseminations()->sync($data['disseminations']);

            // IMPORTANT NOTE
            // Attachments are handled by UploadXhrField in the fron-end

            DB::connection('mysql_educational')->commit();
        } catch (Exception $e) {
            DB::connection('mysql_educational')->rollBack();
            throw $e;
        }

        return $action;
    }

    public function destroy(Action $action): void
    {
        // FIXME when I delete an action, I should also delete all related data (disseminations, etc.)
        try {
            DB::connection('mysql_educational')->beginTransaction();
            $action->delete();
            try {
                $attachmentIds = $action->getMedia('actions')->pluck('id');
                foreach ($attachmentIds as $id) {
                    $action->deleteMedia($id);
                }
            } catch (Exception $e) {
                throw $e;
            }
            DB::connection('mysql_educational')->commit();
        } catch (Exception $e) {
            DB::connection('mysql_educational')->rollBack();
            throw $e;
        }
    }

    /**
     * Handle form data that derived from multiple select checkboxes.
     *
     * We give users the ability to create new items by typing in the input box of TagFiled.vue component
     * The newly created items must be added to database before being associated with an action. This
     * applies both to locations and collaborators
     *
     * The received form data (in case of `locations`, `targets`, `collaborators`) have the following structure:
     * $formData = [modelTypeId1 => [modelId1, modelId2, ...], modelTypeId2 [modelId3, modelId4, ...], ...]
     * modelIdX can be either the id of an already existing model (in case of MultipleselectFields),
     * or the name of a model that does not yet exists in the database (in case of TagFields).
     * If the modelId is actually a name we first create the model in the database and
     * then return its id. The resulting array of ids is finally flatten.
     *
     * @param  array|null  $data
     * @param  string  $className
     *
     * @return Collection
     */
    protected function handleFormData(?array $data, string $className): Collection
    {
        return collect($data)->map(function ($models, $modelTypeId) use ($className) {
            return collect($models)->map(function ($model) use ($modelTypeId, $className) {
                if ($this->isNewModel($model)) {
                    return $this->createNewModel($model, $modelTypeId, $className)->id;
                } else {
                    return $model;
                }
            });
        })->flatten();
    }

    protected function isNewModel($model): bool
    {
        return ! is_numeric($model);
    }

    protected function createNewModel(string $name, int $type, string $className): Model
    {
        return $className::create([
            'name' => $name,
            strtolower(class_basename($className)).'_type_id' => $type,
        ]);
    }
}
