<?php

namespace App\Services\MediaLibrary;

use Illuminate\Support\Collection;
use Spatie\MediaLibrary\Support\MediaStream as SpatieMediaStream;

class MediaStream extends SpatieMediaStream
{
    protected function getFileNameWithSuffix(Collection $mediaItems, int $currentIndex): string
    {
        $fileNameCount = 0;

        $fileName = $mediaItems[$currentIndex]->file_name;
        $trueName = $mediaItems[$currentIndex]->name;

        foreach ($mediaItems as $index => $media) {
            if ($index >= $currentIndex) {
                break;
            }

            if ($this->getZipFileNamePrefix($mediaItems, $index).$media->name === $this->getZipFileNamePrefix($mediaItems, $currentIndex).$trueName) {
                $fileNameCount++;
            }
        }

        $extension = pathinfo($fileName, PATHINFO_EXTENSION);

        if ($fileNameCount === 0) {
            return "{$trueName}.{$extension}";;
        }

        return "{$trueName} ({$fileNameCount}).{$extension}";
    }
}
