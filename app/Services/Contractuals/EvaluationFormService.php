<?php

namespace App\Services\Contractuals;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Models\Contractuals\Evaluation;

class EvaluationFormService
{
    public function update($evaluations, $application)
    {
        if (!empty($evaluations) && $application->meetsGlobalRequirements()) {
            try {
                DB::connection('mysql_contractuals')->beginTransaction();

                collect($evaluations)->each(function ($evaluation) {
                    Evaluation::find($evaluation['id'])->update(Arr::except($evaluation, ['id','locality']));
                });

                foreach ($application->positions()->get() as $position) {
                    // TODO: maybe it is better to pass selected auxiliary_level per position as a request parameter by updating EvaluationForm
                    $selectedAuxiliaryLevel = collect($evaluations)->filter(function ($evaluation) use ($position) {
                        return $evaluation['position_id'] === $position->id;
                    })->unique('auxiliary_level')->first()['auxiliary_level'];
                    $application->updateAuxiliaryLevel($selectedAuxiliaryLevel, $position);

                    if ($position->has_locality) {
                        $evaluation = collect($evaluations)->filter(function ($evaluation) use ($position) {
                            return $evaluation['position_id'] === $position->id;
                        })->first();
                        $selectedLocality = array_key_exists('locality', $evaluation) && $evaluation['locality'];

                        $application->updateLocality($selectedLocality, $position);
                    }
                }

                DB::connection('mysql_contractuals')->commit();
            } catch (\Exception $e) {
                DB::connection('mysql_contractuals')->rollBack();

                throw new \Exception($e);
            }
        }

        return Evaluation::find(collect($evaluations)->pluck('id'));
    }
}
