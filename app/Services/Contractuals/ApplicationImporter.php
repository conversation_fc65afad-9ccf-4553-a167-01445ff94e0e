<?php

namespace App\Services\Contractuals;

use App\Exceptions\Contractuals\ApplicationCouldNotBeImportedException;
use App\Models\Contractuals\Applicant;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Evaluation;
use App\Models\Contractuals\Position;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ApplicationImporter
{
    public const QUALIFIABLE_TABLES = [
        'computer_skills',
        'language_skills',
        'unemployments',
        'multi_child_families',
        'three_child_families',
        'single_parent_families',
        'minors',
        'degrees',
        'doctorates',
        'postgraduates',
        'experiences',
        'disabilities',
        'family_disabilities',
    ];

    public function run($publicApplication): Application
    {
        DB::connection('mysql_contractuals')->beginTransaction();
        try {
            // Create new applicant
            //            $applicant = Applicant::updateOrCreate(
            //                [ 'afm' => $publicApplication->afm ],
            //                [
            //                    'name' => $publicApplication->first_name,
            //                    'surname' => $publicApplication->last_name,
            //                    'fathername' => $publicApplication->father_name,
            //                    'mothername' => $publicApplication->mother_name,
            //                    'policeid_number' => $publicApplication->adt,
            //                    'amka' => $publicApplication->amka,
            //                    'birthdate' => $publicApplication->birthdate,
            //                    'street' => $publicApplication->address,
            //                    'postcode' => $publicApplication->postcode,
            //                    'city' => $publicApplication->city,
            //                    'phonenumber1' => $publicApplication->telephone,
            //                    'phonenumber2' => $publicApplication->mobile,
            //                    'email' => $publicApplication->email,
            //                    'greek_nationality' => true,
            //                ]
            //            );
            // Create new application
            $application = Application::updateOrCreate(
                ['public_application_id' => $publicApplication->id],
                [
                    'applicant_id' => null,
                    'contest_id' => $publicApplication->contest_id,
                    'name' => $publicApplication->first_name,
                    'surname' => $publicApplication->last_name,
                    'fathername' => $publicApplication->father_name,
                    'mothername' => $publicApplication->mother_name,
                    'birthdate' => $publicApplication->birthdate,
                    'greek_nationality' => true,
                    // 'eu_citizen' => null, // default(1)
                    'policeid_number' => $publicApplication->adt,
                    // 'policeid_date' => null,
                    'afm' => $publicApplication->afm,
                    // 'doy' => null,
                    'amka' => $publicApplication->amka,
                    'street' => $publicApplication->address,
                    // 'street_number' => null,
                    'postcode' => $publicApplication->postcode,
                    'city' => $publicApplication->city,
                    'phonenumber1' => $publicApplication->telephone,
                    'phonenumber2' => $publicApplication->mobile,
                    'email' => $publicApplication->email,
                    'applicant_category' => $publicApplication->specialization_type_id,
                    'meets_general_requirements' => $publicApplication->meets_general_requirements,
                    'auxiliary_level' => isset($publicApplication->auxiliary_level) ? $publicApplication->auxiliary_level : 0,
                    'has_eight_months_employment' => $publicApplication->has_eight_months_employment ?? false,
                    'impediment_eight_months' => false,
                    'submitted_at' => $publicApplication->submitted_at,
                    'protocol_number' => $publicApplication->protocol_number,
                    'protocol_date' => $publicApplication->submitted_at,
                    'public_application_id' => $publicApplication->id,
                    'invalidated' => false,
                    'invalidation_description' => null,
                    'invalidation_description_id' => null,
                    'rated_by' => null,
                    'rated_by_unit_id' => null,
                    'rejected' => null,
                ]
            );

            // Clear qualifications, evaluations, application_positions
            $application->qualifications()->each(function ($qualification) {
                $qualification->qualifiable->forceDelete();
            });
            $application->qualifications()->forceDelete();
            //            $application->evaluations()->forceDelete();
            $application->positions()->sync([]);
            //            $application->ratings()->delete();

            // Create qualifiables & qualifications
            $publicQualifications = DB::connection('mysql_public_contractuals')
                ->table('qualifications')
                ->where('application_id', $publicApplication->id)
                ->get();

            foreach (static::QUALIFIABLE_TABLES as $qualifiableTable) {
                if ($qualifiableTable == 'minors') {
                    $publicQualifiableTable = 'minor_children';
                } else {
                    $publicQualifiableTable = $qualifiableTable;
                }

                DB::connection('mysql_public_contractuals')
                    ->table($publicQualifiableTable)
                    ->whereIn('id', $publicQualifications->where('qualifiable_type', 'App\Models\\'.Str::singular(Str::studly($publicQualifiableTable)))->pluck('qualifiable_id'))
                    ->orderBy('id')
                    ->each(function ($publicQualifiable) use ($application, $qualifiableTable) {
                        // Fix data
                        $data = collect($publicQualifiable)->put('applicant_id', null)->except('id');
                        if ($data->has('title')) {
                            $data->put('name', $data->get('title'));
                            $data = $data->except('title');
                        }

                        // Skip qualifications with empty values which are auto created.
                        //                        if (
                        //                            ! ($data->has('months') && (is_null($data->get('months')) || $data->get('months') == 0)) &&
                        //                            ! ($data->has('amount') && (is_null($data->get('amount')) || $data->get('amount') == 0)) &&
                        //                            ! ($data->has('is_eligible') && $data->get('is_eligible') == false)
                        //                        ) {

                        // Fix null values
                        if ($data->has('months') && is_null($data->get('months'))) {
                            $data = $data->merge(['months' => 0]);
                        }
                        if ($data->has('amount') && is_null($data->get('amount'))) {
                            $data = $data->merge(['amount' => 0]);
                        }
                        if ($data->has('is_eligible') && is_null($data->get('is_eligible'))) {
                            $data = $data->merge(['is_eligible' => false]);
                        }

                        $id = DB::connection('mysql_contractuals')
                            ->table($qualifiableTable)
                            ->insertGetId($data->toArray());

                        DB::connection('mysql_contractuals')
                            ->table('qualifications')
                            ->insert([
                                'application_id' => $application->id,
                                'qualifiable_id' => $id,
                                'qualifiable_type' => 'App\Models\Contractuals\\'.Str::singular(Str::studly($qualifiableTable)),
                            ]);
                    });
            }

            // Create application_positions & evaluations
            DB::connection('mysql_public_contractuals')->table('application_position')
                ->where('application_id', $publicApplication->id)
                ->orderBy('order')
                ->each(function ($position) use ($application) {
                    // Create application_positions with auxiliary_level and locality only where applicable.
                    $hasLocality = Position::find($position->position_id)->has_locality;
                    DB::connection('mysql_contractuals')->table('application_position')->insert([
                        'application_id' => $application->id,
                        'position_id' => $position->position_id,
                        'order' => $position->order,
                        'auxiliary_level' => $position->auxiliary_level,
                        'locality' => $hasLocality ? $position->locality : false,
                    ]);
                    // Create Evaluations
                    /*
                    $requiredQualificationIds = [];
                    // Required
                    $requirements = Position::find($position->position_id)->requirements()
                        ->where('auxiliary_level', $position->auxiliary_level)
                        ->with('requirementType')
                        ->each(function ($requirement) use ($application, $position, &$requiredQualificationIds) {
                            $qualifiableType = 'App\Models\Contractuals\\'.Str::singular(Str::studly($requirement->requirementType->qualifiable_type));
                            if ($qualifiableType === "App\Models\Contractuals\Degree") {
                                $qualifications = $application->qualifications()->where('qualifiable_type', $qualifiableType)->get();
                                $primaryDegree = $qualifications->filter(function ($i) {
                                    return $i->qualifiable->is_primary === 1;
                                })->first();
                                if (is_null($primaryDegree)) {
                                    $qualification = $qualifications->first();
                                } else {
                                    $qualification = $primaryDegree;
                                }
                            } else {
                                $qualification = $application->qualifications()->where('qualifiable_type', $qualifiableType)->first();
                            }
                            if ($qualification) {
                                array_push($requiredQualificationIds, $qualification->id);
                                Evaluation::create([
                                    'application_id' => $application->id,
                                    'qualification_id' => $qualification->id,
                                    'position_id' => $position->position_id,
                                    'requirement_id' => $requirement->id,
                                    'auxiliary_level' => $position->auxiliary_level,
                                ]);
                            }
                        });
                    // Non-required
                    DB::connection('mysql_contractuals')
                        ->table('qualifications')
                        ->where('application_id', $application->id)
                        ->whereNotIn('id', $requiredQualificationIds)
                        ->selectRaw('application_id, id as qualification_id, ? as position_id, ? as auxiliary_level, ? as relevant', [$position->position_id, $position->auxiliary_level, 1])
                        ->orderBy('id')
                        ->each(function ($eval) use ($application) {
                            Evaluation::create(collect($eval)->toArray());
                        });
                    */
                });
        } catch (\Throwable $e) {
            DB::connection('mysql_contractuals')->rollBack();

            Log::error($e->getMessage(), ['app' => 'contractuals']);

            throw new ApplicationCouldNotBeImportedException($e->getMessage());
        }

        DB::connection('mysql_contractuals')->commit();

        return $application;
    }
}
