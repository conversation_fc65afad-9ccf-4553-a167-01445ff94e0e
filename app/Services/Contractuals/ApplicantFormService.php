<?php

namespace App\Services\Contractuals;

use DB;
use App\Models\Contractuals\Applicant;

class ApplicantFormService
{
    const APPLICANT_FIELDS_EXCLUDING_ASSETS = [
        'name',
        'surname',
        'fathername',
        'mothername',
        'policeid_number',
        'policeid_date',
        'afm',
        'doy',
        'amka',
        'birthdate',
        'street',
        'street_number',
        'postcode',
        'city',
        'phonenumber1',
        'phonenumber2',
        'email',
        'eu_citizen',
        'greek_nationality',
    ];

    /**
     * @param $applicantFormData
     * @throws \Exception
     * @return array
     */
    public function store($applicantFormData)
    {
        try {
            DB::connection('mysql_contractuals')->beginTransaction();

            $applicant = Applicant::create(collect($applicantFormData)->only(self::APPLICANT_FIELDS_EXCLUDING_ASSETS)->toArray());

            //FIXME: Refactor
            $degrees = $applicant->addAssets('degrees', $applicantFormData['degrees']);
            $postgraduates = $applicant->addAssets('postgraduates', $applicantFormData['postgraduates']);
            $doctorates = $applicant->addAssets('doctorates', $applicantFormData['doctorates']);
            $experiences = $applicant->addAssets('experiences', $applicantFormData['experiences']);
            $languageSkills = $applicant->addAssets('languageSkills', $applicantFormData['language_skills']);
            $computerSkills = $applicant->addAssets('computerSkills', $applicantFormData['computer_skills']);
            $unemployments = $applicant->addAssets('unemployments', $applicantFormData['unemployments']);
            $multiChildFamilies = $applicant->addAssets('multiChildFamilies', $applicantFormData['multi_child_families']);
            $threeChildFamilies = $applicant->addAssets('threeChildFamilies', $applicantFormData['three_child_families']);
            $singleParentFamilies = $applicant->addAssets('singleParentFamilies', $applicantFormData['single_parent_families']);
            $disabilities = $applicant->addAssets('disabilities', $applicantFormData['disabilities']);
            $familyDisabilities = $applicant->addAssets('familyDisabilities', $applicantFormData['family_disabilities']);
            $minors = $applicant->addAssets('minors', $applicantFormData['minors']);
            $greekLanguages = $applicant->addAssets('greekLanguages', $applicantFormData['greek_languages']);

            DB::connection('mysql_contractuals')->commit();

            return [
                'applicant' => $applicant->load([
                    'applications',
                    'computerSkills',
                    'degrees',
                    'doctorates',
                    'experiences',
                    'greekLanguages',
                    'languageSkills',
                    'minors',
                    'multiChildFamilies',
                    'threeChildFamilies',
                    'singleParentFamilies',
                    'disabilities',
                    'familyDisabilities',
                    'postgraduates',
                    'unemployments',
                    'verifications',
                ]),
                'assets'    => [
                    'computerSkills'       => $computerSkills,
                    'degrees'              => $degrees,
                    'doctorates'           => $doctorates,
                    'experiences'          => $experiences,
                    'greekLanguages'       => $greekLanguages,
                    'languageSkills'       => $languageSkills,
                    'minors'               => $minors,
                    'multiChildFamilies'   => $multiChildFamilies,
                    'postgraduates'        => $postgraduates,
                    'unemployments'        => $unemployments,
                    'threeChildFamilies'   => $threeChildFamilies,
                    'singleParentFamilies' => $singleParentFamilies,
                    'disabilities'         => $disabilities,
                    'familyDisabilities'   => $familyDisabilities,
                ],
            ];
        } catch (\Exception $e) {
            DB::connection('mysql_contractuals')->rollBack();

            throw $e; //TODO: create a custom exception
        }
    }

    public function update(Applicant $applicant, $applicantFormData)
    {
        try {
            DB::connection('mysql_contractuals')->beginTransaction();

            $applicant->update(collect($applicantFormData)->only(self::APPLICANT_FIELDS_EXCLUDING_ASSETS)->toArray());

            //FIXME: Refactor
            $degrees = $applicant->updateAssets('degrees', $applicantFormData['degrees']);
            $postgraduates = $applicant->updateAssets('postgraduates', $applicantFormData['postgraduates']);
            $doctorates = $applicant->updateAssets('doctorates', $applicantFormData['doctorates']);
            $experiences = $applicant->updateAssets('experiences', $applicantFormData['experiences']);
            $languageSkills = $applicant->updateAssets('languageSkills', $applicantFormData['language_skills']);
            $computerSkills = $applicant->updateAssets('computerSkills', $applicantFormData['computer_skills']);
            $unemployments = $applicant->updateAssets('unemployments', $applicantFormData['unemployments']);
            $multiChildFamilies = $applicant->updateAssets('multiChildFamilies', $applicantFormData['multi_child_families']);
            $threeChildFamilies = $applicant->updateAssets('threeChildFamilies', $applicantFormData['three_child_families']);
            $singleParentFamilies = $applicant->updateAssets('singleParentFamilies', $applicantFormData['single_parent_families']);
            $disabilities = $applicant->updateAssets('disabilities', $applicantFormData['disabilities']);
            $familyDisabilities = $applicant->updateAssets('familyDisabilities', $applicantFormData['family_disabilities']);
            $minors = $applicant->updateAssets('minors', $applicantFormData['minors']);
            $greekLanguages = $applicant->updateAssets('greekLanguages', $applicantFormData['greek_languages']);

            DB::connection('mysql_contractuals')->commit();

            return [
                'applicant' => $applicant->load([
                    'greekLanguages',
                    'degrees',
                    'postgraduates',
                    'doctorates',
                    'experiences',
                    'languageSkills',
                    'computerSkills',
                    'unemployments',
                    'multiChildFamilies',
                    'threeChildFamilies',
                    'singleParentFamilies',
                    'disabilities',
                    'familyDisabilities',
                    'minors',
                    'verifications',
                    'applications',
                ]),
                'assets'    => [
                    'degrees'              => $degrees,
                    'postgraduates'        => $postgraduates,
                    'doctorates'           => $doctorates,
                    'experiences'          => $experiences,
                    'languageSkills'       => $languageSkills,
                    'computerSkills'       => $computerSkills,
                    'unemployments'        => $unemployments,
                    'multiChildFamilies'   => $multiChildFamilies,
                    'minors'               => $minors,
                    'greekLanguages'       => $greekLanguages,
                    'threeChildFamilies'   => $threeChildFamilies,
                    'singleParentFamilies' => $singleParentFamilies,
                    'disabilities'         => $disabilities,
                    'familyDisabilities'   => $familyDisabilities,
                ],
            ];
        } catch (\Exception $e) {
            DB::connection('mysql_contractuals')->rollBack();

            throw $e;
        }
    }
}
