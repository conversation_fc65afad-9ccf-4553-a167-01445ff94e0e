<?php

namespace App\Services\Contractuals\EmploymentManagers;

use App\Exceptions\Contractuals\EmploymentManagerHasFailedException;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Ranking;
use App\Models\Contractuals\Specialization;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BasicEmploymentManager implements EmploymentManager
{
    protected $positionRemainingCapacities;

    protected $positionTotalCapacities;

    protected $employablesList;

    protected $positionEmployables;

    protected $iterations;

    protected $skippedApplicationIds;

    protected $runnerUpApplicationIds;

    protected $declinedApplicationIds;

    protected $hireRunnersUp;

    public function __construct()
    {
        $this->positionRemainingCapacities = collect([]); // [position_id => position_remaining_amount]
        $this->positionTotalCapacities = collect([]); // [position_id => position_amount]
        $this->employablesList = collect([]); // [application_id => position_id]
        $this->positionEmployables = collect([]); // [position_id => total_employed]
        $this->skippedApplicationIds = collect([]);
        $this->runnerUpApplicationIds = collect([]);
        $this->declinedApplicationIds = collect([]);
        $this->hireRunnersUp = false;
    }

    // We pass the position capacity as a variable ($n) because we are using it in the Distributor.
    public function employ(Contest $contest, Calculation $calculation, int $n = 1): void
    {
        try {
            DB::connection('mysql_contractuals')->beginTransaction();

            $this->hireRunnersUp = $calculation->hire_runners_up;

            if ($this->hireRunnersUp) {
                $previousCalculation = Calculation::ofContest($contest->id)
                    ->where('distribution_run', false)
                    ->where('id', '!=', $calculation->id)
                    ->orderByDesc('id')
                    ->first();
                $previousNonRunnerUpCalculation = Calculation::ofContest($contest->id)
                    ->where('distribution_run', false)
                    ->where('hire_runners_up', false)
                    ->where('id', '!=', $calculation->id)
                    ->orderByDesc('id')
                    ->first();

                // Iterate applications who accepted or rejected employment.
                Application::ofContest($contest->id)
                    ->whereNotNull('employed_in')
                    ->each(function ($application) use ($calculation, $previousCalculation) {
                        $rejectedPreviousEmployment = Ranking::ofCalculation($calculation->id)
                            ->ofApplication($application->id)
                            ->ofPosition($application->employed_in) // the ranking of the position he was employed in
                            ->where('rejected', 1) // validate new calculation has NOT rejected the application
                            ->exists();
                        if (! $rejectedPreviousEmployment) {
                            // Only handle those that were previously declined or those that were employed and now not rejected.
                            // Skip those that are now rejected in the position that they were employed!
                            if ($application->accepted == 1) {
                                // Has accepted position
                                $employablePositionId = $application->employed_in;
                                $employedPositionId = $application->employed_in;
                            } else {
                                // Has declined position
                                $employableRanking = Ranking::ofApplication($application->id)
                                    ->ofCalculation($previousCalculation->id)
                                    ->where('employable', 1)
                                    ->first();
                                $employablePositionId = ! is_null($employableRanking) ? $employableRanking->position_id : null;
                                // Value 0 would only occur if a user set employable_in to NO for an application that had no employable position. ??
                                $employedPositionId = 0;
                            }
                            Ranking::ofCalculation($calculation->id)->ofApplication($application->id)->update([
                                'employable_in' => $employablePositionId,
                                'employed_in' => $employedPositionId,
                                'accepted' => $application->accepted,
                            ]);
                            Ranking::ofCalculation($calculation->id)->ofApplication($application->id)->ofPosition($employablePositionId)->update([
                                'employable' => 1,
                            ]);
                        } else {
                            if ($application->employed_in != 0 && ! is_null($application->employed_in)) {
                                Application::where('id', $application->id)->update([
                                    'employed_in' => null,
                                    'accepted' => null,
                                ]);
                            }
                        }
                    });

                $this->skippedApplicationIds = Application::ofContest($contest->id)->whereNotNull('employed_in')->pluck('id');
                $this->runnerUpApplicationIds = Ranking::ofCalculation($previousNonRunnerUpCalculation->id)
                    ->succeeded()
                    ->whereNull('employable_in')
                    ->select('application_id')
                    ->distinct()
                    ->pluck('application_id');
            } else {
                Application::ofContest($contest->id)->employed()
                    ->update([
                        'employed_in' => null,
                        'accepted' => null,
                    ]);
                $this->declinedApplicationIds = Application::ofContest($contest->id)
                    ->where('accepted', 0)
                    ->pluck('id');
            }

            $contest->positions->each(function ($position) use ($n) {
                if ($this->hireRunnersUp) {
                    $skipped = Application::ofContest($position->contest_id)->where('employed_in', $position->id)->count();
                    $this->positionRemainingCapacities[$position->id] = $position->amount - $skipped;
                    $this->positionTotalCapacities[$position->id] = $position->amount - $skipped;
                    $this->positionEmployables[$position->id] = 0;
                } else {
                    $this->positionRemainingCapacities[$position->id] = $n * $position->amount;
                    $this->positionTotalCapacities[$position->id] = $n * $position->amount;
                    $this->positionEmployables[$position->id] = 0;
                }
            });

            $specializationTypeIds = Ranking::ofCalculation($calculation->id)
                ->distinct('specialization_type_id')
                ->pluck('specialization_type_id')
                ->sort()
                ->values();

            $specializationTypeIds->each(function ($specializationTypeId) use ($calculation) {
                // Reset here, so that it contains only positions of this specializationType
                $this->iterations = 0;
                $positionIds = Ranking::ofCalculation($calculation->id)
                    ->where('specialization_type_id', $specializationTypeId)
                    ->distinct('position_id')
                    ->pluck('position_id');

                // First iteration
                $this->updateEmployableStatus($calculation, $specializationTypeId);

                // Iterate if we have overfilled position capacities
                do {
                    $overfilledScores = collect([]);
                    $positionIds->each(function ($positionId) use ($calculation, $overfilledScores) {
                        if ($this->positionEmployables[$positionId] > $this->positionTotalCapacities[$positionId]) {
                            $r = Ranking::ofCalculation($calculation->id)
                                ->whereNotIn('application_id', $this->skippedApplicationIds)
                                ->ofPosition($positionId)
                                ->where('employable', true)
                                ->orderBy('combined_score')
                                ->offset($this->positionTotalCapacities[$positionId])
                                ->limit(1)
                                ->first();

                            $overfilledScores->push($r->combined_score);
                        }
                    });

                    if ($overfilledScores->count() > 0) {
                        // Now reset those applications and re-calculate position capacities
                        $minAffectedScore = $overfilledScores->min();
                        $affectedPositionIds = Ranking::ofCalculation($calculation->id)
                            ->whereNotIn('application_id', $this->skippedApplicationIds)
                            ->where('specialization_type_id', $specializationTypeId)
                            ->where('combined_score', '>=', $minAffectedScore)
                            ->distinct('position_id')
                            ->pluck('position_id');
                        // Reset overfilled rankings
                        $affectedApplicationIds = Ranking::ofCalculation($calculation->id)
                            ->whereNotIn('application_id', $this->skippedApplicationIds)
                            ->where('specialization_type_id', $specializationTypeId)
                            ->where('combined_score', '>=', $minAffectedScore)
                            ->distinct('application_id')
                            ->pluck('application_id');
                        // Find rankings for applicants already employed with a better combined score (due to locality) in another position
                        $employedWithOtherScoreApplicationIds = Ranking::ofCalculation($calculation->id)
                            ->whereNotIn('application_id', $this->skippedApplicationIds)
                            ->where('specialization_type_id', $specializationTypeId)
                            ->where('combined_score', '<', $minAffectedScore)
                            ->where('employable', true)
                            ->distinct('application_id')
                            ->pluck('application_id');
                        $this->employablesList->forget($affectedApplicationIds->diff($employedWithOtherScoreApplicationIds)->toArray());
                        Ranking::ofCalculation($calculation->id)
                            ->whereNotIn('application_id', $this->skippedApplicationIds)
                            ->where('specialization_type_id', $specializationTypeId)
                            ->where('combined_score', '>=', $minAffectedScore)
                            ->update([
                                'employable' => null,
                            ]);
                        // Recalculate position employed and remaining position capacities
                        $affectedPositionIds->each(function ($positionId) use ($calculation) {
                            $employedCount = Ranking::ofCalculation($calculation->id)
                                ->whereNotIn('application_id', $this->skippedApplicationIds)
                                ->ofPosition($positionId)
                                ->where('employable', true)
                                ->count();
                            $this->positionEmployables[$positionId] = $employedCount;
                            $this->positionRemainingCapacities[$positionId] = $this->positionTotalCapacities[$positionId] - $employedCount;
                        });
                        // Iterate
                        $this->updateEmployableStatus($calculation, $specializationTypeId, $minAffectedScore);
                    }
                } while ($overfilledScores->count() > 0);
            });

            // Set employable_in of all applications not rejected (exist in employablesList)
            $this->employablesList->each(function ($positionId, $applicationId) use ($calculation) {
                DB::connection('mysql_contractuals')->table('rankings')
                    ->where('calculation_id', $calculation->id)
                    ->where('application_id', $applicationId)
                    ->update(['employable_in' => $positionId]);
            });

            // Set was_runner_up flag on all rankings
            if ($this->hireRunnersUp) {
                Ranking::ofCalculation($calculation->id)
                    ->whereNotNull('employable_in')
                    ->whereIn('application_id', $this->runnerUpApplicationIds)
                    ->update(['was_runner_up' => true]);
            } else {
                // Set to declined to those that have previously declined
                Ranking::ofCalculation($calculation->id)
                    ->whereIn('application_id', $this->declinedApplicationIds)
                    ->update([
                        'employed_in' => 0,
                        'accepted' => 0,
                    ]);
                // Set employed_in for those that didn't change
                $previousCalculation = Calculation::ofContest($contest->id)
                    ->where('distribution_run', false)
                    ->where('id', '!=', $calculation->id)
                    ->orderByDesc('id')
                    ->first();
                if ($previousCalculation) {
                    $rankings = Ranking::ofCalculation($calculation->id)
                        ->where('employable', 1)
                        ->where('rejected', 0)
                        ->whereNotNull('employable_in');
                    foreach ($rankings->lazy() as $ranking) {
                        $employmentNotChanged = Ranking::ofCalculation($previousCalculation->id)
                            ->ofApplication($ranking->application_id)
                            ->ofPosition($ranking->position_id)
                            ->where('employable', 1)
                            ->where('employed_in', $ranking->position_id)
                            ->exists();
                        if ($employmentNotChanged) {
                            DB::connection('mysql_contractuals')->table('rankings')
                                ->where('id', $ranking->id)
                                ->update([
                                    'employed_in' => $ranking->position_id,
                                    'accepted' => 1,
                                ]);
                            DB::connection('mysql_contractuals')->table('applications')
                                ->where('id', $ranking->application_id)
                                ->update([
                                    'employed_in' => $ranking->position_id,
                                    'accepted' => 1,
                                ]);
                        }
                    }
                }
            }
            DB::connection('mysql_contractuals')->commit();
        } catch (\Throwable $e) {
            DB::connection('mysql_contractuals')->rollBack();
            Log::error($e->getMessage());
            throw new EmploymentManagerHasFailedException($e->getMessage());
        }

        // TODO - consider to return the updated rankings
    }

    protected function updateEmployableStatus($calculation, $specializationTypeId, $combinedScore = null)
    {
        $rankings = Ranking::ofCalculation($calculation->id)
            ->whereNotIn('application_id', $this->skippedApplicationIds)
            ->where('specialization_type_id', $specializationTypeId)
            ->orderBy('combined_score')
            ->orderBy('position_order');
        if (! is_null($combinedScore)) {
            $rankings = $rankings->where('combined_score', '>=', $combinedScore);
        }
        foreach ($rankings->lazy() as $ranking) {
            $employable = $this->employableStatusCalculator($ranking);
            DB::connection('mysql_contractuals')->table('rankings')
                ->where('id', $ranking->id)
                ->update([
                    'employable'    => $employable,
                ]);
        }
    }

    protected function employableStatusCalculator($ranking)
    {
        if ($this->positionHasCapacity($ranking->position_id) && $this->notRejected($ranking)) {
            if ($this->declinedApplicationIds->contains($ranking->application_id)) {
                // Would be hired but has declined. Just set employable true without updating lists.

                return true;
            } elseif (! $this->existsInEmployablesList($ranking->application_id)) {
                // Not in employablesList. Coming here means that it's the 1st choice having available capacity.

                $this->addToEmployablesList($ranking->application_id, $ranking->position_id);
                $this->incrementPositionEmployables($ranking->position_id);

                if ($ranking->locality === 0 || ($ranking->locality === 1 && $this->rankingIsTopChoice($ranking))) {
                    // Only decrease position capacity if:
                    // - application has no locality
                    // - application has locality and is the top choice of applicant (cannot be moved)
                    $this->decrementPositionCapacity($ranking->position_id);
                }
                // This means that when adding to employables,
                // an application that has locality but is not the top choice
                // then the position will be overfilled.
                // We set them as employable at this position and then we'll iterate overfilled positions to remove
                // applications that can't be used.

                return true;
            } else {
                // Already hired. Find ranking that hired him
                $employedInRanking = Ranking::ofCalculation($ranking->calculation_id)
                    ->ofApplication($ranking->application_id)
                    ->ofPosition($this->employablesList[$ranking->application_id])
                    ->first();

                // Check if he was hired through locality on lower preference (position_order)
                if ($employedInRanking->position_order > $ranking->position_order) {
                    // Hired on lower preference, but since we're here,
                    // there is vacancy and can also be hired in better preference.

                    // Swap old hired position with new one
                    $this->addToEmployablesList($ranking->application_id, $ranking->position_id);
                    $this->incrementPositionEmployables($ranking->position_id);
                    $this->decrementPositionCapacity($ranking->position_id);

                    // Undo old ranking
                    $this->decrementPositionEmployables($employedInRanking->position_id);
                    DB::connection('mysql_contractuals')->table('rankings')
                        ->where('id', $employedInRanking->id)
                        ->update([
                            'employable'    => null,
                        ]);

                    return true;
                }
            } // elseif (already hired on better preference) -> do nothing
        }

        return null;
    }

//    protected function employableInStatusCalculator($ranking)
//    {
//        if ($this->existsInEmployablesList($ranking['application_id'])) {
//            return $this->employablesList[$ranking['application_id']];
//        }
//
//        return null;
//    }

    protected function existsInEmployablesList($applicationId): bool
    {
        return $this->employablesList->has($applicationId);
    }

    protected function addToEmployablesList($applicationId, $positionId): void
    {
        $this->employablesList->put($applicationId, $positionId);
    }

    protected function positionHasCapacity($positionId): bool
    {
        return $this->positionRemainingCapacities[$positionId] > 0;
    }

//    protected function incrementPositionCapacity($positionId): void
//    {
//        $this->positionRemainingCapacities[$positionId] = $this->positionRemainingCapacities[$positionId] + 1;
//    }

    protected function decrementPositionCapacity($positionId): void
    {
        $this->positionRemainingCapacities[$positionId] = $this->positionRemainingCapacities[$positionId] - 1;
    }

    protected function incrementPositionEmployables($positionId): void
    {
        $this->positionEmployables[$positionId] = $this->positionEmployables[$positionId] + 1;
    }

    protected function decrementPositionEmployables($positionId): void
    {
        $this->positionEmployables[$positionId] = $this->positionEmployables[$positionId] - 1;
    }

    protected function notRejected($ranking): bool
    {
        return 1 !== $ranking->rejected;
    }

    protected function rankingIsTopChoice($ranking): bool
    {
        return ! Ranking::ofCalculation($ranking->calculation_id)
            ->ofApplication($ranking->application_id)
            ->where('combined_score', '>', $ranking->combined_score)
            ->where('position_order', '<', $ranking->position_order)
            ->exists();
    }
}
