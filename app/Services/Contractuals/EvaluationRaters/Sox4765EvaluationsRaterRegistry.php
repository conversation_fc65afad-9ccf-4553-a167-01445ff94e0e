<?php

namespace App\Services\Contractuals\EvaluationRaters;

class Sox4765EvaluationsRaterRegistry
{
    protected $soxRaters;

    public function push($name, SoxEvaluationsRater $soxRater)
    {
        $this->soxRaters[$name] = $soxRater;

        return $this;
    }

    public function pull($name)
    {
        if (array_key_exists($name, $this->soxRaters)) {
            return $this->soxRaters[$name];
        }

        throw new \Exception("Cannot find '$name' SOX 4765 Rater");
    }
}
