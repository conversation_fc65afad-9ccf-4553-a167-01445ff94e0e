<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox4765;

use App\Models\Contractuals\Rating;
use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

/**
 * Class DegreesSoxEvaluationsRater.
 *
 * ΤΡΟΠΟΣ ΒΑΘΜΟΛΟΓΙΣΗΣ:
 * ΠΕ, ΤΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 40
 * ΔΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 20
 */
class DoctoratesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($evaluations, $applicantCategory, $auxiliaryLevel, $rating): float
    {
        $eligibleEvaluation = $evaluations->first();

        if ($eligibleEvaluation === null) {
            return 0;
        }

        $points = 0;
        $additionalPoints = 0;

        switch ($applicantCategory) {
            case 1: // PE
            case 2: // TE
                $points = 150;

                $eligibleEvaluation->update(['points' => $points]);

                break;
            case 3: // DE
                $points = 0;

                $eligibleEvaluation->update(['points' => $points]);

                break;
            case 4: // YE
                $points = 0;

                $eligibleEvaluation->update(['points' => $points]);

                break;
        }

        $rating->update([
            'doctorates' => $points > 0 ? $evaluations->count() : 0,
            'doctorates_points' => $points,
        ]);

        if ($applicantCategory == 1 || $applicantCategory == 2) {
            $additionalEvaluation = $evaluations->first(function ($evaluation) use ($eligibleEvaluation) {
                return $eligibleEvaluation->id !== $evaluation->id;
            });

            if ($additionalEvaluation) {
                $additionalPoints = $eligibleEvaluation->points / 2;
                $additionalEvaluation->update(['points' => $additionalPoints]);

                $rating->update([
                    'other_degrees' => $additionalPoints,
                    'other_degrees_points' => $additionalPoints,
                ]);
            }
        }



        return $points + $additionalPoints;
    }
}
