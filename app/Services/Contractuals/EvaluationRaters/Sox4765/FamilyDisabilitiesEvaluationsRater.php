<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox4765;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

class FamilyDisabilitiesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($familyDisabilitiesEvaluations, $applicantCategory, $auxiliaryLevel, $rating): float
    {
        $familyDisabilitiesEvaluation = $familyDisabilitiesEvaluations->first();

        if ($familyDisabilitiesEvaluation === null) {
            return 0;
        }

        if ($familyDisabilitiesEvaluation->qualification->qualifiable->is_eligible) {

            $points = 130;

            $familyDisabilitiesEvaluation->points = $points;
            $familyDisabilitiesEvaluation->save();

        } else {

            $points = 0;

            $familyDisabilitiesEvaluation->points = 0;
            $familyDisabilitiesEvaluation->save();

        }

        $rating->update([
            'family_disabilities_eligibility' => $familyDisabilitiesEvaluation->qualification->qualifiable->is_eligible,
            'family_disabilities_points' => $points,
        ]);

        return $points;
    }
}
