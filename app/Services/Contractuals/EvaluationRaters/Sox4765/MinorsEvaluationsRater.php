<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox4765;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

class MinorsEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($minorsEvaluations, $applicantCategory, $auxiliaryLevel, $rating): float
    {
        $minorsEvaluation = $minorsEvaluations->first();

        if ($minorsEvaluation === null) {
            return 0;
        }

        $children = $minorsEvaluation->qualification->qualifiable->amount;

        if ($children <= 6) {

            $points = $children * 50;

            $minorsEvaluation->points = $points;
            $minorsEvaluation->save();

        } else {

            $points = 300;

            $minorsEvaluation->points = $points;
            $minorsEvaluation->save();

        }

        $rating->update([
            'minors' => $children,
            'minors_points' => $points,
        ]);

        return $minorsEvaluation->points;
    }
}
