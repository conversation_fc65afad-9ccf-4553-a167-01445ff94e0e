<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox4765;

use App\Models\Contractuals\Rating;
use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

/**
 * Class ExperiencesSoxEvaluationsRater.
 *
 * ΤΡΟΠΟΣ ΒΑΘΜΟΛΟΓΙΣΗΣ:
 * 7 μονάδες ανά μήνα εμπειρίας και έως 7 μήνες
 */
class ExperiencesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($experienceEvaluations, $applicantCategory, $auxiliaryLevel, $rating): float
    {
        $experienceEvaluation = $experienceEvaluations->first();

        if ($experienceEvaluation === null) {
            return 0;
        }

        $months = $experienceEvaluation->qualification->qualifiable->months;

        $points = $months * 7;
        if ($points > 588) {
            $points = 588;
        }
        $experienceEvaluation->points = $points;
        $experienceEvaluation->save();

        $rating->update([
            'experiences' => $months,
            'experiences_points' => $points,
        ]);

        return $points;
    }
}
