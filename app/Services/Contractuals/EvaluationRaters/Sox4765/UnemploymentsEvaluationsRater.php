<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox4765;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

class UnemploymentsEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($unemploymentEvaluations, $applicantCategory, $auxiliaryLevel, $rating): float
    {
        $unemploymentEvaluation = $unemploymentEvaluations->first();
        if ($unemploymentEvaluation === null) {
            return 0;
        }

        $unemploymentMonths = $unemploymentEvaluation->qualification->qualifiable->months;
        $isContinued = $unemploymentEvaluation->qualification->qualifiable->is_continued;

        if (! $isContinued) {
            if ($unemploymentMonths <= 9) {
                $points = $unemploymentMonths * 40;
            } else {
                $points = 360;
            }
        } else {
            if ($unemploymentMonths < 4) {
                $points = 0;
            } elseif ($unemploymentMonths >= 4 && $unemploymentMonths <= 18) {
                $points = ($unemploymentMonths - 4) * 60 + 200;
            } else {
                $points = 1040;
            }
        }

        $unemploymentEvaluation->points = $points;
        $unemploymentEvaluation->save();

        $rating->update([
            'unemployments_continued' => $isContinued ? $unemploymentMonths : 0,
            'unemployments' => ! $isContinued ? $unemploymentMonths : 0,
            'unemployments_continued_points' => $isContinued ? $points : 0,
            'unemployments_points' => ! $isContinued ? $points : 0,
        ]);

        return $unemploymentEvaluation->points;
    }
}
