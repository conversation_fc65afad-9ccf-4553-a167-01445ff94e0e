<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox4765;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

/**
 * Class DegreesSoxEvaluationsRater.
 *
 * ΤΡΟΠΟΣ ΒΑΘΜΟΛΟΓΙΣΗΣ:
 * ΠΕ, ΤΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 40
 * ΔΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 20
 */
class DegreesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($evaluations, $applicantCategory, $auxiliaryLevel, $rating): float
    {
        if ($evaluations->count() == 0) {
            return 0;
        }

        $degreePoints = 0;
        $secondDegreePoints = 0;
        $additionalPoints = 0;

        $primaryEvaluation = $evaluations->first(function ($evaluation) {
            return $evaluation->qualification->qualifiable->is_primary;
        });

        if (! is_null($primaryEvaluation)) {
            $secondEvaluation = $evaluations->first(function ($evaluation) use ($primaryEvaluation) {
                return $primaryEvaluation->id !== $evaluation->id;
            });
        } else {
            $secondEvaluation = null;
        }

        if (! is_null($primaryEvaluation) && ! is_null($secondEvaluation)) {
            $additionalEvaluation = $evaluations->first(function ($evaluation) use ($primaryEvaluation, $secondEvaluation) {
                return $primaryEvaluation->id !== $evaluation->id && $secondEvaluation->id !== $evaluation->id;
            });
        } else {
            $additionalEvaluation = null;
        }

        switch ($applicantCategory) {
            case 1: // PE
            case 2: // TE
                if (! is_null($primaryEvaluation)) {
                    if ($primaryEvaluation->qualification->qualifiable->mark >= 5) {
                        $degreePoints = $primaryEvaluation->qualification->qualifiable->mark * 40;
                        if ($degreePoints >= 400) {
                            $degreePoints = 400;
                        }
                    } else {
                        $degreePoints = 0;
                    }
                    $primaryEvaluation->update(['points' => $degreePoints]);
                    $rating->update([
                        'degrees' => $primaryEvaluation->qualification->qualifiable->mark,
                        'degrees_points' => $degreePoints,
                    ]);
                }
                break;
            case 3: // DE
                if (! is_null($primaryEvaluation)) {
                    if ($primaryEvaluation->qualification->qualifiable->mark >= 10) {
                        $degreePoints = $primaryEvaluation->qualification->qualifiable->mark * 20;
                        if ($degreePoints >= 400) {
                            $degreePoints = 400;
                        }
                    } else {
                        $degreePoints = 0;
                    }
                    $primaryEvaluation->update(['points' => $degreePoints]);
                    $rating->update([
                        'degrees' => $primaryEvaluation->qualification->qualifiable->mark,
                        'degrees_points' => $degreePoints,
                    ]);
                }
                break;
            case 4: // YE
                if (! is_null($primaryEvaluation)) {
                    $degreePoints = 0;
                    $primaryEvaluation->update(['points' => $degreePoints]);
                    $rating->update([
                        'degrees' => $primaryEvaluation->qualification->qualifiable->mark,
                        'degrees_points' => $degreePoints,
                    ]);
                }
                break;
        }

        if ($applicantCategory == 1 || $applicantCategory == 2) {
            if (! is_null($secondEvaluation)) {
                $secondDegreePoints = 30;
                $secondEvaluation->update(['points' => $secondDegreePoints]);
                $rating->update([
                    'second_degrees' => true,
                    'second_degrees_points' => $secondDegreePoints
                ]);
            }

            if ($additionalEvaluation) {
                $doctorateEvaluationsCount = $primaryEvaluation->application->evaluations
                    ->filter(function ($evaluation) use ($primaryEvaluation) {
                        return $evaluation->isOfPosition($primaryEvaluation->position_id)
                            && $evaluation->isOfAuxiliaryLevel($primaryEvaluation->auxiliary_level)
                            && $evaluation->isOfType('doctorates');
                    })->count();
                $postgraduateEvaluationsCount = $primaryEvaluation->application->evaluations
                    ->filter(function ($evaluation) use ($primaryEvaluation) {
                        return $evaluation->isOfPosition($primaryEvaluation->position_id)
                            && $evaluation->isOfAuxiliaryLevel($primaryEvaluation->auxiliary_level)
                            && $evaluation->isOfType('postgraduates')
                            && ! $evaluation->qualification->qualifiable->is_integrated;
                    })->count();
                $integratedPostgraduateEvaluationsCount = $primaryEvaluation->application->evaluations
                    ->filter(function ($evaluation) use ($primaryEvaluation) {
                        return $evaluation->isOfPosition($primaryEvaluation->position_id)
                            && $evaluation->isOfAuxiliaryLevel($primaryEvaluation->auxiliary_level)
                            && $evaluation->isOfType('postgraduates')
                            && ($evaluation->qualification->qualifiable->is_integrated);
                    })->count();

                if ($doctorateEvaluationsCount < 2 && $postgraduateEvaluationsCount < 2 && $integratedPostgraduateEvaluationsCount < 2) {
                    $additionalPoints = 15;
                    $additionalEvaluation->update(['points' => $additionalPoints]);
                    $rating->update([
                        'other_degrees' => $additionalPoints,
                        'other_degrees_points' => $additionalPoints,
                    ]);
                }
            }
        } elseif ($applicantCategory == 3) {
            // ΔΕ:
            // όσοι είναι κάτοχοι απολυτηρίου Λυκείου κι έχουν επιπλέον κάποιο πτυχίο
            // ΙΕΚ/μεταλυκειακής εκπαίδευσης (οποιασδήποτε ειδικότητας) συν 25 μόρια.
            if (! is_null($secondEvaluation)) {
                $secondDegreePoints = 25;
                $secondEvaluation->update(['points' => $secondDegreePoints]);
                $rating->update([
                    'second_degrees' => true,
                    'second_degrees_points' => $secondDegreePoints
                ]);
            }
        }

        return $degreePoints + $secondDegreePoints + $additionalPoints;
    }
}
