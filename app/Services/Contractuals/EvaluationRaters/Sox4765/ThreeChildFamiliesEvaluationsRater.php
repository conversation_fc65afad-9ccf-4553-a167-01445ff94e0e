<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox4765;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

class ThreeChildFamiliesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($threeChildFamiliesEvaluations, $applicantCategory, $auxiliaryLevel, $rating): float
    {
        $threeChildFamiliesEvaluation = $threeChildFamiliesEvaluations->first();

        if ($threeChildFamiliesEvaluation === null) {
            return 0;
        }

        if ($threeChildFamiliesEvaluation->qualification->qualifiable->is_eligible) {
            $points = 200;
            $threeChildFamiliesEvaluation->points = $points;
            $threeChildFamiliesEvaluation->save();
        } else {
            $points = 0;
            $threeChildFamiliesEvaluation->points = $points;
            $threeChildFamiliesEvaluation->save();
        }

        $rating->update([
            'three_child_families_eligibility' => $threeChildFamiliesEvaluation->qualification->qualifiable->is_eligible,
            'three_child_families_points' => $points,
        ]);

        return $points;
    }
}
