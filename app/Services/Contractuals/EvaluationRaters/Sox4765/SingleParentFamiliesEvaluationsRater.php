<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox4765;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

class SingleParentFamiliesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($singleParentFamiliesEvaluations, $applicantCategory, $auxiliaryLevel, $rating): float
    {
        $singleParentFamiliesEvaluation = $singleParentFamiliesEvaluations->first();

        if ($singleParentFamiliesEvaluation === null) {
            return 0;
        }

        if ($singleParentFamiliesEvaluation->qualification->qualifiable->is_eligible) {
            $points = 100;
            $singleParentFamiliesEvaluation->points = $points;
            $singleParentFamiliesEvaluation->save();
        } else {
            $points = 0;
            $singleParentFamiliesEvaluation->points = $points;
            $singleParentFamiliesEvaluation->save();
        }

        $rating->update([
            'single_parent_families_eligibility' => $singleParentFamiliesEvaluation->qualification->qualifiable->is_eligible,
            'single_parent_families_points' => $points,
        ]);

        return $points;
    }
}
