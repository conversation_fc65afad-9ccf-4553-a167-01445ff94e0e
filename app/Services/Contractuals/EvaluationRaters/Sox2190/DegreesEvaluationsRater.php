<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox2190;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

/**
 * Class DegreesSoxEvaluationsRater.
 *
 * ΤΡΟΠΟΣ ΒΑΘΜΟΛΟΓΙΣΗΣ:
 * ΠΕ, ΤΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 40
 * ΔΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 20
 */
class DegreesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($evaluations, $applicantCategory, $auxiliaryLevel, $rating = null): float
    {
        $elligibleEvaluation = $evaluations->first(function ($evaluation) {
            return null !== $evaluation->requirement_id;
        });

        if ($elligibleEvaluation === null) {
            return 0;
        }

        switch ($applicantCategory) {
            case 1:
            case 2: // PE TE
                $points = $elligibleEvaluation->qualification->qualifiable->mark * 40;

                $elligibleEvaluation->update(['points' => $points]);

                break;
            case 3: // DE
                $points = $elligibleEvaluation->qualification->qualifiable->mark * 20;

                $elligibleEvaluation->update(['points' => $points]);

                break;
            case 4: // YE
                $points = 0;

                $elligibleEvaluation->update(['points' => $points]);

                break;
        }

        return $elligibleEvaluation->points;
    }
}
