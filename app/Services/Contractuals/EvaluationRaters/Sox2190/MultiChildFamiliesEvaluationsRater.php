<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox2190;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

class MultiChildFamiliesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($multiChildFamiliesEvaluations, $applicantCategory, $auxiliaryLevel, $rating = null): float
    {
        $multiChildFamiliesEvaluation = $multiChildFamiliesEvaluations->first();
        $children = $multiChildFamiliesEvaluation->qualification->qualifiable->children;
        $siblings = $multiChildFamiliesEvaluation->qualification->qualifiable->siblings;

        if ($children < 3 && $siblings < 3) {
            $multiChildFamiliesEvaluation->points = 0;
            $multiChildFamiliesEvaluation->save();
        } elseif ($children >= $siblings) {
            $multiChildFamiliesEvaluation->points = $children * 50;
            $multiChildFamiliesEvaluation->save();
        } elseif ($children < $siblings) {
            $multiChildFamiliesEvaluation->points = $siblings * 50;
            $multiChildFamiliesEvaluation->save();
        }

        return $multiChildFamiliesEvaluation->points;
    }
}
