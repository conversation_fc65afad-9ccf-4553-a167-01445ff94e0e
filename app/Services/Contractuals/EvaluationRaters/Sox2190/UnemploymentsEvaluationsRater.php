<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox2190;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

class UnemploymentsEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($unemploymentEvaluations, $applicantCategory, $auxiliaryLevel, $rating = null): float
    {
        $unemploymentEvaluation = $unemploymentEvaluations->first();
        $unemploymentMonths = $unemploymentEvaluation->qualification->qualifiable->months;

        if ($unemploymentMonths < 4) {
            $unemploymentEvaluation->points = 0;
            $unemploymentEvaluation->save();
        } elseif ($unemploymentMonths >= 4 && $unemploymentMonths <= 12) {
            $unemploymentEvaluation->points = ($unemploymentMonths - 4) * 75 + 200;
            $unemploymentEvaluation->save();
        } else {
            $unemploymentEvaluation->points = 800;
            $unemploymentEvaluation->save();
        }

        return $unemploymentEvaluation->points;
    }
}
