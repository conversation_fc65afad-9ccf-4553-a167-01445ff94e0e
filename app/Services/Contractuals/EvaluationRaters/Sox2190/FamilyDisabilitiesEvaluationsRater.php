<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox2190;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

class FamilyDisabilitiesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($familyDisabilitiesEvaluations, $applicantCategory, $auxiliaryLevel, $rating = null): float
    {
        $familyDisabilitiesEvaluation = $familyDisabilitiesEvaluations->first();
        $percentage = $familyDisabilitiesEvaluation->qualification->qualifiable->percentage;

        if ($percentage >= 50) {
            $familyDisabilitiesEvaluation->points = $percentage * 2;
            $familyDisabilitiesEvaluation->save();
        } else {
            $familyDisabilitiesEvaluation->points = 0;
            $familyDisabilitiesEvaluation->save();
        }

        return $familyDisabilitiesEvaluation->points;
    }
}
