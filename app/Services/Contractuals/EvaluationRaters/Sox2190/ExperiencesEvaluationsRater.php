<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox2190;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

/**
 * Class ExperiencesSoxEvaluationsRater.
 *
 * ΤΡΟΠΟΣ ΒΑΘΜΟΛΟΓΙΣΗΣ:
 * 7 μονάδες ανά μήνα εμπειρίας και έως 7 μήνες
 */
class ExperiencesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($experienceEvaluations, $applicantCategory, $auxiliaryLevel, $rating = null): float
    {
        $experienceEvaluations->filter(function ($experienceEvaluation) {
            return $experienceEvaluation->isRelevant();
        })->each(function ($experienceEvaluation) {
            $experienceEvaluation->points = $experienceEvaluation->qualification->qualifiable->months * 7;
            $experienceEvaluation->save();
        });

        return $experienceEvaluations->sum('points') <= 420
            ? $experienceEvaluations->sum('points')
            : 420;
    }
}
