<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox2190;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

class ThreeChildFamiliesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($threeChildFamiliesEvaluations, $applicantCategory, $auxiliaryLevel, $rating = null): float
    {
        $threeChildFamiliesEvaluation = $threeChildFamiliesEvaluations->first();
        $children = $threeChildFamiliesEvaluation->qualification->qualifiable->children;
        $siblings = $threeChildFamiliesEvaluation->qualification->qualifiable->siblings;

        if ($children || $siblings) {
            $threeChildFamiliesEvaluation->points = 120;
            $threeChildFamiliesEvaluation->save();
        } else {
            $threeChildFamiliesEvaluation->points = 0;
            $threeChildFamiliesEvaluation->save();
        }

        return $threeChildFamiliesEvaluation->points;
    }
}
