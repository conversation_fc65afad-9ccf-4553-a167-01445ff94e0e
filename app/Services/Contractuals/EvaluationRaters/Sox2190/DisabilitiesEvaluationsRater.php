<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox2190;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

class DisabilitiesEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($disabilitiesEvaluations, $applicantCategory, $auxiliaryLevel, $rating = null): float
    {
        $disabilitiesEvaluation = $disabilitiesEvaluations->first();
        $percentage = $disabilitiesEvaluation->qualification->qualifiable->percentage;

        if ($percentage >= 50) {
            $disabilitiesEvaluation->points = $percentage * 3;
            $disabilitiesEvaluation->save();
        } else {
            $disabilitiesEvaluation->points = 0;
            $disabilitiesEvaluation->save();
        }

        return $disabilitiesEvaluation->points;
    }
}
