<?php

namespace App\Services\Contractuals\EvaluationRaters\Sox2190;

use App\Services\Contractuals\EvaluationRaters\SoxEvaluationsRater;

class MinorsEvaluationsRater implements SoxEvaluationsRater
{
    public function rate($minorsEvaluations, $applicantCategory, $auxiliaryLevel, $rating = null): float
    {
        $minorsEvaluation = $minorsEvaluations->first();
        $children = $minorsEvaluation->qualification->qualifiable->amount;

        if ($children < 3) {
            $minorsEvaluation->points = $children * 30;
            $minorsEvaluation->save();
        } else {
            $minorsEvaluation->points = 110;
            $minorsEvaluation->save();
        }

        return $minorsEvaluation->points;
    }
}
