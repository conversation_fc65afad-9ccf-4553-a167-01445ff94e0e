<?php

namespace App\Services\Contractuals\QualificationRaters\Sox4765;

use App\Services\Contractuals\QualificationRaters\SoxQualificationsRater;

class FamilyDisabilityQualificationsRater implements SoxQualificationsRater
{
    use HasRatings;

    public function rate($qualifications, $applicantCategory, $auxiliaryLevel): void
    {
        $familyDisabilitiesQualification = $qualifications->first();

        if ($familyDisabilitiesQualification === null) {

            return;
        }

        if ($familyDisabilitiesQualification->qualifiable->is_eligible) {

            $this->points = 130;

            $familyDisabilitiesQualification->points = $this->points;
            $familyDisabilitiesQualification->save();

        } else {

            $this->points = 0;

            $familyDisabilitiesQualification->points = 0;
            $familyDisabilitiesQualification->save();

        }

        $this->ratings['family_disabilities_eligibility'] = $familyDisabilitiesQualification->qualifiable->is_eligible;
        $this->ratings['family_disabilities_points'] = $this->points;
    }
}
