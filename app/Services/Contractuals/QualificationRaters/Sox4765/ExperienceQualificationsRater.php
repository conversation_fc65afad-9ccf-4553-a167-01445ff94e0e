<?php

namespace App\Services\Contractuals\QualificationRaters\Sox4765;

use App\Services\Contractuals\QualificationRaters\SoxQualificationsRater;

/**
 * Class ExperiencesSoxQualificationsRater.
 *
 * ΤΡΟΠΟΣ ΒΑΘΜΟΛΟΓΙΣΗΣ:
 * 7 μονάδες ανά μήνα εμπειρίας και έως 7 μήνες
 */
class ExperienceQualificationsRater implements SoxQualificationsRater
{
    use HasRatings;

    public function rate($qualifications, $applicantCategory, $auxiliaryLevel): void
    {
        $experienceQualification = $qualifications->first();

        if ($experienceQualification === null) {

            return;
        }

        $months = $experienceQualification->qualifiable->months;

        $this->points = $months * 7;
        if ($this->points > 588) {
            $this->points = 588;
        }
        $experienceQualification->points = $this->points;
        $experienceQualification->save();

        $this->ratings['experiences'] = $months;
        $this->ratings['experiences_points'] = $this->points;
    }
}
