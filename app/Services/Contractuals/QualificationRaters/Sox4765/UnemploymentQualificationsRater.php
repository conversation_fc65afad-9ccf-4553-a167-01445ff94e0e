<?php

namespace App\Services\Contractuals\QualificationRaters\Sox4765;

use App\Services\Contractuals\QualificationRaters\SoxQualificationsRater;

class UnemploymentQualificationsRater implements SoxQualificationsRater
{
    use HasRatings;

    public function rate($qualifications, $applicantCategory, $auxiliaryLevel): void
    {
        $unemploymentQualification = $qualifications->first();
        if ($unemploymentQualification === null) {

            return;
        }

        $unemploymentMonths = $unemploymentQualification->qualifiable->months;
        $isContinued = $unemploymentQualification->qualifiable->is_continued;

        if (! $isContinued) {
            if ($unemploymentMonths <= 9) {
                $this->points = $unemploymentMonths * 40;
            } else {
                $this->points = 360;
            }
        } else {
            if ($unemploymentMonths < 4) {
                $this->points = 0;
            } elseif ($unemploymentMonths >= 4 && $unemploymentMonths <= 18) {
                $this->points = ($unemploymentMonths - 4) * 60 + 200;
            } else {
                $this->points = 1040;
            }
        }

        $unemploymentQualification->points = $this->points;
        $unemploymentQualification->save();

        $this->ratings['unemployments_continued'] = $isContinued ? $unemploymentMonths : 0;
        $this->ratings['unemployments'] = ! $isContinued ? $unemploymentMonths : 0;
        $this->ratings['unemployments_continued_points'] = $isContinued ? $this->points : 0;
        $this->ratings['unemployments_points'] = ! $isContinued ? $this->points : 0;
    }
}
