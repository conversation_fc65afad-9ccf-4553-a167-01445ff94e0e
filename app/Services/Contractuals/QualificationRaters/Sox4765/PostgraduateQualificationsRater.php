<?php

namespace App\Services\Contractuals\QualificationRaters\Sox4765;

use App\Services\Contractuals\QualificationRaters\SoxQualificationsRater;

/**
 * Class PostgraduatesQualificationsRater.
 *
 * ΤΡΟΠΟΣ ΒΑΘΜΟΛΟΓΙΣΗΣ:
 * ΠΕ, ΤΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 40
 * ΔΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 20
 */
class PostgraduateQualificationsRater implements SoxQualificationsRater
{
    use HasRatings;

    public function rate($qualifications, $applicantCategory, $auxiliaryLevel): void
    {
        if ($qualifications->count() === 0) {

            return;
        }

        $postgraduateQualifications = $qualifications->filter(function ($qualification) {
            return ! $qualification->qualifiable->is_integrated;
        });
        $postgraduateQualification = $postgraduateQualifications->first();
        $additionalPostgraduate = $postgraduateQualifications->first(function ($qualification) use ($postgraduateQualification) {
            return $postgraduateQualification->id !== $qualification->id;
        });
        $integratedPostgraduateQualifications = $qualifications->filter(function ($qualification) {
            return $qualification->qualifiable->is_integrated;
        });
        $integratedPostgraduateQualification = $integratedPostgraduateQualifications->first();
        $additionalIntegratedPostgraduate = $integratedPostgraduateQualifications->first(function ($qualification) use ($integratedPostgraduateQualification) {
            return $integratedPostgraduateQualification->id !== $qualification->id;
        });

        $postgraduatePoints = 0;
        $integratedPostgraduatePoints = 0;
        $additionalPoints = 0;

        if ($postgraduateQualification) {
            switch ($applicantCategory) {
                case 1: // PE
                case 2: // TE
                    $postgraduatePoints =  70;
                    $postgraduateQualification->update(['points' => $postgraduatePoints]);
                    break;
                case 3: // DE
                    $postgraduatePoints = 0;
                    $postgraduateQualification->update(['points' => $postgraduatePoints]);
                    break;
                case 4: // YE
                    $postgraduatePoints = 0;
                    $postgraduateQualification->update(['points' => $postgraduatePoints]);
                    break;
            }

            $this->ratings['postgraduates'] = $postgraduatePoints > 0 ? $postgraduateQualifications->count() : 0;
            $this->ratings['postgraduates_points'] = $postgraduatePoints;
        }

        if ($integratedPostgraduateQualification) {
            switch ($applicantCategory) {
                case 1: // PE
                case 2: // TE
                    $integratedPostgraduatePoints =  35;
                    $integratedPostgraduateQualification->update(['points' => $integratedPostgraduatePoints]);
                     break;
                case 3: // DE
                    $integratedPostgraduatePoints = 0;
                    $integratedPostgraduateQualification->update(['points' => $integratedPostgraduatePoints]);
                    break;
                case 4: // YE
                    $integratedPostgraduatePoints = 0;
                    $integratedPostgraduateQualification->update(['points' => $integratedPostgraduatePoints]);
                    break;
            }

            $this->ratings['postgraduates_integrated'] = $integratedPostgraduatePoints > 0 ? $integratedPostgraduateQualifications->count() : 0;
            $this->ratings['postgraduates_integrated_points'] = $integratedPostgraduatePoints;
        }

        if ($applicantCategory == 1 || $applicantCategory == 2) {
           if ($additionalPostgraduate) {
                $doctorateQualificationsCount = $postgraduateQualification->application->qualifications
                    ->filter(function ($qualification) {
                        return $qualification->isOfType('doctorates');
                })->count();

                if ($doctorateQualificationsCount < 2) {
                    $additionalPoints = $postgraduateQualification->points / 2;
                    $additionalPostgraduate->update(['points' => $additionalPoints]);

                    $this->ratings['other_degrees'] = $additionalPoints;
                    $this->ratings['other_degrees_points'] = $additionalPoints;
                }
            }

            if ($additionalIntegratedPostgraduate) {
                $doctorateQualificationsCount = $integratedPostgraduateQualification->application->qualifications
                    ->filter(function ($qualification) {
                        return $qualification->isOfType('doctorates');
                    })->count();

                if ($doctorateQualificationsCount < 2 && is_null($additionalPostgraduate)) {
                    $additionalPoints = $integratedPostgraduateQualification->points / 2;
                    $additionalIntegratedPostgraduate->update(['points' => $additionalPoints]);

                    $this->ratings['other_degrees'] = $additionalPoints;
                    $this->ratings['other_degrees_points'] = $additionalPoints;
                }
            }
        }

        if ($applicantCategory == 1 || $applicantCategory == 2) {
            $this->points = $postgraduatePoints + $integratedPostgraduatePoints + $additionalPoints;
        }
    }
}
