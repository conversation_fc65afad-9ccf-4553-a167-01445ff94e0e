<?php

namespace App\Services\Contractuals\QualificationRaters\Sox4765;

use App\Services\Contractuals\QualificationRaters\SoxQualificationsRater;

class MinorQualificationsRater implements SoxQualificationsRater
{
    use HasRatings;

    public function rate($qualifications, $applicantCategory, $auxiliaryLevel): void
    {
        $minorsQualification = $qualifications->first();

        if ($minorsQualification === null) {

            return;
        }

        $children = $minorsQualification->qualifiable->amount;

        if ($children <= 6) {

            $this->points = $children * 50;

            $minorsQualification->points = $this->points;
            $minorsQualification->save();

        } else {

            $this->points = 300;

            $minorsQualification->points = $this->points;
            $minorsQualification->save();

        }

        $this->ratings['minors'] = $children;
        $this->ratings['minors_points'] = $this->points;
    }
}
