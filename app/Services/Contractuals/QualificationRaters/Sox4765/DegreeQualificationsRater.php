<?php

namespace App\Services\Contractuals\QualificationRaters\Sox4765;

use App\Services\Contractuals\QualificationRaters\SoxQualificationsRater;

/**
 * Class DegreesSoxQualificationsRater.
 *
 * ΤΡΟΠΟΣ ΒΑΘΜΟΛΟΓΙΣΗΣ:
 * ΠΕ, ΤΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 40
 * ΔΕ: οι μονάδες του βασικού τίτλου με 2 δεκαδικά πολλαπλασιάζονται με το 20
 */
class DegreeQualificationsRater implements SoxQualificationsRater
{
    use HasRatings;

    public function rate($qualifications, $applicantCategory, $auxiliaryLevel): void
    {
        if ($qualifications->count() == 0) {

            return;
        }

        $degreePoints = 0;
        $secondDegreePoints = 0;
        $additionalPoints = 0;

        $primaryQualification = $qualifications->first(function ($qualification) {
            return $qualification->qualifiable->is_primary;
        });

        if (! is_null($primaryQualification)) {
            $secondQualification = $qualifications->first(function ($qualification) use ($primaryQualification) {
                return $primaryQualification->id !== $qualification->id;
            });
        } else {
            $secondQualification = null;
        }

        if (! is_null($primaryQualification) && ! is_null($secondQualification)) {
            $additionalQualification = $qualifications->first(function ($qualification) use ($primaryQualification, $secondQualification) {
                return $primaryQualification->id !== $qualification->id && $secondQualification->id !== $qualification->id;
            });
        } else {
            $additionalQualification = null;
        }

        switch ($applicantCategory) {
            case 1: // PE
            case 2: // TE
                if (! is_null($primaryQualification)) {
                    if ($primaryQualification->qualifiable->mark >= 5) {
                        $degreePoints = $primaryQualification->qualifiable->mark * 40;
                        if ($degreePoints >= 400) {
                            $degreePoints = 400;
                        }
                    } else {
                        $degreePoints = 0;
                    }
                    $primaryQualification->update(['points' => $degreePoints]);
                    $this->ratings['degrees'] = $primaryQualification->qualifiable->mark;
                    $this->ratings['degrees_points'] = $degreePoints;
                }
                break;
            case 3: // DE
                if (! is_null($primaryQualification)) {
                    if ($primaryQualification->qualifiable->mark >= 10) {
                        $degreePoints = $primaryQualification->qualifiable->mark * 20;
                        if ($degreePoints >= 400) {
                            $degreePoints = 400;
                        }
                    } else {
                        $degreePoints = 0;
                    }
                    $primaryQualification->update(['points' => $degreePoints]);
                    $this->ratings['degrees'] = $primaryQualification->qualifiable->mark;
                    $this->ratings['degrees_points'] = $degreePoints;
                }
                break;
            case 4: // YE
                if (! is_null($primaryQualification)) {
                    $degreePoints = 0;
                    $primaryQualification->update(['points' => $degreePoints]);
                    $this->ratings['degrees'] = $primaryQualification->qualifiable->mark;
                    $this->ratings['degrees_points'] = $degreePoints;
                }
                break;
        }

        if ($applicantCategory == 1 || $applicantCategory == 2) {
            if (! is_null($secondQualification)) {
                $secondDegreePoints = 30;
                $secondQualification->update(['points' => $secondDegreePoints]);
                $this->ratings['second_degrees'] = true;
                $this->ratings['second_degrees_points'] = $secondDegreePoints;
            }

            if ($additionalQualification) {
                $doctorateQualificationsCount = $primaryQualification->application->qualifications
                    ->filter(function ($qualification) {
                        return $qualification->isOfType('doctorates');
                    })->count();
                $postgraduateQualificationsCount = $primaryQualification->application->qualifications
                    ->filter(function ($qualification) {
                        return $qualification->isOfType('postgraduates') && ! $qualification->qualifiable->is_integrated;
                    })->count();
                $integratedPostgraduateQualificationsCount = $primaryQualification->application->qualifications
                    ->filter(function ($qualification) {
                        return $qualification->isOfType('postgraduates') && ($qualification->qualifiable->is_integrated);
                    })->count();

                if ($doctorateQualificationsCount < 2 && $postgraduateQualificationsCount < 2 && $integratedPostgraduateQualificationsCount < 2) {
                    $additionalPoints = 15;
                    $additionalQualification->update(['points' => $additionalPoints]);
                    $this->ratings['other_degrees'] = $additionalPoints;
                    $this->ratings['other_degrees_points'] = $additionalPoints;
                }
            }
        } elseif ($applicantCategory == 3) {
            // ΔΕ:
            // όσοι είναι κάτοχοι απολυτηρίου Λυκείου κι έχουν επιπλέον κάποιο πτυχίο
            // ΙΕΚ/μεταλυκειακής εκπαίδευσης (οποιασδήποτε ειδικότητας) συν 25 μόρια.
            if (! is_null($secondQualification)) {
                $secondDegreePoints = 25;
                $secondQualification->update(['points' => $secondDegreePoints]);
                $this->ratings['second_degrees'] = true;
                $this->ratings['second_degrees_points'] = $secondDegreePoints;
            }
        }

        $this->points = $degreePoints + $secondDegreePoints + $additionalPoints;
    }
}
