<?php

namespace App\Services\Contractuals\QualificationRaters\Sox4765;

use App\Services\Contractuals\QualificationRaters\SoxQualificationsRater;

class DisabilityQualificationsRater implements SoxQualificationsRater
{
    use HasRatings;

    public function rate($qualifications, $applicantCategory, $auxiliaryLevel): void
    {
        $disabilitiesQualification = $qualifications->first();

        if ($disabilitiesQualification === null) {

            return;
        }

        if ($applicantCategory == 3) {
            // ΔΕ ΦΥΛΑΚΕΣ, ΔΕ ΝΥΧΤΟΦΥΛΑΚΕΣ
            // We check if the category is 'ΔΕ' and this works as long as the only 'ΔΕ' specializations are the above
            $this->points = 0;

            $disabilitiesQualification->points = $this->points;
            $disabilitiesQualification->save();

        } elseif ($disabilitiesQualification->qualifiable->is_eligible) {

            $this->points = 200;

            $disabilitiesQualification->points = $this->points;
            $disabilitiesQualification->save();

        } else {
            $this->points = 0;

            $disabilitiesQualification->points = $this->points;
            $disabilitiesQualification->save();
        }

        $this->ratings['disabilities_eligibility'] = $disabilitiesQualification->qualifiable->is_eligible;
        $this->ratings['disabilities_points'] = $this->points;
    }
}
