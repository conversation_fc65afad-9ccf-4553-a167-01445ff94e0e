<?php

namespace App\Services\Contractuals\QualificationRaters\Sox4765;

use App\Services\Contractuals\QualificationRaters\SoxQualificationsRater;

class SingleParentFamilyQualificationsRater implements SoxQualificationsRater
{
    use HasRatings;

    public function rate($qualifications, $applicantCategory, $auxiliaryLevel): void
    {
        $singleParentFamiliesQualification = $qualifications->first();

        if ($singleParentFamiliesQualification === null) {

            return;
        }

        if ($singleParentFamiliesQualification->qualifiable->is_eligible) {
            $this->points = 100;
            $singleParentFamiliesQualification->points = $this->points;
            $singleParentFamiliesQualification->save();
        } else {
            $this->points = 0;
            $singleParentFamiliesQualification->points = $this->points;
            $singleParentFamiliesQualification->save();
        }

        $this->ratings['single_parent_families_eligibility'] = $singleParentFamiliesQualification->qualifiable->is_eligible;
        $this->ratings['single_parent_families_points'] = $this->points;
    }
}
