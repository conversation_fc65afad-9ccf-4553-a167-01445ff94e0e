<?php

namespace App\Services\Contractuals\QualificationRaters;

class Sox4765QualificationsRaterRegistry
{
    protected $soxRaters;

    public function push($name, SoxQualificationsRater $soxRater)
    {
        $this->soxRaters[$name] = $soxRater;

        return $this;
    }

    public function pull($name)
    {
        if (array_key_exists($name, $this->soxRaters)) {
            return $this->soxRaters[$name];
        }

        throw new \Exception("Cannot find '$name' SOX 4765 Rater");
    }
}
