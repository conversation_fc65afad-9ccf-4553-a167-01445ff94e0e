<?php

namespace App\Services\Contractuals;

use App\Exceptions\Contractuals\CalculationHasFailedException;
use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Contest;
use App\Services\Contractuals\ContestRankers\ContestRankerRegistry;
use App\Services\Contractuals\EmploymentManagers\BasicEmploymentManager;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ContestRankingCalculator
{
    public function __construct(
        protected ContestRankerRegistry $contestRankerRegistry,
        protected BasicEmploymentManager $employmentManager
    ) {}

    public function run(int $contestId, string $description, bool $hireRunnersUp): Calculation
    {
        $contest = Contest::findOrFail($contestId);

        $calculationTimestamp = now();

        try {
            DB::connection('mysql_contractuals')->beginTransaction();

            $calculation = $contest->calculations()->create([
                'description' => $description.' ('.$calculationTimestamp->format('d/m/Y H:i').')',
                'run_at' => now(),
                'distribution_run' => false,
                'hire_runners_up' => $hireRunnersUp,
            ]);

            info("Ranking calculation #{$calculation->id} started...");

            info('Starting contest ranker...');

            $this->contestRankerRegistry->pull($contest->contestType->id)->rank($contest, $calculation);

            info('Starting contest employment manager...');

            $this->employmentManager->employ($contest, $calculation);

            $contest->markAsRanked($calculationTimestamp);

            info("Ranking calculation #{$calculation->id} completed successfully.");

            DB::connection('mysql_contractuals')->commit();
        } catch (\Throwable $e) {
            DB::connection('mysql_contractuals')->rollBack();

            Log::error($e->getMessage());

            throw new CalculationHasFailedException($e->getMessage());
        }

        return $calculation;
    }
}
