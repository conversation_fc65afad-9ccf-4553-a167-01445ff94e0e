<?php

namespace App\Services\Contractuals;

use App\Exceptions\Contractuals\CalculationHasFailedException;
use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Contest;
use App\Services\Contractuals\ContestRankers\ContestRankerRegistry;
use App\Services\Contractuals\Distributors\BasicEmploymentDistributor;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ContestDistributionCalculator
{
    public function __construct(
        protected ContestRankerRegistry $contestRankerRegistry,
        protected BasicEmploymentDistributor $employmentDistributor
    ) {}

    public function run(int $contestId, int $n, int $limit): Calculation
    {
        $contest = Contest::findOrFail($contestId);

        $calculationTimestamp = now();

        try {
            DB::connection('mysql_contractuals')->beginTransaction();

            $calculation = $contest->calculations()->create([
                'description' => 'Διαμοιρασμός αιτήσεων (N:'.$n.', ΟΕ:'.$limit.', '.$calculationTimestamp->format('d/m/Y H:i').')',
                'run_at' => $calculationTimestamp,
                'distribution_run' => true,
                'hire_runners_up' => false,
            ]);

            info("Distribution calculation #{$calculation->id} started...");

            info('Starting contest ranker...');

            $this->contestRankerRegistry->pull($contest->contestType->id)->rank($contest, $calculation);

            info('Starting contest distributor...');

            $this->employmentDistributor->distribute($contest, $calculation, $n, $limit);

            info("Distribution calculation #{$calculation->id} completed successfully.");

            DB::connection('mysql_contractuals')->commit();
        } catch (\Throwable $e) {
            DB::connection('mysql_contractuals')->rollBack();

            Log::error($e->getMessage());

            throw new CalculationHasFailedException($e->getMessage());
        }

        return $calculation;
    }
}
