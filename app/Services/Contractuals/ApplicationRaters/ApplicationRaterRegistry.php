<?php

namespace App\Services\Contractuals\ApplicationRaters;

class ApplicationRaterRegistry
{
    protected $evaluators;

    public function push($name, ApplicationRater $evaluator)
    {
        $this->evaluators[$name] = $evaluator;

        return $this;
    }

    public function pull($contest_type_id): ApplicationRater
    {
        if (array_key_exists($contest_type_id, $this->evaluators)) {
            return $this->evaluators[$contest_type_id];
        }

        throw new \Exception("Cannot find $contest_type_id evaluator");
    }
}
