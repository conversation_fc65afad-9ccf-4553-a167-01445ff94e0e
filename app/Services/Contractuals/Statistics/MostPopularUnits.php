<?php

namespace App\Services\Contractuals\Statistics;

use Illuminate\Support\Facades\DB;

class MostPopularUnits
{
    public static function get(int $contestId): array
    {
        $mostPopularUnits = DB::connection('mysql_public_contractuals')->table('application_position as ap')
            ->join('positions as p', 'p.id', '=', 'ap.position_id')
            ->join('applications as a', 'a.id', '=', 'ap.application_id')
            ->selectRaw('
            p.unit,
            sum(a.specialization_type_id=1) as pe,
	        sum(a.specialization_type_id=2) as te,
	        sum(a.specialization_type_id=3) as de,
	        sum(a.specialization_type_id=4) as ye,
	        count(*) as total
            ')
            ->where('a.is_submitted', true)
            ->where('a.contest_id', '=', $contestId)
            ->groupBy('p.unit')
            ->orderByDesc('total')
            ->limit(10)
            ->get();

        $mostPopularUnitData = ['units' => [], 'pe' => [], 'te' => [], 'de' => [], 'ye' => []];
        foreach ($mostPopularUnits as $most) {
            $mostPopularUnitData['units'][] = $most->unit;
            $mostPopularUnitData['pe'][] = $most->pe;
            $mostPopularUnitData['te'][] = $most->te;
            $mostPopularUnitData['de'][] = $most->de;
            $mostPopularUnitData['ye'][] = $most->ye;
        }

        return $mostPopularUnitData;
    }
}
