<?php

namespace App\Services\Contractuals\Distributors;

use App\Models\Contractuals\Application;
use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Specialization;
use App\Services\Contractuals\EmploymentManagers\BasicEmploymentManager;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BasicEmploymentDistributor implements Distributor
{
    public function __construct(
        protected BasicEmploymentManager $employmentManager
    ) {}

    public function distribute(Contest $contest, Calculation $calculation, int $n = 2, int $limit = 500): void
    {
        Log::info('Distribute with n='.$n.' and limit='.$limit);
        $this->employmentManager->employ($contest, $calculation, n: $n);

       $contest->refresh();

        $this->setValidators($contest, $calculation);

        $this->setRemainingValidators($contest, $limit);
    }

    protected function setValidators($contest, $calculation): void
    {
        $q = DB::connection('mysql_contractuals')->table('rankings')
            ->join('positions', 'rankings.position_id', '=', 'positions.id')
            ->select('rankings.application_id', 'positions.unit_id')
            ->where('rankings.calculation_id', $calculation->id)
            ->where('rankings.employable', true);

        $updated = DB::connection('mysql_contractuals')->table('applications')
            ->leftJoinSub($q, 'q', function ($join) {
                $join->on('applications.id', '=', 'q.application_id');
            })->where('applications.contest_id', $contest->id)
            ->whereNull('applications.deleted_at')
            ->update(['applications.validating_unit_id' => DB::raw('q.unit_id')]);
        Log::info('set validators to '.$updated.' applications');
    }

    protected function setRemainingValidators(Contest $contest, int $limit)
    {
        $specializationTypeIds = Application::ofContest($contest->id)
            ->distinct('applicant_category')
            ->pluck('applicant_category')
            ->sort()
            ->values();

        $skippedApplicationIds = Application::ofContest($contest->id)
            ->whereNull('validating_unit_id')
            ->inRandomOrder()
            ->limit($limit)
            ->pluck('id');

        Log::info('Skipping '.$skippedApplicationIds->count().' applications');

        $specializationTypeIds->each(function ($specializationTypeId) use ($contest, $skippedApplicationIds) {
            // Get all units who hire these specializations and order them by least amount of hires.
            Log::info('distribute specialization type:'.$specializationTypeId);
            $specializationIds = Specialization::where('specialization_type_id', $specializationTypeId)->pluck('id');
            $unitIds = DB::connection('mysql_contractuals')
                ->table('positions')
                ->selectRaw('unit_id, sum(amount) as hires')
                ->where('contest_id', $contest->id)
                ->whereIn('specialization_id', $specializationIds)
                ->groupBy('unit_id')
                ->orderBy('hires')
                ->pluck('unit_id');

            $applications = Application::ofContest($contest->id)
                ->whereNull('validating_unit_id')
                ->whereNotIn('id', $skippedApplicationIds)
                ->where('applicant_category', $specializationTypeId);
            Log::info('Distribute '.$applications->count().' applications to '.$unitIds->count().' units');

            $i = 0;
            $j = 0;
            $applications->lazyById(200, $column = 'id')->each(function ($application) use ($unitIds, &$i, &$j) {
                $application->update(['validating_unit_id' => $unitIds->get($i)]);
                $i++;
                $j++;
                if ($i >= $unitIds->count()) {
                    $i = 0;
                }
            });
            Log::info('Distributed '.$j.' applications');
        });
    }
}
