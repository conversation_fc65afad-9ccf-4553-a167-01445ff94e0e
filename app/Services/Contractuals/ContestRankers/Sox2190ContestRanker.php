<?php

namespace App\Services\Contractuals\ContestRankers;

use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Calculation;

class Sox2190<PERSON>ontestRanker implements ContestRanker
{
    protected $contestRankings;
    protected $evaluatingPositionId;

    public function __construct()
    {
        $this->contestRankings = collect([]);
    }

    public function rank(Contest $contest, Calculation $calculation): void
    {
        $contest->load([
            'positions.applications:id,protocol_number,name,surname,fathername,policeid_number,impediment_eight_months,contest_id',
            'positions.applications.unemployments:months',
            'positions.applications.multiChildFamilies:children,siblings',
            'positions.applications.threeChildFamilies:children,siblings',
            'positions.applications.minors:amount',
            'positions.applications.singleParentFamilies:children,siblings',
            'positions.applications.disabilities:percentage',
            'positions.applications.familyDisabilities:percentage',
            'positions.applications.degrees.qualifications.evaluation',
            'positions.applications.experiences.qualifications.evaluation',
            'positions.applications.evaluations.qualification',
        ]);

        /*
        |--------------------------------------------------------------------------
        | Calculate contest rankings
        |--------------------------------------------------------------------------
        |
        */
        foreach ($contest->positions as $position) {
            $positionRankings = $position->applications->map(function ($application) use ($calculation) {
                return $this->rankingBuilder($application, $calculation->id);
            })->sortBy(function ($item) {
                return $this->combinedScore($item);
            })->values()->map(function ($ranking, $order) {
                return $this->updateRank($ranking, $order);
            });

            $this->contestRankings = $this->contestRankings->merge($positionRankings);
        }
    }

    protected function rankingBuilder($application, $calculationId)
    {
        $this->evaluatingPositionId = $application->pivot->position_id;

        return [
            'contest_id'                      => $application->contest_id,
            'calculation_id'                  => $calculationId,
            'position_id'                     => $application->pivot->position_id,
            'application_id'                  => $application->id,
            'protocol_number'                 => $application->protocol_number,
            'surname'                         => $application->surname,
            'name'                            => $application->name,
            'fathername'                      => $application->fathername,
            'policeid_number'                 => $application->policeid_number,
            'position_order'                  => $application->pivot->order,
            'impediment_eight_months'         => $application->impediment_eight_months,
            'auxiliary_level'                 => $application->pivot->auxiliary_level,
            'unemployments'                   => $application->unemployments->first()->months,
            'multi_child_families_children'   => $application->multiChildFamilies->first()->children,
            'multi_child_families_siblings'   => $application->multiChildFamilies->first()->siblings,
            'three_child_families_children'   => $application->threeChildFamilies->first()->children,
            'three_child_families_siblings'   => $application->threeChildFamilies->first()->siblings,
            'minors'                          => $application->minors->first()->amount,
            'single_parent_families_children' => $application->singleParentFamilies->first()->children,
            'single_parent_families_siblings' => $application->singleParentFamilies->first()->siblings,

            'degrees' => $application->degrees->filter(function ($degree) {
                return $degree->isPrimary();
            })->first()->mark ?? '0.00',

            'experiences' => $application->experiences->filter(function ($experience) {
                return $experience->isRelevant();
            })->sum('months') ?? '0.00',

            'disabilities'        => $application->disabilities->first()->percentage,
            'family_disabilities' => $application->familyDisabilities->first()->percentage,

            // Calculate evaluation points

            'unemployments_points' => $application->evaluations->filter(function ($evaluation) {
                return $evaluation->isOfType('unemployments')
                    && $evaluation->isOfPosition($this->evaluatingPositionId);
            })->first()->points,

            'multi_child_families_children_points' => $application->multiChildFamilies->first()->isParent()
                ? $application->evaluations->filter(function ($evaluation) {
                    return $evaluation->isOfType('multi_child_families')
                        && $evaluation->isOfPosition($this->evaluatingPositionId);
                })->first()->points
                : '0.00',

            'multi_child_families_siblings_points' => ! $application->multiChildFamilies->first()->isParent()
                ? $application->evaluations->filter(function ($evaluation) {
                    return $evaluation->isOfType('multi_child_families')
                        && $evaluation->isOfPosition($this->evaluatingPositionId);
                })->first()->points
                : '0.00',

            'three_child_families_children_points' => $application->threeChildFamilies->first()->isParent()
                ? $application->evaluations->filter(function ($evaluation) {
                    return $evaluation->isOfType('three_child_families')
                        && $evaluation->isOfPosition($this->evaluatingPositionId);
                })->first()->points
                : '0.00',

            'three_child_families_siblings_points' => ! $application->threeChildFamilies->first()->isParent()
                ? $application->evaluations->filter(function ($evaluation) {
                    return $evaluation->isOfType('three_child_families')
                        && $evaluation->isOfPosition($this->evaluatingPositionId);
                })->first()->points
                : '0.00',

            'minors_points' => $application->evaluations->filter(function ($evaluation) {
                return $evaluation->isOfType('minors')
                    && $evaluation->isOfPosition($this->evaluatingPositionId);
            })->first()->points,

            'single_parent_families_children_points' => $application->singleParentFamilies->first()->isParent()
                ? $application->evaluations->filter(function ($evaluation) {
                    return $evaluation->isOfType('single_parent_families')
                        && $evaluation->isOfPosition($this->evaluatingPositionId);
                })->first()->points
                : '0.00',

            'single_parent_families_siblings_points' => ! $application->singleParentFamilies->first()->isParent()
                ? $application->evaluations->filter(function ($evaluation) {
                    return $evaluation->isOfType('single_parent_families')
                        && $evaluation->isOfPosition($this->evaluatingPositionId);
                })->first()->points
                : '0.00',

            'degrees_points' => $application->evaluations->filter(function ($evaluation) {
                return $evaluation->isOfType('degrees')
                    && null !== $evaluation->requirement_id
                    && $evaluation->isOfPosition($this->evaluatingPositionId);
            })->first()->points ?? '0.00',

            'experiences_points' => $application->evaluations->filter(function ($evaluation) {
                return $evaluation->isOfType('experiences')
                    && $evaluation->isRelevant()
                    && $evaluation->isOfPosition($this->evaluatingPositionId);
            })->sum('points') ?? '0.00',

            'disabilities_points' => $application->evaluations->filter(function ($evaluation) {
                return $evaluation->isOfType('disabilities')
                    && $evaluation->isOfPosition($this->evaluatingPositionId);
            })->first()->points ?? '0.00',

            'family_disabilities_points' => $application->evaluations->filter(function ($evaluation) {
                return $evaluation->isOfType('family_disabilities')
                    && $evaluation->isOfPosition($this->evaluatingPositionId);
            })->first()->points ?? '0.00',

            'score'    => $application->pivot->score ?? 0,
            'rejected' => (int) $application->pivot->rejected,
            'rank'     => null,
        ];
    }

    protected function combinedScore($item)
    {
        // 1. `rejected` - ASC:
        //      Applications that are not rejected (rejected = 0) are moved to the top;
        //      Application that are rejected (rejected = 1) are moved to the bottom
        // 2. `impediment_eight_months` - ASC:
        //      Applications without eight months impediment (impediment_eight_months = 0) moved to the top;
        //      Applications with impediment (impediment_eight_months = 1) at the bottom
        // 2. `auxiliary_level` - ASC
        //      Applications that meet main requirements (auxiliary_level = 0) moved to the top;
        //      Applications that meet auxiliary requirements (auxiliary_level = 1, 2) moved to the bottom
        // 3. `score` - DESC:
        //      Applications with the highest score moved to the top;
        //      APplications with the lowest moved to the bottom

        // Important
        // -----------------
        // Returning non-integer values from the comparison function, such as float, will result
        // in an internal cast to integer of the callback's return value. So values such as
        // 0.99 and 0.1 will both be cast to an integer value of 0, which will compare
        // such values as equal. Thus we multiply the decimal score field by 100
        // see also [here](https://stackoverflow.com/a/33713443/2235814)

        return $item['rejected'].'#'
            .$item['impediment_eight_months'].'#'
            .$item['auxiliary_level'].'#'
            .sprintf('%06d', (100000 - $item['score'])).'#'
            .sprintf('%06d', $item['unemployments']).'#'
            .sprintf('%06d', $item['multi_child_families_children_points']).'#'
            .sprintf('%06d', $item['multi_child_families_siblings_points']).'#'
            .sprintf('%06d', $item['three_child_families_children_points']).'#'
            .sprintf('%06d', $item['three_child_families_siblings_points']).'#'
            .sprintf('%06d', $item['minors_points']).'#'
            .sprintf('%06d', $item['single_parent_families_children_points']).'#'
            .sprintf('%06d', $item['single_parent_families_siblings_points']).'#'
            .sprintf('%06d', ($item['degrees_points'] * 100)).'#'
            .sprintf('%06d', $item['experiences_points']).'#'
            .sprintf('%06d', $item['disabilities_points']).'#'
            .sprintf('%06d', $item['family_disabilities_points']).'#'
            .$item['surname'].'#'
            .$item['name'].'#'
            .$item['fathername']; // FIXME last resort!!!!
    }

    protected function updateRank($ranking, $order)
    {
        if (1 !== $ranking['rejected']) {
            $ranking['rank'] = $order + 1;
        }

        return $ranking;
    }
}
