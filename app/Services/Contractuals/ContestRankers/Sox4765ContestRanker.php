<?php

namespace App\Services\Contractuals\ContestRankers;

use App\Exceptions\Contractuals\ContestRankerHasFailedException;
use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Contest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Sox4765ContestRanker implements ContestRanker
{
    public function rank(Contest $contest, Calculation $calculation): void
    {
        try {
            DB::connection('mysql_contractuals')->beginTransaction();

            foreach ($contest->positions as $position) {
                $positionRatingIds = DB::connection('mysql_contractuals')
                    ->table('position_ratings')
                    ->select(['application_id','position_id',DB::raw('max(id) as max_id')])
                    ->where('position_id', $position->id)
                    ->whereNotNull('combined_score')
                    ->groupBy(['application_id','position_id'])
                    ->pluck('max_id')
                    ->toArray();

                $positionRankings = DB::connection('mysql_contractuals')
                    ->table('position_ratings')
                    ->join('application_ratings', 'position_ratings.application_rating_id', '=', 'application_ratings.id')
                    ->join('applications', 'application_ratings.application_id', '=', 'applications.id')
                    ->select([
                        'applications.contest_id',
                        DB::raw("{$calculation->id} as calculation_id"),
                        'application_ratings.application_id',
                        'application_ratings.specialization_type_id',
                        'position_ratings.position_id',
                        'position_ratings.position_order',
                        'position_ratings.locality',
                        'position_ratings.rejected',
                        'position_ratings.combined_score',
                        DB::raw("position_ratings.id as position_rating_id"),
                    ])
                    ->whereIn('position_ratings.id', $positionRatingIds)
                    ->orderBy('position_ratings.combined_score')
                    ->get()
                    ->values()
                    ->map(function ($ranking, $order) {
                        return $this->updateRank(collect($ranking)->toArray(), $order);
                    });

                // Store new rankings into the database
                if ($positionRankings->isNotEmpty()) {
                    $contest->setRankings($positionRankings);
                }
            }

            DB::connection('mysql_contractuals')->commit();
        } catch (\Throwable $e) {
            DB::connection('mysql_contractuals')->rollBack();
            Log::error($e->getMessage());
            throw new ContestRankerHasFailedException($e->getMessage());
        }

        // TODO - consider to return the rankings
    }

    protected function updateRank($ranking, $order)
    {
        if (1 !== $ranking['rejected']) {
            $ranking['rank'] = $order + 1;
        } else {
            $ranking['rank'] = null;
        }

        return $ranking;
    }
}
