<?php

namespace App\Providers;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;

class CustomValidationRulesServiceProvider extends ServiceProvider
{
    public function register()
    {
        //
    }

    public function boot()
    {
        /*
         * Validate that an attribute is false when another attribute is true
         */
        Validator::extend('false_if_true', function ($attribute, $value, $parameters, $validator) {
            foreach ($parameters as $param) {
                $param_value = Arr::get($validator->getData(), $param);
                if ($param_value == '1' || $param_value == true || $param_value == 'on') {
                    if ($value != null || $value == true || $value == '1' || $value == 'on') {
                        return false;
                    }
                }
            }

            return true;
        });

        /*
         * Validate that an attribute is false when another attribute is also false
         */
        Validator::extend('false_if_false', function ($attribute, $value, $parameters, $validator) {
            foreach ($parameters as $param) {
                $param_value = Arr::get($validator->getData(), $param);
                if ($param_value == '1' || $param_value == true || $param_value == 'on') {
                    if ($value != null || $value == true || $value == '1' || $value == 'on') {
                        return false;
                    }
                }
            }

            return true;
        });

        /*
         * Validate that an attribute is of greek date format.
         */
        Validator::extend('date_format_gr', function ($attribute, $value, $parameters, $validator) {
            if (! is_string($value) && ! is_numeric($value)) {
                return false;
            }

            $parsed = date_parse_from_format('d-m-Y', $value);

            return $parsed['error_count'] === 0 && $parsed['warning_count'] === 0;
        });

        /*
         * Validate that an attribute is of a valid AFM.
         */
        Validator::extend('afm', function ($attribute, $value, $parameters, $validator) {
            if (config('app.env') == 'local') {

                return true;
            }

            if (! is_numeric($value) || empty($value)) {
                return false;
            }

            if (! preg_match('/^\d{9}$/', $value) || $value === '000000000') {
                return false;
            }

            $m = 1;
            $sum = 0;
            for ($i = 7; $i >= 0; $i--) {
                $m *= 2;
                $sum += (int) substr($value, $i, 1) * $m;
            }

            return $sum % 11 % 10 == substr($value, 8, 1);
        });

        /*
         * Validate that an attribute is of a valid AMKA.
         */
        Validator::extend('amka', function ($attribute, $value, $parameters, $validator) {
            if (! is_numeric($value) || empty($value)) {
                return false;
            }

            if (! preg_match('/^\d{11}$/', $value) || $value === '00000000000') {
                return false;
            }

            $amkaDate = date_parse_from_format('dmy', substr($value, 0, 6));

            if ($amkaDate['error_count'] !== 0 || $amkaDate['warning_count'] !== 0) {
                return false;
            }

            $digits = str_split($value);
            $sum = 0;
            $i = 0;

            foreach ($digits as $digit) {
                $i++;

                if ($i % 2 == 0) {
                    $number = 2 * (int) $digit;
                    if ($number >= 10) {
                        $number = array_sum(str_split((string) $number));
                    }
                } else {
                    $number = $digit;
                }

                $sum += $number;
            }

            return $sum % 10 === 0;
        });

        /*
         * Validate that a date range is not conflicting with date ranges already in DB
         * $value : end date
         * $parameters[0] : start date
         * $parameters[1] : table
         * $parameters[2] : id to ignore - optional
         * $parameters[3] : key of related entity (eg. vehicle_id) - optional
         * $parameters[4] : value of related entity - optional
         * $parameters[5] : start date column  - optional (defaults to parameter[0])
         * $parameters[6] : end date column - optional (defaults to attribute)
         */
        Validator::extendDependent('unique_date_range', function ($attribute, $value, $parameters, $validator) {
            $start_date = Carbon::createFromFormat('Y-m-d', Arr::get($validator->getData(), $parameters[0]))->format('Y-m-d');
            $end_date = Carbon::createFromFormat('Y-m-d', $value)->format('Y-m-d');

            // Find any rows with conflicting dates
            $available = DB::table($parameters[1])
                ->whereNull('deleted_at')
                ->where($attribute, '>', $start_date)
                ->where($parameters[0], '<', $end_date);

            // Ignore id
            if (! empty($parameters[2])) {
                $available->where('id', '!=', Arr::get($validator->getData(), $parameters[2]));
            }

            // Check value of other column if given
            // In case we need to check only a subset of a table's rows
            // e.g. Dates of Contracts of Vehicles where rows have a vehicle_id
            if (! empty($parameters[3]) && ! empty($parameters[4])) {
                $available->where($parameters[3], $parameters[4]);
            }

            if ($available->count() == 0) {
                return true;
            }

            return false;
        });

        Validator::replacer('unique_date_range', function ($message, $attribute, $rule, $parameters, $validator) {
            return str_replace(':start', $validator->getDisplayableAttribute($parameters[0]), $message);
        });

        /*
         * Validate that a date is within a given year
         */
        Validator::extendDependent('date_in_year', function ($attribute, $value, $parameters, $validator) {
            $year = Arr::get($validator->getData(), $parameters[0]);

            $date = Carbon::createFromFormat('d-m-Y', $value)->startOfDay();

            if ($date->year == $year) {
                return true;
            }

            return false;
        });

        Validator::replacer('date_in_year', function ($message, $attribute, $rule, $parameters, $validator) {
            return str_replace(':year', $validator->getDisplayableAttribute($parameters[0]), $message);
        });
    }
}
