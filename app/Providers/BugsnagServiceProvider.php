<?php

namespace App\Providers;

use App\Models\User;
use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
use Illuminate\Support\ServiceProvider;

class BugsnagServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        Bugsnag::registerCallback(function (\Bugsnag\Report $report) {
            if ($report->getUser() && $report->getUser()['id']) {
                $user = User::find($report->getUser()['id']);
                if ($user) {
                    $report->setUser([
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'unit' => $user->unit?->abbrv,
                        'roles' => $user->roles->pluck('name')->toArray(),
                    ]);
                }
            }
        });
    }
}
