<?php

namespace App\Providers\Contractuals;

use App\Services\Contractuals\QualificationRaters\Sox4765\DegreeQualificationsRater;
use App\Services\Contractuals\QualificationRaters\Sox4765\DisabilityQualificationsRater;
use App\Services\Contractuals\QualificationRaters\Sox4765\DoctorateQualificationsRater;
use App\Services\Contractuals\QualificationRaters\Sox4765\ExperienceQualificationsRater;
use App\Services\Contractuals\QualificationRaters\Sox4765\FamilyDisabilityQualificationsRater;
use App\Services\Contractuals\QualificationRaters\Sox4765\MinorQualificationsRater;
use App\Services\Contractuals\QualificationRaters\Sox4765\MultiChildFamilyQualificationsRater;
use App\Services\Contractuals\QualificationRaters\Sox4765\PostgraduateQualificationsRater;
use App\Services\Contractuals\QualificationRaters\Sox4765\SingleParentFamilyQualificationsRater;
use App\Services\Contractuals\QualificationRaters\Sox4765\ThreeChildFamilyQualificationsRater;
use App\Services\Contractuals\QualificationRaters\Sox4765\UnemploymentQualificationsRater;
use App\Services\Contractuals\QualificationRaters\Sox4765QualificationsRaterRegistry;
use Illuminate\Support\ServiceProvider;

class Sox4765QualificationsRatersServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->app->make(Sox4765QualificationsRaterRegistry::class)
            ->push('degree', new DegreeQualificationsRater())
            ->push('postgraduate', new PostgraduateQualificationsRater())
            ->push('doctorate', new DoctorateQualificationsRater())
            ->push('experience', new ExperienceQualificationsRater())
            ->push('unemployment', new UnemploymentQualificationsRater())
            ->push('multi_child_family', new MultiChildFamilyQualificationsRater())
            ->push('three_child_family', new ThreeChildFamilyQualificationsRater())
            ->push('single_parent_family', new SingleParentFamilyQualificationsRater())
            ->push('minor', new MinorQualificationsRater())
            ->push('disability', new DisabilityQualificationsRater())
            ->push('family_disability', new FamilyDisabilityQualificationsRater());
    }

    public function register()
    {
        $this->app->singleton(Sox4765QualificationsRaterRegistry::class);
    }
}
