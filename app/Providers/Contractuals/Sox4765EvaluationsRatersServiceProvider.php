<?php

namespace App\Providers\Contractuals;

use App\Services\Contractuals\EvaluationRaters\Sox4765\DegreesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox4765\DisabilitiesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox4765\DoctoratesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox4765\ExperiencesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox4765\FamilyDisabilitiesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox4765\MinorsEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox4765\MultiChildFamiliesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox4765\PostgraduatesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox4765\SingleParentFamiliesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox4765\ThreeChildFamiliesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox4765\UnemploymentsEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox4765EvaluationsRaterRegistry;
use Illuminate\Support\ServiceProvider;


class Sox4765EvaluationsRatersServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->app->make(Sox4765EvaluationsRaterRegistry::class)
            ->push('degrees', new DegreesEvaluationsRater())
            ->push('postgraduates', new PostgraduatesEvaluationsRater())
            ->push('doctorates', new DoctoratesEvaluationsRater())
            ->push('experiences', new ExperiencesEvaluationsRater())
            ->push('unemployments', new UnemploymentsEvaluationsRater())
            ->push('multi_child_families', new MultiChildFamiliesEvaluationsRater())
            ->push('three_child_families', new ThreeChildFamiliesEvaluationsRater())
            ->push('single_parent_families', new SingleParentFamiliesEvaluationsRater())
            ->push('minors', new MinorsEvaluationsRater())
            ->push('disabilities', new DisabilitiesEvaluationsRater())
            ->push('family_disabilities', new FamilyDisabilitiesEvaluationsRater());
    }

    public function register()
    {
        $this->app->singleton(Sox4765EvaluationsRaterRegistry::class);
    }
}
