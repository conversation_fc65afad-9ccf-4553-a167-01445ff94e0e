<?php

namespace App\Providers\Contractuals;

use App\Services\Contractuals\ContestRankers\ContestRankerRegistry;
use App\Services\Contractuals\ContestRankers\Sox2190ContestRanker;
use App\Services\Contractuals\ContestRankers\Sox4765ContestRanker;
use Illuminate\Support\ServiceProvider;

class ContestRankerServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->app->make(ContestRankerRegistry::class)
            ->push(1, new Sox2190ContestRanker())
            ->push(2, new Sox4765ContestRanker());
    }

    public function register()
    {
        $this->app->singleton(ContestRankerRegistry::class);
    }
}
