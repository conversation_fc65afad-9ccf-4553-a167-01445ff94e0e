<?php

namespace App\Providers\Contractuals;

use App\Services\Contractuals\EvaluationRaters\Sox2190\DegreesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox2190\DisabilitiesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox2190\ExperiencesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox2190\FamilyDisabilitiesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox2190\MinorsEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox2190\MultiChildFamiliesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox2190\SingleParentFamiliesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox2190\ThreeChildFamiliesEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox2190\UnemploymentsEvaluationsRater;
use App\Services\Contractuals\EvaluationRaters\Sox2190EvaluationsRaterRegistry;
use Illuminate\Support\ServiceProvider;

class Sox2190EvaluationsRatersServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->app->make(Sox2190EvaluationsRaterRegistry::class)
            ->push('degrees', new DegreesEvaluationsRater())
            ->push('experiences', new ExperiencesEvaluationsRater())
            ->push('unemployments', new UnemploymentsEvaluationsRater())
            ->push('multi_child_families', new MultiChildFamiliesEvaluationsRater())
            ->push('three_child_families', new ThreeChildFamiliesEvaluationsRater())
            ->push('single_parent_families', new SingleParentFamiliesEvaluationsRater())
            ->push('minors', new MinorsEvaluationsRater())
            ->push('disabilities', new DisabilitiesEvaluationsRater())
            ->push('family_disabilities', new FamilyDisabilitiesEvaluationsRater());
    }

    public function register()
    {
        $this->app->singleton(Sox2190EvaluationsRaterRegistry::class);
    }
}
