<?php

namespace App\Providers\Contractuals;

use App\Services\Contractuals\ApplicationRaters\ApplicationRaterRegistry;
use App\Services\Contractuals\ApplicationRaters\Sox4765ApplicationRater;
use App\Services\Contractuals\ApplicationRaters\Sox2190ApplicationRater;
//use App\Services\Contractuals\EvaluationRaters\Sox2190EvaluationsRaterRegistry;
use App\Services\Contractuals\QualificationRaters\Sox4765QualificationsRaterRegistry;
use Illuminate\Support\ServiceProvider;

class ApplicationRatersServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->app->make(ApplicationRaterRegistry::class)
            //->push(1, new Sox2190ApplicationRater(resolve(Sox2190EvaluationsRaterRegistry::class)))
            ->push(2, new Sox4765ApplicationRater(resolve(Sox4765QualificationsRaterRegistry::class)));
    }

    public function register()
    {
        $this->app->singleton(ApplicationRaterRegistry::class);
    }
}
