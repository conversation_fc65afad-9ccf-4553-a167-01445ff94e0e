<?php

namespace App\Providers;

use App\Listeners\PerformPostLoginActions;
use App\Models\Contractuals\Verification;
use Illuminate\Auth\Events\Login;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        Login::class => [
            PerformPostLoginActions::class,
        ],
    ];

    public function boot()
    {
        parent::boot();

        Verification::saving(function ($verification) {
            $verification->user_id = Auth::id();
        });
    }
}
