<?php

namespace App\Providers;

use App\Services\SummerCamps\Protocol\ProtocolNumberGenerator;
use App\Services\SummerCamps\Protocol\SequentialProtocolNumberGenerator;
use Barryvdh\Debugbar\ServiceProvider as DebugbarServiceProvider;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\ServiceProvider;
use Laravel\Passport\Passport;

class AppServiceProvider extends ServiceProvider
{
    public function boot()
    {
        Passport::withoutCookieSerialization();

        Collection::macro('recursive', function () {
            return $this->map(function ($value) {
                if (is_array($value) || is_object($value)) {
                    return collect($value)->recursive();
                }

                return $value;
            });
        });

        Collection::macro('sortByGreek', function ($column) {
            return $this->sortBy(function ($item) use ($column) {
                $str = data_get($item, $column);
                $normalized = \Normalizer::normalize($str, \Normalizer::FORM_D);
                $strWithoutDiacritics = preg_replace('/\p{Mn}/u', '', $normalized);

                return $strWithoutDiacritics;
            });
        });

        JsonResource::withoutWrapping();

        if ($this->app->environment('local')) {
            Mail::alwaysTo('<EMAIL>');
        }
    }

    public function register()
    {
        if (App::environment('local')) {
            $this->app->register(DebugbarServiceProvider::class);
        }

        $this->app->bind(ProtocolNumberGenerator::class, SequentialProtocolNumberGenerator::class);
    }
}
