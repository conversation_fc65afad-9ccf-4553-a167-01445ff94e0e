<?php

namespace App\Models\Traits\User;

use App\Models\CustomRelations\UserPermissionRelation;
use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

trait HasRoles
{
    public function hasRole($role): bool
    {
        if (is_string($role)) {
            return $this->roles->contains('name', $role);
        } elseif (is_array($role)) {
            $role = Role::whereIn('name', $role)->get();
        }

        // We have a collection so we do an intersect with the user's roles collection
        return (bool) $role->intersect($this->roles)->count();
    }

    public function isAdmin(string $appName = ''): bool
    {
        if (! $appName) {
            return $this->hasRole('admin');
        }

        return $this->hasRole($appName.'.admin');

    }

    public function isAdminUnit(string $appName): bool
    {
        if ($this->isAdmin($appName) || $this->isAdmin()) {
            return false;
        }

        return $this->hasRole($appName.'.adminUnit');
    }

    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class);
    }

    public function permissions(): UserPermissionRelation
    {
        return $this->relatesTo(Permission::class, UserPermissionRelation::class);
    }
}
