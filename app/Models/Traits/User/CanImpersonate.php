<?php

namespace App\Models\Traits\User;

use Illuminate\Support\Facades\Session;

trait CanImpersonate
{
    public function startImpersonating($user)
    {
        if ($this->isAdmin() && ! $user->isAdmin()) {
            Session::put('impersonated_by', $this->id);
            Session::put('impersonate', $user->id);

            if ($user->unit_id === null) {
                $user->update(['unit_id' => $user->primary_unit_id]);
            }

            activity()
                ->causedBy($this)
                ->performedOn($user)
                ->log('impersonate');

            return true;
        }

        return false;
    }

    public function stopImpersonating()
    {
        $adminUser = Session::get('impersonated_by');

        Session::forget('impersonated_by');
        Session::forget('impersonate');

        activity()
            ->causedBy($adminUser)
            ->performedOn($this)
            ->log('impersonate-end');
    }

    public function isImpersonating()
    {
        return Session::has('impersonate');
    }
}
