<?php

namespace App\Models\Traits\User;

use App\Models\Unit;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

trait HasUnits
{
    public function scopeOfUnit(Builder $query, Unit $unit): Builder
    {
        return $query->where('unit_id', '=', $unit->id);
    }

    public function scopeOfPrimaryUnit(Builder $query, Unit $unit): Builder
    {
        return $query->where('primary_unit_id', '=', $unit->id);
    }

    public function hasPrimaryUnit(): bool
    {
        return $this->primary_unit_id !== null;
    }

    public function hasAssignedUnit(): bool
    {
        return $this->unit_id !== null;
    }

    public function attachPrimaryUnit(int $primaryUnitId): void
    {
        if ($this->units()->count() <= 1) {
            // If user has only one unit, then detach old and attach new
            $this->units()->sync([$primaryUnitId]);
        } else {
            // If user has more than one units, attach new primary unit if not already attached
            $unitIds = $this->units()->get()
                ->map(fn ($unit) => $unit->id)
                ->push($primaryUnitId)
                ->unique()
                ->toArray();

            $this->units()->sync($unitIds);
        }
    }

    public function detachSecondaryUnit(Unit $unit): void
    {
        $this->units()->detach($unit->id);

        // If the detached unit was assigned as user's primary unit
        // then both primary and default units will be removed
        // FIXME maybe I don't really need this if statement because this method regards to secondary units
        //    and secondary units never being assigned as primary units
        if ($this->primary_unit_id === $unit->id) {
            $this->update(['primary_unit_id' => null]);
            $this->update(['unit_id' => null]);
        }

        // If the detached unit was assigned as user's default unit,
        // then assign the user's primary unit as its new default unit
        if ($this->unit_id === $unit->id) {
            $this->update(['unit_id' => $this->primary_unit_id]);
        }
    }

    public function sameUnit(Model $related): bool
    {
        throw_if(! isset($related->unit_id), new \Exception('Model does not belongs to a unit'));

        return in_array($related->unit_id, $this->units()->pluck('id')->all());
    }

    public function sameGdirectorate(Model $related): bool
    {
        throw_if(! isset($related->unit_id), new \Exception(get_class($related).' does not have a unit_id'));
        throw_if(! method_exists($related, 'unit'), new \Exception(get_class($related).' does not implement a unit() relationship'));

        return $this->getGdirectorates()->contains($related->unit)
            || $this->getGdirectorates()->contains($related->unit->getParentGdirectorate());
    }

    public function getGdirectorates(): Collection
    {
        return $this->units()->get()->map(function (Unit $unit) {
            if ($unit->isGdirectorate()) {
                return $unit;
            }

            return $unit->getParentGdirectorate();
        })->unique();
    }

    public function getGdirectorate(): ?Unit
    {
        $userUnit = $this->unit;
        if ($userUnit->isGdirectorate()) {
            return $userUnit;
        }

        return $userUnit->getParentGdirectorate();
    }

    /**
     * User's assigned unit (changes w/ switch-unit).
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'unit_id');
    }

    public function units(): BelongsToMany
    {
        return $this->belongsToMany(Unit::class);
    }

    public function primaryUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'primary_unit_id');
    }

    public function secondaryUnits(): BelongsToMany
    {
        return $this->units()->where('id', '!=', $this->primary_unit_id);
    }
}
