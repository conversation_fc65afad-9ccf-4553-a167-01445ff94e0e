<?php

namespace App\Models\Traits;

use Illuminate\Support\Str;
use ReflectionClass;

trait HasParentModel
{
    public function getTable()
    {
        if ($this->table === null) {
            return str_replace('\\', '', Str::snake(Str::plural(class_basename($this->getParentClass()))));
        }

        return $this->table;
    }

    public function getForeignKey()
    {
        return Str::snake(class_basename($this->getParentClass())).'_'.$this->primaryKey;
    }

    public function joiningTable($related, $instance = null)
    {
        $relatedClassName = method_exists((new $related), 'getClassNameForRelationships')
            ? (new $related)->getClassNameForRelationships()
            : class_basename($related);
        $models = [
            Str::snake($relatedClassName),
            Str::snake($this->getClassNameForRelationships()),
        ];
        sort($models);

        return strtolower(implode('_', $models));
    }

    public function getMorphClass()
    {
        //        if ($this->parentHasHasChildrenTrait()) {
        //            $parentClass = $this->getParentClass();
        //
        //            return (new $parentClass)->getMorphClass();
        //        }

        return parent::getMorphClass();
    }

    protected function getClassNameForRelationships()
    {
        return class_basename($this->getParentClass());
    }

    protected function getParentClass()
    {
        static $parentClassName;

        return $parentClassName ?: $parentClassName = (new ReflectionClass($this))->getParentClass()->getName();
    }
}
