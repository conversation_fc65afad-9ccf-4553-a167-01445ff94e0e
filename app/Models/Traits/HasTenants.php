<?php

namespace App\Models\Traits;

use Exception;
use Illuminate\Database\Eloquent\Builder;

trait HasTenants
{
    protected static function bootHasTenants()
    {
        if (! app()->runningInConsole()) {
            static::addGlobalScope('ofTenant', function (Builder $builder) {
                static::userBelongsToUnitGuard();
                static::modelBelongsToUnitGuard();

                if (
                    auth()->user()->cannot(static::getApplicationName().'.'.'readAll')
                    && auth()->user()->cannot(static::getApplicationName().'.'.'admin')
                    && ! auth()->user()->isAdmin()
                ) {
                    // Scope is applied only if user has NOT "readAll" permissions, or is NOT "admin", or is NOT "appAdmin"
                    if (auth()->user()->can(static::getApplicationName().'.'.'readGd')) {
                        // If user has "readGd" permissions
                        if (method_exists(new self, 'unit')) {
                            // If model belongs to a single unit
                            $builder->whereHas('unit', function ($q) {
                                $q->from('laravel.units')->whereIn(
                                    'units.id',
                                    auth()->user()->unit->getParentGdirectorate()->children()->pluck('id')
                                );
                            });
                        } else {
                            // If model belongs to many units
                            $builder->whereHas('units', function ($q) {
                                $q->from('laravel.units')->whereIn(
                                    'units.id',
                                    auth()->user()->unit->getParentGdirectorate()->children()->pluck('id')
                                );
                            });
                        }

                        // FIXME: we basically want not adminUnit but adminUnitExclusive
                    } elseif (auth()->user()->can(static::getApplicationName().'.'.'adminUnit')) {
                        if (method_exists(new self, 'unit')) {
                            // If model belongs to a single unit
                            $builder->whereHas('unit', function ($q) {
                                $q->from('laravel.units')->where('units.id', auth()->user()->unit_id);
                            });
                        } else {
                            // If model belongs to many units
                            $builder->whereHas('units', function ($q) {
                                $q->from('laravel.units')->where('units.id', auth()->user()->unit_id);
                            })
                                ->withCount('units')
                                ->having('units_count', '=', 1);
                        }

                    } else {
                        // If user has normal "read" permissions
                        if (method_exists(new self, 'unit')) {
                            // If model belongs to a single unit
                            $builder->whereHas('unit', function ($q) {
                                $q->from('laravel.units')->where('units.id', auth()->user()->unit_id);
                            });
                        } else {
                            // If model belongs to many units
                            $builder->whereHas('units', function ($q) {
                                $q->from('laravel.units')->where('units.id', auth()->user()->unit_id);
                            });
                        }
                    }
                }
            });
        }
    }

    public function scopeWithoutTenants(Builder $query)
    {
        return $query->withoutGlobalScope('ofTenant');
    }

    /**
     * Check if user belongs to a unit.
     *
     * @throws Exception
     */
    protected static function userBelongsToUnitGuard(): void
    {
        if (auth()->guest()) {
            throw new Exception('User is not authenticated!');
        }

        if (! auth()->user()->hasAssignedUnit() && ! auth()->user()->isAdmin()) {
            throw new Exception('Unit has not been assigned to user '.auth()->user()->username);
        }
    }

    /**
     * Check if the model belongs to one or more units.
     *
     * @throws Exception
     */
    protected static function modelBelongsToUnitGuard(): void
    {
        if (! (method_exists(new self, 'unit') xor method_exists(new self, 'units'))) {
            throw new Exception('Model "'.self::class.'" has not a unit(s) relationship or it has both');
        }
    }

    /**
     * Get the application name from request.
     */
    protected static function getApplicationName(): string
    {
        return static::$applicationName ?? request()->segment(1);
    }
}
