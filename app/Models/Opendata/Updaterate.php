<?php

namespace App\Models\Opendata;

use App\Models\Traits\SoftDeletesHistoric;
use App\Models\Traits\LogsAttributeActivity;
use Illuminate\Database\Eloquent\Model;

class Updaterate extends Model
{
    use SoftDeletesHistoric, LogsAttributeActivity;

    protected $connection = 'mysql_opendata';

    protected $fillable = ['name', 'description'];

    public function datasets()
    {
        return $this->hasMany(Dataset::class);
    }
}
