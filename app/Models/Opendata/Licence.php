<?php

namespace App\Models\Opendata;

use App\Models\Traits\SoftDeletesHistoric;
use App\Models\Traits\LogsAttributeActivity;
use Illuminate\Database\Eloquent\Model;

class Licence extends Model
{
    use SoftDeletesHistoric, LogsAttributeActivity;

    protected $connection = 'mysql_opendata';

    protected $fillable = ['name', 'description'];

    protected $appends = ['fullname'];

    public function getFullnameAttribute($value)
    {
        return $this->name.' : '.$this->description;
    }

    public function datasets()
    {
        return $this->hasMany(Dataset::class);
    }
}
