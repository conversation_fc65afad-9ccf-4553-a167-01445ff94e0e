<?php

namespace App\Models\Opendata;

use App\Models\Traits\SoftDeletesHistoric;
use App\Models\Traits\LogsAttributeActivity;
use Illuminate\Database\Eloquent\Model;

class Filetype extends Model
{
    use SoftDeletesHistoric, LogsAttributeActivity;

    protected $connection = 'mysql_opendata';

    protected $fillable = ['name', 'description', 'machine_readable'];

    public function datasets()
    {
        return $this->belongsToMany(Dataset::class)->withTimestamps();
    }
}
