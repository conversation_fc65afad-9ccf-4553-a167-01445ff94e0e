<?php

namespace App\Models\Personnel;

use App\Models\Unit;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Position extends Model
{
    protected $connection = 'mysql_personnel';

    protected $fillable = ['name'];

    protected $appends = ['unfilled'];

    public function getUnfilledAttribute($value)
    {
        return $this->attributes['org_nr'] + $this->attributes['epet_pros_nr'] - $this->attributes['epet_yphr_nr'] - $this->attributes['desm_nr'];
    }

    public function scopeOverfilled($query)
    {
        return $query->whereraw('(org_nr + epet_pros_nr) < (epet_yphr_nr + desm_nr)');
    }

    public function queryPosition($request): Builder
    {
        $query = $this->query();

        // Initialize a collection of specialization ids
        $specializations = collect([]);

        //ΦΙΛΤΡΑ ΕΙΔΙΚΟΤΗΤΑΣ
        if (! empty($request->specialization)) {
            //ΕΙΔΙΚΟΤΗΤΑ
            $specialization = Specialization::find($request->specialization);
            //$occupation = Specialization::where('id', '=', $specialization_id)->get(array('occupation_id'));
            if (is_null($specialization->specialization_parent_id)) {
                // Main specialization (ΚΛΑΔΟΣ)

                // First get child specializations
                $specializations = Specialization::where('specialization_parent_id', '=',
                    $request->specialization)->pluck('id');

                // Add parent
                $specializations->prepend($request->specialization);
            } else {
                // Just a child specialization
                $specializations = collect($request->specialization);
            }
        } elseif (! empty($request->hroffice)) { //ΓΡΑΦΕΙΟ ΔΙΟΙΚΗΤΙΚΟΥ ΙΔΑΧ, Α1, ΦΥΛΑΚΕΣ, ΑΡΧΑΙΟΛΟΓΟΙ, ΜΗΧΑΝΙΚΟΙ
            // Get all specializations of Hr Office
            $specializations = Hroffice::find($request->hroffice)->specializations()->pluck('id');
        }

        // If we have any specialization_id in our collection, add them to the query
        if ($specializations->count()) {
            $query->whereIn('specialization_id', $specializations);
        }

        if (! empty($request->occupation)) { //ΜΟΝΙΜΟΙ - ΙΔΑΧ
            $query->where('positions.occupation_id', $request->occupation);
        }

        if ($request->guards_hidden) {
            $query->whereNotIn('specialization_id', Specialization::ofAreaPositions()->pluck('id'));
        }

        //ΦΙΛΤΡΑ ΥΠΗΡΕΣΙΑΣ
        if (! empty($request->unit_id)) { //ΥΠΗΡΕΣΙΑ
            $query->where(function (Builder $q) use ($request) {
                $q->whereIn('unit_id', $request->unit_id)->orWhereIn('department_id', $request->unit_id);
            });
        }

        return $query;
    }

    public function employees(): array
    {
        $employees = Employee::permanent()
            ->ofPosition($this->unit_id, $this->department_id)
            ->ofSpecialization($this->specialization_id)
            ->ofOccupation($this->occupation_id)
            ->orderBy('fullname', 'ASC')->get();

        $temporaryEmployees = Employee::temporary()
            ->ofPosition($this->unit_id, $this->department_id)
            ->ofSpecialization($this->specialization_id)
            ->ofOccupation($this->occupation_id)
            ->orderBy('fullname', 'ASC')->get();

        $notPlacedEmployees = Employee::notPlaced()
            ->ofPosition($this->unit_id, $this->department_id)
            ->ofSpecialization($this->specialization_id)
            ->ofOccupation($this->occupation_id)
            ->orderBy('fullname', 'ASC')->get();

        $secondedEmployees = Employee::seconded()
            ->ofUnit($this->unit_id, $this->department_id)
            ->ofSpecialization($this->specialization_id)
            ->ofOccupation($this->occupation_id)
            ->orderBy('fullname', 'ASC')->get();

        return compact('employees', 'temporaryEmployees', 'notPlacedEmployees', 'secondedEmployees');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    public function specialization(): BelongsTo
    {
        return $this->belongsTo(Specialization::class);
    }

    public function occupation(): BelongsTo
    {
        return $this->belongsTo(Occupation::class);
    }
}
