<?php

namespace App\Models\Personnel;

use App\Models\Phonebook\Telephone;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Employee extends Model
{
    use LogsAttributeActivity;

    protected $connection = 'mysql_personnel';

    protected $fillable = [
        'name',
        'surname',
        'email',
        'compass_id',
        'unit_id',
    ];

    public function getWorkEmail()
    {
        if (strpos($this->email, '@culture.gr') !== false || strpos($this->email, '@gga.gov.gr') !== false) {
            return $this->email;
        }

        return '';
    }

    public function scopePermanent($query)
    {
        return $query->where('place', '=', 'ΟΡΓΑΝΙΚΗ');
    }

    public function scopeTemporary($query)
    {
        return $query->where('place', '=', 'ΠΡΟΣΩΠΟΠΑΓΗΣ');
    }

    public function scopeNotPlaced($query)
    {
        return $query->where('place', '!=', 'ΠΡΟΣΩΠΟΠΑΓΗΣ')->where('place', '!=', 'ΟΡΓΑΝΙΚΗ');
    }

    public function scopeSeconded($query)
    {
        return $query->whereraw('((unit_id != position_unit_id) or (department_id != position_department_id))');
    }

    public function scopeOfUnit($query, $unit_id, $department_id = null)
    {
        if (! is_null($department_id)) {
            return $query->where('department_id', $department_id);
        }

        return $query->where('unit_id', $unit_id);
    }

    public function scopeOfPosition($query, $unit_id, $department_id = null)
    {
        if (! is_null($department_id)) {
            return $query->where('position_department_id', $department_id);
        }

        return $query->where('position_unit_id', $unit_id)->whereNull('position_department_id');
    }

    public function scopeOfSpecialization($query, $specialization_id)
    {
        return $query->where('specialization_id', $specialization_id);
    }

    public function scopeOfOccupation($query, $occupation_id)
    {
        return $query->where('occupation_id', $occupation_id);
    }

    public function ranks(): BelongsToMany
    {
        return $this->belongsToMany(Rank::class)->withPivot('unit_id');
    }

    public function specialization(): BelongsTo
    {
        return $this->belongsTo(Specialization::class);
    }

    public function occupation(): BelongsTo
    {
        return $this->belongsTo(Occupation::class);
    }

    public function unit(): BelongsTo
    {
        return $this->setConnection('mysql_main')->belongsTo(Unit::class);
    }

    public function telephones(): HasMany
    {
        return $this->hasMany(Telephone::class);
    }

    public function department(): BelongsTo
    {
        return $this->setConnection('mysql_main')->belongsTo(Unit::class);
    }
}
