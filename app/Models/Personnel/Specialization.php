<?php

namespace App\Models\Personnel;

use Illuminate\Database\Eloquent\Model;

class Specialization extends Model
{
    protected $connection = 'mysql_personnel';

    protected $fillable = [];

    protected $appends = array('compass_name');

    public function getCompassNameAttribute($value)
    {
        return $this->compass_id.': '.$this->fullname;
    }

    public function scopeOfAreaPositions($query)
    {
        return $query->where('area_positions', true);
    }

    public function employees()
    {
        return $this->hasMany(Employee::class);
    }

    public function hroffices()
    {
        return $this->belongstomany(Hroffice::class);
    }

    public function departmentBoards()
    {
        return $this->belongsToMany(DepartmentBoard::class);
    }

    public function occupation()
    {
        return $this->belongsTo(Occupation::class);
    }

    public function specializationType()
    {
        return $this->belongsTo(SpecializationType::class);
    }

    public function specializationParent()
    {
        return $this->belongsTo(Specialization::class, 'specialization_parent_id');
    }

    public function positions()
    {
        return $this->hasMany(Position::class);
    }
}
