<?php

namespace App\Models\SummerCamps;

use App\Models\User;
use CultureGr\Filterer\Filterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;


class PaymentApplication extends Model implements HasMedia
{
    use InteractsWithMedia, Filterable;

    protected $connection = 'mysql_summer_camps';

    protected $fillable = [
        'user_id',
        'season_id',
        'name',
        'surname',
        'father_name',
        'employment_sector_id',
        'employment_type_id',
        'position',
        'payroll_id',
        'personal_phone',
        'mobile_phone',
        'work_phone',
        'email_address',
        'is_submitted',
        'submitted_at',
        'protocol'
    ];

    protected $casts = [
        'is_submitted' => 'boolean',
        'submitted_at' => 'datetime:d-m-Y H:i:s',
        'updated_at' => 'datetime:d-m-Y H:i:s',
    ];

    protected array $filterable = [
        'surname',
        'name',
        'protocol',
        'employment_sector_id',
        'employment_type_id',
        'is_submitted',
    ];

    protected array $sortable = [
        'surname',
        'protocol',
        'employment_sector_id',
        'employment_type_id',
        'is_submitted',
    ];

    public function scopeOfSeason(Builder $query, int $seasonId): Builder
    {
        return $query->where('season_id', $seasonId);
    }

    public function scopeSubmitted(Builder $query): Builder
    {
        return $query->where('is_submitted', 1);
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where('is_submitted', 0);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function season(): BelongsTo
    {
        return $this->belongsTo(Season::class);
    }

    public function employmentSector(): BelongsTo
    {
        return $this->belongsTo(EmploymentSector::class);
    }

    public function employmentType(): BelongsTo
    {
        return $this->belongsTo(EmploymentType::class);
    }

    public function paymentApplicationChildren(): HasMany
    {
        return $this->hasMany(PaymentApplicationChild::class);
    }

    public function belongsToUser(User $user): bool
    {
        return $this->user_id === $user->id;
    }

    public function canBeEdited(): bool
    {
        $season = $this->season;

        return (bool) ($season->start_date <= now() && $season->end_date >= now());
    }
}
