<?php

namespace App\Models\SummerCamps;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentApplicationChild extends Model
{
    protected $connection = 'mysql_summer_camps';

    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('d-m-Y');
    }

    protected $fillable = [
        'payment_application_id',
        'full_name',
        'has_disability',
        'summer_camps',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'has_disability' => 'boolean',
    ];

    public function paymentApplication(): BelongsTo
    {
        return $this->belongsTo(PaymentApplication::class);
    }
}
