<?php

namespace App\Models\SummerCamps;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EmploymentSector extends Model
{
    use HasFactory;

    protected $connection = 'mysql_summer_camps';

    protected $fillable = ['name'];

    public function applications(): HasMany
    {
        return $this->hasMany(Application::class);
    }
}
