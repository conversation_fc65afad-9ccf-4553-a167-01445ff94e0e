<?php

namespace App\Models\Contractuals;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;

class PositionRating extends Model
{
    protected $connection = 'mysql_contractuals';

    protected $guarded = [];

    protected $casts = [
        'locality' => 'boolean',
    ];

    public function scopeWithUnitName(Builder $query)
    {
        $query->addSelect([
            'unit_name' => DB::connection('mysql_contractuals')
                ->table('positions')
                ->join('laravel.units', 'positions.unit_id', '=', 'units.id')
                ->whereColumn('positions.id', '=', 'position_ratings.position_id')
                ->select('units.abbrv')
                ->take(1),
        ]);
    }

    public function scopeWithSpecializationName(Builder $query)
    {
        $query->addSelect([
            'specialization_name' => DB::connection('mysql_contractuals')
                ->table('positions')
                ->join('specializations', 'positions.specialization_id', '=', 'specializations.id')
                ->whereColumn('positions.id', '=', 'position_ratings.position_id')
                ->select('specializations.shortname')
                ->take(1),
        ]);
    }

    public function scopeWithPositionCode(Builder $query)
    {
        $query->addSelect([
            'position_code' => DB::connection('mysql_contractuals')
                ->table('positions')
                ->whereColumn('positions.id', '=', 'position_ratings.position_id')
                ->select('positions.code')
                ->take(1),
        ]);
    }

    public function scopeWithPositionLocation(Builder $query)
    {
        $query->addSelect([
            'position_location' => DB::connection('mysql_contractuals')
                ->table('positions')
                ->whereColumn('positions.id', '=', 'position_ratings.position_id')
                ->select('positions.location')
                ->take(1),
        ]);
    }


    public function application(): BelongsTo
    {
        return $this->belongsTo(Application::class);
    }

    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class);
    }

    public function applicationRating(): BelongsTo
    {
        return $this->belongsTo(ApplicationRating::class);
    }

    public function setCombinedScore($application = null, $applicationRating = null)
    {
        if (is_null($application)) {
            $application = $this->application;
        }
        if (is_null($applicationRating)) {
            $applicationRating = $this->applicationRating;
        }
        $combinedScore = (int)$this->rejected.'#'
            .(int)$application->impediment_eight_months.'#';
        if ($application->applicant_category == 2 || $application->applicant_category == 3) {
            $combinedScore .= $applicationRating->auxiliary_level.'#';
        }

        $combinedScore .= sprintf('%02d', (10 - (int)$this->locality)).'#';

        $combinedScore .= $applicationRating->combined_score;

        $this->combined_score = $combinedScore;
        $this->save();
    }

    public function reject($description)
    {
        $this->rejected = true;
        $this->rejection_description = $description;
        $this->save();
        $this->setCombinedScore();
    }
}
