<?php

namespace App\Models\Contractuals;

class SingleParentFamily extends Qualifiable
{
    protected $fillable = [
        'applicant_id',
        'children',
        'siblings',
        'is_eligible',
    ];

    public function getDescriptiveNameAttribute(): string
    {
        if ($this->is_eligible === null) {
            return "Αρ. τέκνων $this->children, αρ. αδερφών: $this->siblings";
        }

        return $this->is_eligible ? 'ΝΑΙ' : 'ΟΧΙ';
    }

    public function isParent(): bool
    {
        return $this->children >= $this->siblings;
    }
}
