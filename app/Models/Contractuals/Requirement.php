<?php

namespace App\Models\Contractuals;

use App\Models\Traits\LogsAttributeActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Requirement extends Model
{
    use SoftDeletes, LogsAttributeActivity;

    protected $connection = 'mysql_contractuals';

    protected $fillable = ['name', 'rejection_description', 'requirement_type_id'];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    protected $hidden = ['created_at', 'updated_at', 'deleted_at'];

    protected $appends = ['requirement_type_slug'];

    public function getRequirementTypeSlugAttribute()
    {
        return $this->requirementType->qualifiable_type;
    }

    public function isOfType(string $type): bool
    {
        return $this->requirementType->qualifiable_type === $type;
    }

    public function requirementType(): BelongsTo
    {
        return $this->belongsTo(RequirementType::class);
    }

    public function positions(): BelongsToMany
    {
        return $this->belongsToMany(Position::class)->withPivot('auxiliary_level');
    }

    public function evaluations(): HasMany
    {
        return $this->hasMany(Evaluation::class);
    }
}
