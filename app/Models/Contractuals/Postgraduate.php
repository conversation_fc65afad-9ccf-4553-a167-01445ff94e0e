<?php

namespace App\Models\Contractuals;

class Postgraduate extends Qualifiable
{
    protected $fillable = [
        'name',
        'issuer',
        'year',
        'applicant_id',
        'is_integrated',
    ];
    
    protected $casts = [
        'is_integrated' => 'boolean',
    ];

    public function getDescriptiveNameAttribute(): string
    {
        $integrated = $this->is_integrated ? '(ΕΝΙΑΙΟ)' : '';
        
        return "$this->name $integrated";
    }
}
