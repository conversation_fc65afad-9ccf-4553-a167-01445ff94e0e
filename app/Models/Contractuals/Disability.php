<?php

namespace App\Models\Contractuals;

class Disability extends Qualifiable
{
    protected $fillable = [
        'applicant_id',
        'percentage',
        'is_eligible',
    ];

    public function getDescriptiveNameAttribute(): string
    {
        if ($this->is_eligible === null) {
            return "Ποσοστό αναπηρίας: $this->percentage%";
        }

        return $this->is_eligible ? 'NAI (>50%)' : 'ΟΧΙ';
    }
}
