<?php

namespace App\Models\Contractuals;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class PublicApplication extends Model
{
    protected $connection = 'mysql_public_contractuals';

    protected $table = 'applications';

    public function scopeSubmitted(Builder $builder): Builder
    {
        return $builder->where('is_submitted', true);
    }

    public function scopePE(Builder $builder): Builder
    {
        return $builder->where('specialization_type_id', 1);
    }

    public function scopeTE(Builder $builder): Builder
    {
        return $builder->where('specialization_type_id', 2);
    }

    public function scopeDE(Builder $builder): Builder
    {
        return $builder->where('specialization_type_id', 3);
    }

    public function scopeYE(Builder $builder): Builder
    {
        return $builder->where('specialization_type_id', 4);
    }

    public function scopeOfContest(Builder $builder, int $contestId): Builder
    {
        return $builder->where('contest_id', $contestId);
    }
}
