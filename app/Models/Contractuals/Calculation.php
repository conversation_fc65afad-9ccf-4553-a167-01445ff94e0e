<?php

namespace App\Models\Contractuals;

use Carbon\Carbon;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Calculation extends Model implements HasMedia
{
    use InteractsWithMedia;

    public $timestamps = false;

    protected $connection = 'mysql_contractuals';

    protected $fillable = [
        'run_at', 'description',
        'distribution_run',
        'hire_runners_up',
    ];

    protected $casts = [
        'run_at' => 'datetime:d-m-Y H:i',
        'distribution_run' => 'boolean',
        'hire_runners_up' => 'boolean',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d');
    }

    public function scopeOfContest($query, $contestId)
    {
        return $query->where('contest_id', $contestId);
    }

    public function scopeForDistribution(Builder $query): Builder
    {
        return $query->where('distribution_run', true);
    }

    public function scopeNotForDistribution(Builder $query): Builder
    {
        return $query->where('distribution_run', false);
    }

    public function scopeForHirringRunnerUps(Builder $query): Builder
    {
        return $query->where('hire_runners_up', true);
    }

    public function isFinalRun(): bool
    {
        return ! $this->distribution_run;
    }

    public function isDistributionRun(): bool
    {
        return $this->distribution_run;
    }

    public function isLatest(): bool
    {
        return $this->contest()->calculations()->latest('run_at')->first()->id === $this->id;
    }

    public function isHirringRunnerUps(): bool
    {
        return $this->hire_runners_up;
    }

    public function markAsCompleted()
    {
        $this->run_at = Carbon::now();
        $this->save();
    }

    public function timestamp()
    {
        return $this->run_at->toDateTimeString();
    }

    public function contest()
    {
        return $this->belongsTo(Contest::class);
    }

    public function rankings()
    {
        return $this->hasMany(Ranking::class);
    }

    public function employableRankings()
    {
        return $this->hasMany(Ranking::class)->succeeded()->orderBy('rank');
    }

    public function rejectedRankings()
    {
        return $this->hasMany(Ranking::class)->rejected()->orderBy('surname')->orderBy('name')->orderBy('fatherName');
    }
}
