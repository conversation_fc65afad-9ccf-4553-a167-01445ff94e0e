<?php

namespace App\Models\Contractuals;

class LanguageSkill extends Qualifiable
{
    protected $fillable = [
        'name',
        'issuer',
        'language_id',
        'language_level_id',
        'applicant_id',
    ];

    protected $with = ['language', 'languageLevel'];

    public function getDescriptiveNameAttribute(): string
    {
        return "{$this->language->name} ({$this->languageLevel->name})";
    }

    public function language()
    {
        return $this->belongsTo(Language::class);
    }

    public function languageLevel()
    {
        return $this->belongsTo(LanguageLevel::class);
    }
}
