<?php

namespace App\Models\Contractuals;

use App\Enums\Contractuals\EvaluationType;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use App\Models\User;
use Carbon\Carbon;
use CultureGr\Filterer\Filterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class Application extends Model
{
    use Filterable, LogsAttributeActivity, SoftDeletes;

    protected $connection = 'mysql_contractuals';

    protected $fillable = [
        'applicant_id',
        'contest_id',
        'name',
        'surname',
        'fathername',
        'mothername',
        'birthdate',
        'greek_nationality',
        'eu_citizen',
        'policeid_number',
        'policeid_date',
        'afm',
        'doy',
        'amka',
        'street',
        'street_number',
        'postcode',
        'city',
        'phonenumber1',
        'phonenumber2',
        'email',
        'meets_general_requirements',
        'auxiliary_level',
        'has_eight_months_employment',
        'unrestricted',
        'impediment_eight_months',
        'protocol_number',
        'protocol_date',
        'invalidated',
        'invalidation_description',
        'invalidation_description_id',
        'applicant_category',
        'submitted_at',
        'rejected',
        'rejection_description',
        'employed_in',
        'accepted',
        'public_application_id',
        'is_auto_rated',
        'validating_unit_id',
        'rated_at',
        'rated_by',
        'rated_by_unit_id',
    ];

    protected $casts = [
        'invalidated' => 'boolean',
        'rejected' => 'boolean',
        'greek_nationality' => 'boolean',
        'is_auto_rated' => 'boolean',
        'birthdate' => 'date:Y-m-d',
        'protocol_date' => 'date:d-m-Y',
        'submitted_at' => 'date:d-m-Y',
        'locked_at' => 'date:d-m-Y',
        'rated_at' => 'date:d-m-Y',
    ];

    protected $hidden = [
        'created_at',
        //        'updated_at',
        'deleted_at',
    ];

    protected array $filterable = [
        'surname',
        'name',
        'protocol_number',
        'applicant_category',
        'validating_unit_id',
        'rated_by_unit_id',
        'rejected',
        'auxiliary_level',
        'impediment_eight_months',
    ];

    protected array $sortable = [
        'surname',
        'applicant_category',
        'validating_unit_id',
        'rated_by_unit_id',
        'score',
    ];

    public function scopeOfContest(Builder $query, int $contest_id): Builder
    {
        return $query->where('contest_id', $contest_id);
    }

    public function scopeOfSpecializationType($query, $specialization_type_id)
    {
        return $query->where('specialization_type_id', $specialization_type_id);
    }

    // Auto-rated, either manually (i.e when user re-imports application) or automatically (i.e system initial import)
    public function scopeAutoRated(Builder $query): Builder
    {
        return $query->where('is_auto_rated', 1);
    }

    public function scopeNotAutoRated(Builder $query): Builder
    {
        return $query->where('is_auto_rated', 0);
    }

    public function scopeReImported(Builder $query): Builder
    {
        return $query->where('is_auto_rated', 1)->whereNotNull('rated_by');
    }

    // Rated means either rated by the system or by a human being
    public function scopeRated(Builder $query): Builder
    {
        return $query->whereNotNull('rated_at');
    }

    // Evaluated means rated by a human being
    public function scopeEvaluated(Builder $query): Builder
    {
        return $query->whereNotNull('rated_at')->where('is_auto_rated', 0);
    }

    public function scopeNotRated($query): Builder
    {
        return $query->whereNull('rated_at');
    }

    public function scopeRejected($query)
    {
        return $query->where('rejected', 1);
    }

    public function scopeNotRejected($query)
    {
        return $query->whereNull('rejected');
    }

    public function scopeDistributed(Builder $query)
    {
        return $query->whereNotNull('validating_unit_id');
    }

    public function scopeNotDistributed(Builder $query)
    {
        return $query->whereNull('validating_unit_id');
    }

    public function scopeEmployed(Builder $query)
    {
        return $query->where(function ($query) {
            $query->whereNotNull('employed_in')->where('accepted', '=', 1);
        });
    }

    public function scopeDeclined(Builder $query)
    {
        return $query->where('accepted', '=', 0);
    }

    public function scopeWithValidator($query)
    {
        return $query->addSelect([
            'validator' => DB::table('laravel.units')
                ->select('abbrv')
                ->whereColumn('id', 'contractuals.applications.validating_unit_id')
                ->limit(1),
        ]);
    }

    public function scopeWithRater($query)
    {
        return $query->addSelect([
            'rater' => DB::table('laravel.users')
                ->select('name')
                ->whereColumn('id', 'contractuals.applications.rated_by')
                ->limit(1),
            'rater_unit' => DB::table('laravel.users')
                ->join('laravel.units', 'laravel.users.primary_unit_id', '=', 'laravel.units.id')
                ->join('laravel.role_user', 'laravel.users.id', '=', 'laravel.role_user.user_id')
                ->join('laravel.roles', 'laravel.roles.id', '=', 'laravel.role_user.role_id')
                ->selectRaw('IF(laravel.roles.name="contractuals.admin", "ΚΕΝΤΡΙΚΗ ΕΠΙΤΡΟΠΗ", laravel.units.abbrv)')
                ->whereColumn('laravel.users.id', '=', 'contractuals.applications.rated_by')
                ->limit(1),
        ]);
    }

    public function scopeWithScore($query)
    {
        return $query->addSelect([
            'score' => DB::connection('mysql_contractuals')
                ->table('application_ratings')
                ->select('score')
                ->whereColumn('application_ratings.application_id', '=', 'applications.id')
                ->where('rejected', 0)
                ->latest('evaluation_number')
                ->limit(1),
        ]);
    }

    public function scopeWithInvalidationDescription($query)
    {
        return $query->addSelect([
            'invalidation_description' => DB::connection('mysql_contractuals')
                ->table('invalidation_descriptions')
                ->select('name')
                ->whereColumn('invalidation_descriptions.id', '=', 'applications.invalidation_description_id')
                ->limit(1),
        ]);
    }

    public function scopeWithSpecializationTypeName(Builder $query)
    {
        return $query->addSelect([
            'specialization_type_name' => DB::connection('mysql_contractuals')
                ->table('specialization_types')
                ->select('name')
                ->whereColumn('specialization_types.id', 'applications.applicant_category')
                ->limit(1),
        ]);
    }

    public function scopeEvaluatedByUnit(Builder $query, string $unitId)
    {
        return $query->where(function ($query) use ($unitId) {
            $query->where('validating_unit_id', $unitId)
                ->orWhere('rated_by_unit_id', $unitId);
        });
    }

    public function isOfGreekNationality(): bool
    {
        return (bool) $this->greek_nationality;
    }

    public function isLocked()
    {
        return (bool) $this->locked_at;
    }

    public function isAutoRated(): bool
    {
        return (bool) $this->is_auto_rated;
    }

    public function isReImported(): bool
    {
        return $this->isAutoRated() && $this->rated_by !== null;
    }

    public function isRated(): bool
    {
        return $this->rated_at !== null;
    }

    public function isNotRated(): bool
    {
        return ! $this->isRated();
    }

    public function isRejected(): bool
    {
        return (bool) $this->rejected;
    }

    public function isInvalidated(): bool
    {
        return (bool) $this->invalidated;
    }

    public function lock(): void
    {
        $this->locked_at = Carbon::now();
        $this->save();
    }

    public function unlock(): void
    {
        $this->locked_at = null;
        $this->save();
    }

    // Menas auto rated by the system
    public function markAsInitiallyImported()
    {
        // TODO: Check if invalidation/rejection status should be reset
        $this->is_auto_rated = true;
        $this->rated_at = now();
        $this->rated_by = null;
        $this->rated_by_unit_id = null;
        $this->save();

        $evaluations = $this->applicationRatings()->count();
        $lastRating = $this->applicationRatings()->orderByDesc('id')->first();
        $lastRating->evaluation_number = $evaluations;
        $lastRating->evaluation_type = EvaluationType::IMPORT;
        $lastRating->save();
    }

    // Means auto rated by the user
    public function markAsReImported()
    {
        // TODO: Check if invalidation/rejection status should be reset
        $authUser = auth()->user();

        $this->is_auto_rated = true;
        $this->rated_at = now();
        $this->rated_by = $authUser->id;
        $this->rated_by_unit_id = $authUser->unit_id;
        $this->save();

        $evaluations = $this->applicationRatings()->count();
        $lastRating = $this->applicationRatings()->orderByDesc('id')->first();
        $lastRating->evaluation_number = $evaluations;
        $lastRating->evaluation_type = EvaluationType::RESET;
        $lastRating->evaluated_by = $authUser->id;
        $lastRating->evaluated_by_unit_id = $authUser->unit_id;
        $lastRating->evaluated_by_admin = $authUser->hasRole('contractuals.admin');
        $lastRating->save();
    }

    public function markAsApprovedEvaluation(): void
    {
        $authUser = auth()->user();

        $this->is_auto_rated = false;
        $this->rated_at = now();
        $this->rated_by = $authUser->id;
        $this->rated_by_unit_id = $authUser->unit_id;
        $this->save();

        $evaluations = $this->applicationRatings()->count();
        $lastRating = $this->applicationRatings()->orderByDesc('id')->first();
        $lastRating->evaluation_number = $evaluations;
        $lastRating->evaluation_type = EvaluationType::APPROVE;
        $lastRating->evaluated_by = $authUser->id;
        $lastRating->evaluated_by_unit_id = $authUser->unit_id;
        $lastRating->evaluated_by_admin = $authUser->hasRole('contractuals.admin');
        $lastRating->save();
    }

    // Either approve auto rating or rate manually
    public function markAsEvaluated(): void
    {
        $authUser = auth()->user();

        $this->is_auto_rated = false;
        $this->rated_at = now();
        $this->rated_by = $authUser->id;
        $this->rated_by_unit_id = $authUser->unit_id;
        $this->save();

        $evaluations = $this->applicationRatings()->count();
        $lastRating = $this->applicationRatings()->orderByDesc('id')->first();
        $lastRating->evaluation_number = $evaluations;
        $lastRating->evaluation_type = EvaluationType::MANUAL;
        $lastRating->evaluated_by = $authUser->id;
        $lastRating->evaluated_by_unit_id = $authUser->unit_id;
        $lastRating->evaluated_by_admin = $authUser->hasRole('contractuals.admin');
        $lastRating->save();
    }

    // Either disapprove auto rating or undo manual rating
    public function markAsUnderEvaluation()
    {
        // TODO: Check if invalidation/rejection status should be reset
        //        if (! $this->isAutoRated()) {
        //            $this->validating_unit_id = null;
        //        }

        $this->rejected = null;
        $this->rejection_description = null;
        $this->rated_at = null;
        $this->is_auto_rated = false;
        $this->rated_by = null;
        $this->rated_by_unit_id = null;

        $this->save();
    }

    // TODO: remove this when the new rating system is in place
    public function initializeEvaluations()
    {
        $positions = $this->positions()->get();
        $qualifications = $this->qualifications()->get();

        $evaluations = collect([]);
        $positions->each(function ($position) use ($qualifications, $evaluations) {
            $evaluations->push($qualifications->map(function ($qualification) use ($position) {
                return [
                    'application_id' => $this->id,
                    'qualification_id' => $qualification->id,
                    'position_id' => $position->id,
                    'requirement_id' => null,
                    'auxiliary_level' => 0,
                    'relevant' => false,
                    'points' => 0.00,
                ];
            }));
        });

        $instances = collect([]);

        $positions->each(function ($position) {
            $position->applications()->updateExistingPivot($this->id, ['auxiliary_level' => 0]);
        });

        $evaluations->flatten(1)->each(function ($evaluation) use ($instances) {
            $instances->push(Evaluation::create($evaluation));
        });

        return $instances;
    }

    // TODO: remove this when the new rating system is in place
    public function clearEvaluations()
    {
        $this->evaluations()->delete();

        //        foreach ($this->positions as $position) {
        //            $this->positions()->updateExistingPivot($position->id, ['auxiliary_level' => null]);
        //        }
    }

    // TODO: remove this when the new rating system is in place
    public function clearRating()
    {
        // If we roll back an application that was "completed" and not auto-rated, then also clear the validating unit.
        // Application roll back is only done by contractuals admins, so the validating unit should be reset.
        if (! $this->is_auto_rated) {
            $this->validating_unit_id = null;
        }
        $this->rejected = null;
        $this->rejection_description = null;
        $this->rated_at = null;
        $this->is_auto_rated = false;
        $this->rated_by = null;
        $this->rated_by_unit_id = null;
        $this->save();

        foreach ($this->positions()->get() as $position) {
            $this->positions()->updateExistingPivot($position->id, [
                'rejected' => null,
                'rejection_description' => null,
                'score' => null,
            ]);
        }

        $this->ratings()->delete();

        // see https://stackoverflow.com/questions/37539275/updated-at-column-ambigious-while-updating-with-eloquent
        // see https://github.com/laravel/framework/issues/1069
        \DB::connection('mysql_contractuals')
            ->table('evaluations')
            ->where('evaluations.application_id', $this->id)
            ->join('qualifications', 'evaluations.qualification_id', 'qualifications.id')
            ->update(['points' => 0.00]);
    }

    // TODO: remove this when the new rating system is in place
    public function hasEvaluations()
    {
        return $this->evaluations()->count() === 0 ? false : true;
    }

    public function getQualificationTypes(?array $filters = null): array
    {
        $types = $this->qualifications->pluck('type')->unique();

        if ($filters === null) {
            return $types->toArray();
        }

        return $types->filter(function ($type) use ($filters) {
            return in_array($type, $filters, true);
        })->toArray();
    }

    // TODO: remove this (find usages)
    public function getAuxiliaryLevel(Position $position): ?int
    {
        //        return $this->positions()->find($position->id)->pivot->auxiliary_level;
        return $this->auxiliary_level;
    }

    // TODO: remove this when the new rating system is in place. Find usages
    public function updateAuxiliaryLevel(int $auxiliaryLevel, Position $position)
    {
        $this->positions()->updateExistingPivot($position->id, ['auxiliary_level' => $auxiliaryLevel]);
    }

    // TODO: remove this when the new rating system is in place. Why? We need to store this
    public function updateLocality(bool $locality, Position $position)
    {
        $this->positions()->updateExistingPivot($position->id, ['locality' => $locality]);
    }

    /*------------------------------------------------------------------------
    | Helper functions used by Application Raters
    | -----------------------------------------------------------------------*/

    public function getApplicantAge()
    {
        return Carbon::parse($this->birthdate)->startOfYear()->diffInYears(Carbon::parse($this->contest->end_date));
    }

    public function getMissingPositionRequirements(Position $position)
    {
        $qualificationTypes = $this->qualifications->pluck('type')->unique()
            ->map(fn ($item) => Str::plural($item));

        return $position->requirementsOfAuxiliaryLevel($this->auxiliary_level)->get()
            ->filter(function (Requirement $requirement) use ($qualificationTypes) {
                return (
                    ! ($requirement->isOfType('greek_languages')
                        && $this->isOfGreekNationality()))
                    && $qualificationTypes->doesntContain(
                        $requirement->requirement_type_slug
                    );
            });
    }

    public function meetsPositionRequirements(Position $position): bool
    {
        $qualificationTypes = $this->qualifications
            ->pluck('type')
            ->unique()
            ->map(fn ($item) => Str::plural($item));

        $missingRequirements = $position->requirementsOfAuxiliaryLevel($this->auxiliary_level)->get()
            ->filter(function (Requirement $requirement) use ($qualificationTypes) {
                return (! ($requirement->isOfType('greek_languages') && $this->isOfGreekNationality())) &&
                    $qualificationTypes->doesntContain($requirement->requirement_type_slug);
            });

        return $missingRequirements->isEmpty();
    }

    public function meetsGlobalRequirements()
    {
        $applicantAge = $this->getApplicantAge();

        //      return ($this->eu_citizen || $this->greek_nationality)
        return ($applicantAge >= 18 && $applicantAge <= 70)
            && $this->meets_general_requirements
            && ! $this->invalidated;
    }

    public function hasValidAge(): bool
    {
        $applicantAge = $this->getApplicantAge();

        return $applicantAge >= 18 && $applicantAge <= 70;
    }

    public function reject($description = null)
    {
        $this->rejected = true;
        $this->rejection_description = $description;
        $this->save();

        $this->positions()->each(function ($position) use ($description) {
            $this->positions()->updateExistingPivot(
                $position->id,
                [
                    'rejected' => true,
                    'score' => null,
                    'rejection_description' => [$description],
                ]
            );

            $rating = Rating::updateOrCreate(
                [
                    'application_id' => $this->id,
                    'position_id' => $position->id,
                ],
                [
                    'position_order' => $position->pivot->order,
                    'specialization_type_id' => $this->applicant_category,
                    'birthdate' => $this->birthdate,
                    'rejected' => true,
                    'impediment_eight_months' => $this->impediment_eight_months,
                    'locality' => $position->pivot->locality,
                    'auxiliary_level' => $position->pivot->auxiliary_level,
                    'score' => 0,
                ]
            );
            $rating->setCombinedScore();
        });
    }

    // Deprecated: Only used in Sox2190ApplicationRater
    public function rejectPosition(Position $position, $description = null)
    {
        $this->positions()->updateExistingPivot($position->id, [
            'rejected' => true,
            'rejection_description' => $description,
        ]);
        $rating = Rating::updateOrCreate(
            [
                'application_id' => $this->id,
                'position_id' => $position->id,
            ],
            [
                'position_order' => $position->pivot->order,
                'specialization_type_id' => $this->applicant_category,
                'birthdate' => $this->birthdate,
                'rejected' => true,
                'impediment_eight_months' => $this->impediment_eight_months,
                'locality' => $position->pivot->locality,
                'auxiliary_level' => $position->pivot->auxiliary_level,
                'score' => 0,
            ]
        );
        $rating->setCombinedScore();
    }

    public function updateScore($scores, Position $position)
    {
        $this->positions()->updateExistingPivot($position->id, ['score' => $scores->sum()]);
    }

    /*---------------------------------------------------------------------------------
    |   Helper functions used by eployable diff
    |---------------------------------------------------------------------------------*/

    public function employablePosition()
    {
        $contest = $this->contest;

        if (! $contest->isRanked()) {
            return null;
        }

        $latestCalculation = $contest->calculations()->latest('run_at')->first();

        $employablePositionId = $contest->rankings()
            ->ofCalculation($latestCalculation->id)
            ->where('application_id', $this->id)
            ->first()
            ->employable_in;

        return $this->positions()->find($employablePositionId);
    }

    public function previousEmployablePosition()
    {
        $contest = $this->contest;

        if (! $contest->isRanked()) {
            return null;
        }

        $previousCalculation = $contest->calculations()->latest('run_at')->skip(1)->first();

        $employablePositionId = $contest->rankings()
            ->ofCalculation($previousCalculation->id)
            ->where('application_id', $this->id)
            ->first()
            ->employable_in;

        return $this->positions()->find($employablePositionId);
    }

    /*---------------------------------------------------------------------------------
    |   Relationships
    |---------------------------------------------------------------------------------*/

    public function contest(): BelongsTo
    {
        return $this->belongsTo(Contest::class);
    }

    public function applicant(): BelongsTo
    {
        return $this->belongsTo(Applicant::class);
    }

    public function positions(): BelongsToMany
    {
        return $this->belongsToMany(Position::class)->withPivot(
            'order',
            'score',
            'auxiliary_level',
            'rejected',
            'rejection_description',
            'locality'
        )->orderBy('order');
    }

    public function qualifications(): HasMany
    {
        return $this->hasMany(Qualification::class);
    }

    public function evaluations(): HasManyThrough
    {
        return $this->hasManyThrough(Evaluation::class, Qualification::class);
    }

    public function degrees(): MorphToMany
    {
        return $this->morphedByMany(Degree::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function postgraduates(): MorphToMany
    {
        return $this->morphedByMany(Postgraduate::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function doctorates(): MorphToMany
    {
        return $this->morphedByMany(Doctorate::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function experiences(): MorphToMany
    {
        return $this->morphedByMany(Experience::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function languageSkills(): MorphToMany
    {
        return $this->morphedByMany(LanguageSkill::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function computerSkills(): MorphToMany
    {
        return $this->morphedByMany(ComputerSkill::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function unemployments(): MorphToMany
    {
        return $this->morphedByMany(Unemployment::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function multiChildFamilies(): MorphToMany
    {
        return $this->morphedByMany(MultiChildFamily::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function threeChildFamilies(): MorphToMany
    {
        return $this->morphedByMany(ThreeChildFamily::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function singleParentFamilies(): MorphToMany
    {
        return $this->morphedByMany(SingleParentFamily::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function minors(): MorphToMany
    {
        return $this->morphedByMany(Minor::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function disabilities(): MorphToMany
    {
        return $this->morphedByMany(Disability::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function familyDisabilities(): MorphToMany
    {
        return $this->morphedByMany(FamilyDisability::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function greekLanguages(): MorphToMany
    {
        return $this->morphedByMany(GreekLanguage::class, 'qualifiable', 'qualifications')->withPivot('id');
    }

    public function employedPosition(): BelongsTo
    {
        return $this->belongsTo(Position::class, 'employed_in', 'id');
    }

    public function validatingUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'validating_unit_id', 'id');
    }

    public function rator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rated_by', 'id');
    }

    public function ratings(): HasMany
    {
        return $this->hasMany(Rating::class);
    }

    public function applicationRatings(): HasMany
    {
        return $this->hasMany(ApplicationRating::class);
    }

    public function positionRatings(): HasMany
    {
        return $this->hasMany(PositionRating::class);
    }

    public function invalidationDescription(): BelongsTo
    {
        return $this->belongsTo(InvalidationDescription::class);
    }
}
