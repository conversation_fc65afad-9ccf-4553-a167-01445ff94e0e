<?php

namespace App\Models\Contractuals;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\DB;

class Qualification extends Model
{
    protected $connection = 'mysql_contractuals';

    protected $casts = [
        'deleted_at' => 'datetime',
        'points' => 'float',
    ];

    protected $fillable = [
        'application_id',
        'qualifiable_id',
        'qualifiable_type',
        'points',
    ];

    protected $with = [
        'qualifiable',
    ];

    protected $appends = [
        'type',
    ];

    public function getTypeAttribute()
    {
        return $this->qualifiable->type_slug;
    }

//    public function type(): string
//    {
//        return $this->qualifiable->type_slug;
//    }

    public function isOfType(string $type): bool
    {
        return $this->qualifiable->type_slug === $type;
    }

    public function application(): BelongsTo
    {
        return $this->belongsTo(Application::class);
    }

    public function qualifiable(): MorphTo
    {
        return $this->morphTo();
    }
}
