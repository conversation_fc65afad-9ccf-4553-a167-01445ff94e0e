<?php

namespace App\Models\Contractuals;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Rating extends Model
{
    use HasFactory;

    protected $connection = 'mysql_contractuals';

    protected $guarded = [];

    public function scopeOfApplicationPosition($query, $application_id, $position_id)
    {
        return $query->where('application_id', $application_id)->where('position_id', $position_id);
    }

    public function scopeOfPosition($query, $position_id)
    {
        return $query->where('position_id', $position_id);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function position()
    {
        return $this->belongsTo(Position::class);
    }

    public function setCombinedScore()
    {
        $score = (int)$this->rejected.'#'
            .(int)$this->impediment_eight_months.'#';
        if ($this->specialization_type_id == 2 || $this->specialization_type_id == 3) {
            $score .= $this->auxiliary_level.'#';
        }

        $score .= sprintf('%02d', (10 - (int)$this->locality)).'#'
            .sprintf('%06d', (100000 - $this->score)).'#'
            .sprintf('%06d', $this->unemployments_continued_points).'#'
            .sprintf('%06d', $this->unemployments_points).'#'
            .sprintf('%06d', $this->multi_child_families_points).'#'
            .sprintf('%06d', $this->three_child_families_points).'#'
            .sprintf('%06d', $this->single_parent_families_points).'#'
            .sprintf('%06d', $this->minors_points).'#';

        if ($this->specialization_type_id == 2) {
            $score .= sprintf('%06d', ($this->degrees_points * 100)).'#'
                .sprintf('%06d', ($this->doctorates_points * 100)).'#'
                .sprintf('%06d', ($this->postgraduates_points * 100)).'#'
                .sprintf('%06d', ($this->postgraduates_integrated_points * 100)).'#'
                .sprintf('%06d', ($this->second_degrees_points * 100)).'#'
                .sprintf('%06d', ($this->other_degrees_points * 100)).'#';
        } elseif ($this->specialization_type_id == 3) {
            $score .= sprintf('%06d', ($this->degrees_points * 100)).'#'
                .sprintf('%06d', ($this->second_degrees_points * 100)).'#';
        }

        $score .= sprintf('%06d', $this->experiences_points).'#'
            .sprintf('%06d', $this->disabilities_points).'#'
            .sprintf('%06d', $this->family_disabilities_points).'#'
            .$this->birthdate;

        $this->combined_score = $score;
        $this->save();
    }
}
