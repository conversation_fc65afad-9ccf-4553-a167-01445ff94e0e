<?php

namespace App\Models\Contractuals;

use App\Models\Traits\LogsAttributeActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

abstract class Qualifiable extends Model
{
    use LogsAttributeActivity;

    protected static $PATH = 'App\Models\Contractuals\\';

    protected $connection = 'mysql_contractuals';

    protected $casts = [
        'deleted_at' => 'datetime',
        'verified' => 'boolean',
    ];

    protected $appends = [
        'type_slug',
        'descriptive_name',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    abstract public function getDescriptiveNameAttribute(): string;

    public function getTypeSlugAttribute(): string
    {
        return Str::snake(class_basename($this));
    }

//    public static function findAsset($type, $id)
//    {
//        $fullAssetClass = self::$PATH.Str::studly(Str::singular($type));
//        $asset = $fullAssetClass::find($id);
//
//        return $asset;
//    }

    public function qualifications(): \Illuminate\Database\Eloquent\Relations\MorphMany
    {
        return $this->morphMany(Qualification::class, 'qualifiable');
    }

//    public function verifications()
//    {
//        return $this->morphMany(Verification::class, 'verifiable');
//    }
}
