<?php

namespace App\Models\Contractuals;

use App\Enums\Contractuals\EvaluationType;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ApplicationRating extends Model
{
    protected $connection = 'mysql_contractuals';

    protected $guarded = [];

    protected $casts = [
        'evaluation_type' => EvaluationType::class
    ];

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function positionRatings(): HasMany
    {
        return $this->hasMany(PositionRating::class);
    }

    public function evaluator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'evaluated_by');
    }

    public function evaluatorUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'evaluated_by_unit_id');
    }

    public function setCombinedScore()
    {
        $score = sprintf('%08d', (1000000 - $this->score*100)).'#'
            .sprintf('%06d', $this->unemployments_continued_points).'#'
            .sprintf('%06d', $this->unemployments_points).'#'
            .sprintf('%06d', $this->multi_child_families_points).'#'
            .sprintf('%06d', $this->three_child_families_points).'#'
            .sprintf('%06d', $this->single_parent_families_points).'#'
            .sprintf('%06d', $this->minors_points).'#';

        if ($this->application->applicant_category == 2) {
            $score .= sprintf('%06d', ($this->degrees_points * 100)).'#'
                .sprintf('%06d', ($this->doctorates_points * 100)).'#'
                .sprintf('%06d', ($this->postgraduates_points * 100)).'#'
                .sprintf('%06d', ($this->postgraduates_integrated_points * 100)).'#'
                .sprintf('%06d', ($this->second_degrees_points * 100)).'#'
                .sprintf('%06d', ($this->other_degrees_points * 100)).'#';
        } elseif ($this->application->applicant_category == 3) {
            $score .= sprintf('%06d', ($this->degrees_points * 100)).'#'
                .sprintf('%06d', ($this->second_degrees_points * 100)).'#';
        }

        $score .= sprintf('%06d', $this->experiences_points).'#'
            .sprintf('%06d', $this->disabilities_points).'#'
            .sprintf('%06d', $this->family_disabilities_points).'#'
            .$this->application->birthdate;

        $this->combined_score = $score;
        $this->save();
    }

    public function reject($description)
    {
        $this->score = 0;
        $this->rejected = true;
        $this->rejection_description = $description;
        $this->save();
        $this->setCombinedScore();

        $this->application->positions()->each(function ($position) use ($description) {
            $positionRating = $this->positionRatings()->create([
                'application_id' => $this->application->id,
                'position_id' => $position->id,
                'position_order' => $position->pivot->order,
                'locality' => $position->pivot->locality,
                'rejected' => true,
                'rejection_description' => $description
            ]);
            $positionRating->setCombinedScore();
        });

        $this->application->update([
            'rejected' => true,
            'rejection_description' => $description,
        ]);
    }
}
