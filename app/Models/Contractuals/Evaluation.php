<?php

namespace App\Models\Contractuals;

use Illuminate\Database\Eloquent\Model;

class Evaluation extends Model
{
    protected $connection = 'mysql_contractuals';

    protected $fillable = [
        'application_id',
        'qualification_id',
        'position_id',
        'requirement_id',
        'auxiliary_level',
        'relevant',
        'points',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
        'laravel_through_key' // see https://github.com/laravel/framework/issues/745
    ];

    protected $casts = [
        'relevant' => 'boolean',
    ];

    public function scopeOfPosition($query, Position $position)
    {
        return $query->where('position_id', $position->id);
    }

    public function isOfPosition(int $positionId): bool
    {
        return $this->position_id === $positionId;
    }

    public function scopeOfAuxiliaryLevel($query, $auxiliaryLevel)
    {
        return $query->where('auxiliary_level', $auxiliaryLevel);
    }

    public function isOfAuxiliaryLevel(int $auxiliaryLevel): bool
    {
        return $this->auxiliary_level === $auxiliaryLevel;
    }

    public function type(): string
    {
        return $this->qualification->qualifiable->type_slug;
    }

    public function isOfType(string $type): bool
    {
        return $this->type() === $type;
    }

    public function isRelevant(): bool
    {
        return (null !== $this->requirement_id) || (false !== $this->relevant);
    }

    public function qualification()
    {
        return $this->belongsTo(Qualification::class);
    }

    public function position()
    {
        return $this->belongsTo(Position::class);
    }

    public function requirement()
    {
        return $this->belongsTo(Requirement::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }
}
