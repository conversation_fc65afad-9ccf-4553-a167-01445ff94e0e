<?php

namespace App\Models\Contractuals;

use App\Models\Traits\LogsAttributeActivity;
use Carbon\Carbon;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

class Applicant extends Model
{
    use SoftDeletes, LogsAttributeActivity;

    protected $connection = 'mysql_contractuals';

    protected $fillable = [
        'name',
        'surname',
        'fathername',
        'mothername',
        'policeid_number',
        'policeid_date',
        'afm',
        'doy',
        'amka',
        'birthdate',
        'street',
        'street_number',
        'postcode',
        'city',
        'phonenumber1',
        'phonenumber2',
        'email',
        'eu_citizen',
        'greek_nationality',
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
        'policeid_date' => 'datetime',
        'birthdate' => 'datetime',
    ];

    protected $appends = [
        'age',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d');
    }

    public static function scopeWithAfm($query, $afm)
    {
        return $query->where('afm', $afm);
    }

    public function getAgeAttribute($value)
    {
        return Carbon::parse($this->birthdate)->diffInYears();
    }

    public function hasAppliedToContest($contest)
    {
        $application = \DB::connection('mysql_contractuals')
            ->table('applications')
            ->select('id')
            ->where('applicant_id', $this->id)
            ->where('contest_id', $contest->id)
            ->first();

        if ($application) {
            return true;
        }

        return false;
    }

    public function getAssets($type = null)
    {
        if (isset($type)) {
            return $this->$type;
        }

        return collect([
            $this->degrees,
            $this->postgraduates,
            $this->doctorates,
            $this->greekLanguages,
            $this->languageSkills,
            $this->computerSkills,
            $this->experiences,
            $this->unemployments,
            $this->multiChildFamilies,
            $this->threeChildFamilies,
            $this->singleParentFamilies,
            $this->minors,
            $this->disabilities,
            $this->familyDisabilities,
        ])->flatten();
    }

    /**
     * Add an asset(s) to an applicant.
     *
     * @param  string  $type  the type of the asset (i.e the relationship name)
     * @param  array  $assetsArr
     * @return array An array that contains the created Asset models
     */
    public function addAssets($type, $assetsArr = [])
    {
        return $this->$type()->createMany($assetsArr);
    }

    /**
     * Update an applicant's asset.
     * @param  string  $type  The type of the asset (i.e the relationship name)
     * @param  array  $assetsArr  Contains the http request values
     * @return array|null
     */
    public function updateAssets($type, $assetsArr = [])
    {
        $instances = [];
        if ($assetsArr) {
            foreach ($assetsArr as $asset) {
                $asset = $this->$type()->updateOrCreate(['id' => $asset['id']], Arr::except($asset, ['id']));
                array_push($instances, $asset);
            }
        }

        return empty($instances) ? null : $instances;
    }

    public function latestApplication()
    {
        return $this->applications()->orderBy('id', 'desc')->first();
    }

    public function degrees()
    {
        return $this->hasMany(Degree::class);
    }

    public function postgraduates()
    {
        return $this->hasMany(Postgraduate::class);
    }

    public function doctorates()
    {
        return $this->hasMany(Doctorate::class);
    }

    public function greekLanguages()
    {
        return $this->hasMany(GreekLanguage::class);
    }

    public function languageSkills()
    {
        return $this->hasMany(LanguageSkill::class);
    }

    public function computerSkills()
    {
        return $this->hasMany(ComputerSkill::class);
    }

    public function experiences()
    {
        return $this->hasMany(Experience::class);
    }

    public function unemployments()
    {
        return $this->hasMany(Unemployment::class);
    }

    public function multiChildFamilies()
    {
        return $this->hasMany(MultiChildFamily::class);
    }

    public function threeChildFamilies()
    {
        return $this->hasMany(ThreeChildFamily::class);
    }

    public function singleParentFamilies()
    {
        return $this->hasMany(SingleParentFamily::class);
    }

    public function minors()
    {
        return $this->hasMany(Minor::class);
    }

    public function disabilities()
    {
        return $this->hasMany(Disability::class);
    }

    public function familyDisabilities()
    {
        return $this->hasMany(FamilyDisability::class);
    }

    public function verifications()
    {
        return $this->hasMany(Verification::class);
    }

    public function applications()
    {
        return $this->hasMany(Application::class);
    }
}
