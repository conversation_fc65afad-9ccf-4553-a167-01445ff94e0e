<?php

namespace App\Models\Contractuals;

class Degree extends Qualifiable
{
    protected $fillable = [
        'name',
        'issuer',
        'mark',
        'year',
        'applicant_id',
        'is_primary'
    ];
    
    protected $casts = [
        'is_primary' => 'boolean',
        'mark' => 'float:2'
    ];

    public function getDescriptiveNameAttribute(): string
    {
        $primary = $this->isPrimary() ? '(ΚΥΡΙΟΣ)' : '';

        return "$this->name, Βαθμός $this->mark, Έτος: $this->year $primary";
    }

    public function isPrimary(): bool
    {
        return (bool) $this->is_primary;
    }
}
