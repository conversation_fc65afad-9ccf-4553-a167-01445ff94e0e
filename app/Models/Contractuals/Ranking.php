<?php

namespace App\Models\Contractuals;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;

class Ranking extends Model
{
    protected $connection = 'mysql_contractuals';

    protected $fillable = [
        'contest_id',
        'calculation_id',
        'position_id',
        'application_id',
        'protocol_number',
        'surname',
        'name',
        'fathername',
        'policeid_number',
        'position_order',
        'impediment_eight_months',
        'auxiliary_level',
        'unemployments',
        'multi_child_families_children',
        'multi_child_families_siblings',
        'three_child_families_children',
        'three_child_families_siblings',
        'minors',
        'single_parent_families_children',
        'single_parent_families_siblings',
        'degrees',
        'experiences',
        'disabilities',
        'family_disabilities',
        'unemployments_points',
        'multi_child_families_children_points',
        'multi_child_families_siblings_points',
        'three_child_families_children_points',
        'three_child_families_siblings_points',
        'minors_points',
        'single_parent_families_children_points',
        'single_parent_families_siblings_points',
        'degrees_points',
        'experiences_points',
        'disabilities_points',
        'family_disabilities_points',
        'score',
        'rejected',
        'rank',
        'was_runner_up',
    ];

    protected $casts = [
        'employable' => 'boolean',
        'was_runner_up' => 'boolean',
    ];

    // TODO: add this as a column in rankings table to preserve history
    public function scopeWithRejectionDescription(Builder $query)
    {
        $query->addSelect([
            'rejection_description' => DB::connection('mysql_contractuals')
                ->table('applications')
                ->join('application_position', 'application_position.application_id', '=', 'applications.id')
                ->join('positions', 'positions.id', '=', 'application_position.position_id')
                ->whereColumn('applications.id', '=', 'rankings.application_id')
                ->select('application_position.rejection_description')
                ->take(1),
        ]);
    }

    public function scopeWithEmployableDescription(Builder $query)
    {
        $query->select(
            DB::raw(
                'rankings.*, (CASE WHEN rankings.employable="1" THEN "NAI"
                WHEN rankings.employable_in IS NULL || rankings.employable  THEN "OXI"
                ELSE CONCAT("ΘΕΣΗ ", positions.code)
                END) as employable_description'
            )
        )->leftJoin('positions', 'positions.id', '=', 'rankings.employable_in');
    }

    // TODO: deprecate
//    public function scopeNotRejected(Builder $query): Builder
//    {
//        return $query->where('rejected', 0);
//    }

    public function scopeSucceeded(Builder $query): Builder
    {
        return $query->where('rejected', 0);
    }

//
    public function scopeRejected(Builder $query): Builder
    {
        return $query->where('rejected', 1);
    }

    public function scopeEmployable(Builder $query): Builder
    {
        // I.e rankings.employable = 1
        return $query->where('employable','=',1);
    }

    public function scopeAccepted(Builder $query): Builder
    {
        return $query->where('accepted', '=', 1);
    }

    public function scopeDeclined(Builder $query): Builder
    {
        return $query->where('accepted', '=', 0);
    }

    public function scopeOfPosition(Builder $query, $positionId): Builder
    {
        return $query->where('position_id', $positionId);
    }

    public function scopeOfContest(Builder $query, $contestId): Builder
    {
        return $query->where('contest_id', $contestId);
    }

    public function scopeOfCalculation(Builder $query, int $calculationId): Builder
    {
        return $query->where('calculation_id', $calculationId);
    }

    public function scopeOfApplication(Builder $query, $applicationId): Builder
    {
        return $query->where('application_id', $applicationId);
    }

    public function calculation(): BelongsTo
    {
        return $this->belongsTo(Calculation::class);
    }

    public function application(): BelongsTo
    {
        return $this->belongsTo(Application::class);
    }

    public function employablePosition(): BelongsTo
    {
        return $this->belongsTo(Position::class, 'employable_in', 'id');
    }

    public function positionRating(): BelongsTo
    {
        return $this->belongsTo(PositionRating::class);
    }
}
