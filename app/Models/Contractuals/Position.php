<?php

namespace App\Models\Contractuals;

use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use CultureGr\Filterer\Filterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Position extends Model
{
    use Filterable, LogsAttributeActivity, SoftDeletes;

    // The maximum available auxiliary levels.
    public const AUXILIARY_LEVELS = [
        0 => 'Κύρια Προσόντα',
        1 => 'Προσόντα Α΄ Επικουρίας',
        2 => 'Προσόντα Β΄ Επικουρίας',
    ];

    protected $connection = 'mysql_contractuals';

    protected $fillable = [
        'specialization_id',
        'amount',
        'location',
        'contest_id',
        'unit_id',
        'code',
        'has_locality',
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
        'has_locality' => 'boolean',
    ];

    protected $filterable = [
        'code',
        'specialization_id',
        'location',
        'unit_id',
    ];

    protected $sortable = [
        'code',
        'specialization_id',
        'location',
        'unit_id',
    ];

    protected $hidden = ['created_at', 'updated_at', 'deleted_at'];

    public function scopeOfContest(Builder $query, int $contestId): Builder
    {
        return $query->where('contest_id', '=', $contestId);
    }

    public function scopeWithEmploymentAggregates($query, $calculationId)
    {
        return $query->withCount([
            'rankings as employable_count' => function (Builder $query) use ($calculationId) {
                // @phpstan-ignore-next-line
                $query->ofCalculation($calculationId)->employable();
            },
            'rankings as accepted_count' => function (Builder $query) use ($calculationId) {
                // @phpstan-ignore-next-line
                $query->ofCalculation($calculationId)->employable()->accepted();
            },
            'rankings as declined_count' => function (Builder $query) use ($calculationId) {
                // @phpstan-ignore-next-line
                $query->ofCalculation($calculationId)->employable()->declined();
            },
        ]);
    }

    public function scopeWithUnitName(Builder $query): Builder
    {
        return $query->addSelect([
            'unit_name' => Unit::query()
                ->select(['abbrv'])
                ->whereColumn('id', 'unit_id')
                ->limit(1),
        ]);
    }

    public function scopeWithSpecializationName(Builder $query): Builder
    {
        return $query->addSelect([
            'specialization_name' => Specialization::query()
                ->select(['name'])
                ->whereColumn('id', 'specialization_id')
                ->limit(1),
            'specialization_shortname' => Specialization::query()
                ->select(['shortname'])
                ->whereColumn('id', 'specialization_id')
                ->limit(1),
        ]);
    }

    public function contest(): BelongsTo
    {
        return $this->belongsTo(Contest::class);
    }

    public function specialization(): BelongsTo
    {
        return $this->belongsTo(Specialization::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    public function requirements(): BelongsToMany
    {
        return $this->belongsToMany(Requirement::class)->withPivot('auxiliary_level');
    }

    public function requirementsOfAuxiliaryLevel($auxiliaryLevel): BelongsToMany
    {
        return $this->requirements()->wherePivot('auxiliary_level', $auxiliaryLevel)->with('requirementType');
    }

    public function evaluations(): HasMany
    {
        return $this->hasMany(Evaluation::class);
    }

    public function applications(): BelongsToMany
    {
        return $this->belongsToMany(Application::class)
            ->withPivot('order', 'score', 'auxiliary_level', 'rejected', 'rejection_description', 'locality');
    }

    public function rankings(): HasMany
    {
        return $this->hasMany(Ranking::class);
    }

    public function succeededRankings(): HasMany
    {
        return $this->hasMany(Ranking::class)->succeeded()->orderBy('rank');
    }

    public function rejectedRankings(): HasMany
    {
        return $this->hasMany(Ranking::class)->rejected()->orderBy('surname')->orderBy('name')->orderBy('fatherName');
    }

    public function ratings(): HasMany
    {
        return $this->hasMany(Rating::class);
    }
}
