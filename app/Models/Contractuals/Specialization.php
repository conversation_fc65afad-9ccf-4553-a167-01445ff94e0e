<?php

namespace App\Models\Contractuals;

use App\Models\Traits\LogsAttributeActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Specialization extends Model
{
    use SoftDeletes, LogsAttributeActivity;

    protected $connection = 'mysql_contractuals';

    protected $fillable = [
        'name',
        'shortname',
        'specialization_type_id',
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    protected $hidden = ['created_at', 'updated_at', 'deleted_at'];

    public function specializationType()
    {
        return $this->belongsTo(SpecializationType::class);
    }

    public function positions()
    {
        return $this->hasMany(Position::class);
    }
}
