<?php

namespace App\Models\Contractuals;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class InvalidationDescription extends Model
{
    protected $connection = 'mysql_contractuals';

    protected $fillable = [
        'name',
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::addGlobalScope('deprecated', function (Builder $builder) {
            $builder->whereNotIn('id', [10, 14, 19, 21]);
        });
    }
}
