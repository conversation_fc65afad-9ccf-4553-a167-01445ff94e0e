<?php

namespace App\Models\Contractuals;

use App\Models\Traits\HasTenants;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use Carbon\Carbon;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

class Contest extends Model
{
    use HasTenants, LogsAttributeActivity, SoftDeletes;

    protected $connection = 'mysql_contractuals';

    protected static $applicationName = 'contractuals';

    protected $fillable = [
        'name',
        'description',
        'type_id',
        'protocol_number',
        'protocol_date',
        'ada',
        'start_date',
        'end_date',
        'organization',
        'contract_duration',
        'undersigned_title_first_row',
        'undersigned_title_second_row',
        'undersigned_name',
        'notifications_email',
    ];

    protected $casts = [
        'protocol_date' => 'datetime',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'locked_at' => 'datetime',
        'restricted_at' => 'datetime',
        'rated_at' => 'datetime',
        'ranked_at' => 'datetime',
    ];

    protected $appends = [
        'year',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d');
    }

    public function getYearAttribute()
    {
        return $this->protocol_date !== null ? Carbon::parse($this->protocol_date)->format('Y') : null;
    }

    public function scopeLocked($query)
    {
        return $query->whereNotNull('locked_at');
    }

    public function scopeUnlocked($query)
    {
        return $query->whereNull('locked_at');
    }

    public function scopeRated($query)
    {
        return $query->whereNotNull('rated_at');
    }

    public function scopeUnrated($query)
    {
        return $query->whereNull('rated_at');
    }

    public function scopeRanked($query)
    {
        return $query->whereNotNull('ranked_at');
    }

    public function scopeNotRanked($query)
    {
        return $query->whereNull('ranked_at');
    }

    public function hasSingleUnit(): bool
    {
        return $this->units()->count() === 1;
    }

    public function hasApplications(): bool
    {
        return ! ($this->applications()->count() === 0);
    }

    public function isLocked(): bool
    {
        return ! ($this->locked_at === null);
    }

    public function lock(): void
    {
        $this->locked_at = Carbon::now();
        $this->save();
    }

    public function unlock(): void
    {
        $this->locked_at = null;
        $this->save();
    }

    public function isRestricted(): bool
    {
        return ! ($this->restricted_at === null);
    }

    public function restrict(): void
    {
        $this->restricted_at = Carbon::now();
        $this->save();
    }

    public function unrestrict(): void
    {
        $this->restricted_at = null;
        $this->save();
    }

    public function isRated(): bool
    {
        return ! ($this->rated_at == null);
    }

    public function rate(): void
    {
        $this->rated_at = Carbon::now();
        $this->save();
    }

    public function unrate(): void
    {
        $this->rated_at = null;
        $this->save();
    }

    public function isRanked(): bool
    {
        return ! ($this->ranked_at === null);
    }

    public function hasRankings(): bool
    {
        return $this->rankings()->exists();
    }

    public function setRankings(Collection $rankings)
    {
        foreach (array_chunk($rankings->toArray(), 500) as $chunk) {
            $this->rankings()->insert($chunk);
        }

        return $this;
    }

    public function markAsRanked($timestamp): void
    {
        $this->ranked_at = $timestamp;
        $this->save();
    }

    public function markAsNotRanked(): void
    {
        $this->ranked_at = null;
        $this->save();
    }

    public function hasPositions(): bool
    {
        return ! ($this->positions()->count() === 0);
    }

    public function hasAllApplicationsRated()
    {
        return $this->applications()
            ->whereNull('rated_at')
            ->doesntExist();
    }

    public function hasAllApplicationsRatedOrAutoRated()
    {
        return $this->applications()
            ->where('rated_at', null)
            ->where('is_auto_rated', false)
            ->doesntExist();
    }

    public function getLatestCalculation(): ?Calculation
    {
        return $this->calculations()->orderBy('id', 'desc')->first();
    }

    //    public function getTotalEmployables(Calculation $calculation): int
    //    {
    //        return $this->rankings()->ofCalculation($calculation->id)->where('employable', 1)->whereNotNull('employable_in')->count();
    //    }

    public function units(): BelongsToMany
    {
        return $this->belongsToMany(Unit::class, 'contractuals.contest_unit');
    }

    public function contestType(): BelongsTo
    {
        return $this->belongsTo(ContestType::class, 'type_id');
    }

    public function positions(): HasMany
    {
        return $this->hasMany(Position::class);
    }

    public function applications(): HasMany
    {
        return $this->hasMany(Application::class);
    }

    public function rankings(): HasMany
    {
        return $this->hasMany(Ranking::class);
    }

    public function calculations(): HasMany
    {
        return $this->hasMany(Calculation::class);
    }
}
