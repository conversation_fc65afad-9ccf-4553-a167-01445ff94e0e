<?php

namespace App\Models\Registry;

use App\Models\Registry\Traits\ValidityRange;
use App\Models\Traits\HasTenants;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

class Utility extends Model
{
    use LogsAttributeActivity, HasTenants, ValidityRange, SoftDeletes;

    protected $connection = 'mysql_registry';

    protected $fillable = [
        'utility_type_id',
        'building_id',
        'utility_provider_id',
        'supply_number',
        'paid_centrally',
        'unit_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'valid_from' => 'datetime',
        'valid_to' => 'datetime',
    ];

    protected static $applicationName = 'registry';

    public static function queryWithFilters($filters)
    {
        $query = static::query();

        $query->when($filters->has('id'), function ($query) use ($filters) {
            return $query->where('id', $filters['id']);
        })
            ->when($filters->has('units'), function ($query) use ($filters) {
                return $query->whereIn('unit_id', Arr::pluck($filters['units'], 'id'));
            })
            ->when(((! $filters->has('show_all') || $filters['show_all'] == false) && ($filters->has('valid_on'))),
                function ($query) use ($filters) {
                    return $query->validOn($filters['valid_on']);
                })
            ->when($filters->has('utility_type_id'), function ($query) use ($filters) {
                return $query->where('utility_type_id', $filters['utility_type_id']);
            })
            ->when($filters->has('utility_provider_id'), function ($query) use ($filters) {
                return $query->where('utility_provider_id', $filters['utility_provider_id']);
            })
            ->when($filters->has('supply_number'), function ($query) use ($filters) {
                return $query->where('supply_number', 'like', '%'.$filters['supply_number'].'%');
            })
            ->when($filters->has('paid_centrally'), function ($query) use ($filters) {
                return $query->where('paid_centrally', $filters['paid_centrally']);
            });

        return $query;
    }

    public function utilityType()
    {
        return $this->belongsTo(UtilityType::class);
    }

    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    public function utilityProvider()
    {
        return $this->belongsTo(UtilityProvider::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
}
