<?php

namespace App\Models\Registry;

use App\Models\Traits\LogsAttributeActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Associate extends Model
{
    use LogsAttributeActivity, SoftDeletes;

    protected $connection = 'mysql_registry';

    protected $fillable = [
        'associate_type_id',
        'building_id',
        'surname',
        'name',
        'afm',
        'doy',
        'iban',
        'address',
        'phone',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $appends = ['fullname'];

    public function getFullnameAttribute($value)
    {
        return trim($this->surname.' '.$this->name);
    }

    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    public function associateType()
    {
        return $this->belongsTo(AssociateType::class);
    }
}
