<?php

namespace App\Models\Registry;

use App\Models\Traits\LogsAttributeActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RentalProcess extends Model
{
    use LogsAttributeActivity, SoftDeletes;

    protected $connection = 'mysql_registry';

    protected $fillable = ['name'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function rentals()
    {
        return $this->hasMany(Rental::class);
    }
}
