<?php

namespace App\Models\Registry;

use App\Models\Traits\HasTenants;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use Illuminate\Database\Eloquent\Model;

class Staff extends Model
{
    use HasTenants, LogsAttributeActivity;

    public static $specializations = [
        ['field' => 'regular_scientists', 'title' => 'Επιστημονικό Προσωπικό'],
        ['field' => 'regular_administratives', 'title' => 'Διοικητικό Προσωπικό'],
        ['field' => 'regular_technicians', 'title' => 'Τεχνικοί'],
        ['field' => 'regular_guards', 'title' => 'Φυλλακτικό Προσωπικό'],
        ['field' => 'regular_workers', 'title' => 'Εργατικό Προσωπικό'],
        ['field' => 'regular_others', 'title' => 'Λοιπό Προσωπικό'],
        ['field' => 'contractual_scientists', 'title' => 'Επιστημονικό Προσωπικό ΙΔΟΧ'],
        ['field' => 'contractual_administratives', 'title' => 'Διοικητικό Προσωπικό ΙΔΟΧ'],
        ['field' => 'contractual_technicians', 'title' => 'Τεχνικοί ΙΔΟΧ'],
        ['field' => 'contractual_guards', 'title' => 'Φυλλακτικό Προσωπικό ΙΔΟΧ'],
        ['field' => 'contractual_workers', 'title' => 'Εργατικό Προσωπικό ΙΔΟΧ'],
        ['field' => 'contractual_others', 'title' => 'Λοιπό Προσωπικό ΙΔΟΧ'],
    ];

    protected $connection = 'mysql_registry';

    protected $table = 'registry.staff';

    protected $fillable = [
        'building_id',
        'unit_id',
        'regular_scientists',
        'regular_administratives',
        'regular_technicians',
        'regular_guards',
        'regular_workers',
        'regular_others',
        'contractual_scientists',
        'contractual_administratives',
        'contractual_technicians',
        'contractual_guards',
        'contractual_workers',
        'contractual_others',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected static $applicationName = 'registry';

    public static function getSpecializations()
    {
        return self::$specializations;
    }

    public function building()
    {
        return $this->belongsTo(Building::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
}
