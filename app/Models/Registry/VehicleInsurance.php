<?php

namespace App\Models\Registry;

use App\Models\Registry\Traits\ValidityRange;
use App\Models\Traits\HasTenants;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

class VehicleInsurance extends Model
{
    use HasTenants, LogsAttributeActivity, SoftDeletes, ValidityRange;

    protected $connection = 'mysql_registry';

    protected $fillable = [
        'vehicle_id',
        'provider',
        'contract_number',
        'amount',
        'unit_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'valid_from' => 'datetime',
        'valid_to' => 'datetime',
    ];

    protected static $applicationName = 'registry';

    public static function queryWithFilters($filters)
    {
        $query = static::query();

        $query->when($filters->has('id'), function ($query) use ($filters) {
            return $query->where('id', $filters['id']);
        })
            ->when(($filters->has('units') && ! empty($filters['units'])), function ($query) use ($filters) {
                return $query->whereIn('unit_id', Arr::pluck($filters['units'], 'id'));
            })
            ->when(
                ((! $filters->has('show_all') || $filters['show_all'] == false) && ($filters->has('valid_on'))),
                function ($query) use ($filters) {
                    return $query->validOn($filters['valid_on']);
                }
            )
            ->when($filters->has('provider'), function ($query) use ($filters) {
                return $query->where('provider', 'like', '%'.$filters['provider'].'%');
            })
            ->when($filters->has('vehicle_type_id'), function ($query) use ($filters) {
                return $query->whereHas('vehicle', function ($vehicleQuery) use ($filters) {
                    $vehicleQuery->where('vehicle_type_id', '=', $filters['vehicle_type_id']);
                });
            });

        return $query;
    }

    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }
}
