<?php

namespace App\Models\Registry;

use App\Models\Registry\Traits\ValidityRange;
use App\Models\Traits\HasTenants;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

class Mobile extends Model
{
    use HasTenants, LogsAttributeActivity, SoftDeletes, ValidityRange;

    protected $connection = 'mysql_registry';

    protected $fillable = [
        'number',
        'mobile_provider_id',
        'holder',
        'paid_centrally',
        'keles_linked',
        'unit_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'valid_from' => 'datetime',
        'valid_to' => 'datetime',
    ];

    protected static $applicationName = 'registry';

    public static function queryWithFilters($filters)
    {
        $query = static::query();

        $query
            ->when($filters->has('id'), function ($query) use ($filters) {
                return $query->where('id', $filters['id']);
            })
            ->when(($filters->has('units') && ! empty($filters['units'])), function ($query) use ($filters) {
                return $query->whereIn('unit_id', Arr::pluck($filters['units'], 'id'));
            })
            ->when(
                ((! $filters->has('show_all') || $filters['show_all'] == false) && ($filters->has('valid_on'))),
                function ($query) use ($filters) {
                    return $query->validOn($filters['valid_on']);
                }
            )
            ->when($filters->has('holder'), function ($query) use ($filters) {
                return $query->where('holder', 'like', '%'.$filters['holder'].'%');
            })
            ->when($filters->has('number'), function ($query) use ($filters) {
                return $query->where('number', 'like', '%'.$filters['number'].'%');
            })
            ->when($filters->has('paid_centrally'), function ($query) use ($filters) {
                return $query->where('paid_centrally', $filters['paid_centrally']);
            })
            ->when($filters->has('keles_linked'), function ($query) use ($filters) {
                return $query->where('keles_linked', $filters['keles_linked']);
            })
            ->when($filters->has('mobile_provider_id'), function ($query) use ($filters) {
                return $query->where('mobile_provider_id', $filters['mobile_provider_id']);
            });

        return $query;
    }

    public function mobileProvider()
    {
        return $this->belongsTo(MobileProvider::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
}
