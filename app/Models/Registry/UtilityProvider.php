<?php

namespace App\Models\Registry;

use App\Models\Traits\LogsAttributeActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UtilityProvider extends Model
{
    use LogsAttributeActivity, SoftDeletes;

    protected $connection = 'mysql_registry';

    protected $fillable = [
        'name',
        'utility_type_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function utilities()
    {
        return $this->hasMany(Utility::class);
    }

    public function utilityType()
    {
        return $this->belongsTo(UtilityType::class);
    }
}
