<?php

namespace App\Models\Registry;

use App\Models\Organogram;
use App\Models\Prefecture;
use App\Models\Region;
use App\Models\Registry\Traits\ValidityRange;
use App\Models\Traits\HasTenants;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Traits\LogsPivot;
use App\Models\Unit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

class Building extends Model
{
    use HasTenants, LogsAttributeActivity, LogsPivot, SoftDeletes, ValidityRange;

    protected $connection = 'mysql_registry';

    protected $fillable = [
        'street',
        'street_number',
        'city',
        'postcode',
        'floor',
        'prefecture_id',
        'region_id',
        'description',
        'interior_area',
        'exterior_area',
        'building_usage_id',
        'building_usage_other',
        'ownership_type_id',
        'unit_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'valid_from' => 'datetime',
        'valid_to' => 'datetime',
    ];

    protected $appends = ['address'];

    protected static $applicationName = 'registry';

    public static function getRelatedModels()
    {
        $ownershipTypes = OwnershipType::orderBy('id')->get(['id', 'name']);
        $buildingUsages = BuildingUsage::orderBy('is_other')->orderBy('name')->get(['id', 'name', 'is_other']);
        $rentalProcesses = RentalProcess::orderBy('id')->get(['id', 'name']);
        $associateTypes = AssociateType::orderBy('id')->get(['id', 'name']);
        $utilityTypes = UtilityType::orderBy('id')->get(['id', 'name']);
        $utilityProviders = UtilityProvider::orderBy('utility_type_id')
            ->orderBy('name')
            ->get(['id', 'name', 'utility_type_id']);
        $regions = Region::orderBy('name')->get(['id', 'name']);
        $prefectures = Prefecture::orderBy('region_id')->orderBy('name')->get(['id', 'name', 'region_id']);
        $hostedUnits = Unit::active()->ofOrganogram(Organogram::orderBy('id', 'desc')->first())
            ->withDepartments(false)->orderBy('order')->get(['id', 'name']); //->map->only(['id', 'name'])->values()->all(),
        //        $hostedUnits = Unit::orderBy('order')->get(['id', 'name']); // TODO: scope units to organogram

        return compact(
            'ownershipTypes',
            'buildingUsages',
            'rentalProcesses',
            'associateTypes',
            'regions',
            'prefectures',
            'utilityTypes',
            'utilityProviders',
            'hostedUnits'
        );
    }

    // Use this to store the units occupying a building. Can be multiple.
    // The unit_id field remains, to show who records the building in the registry.

    public static function queryWithFilters($filters)
    {
        $query = static::query();

        $query->when($filters->has('id'), function ($query) use ($filters) {
            return $query->where('id', $filters['id']);
        })
            ->when(($filters->has('units') && ! empty($filters['units'])), function ($query) use ($filters) {
                return $query->whereIn('unit_id', Arr::pluck($filters['units'], 'id'));
            })
            ->when(
                ((! $filters->has('show_all') || $filters['show_all'] == false) && ($filters->has('valid_on'))),
                function ($query) use ($filters) {
                    return $query->validOn($filters['valid_on']);
                }
            )
            ->when($filters->has('postcode'), function ($query) use ($filters) {
                return $query->where('postcode', 'like', '%'.$filters['postcode'].'%');
            })
            ->when($filters->has('city'), function ($query) use ($filters) {
                return $query->where('city', 'like', '%'.$filters['city'].'%');
            })
            ->when($filters->has('building_usage_id'), function ($query) use ($filters) {
                return $query->where('building_usage_id', $filters['building_usage_id']);
            })
            ->when($filters->has('ownership_type_id'), function ($query) use ($filters) {
                return $query->where('ownership_type_id', $filters['ownership_type_id']);
            });

        return $query;
    }

    public function getAddressAttribute($value)
    {
        return trim($this->street.' '.$this->street_number);
    }

    public function hostedUnits()
    {
        return $this->belongsToMany(Unit::class, 'registry.building_unit');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function staff()
    {
        return $this->hasMany(Staff::class);
    }

    public function buildingUsage()
    {
        return $this->belongsTo(BuildingUsage::class);
    }

    public function ownershipType()
    {
        return $this->belongsTo(OwnershipType::class);
    }

    public function prefecture()
    {
        return $this->belongsTo(Prefecture::class);
    }

    public function region()
    {
        return $this->belongsTo(Region::class);
    }

    public function rentals()
    {
        return $this->hasMany(Rental::class)->withoutTenants();
    }

    public function associates()
    {
        return $this->hasMany(Associate::class);
    }

    public function owner()
    {
        return $this->hasOne(Associate::class)->where('associate_type_id', 1);
    }

    public function phones()
    {
        return $this->hasMany(Phone::class)->withoutTenants();
    }

    public function utilities()
    {
        return $this->hasMany(Utility::class)->withoutTenants();
    }
}
