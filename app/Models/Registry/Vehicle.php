<?php

namespace App\Models\Registry;

use App\Models\Personnel\Employee;
use App\Models\Registry\Traits\ValidityRange;
use App\Models\Traits\HasTenants;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

class Vehicle extends Model
{
    use LogsAttributeActivity, HasTenants, ValidityRange, SoftDeletes;

    protected $connection = 'mysql_registry';

    protected $fillable = [
        'brand',
        'model',
        'vehicle_type_id',
        'frame_number',
        'cc',
        'registration_number',
        'license_date',
        'license_number',
        'insurance_obligation',
        'employee_id',
        'employee_other',
        'unit_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'license_date' => 'datetime',
        'valid_from' => 'datetime',
        'valid_to' => 'datetime',
    ];

    protected static $applicationName = 'registry';

    public static function queryWithFilters($filters)
    {
        $query = static::query();

        $query->when($filters->has('id'), function ($query) use ($filters) {
            return $query->where('id', $filters['id']);
        })
            ->when(($filters->has('units') && !empty($filters['units'])), function ($query) use ($filters) {
                return $query->whereIn('unit_id', Arr::pluck($filters['units'], 'id'));
            })
            ->when(
                ((! $filters->has('show_all') || $filters['show_all'] == false) && ($filters->has('valid_on'))),
                function ($query) use ($filters) {
                    return $query->validOn($filters['valid_on']);
                }
            )
            ->when($filters->has('brand'), function ($query) use ($filters) {
                return $query->where('brand', 'like', '%'.$filters['brand'].'%');
            })
            ->when($filters->has('model'), function ($query) use ($filters) {
                return $query->where('model', 'like', '%'.$filters['model'].'%');
            })
            ->when($filters->has('registration_number'), function ($query) use ($filters) {
                return $query->where('registration_number', 'like', '%'.$filters['registration_number'].'%');
            })
            ->when($filters->has('vehicle_type_id'), function ($query) use ($filters) {
                return $query->where('vehicle_type_id', $filters['vehicle_type_id']);
            });

        return $query;
    }

    public function vehicleInsurances()
    {
        return $this->hasMany(VehicleInsurance::class);
    }

    public function vehicleType()
    {
        return $this->belongsTo(VehicleType::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
}
