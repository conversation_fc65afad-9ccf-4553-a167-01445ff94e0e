<?php

namespace App\Models\RoleManagement\CustomRelations;

use App\Models\RoleManagement\Manager;
use CultureGr\CustomRelation\CustomRelation;
use Illuminate\Database\Eloquent\Collection;

class ManagedAppManagerRelation extends CustomRelation
{
    /**
     * The Eloquent query builder instance.
     *
     * @var \Illuminate\Database\Eloquent\Builder
     */
    protected $query;

    /**
     * The parent model instance.
     *
     * @var \Illuminate\Database\Eloquent\Model
     */
    protected $parent;

    /**
     * Set the base constraints on the relation query.
     *
     * @return void
     */
    public function addConstraints()
    {
        $this->query
            ->distinct()
            ->join('role_manager', 'role_manager.user_id', '=', 'users.id')
            ->join('roles', 'roles.id', '=', 'role_manager.role_id');

        // If relation is not eager loaded
        if (! is_null($this->parent->getAttribute('id'))) {
            $this->query->where('roles.app_id', '=', $this->parent->getAttribute('id'));
        }
    }

    /**
     * Set the constraints for an eager load of the relation.
     *
     * @param  array  $managedApps  An array of parent models
     * @return void
     */
    public function addEagerConstraints(array $managedApps)
    {
        $this->query
            ->whereIn('roles.app_id', collect($managedApps)->pluck('id'))
            ->with('managedRoles'); // To avoid N+1 problem when eager loading
    }

    /**
     * Match the eagerly loaded results to their parents.
     *
     * @param  array  $managedApps  An array of parent models
     * @param  \Illuminate\Database\Eloquent\Collection  $results  The result of the query executed by our relation class.
     * @param  string  $relation  The name of the relation
     * @return array
     */
    public function match(array $managedApps, Collection $results, $relation)
    {
        if ($results->isEmpty()) {
            return $managedApps;
        }

        foreach ($managedApps as $managedApp) {
            $managedApp->setRelation(
                $relation,
                $results->unique()->filter(function (Manager $manager) use ($managedApp) {
                    return $manager->managedRoles->pluck('app_id')->contains($managedApp->id);
                })->values()
            );
        }

        return $managedApps;
    }
}
