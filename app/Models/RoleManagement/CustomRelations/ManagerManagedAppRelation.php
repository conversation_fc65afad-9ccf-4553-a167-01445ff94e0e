<?php

namespace App\Models\RoleManagement\CustomRelations;

use App\Models\RoleManagement\ManagedApp;
use CultureGr\CustomRelation\CustomRelation;
use Illuminate\Database\Eloquent\Collection;

class ManagerManagedAppRelation extends CustomRelation
{
    /**
     * The Eloquent query builder instance.
     *
     * @var \Illuminate\Database\Eloquent\Builder
     */
    protected $query;


    /**
     * The parent model instance.
     *
     * @var \Illuminate\Database\Eloquent\Model
     */
    protected $parent;


    /**
     * Set the base constraints on the relation query.
     *
     * @return void
     */
    public function addConstraints()
    {
        $this->query
            ->distinct()
            ->join('roles', 'roles.app_id', '=', 'apps.id')
            ->join('role_manager', 'role_manager.role_id', '=', 'roles.id');

        // If relation is not eager loaded
        if (!is_null($this->parent->getAttribute('id'))) {
            $this->query->where('role_manager.user_id', '=', $this->parent->getAttribute('id'));
        }
    }


    /**
     * Set the constraints for an eager load of the relation.
     *
     * @param  array  $managers  An array of parent models
     * @return void
     */
    public function addEagerConstraints(array $managers)
    {
        $this->query
            ->whereIn('role_manager.user_id', collect($managers)->pluck('id'))
            ->with('managedRoles.managers'); // To avoid N+1 problem when eager loading
    }


    /**
     * Match the eagerly loaded results to their parents.
     *
     * @param  array  $managers  An array of parent models
     * @param  \Illuminate\Database\Eloquent\Collection  $managedApps  The result of the query executed by our relation class.
     * @param  string  $relation  The name of the relation
     * @return array
     */
    public function match(array $managers, Collection $managedApps, $relation)
    {
        if ($managedApps->isEmpty()) {
            return $managers;
        }

        foreach ($managers as $manager) {
            $manager->setRelation(
                $relation,
                $managedApps->filter(function (ManagedApp $managedApp) use ($manager) {
                    return $managedApp->managedRoles->flatMap(function ($managedRole) {
                        return $managedRole->managers->pluck('id');
                    })->contains($manager->id);
                })->values()
            );
        }

        return $managers;
    }
}
