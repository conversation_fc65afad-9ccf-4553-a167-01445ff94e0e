<?php

namespace App\Models\RoleManagement;

use App\Models\App;
use App\Models\RoleManagement\CustomRelations\ManagedAppManagerRelation;
use App\Models\Traits\HasParentModel;
use CultureGr\CustomRelation\HasCustomRelation;
use Illuminate\Database\Eloquent\Builder;

class ManagedApp extends App
{
    use HasParentModel, HasCustomRelation;

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(function (Builder $builder) {
            $builder->whereHas('managedRoles');
        });
    }

    public function scopeManagedBy($query, Manager $manager)
    {
        $query->whereHas('managedRoles', function ($query) use ($manager) {
            return $query->whereHas('managers', function ($query) use ($manager) {
                return $query->where('id', $manager->id);
            });
        });
    }

    public function managedRoles()
    {
        return $this->hasMany(ManagedRole::class)->orderBy('description');
    }

    public function managers(): ManagedAppManagerRelation
    {
        return $this->relatesTo(Manager::class, ManagedAppManagerRelation::class);
    }
}
