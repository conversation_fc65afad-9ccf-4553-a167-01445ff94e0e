<?php

namespace App\Models\RoleManagement;

use App\Models\Role;
use App\Models\Traits\HasParentModel;
use Illuminate\Database\Eloquent\Builder;

class ManagedRole extends Role
{
    use HasParentModel;

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(function (Builder $builder) {
            $builder->whereHas('managers');
        });
    }

    public function scopeManagedBy($query, Manager $manager)
    {
        $query->whereHas('managers', function ($query) use ($manager) {
            return $query->where('id', $manager->id);
        });
    }

    public function managers()
    {
        return $this->belongsToMany(Manager::class, 'role_manager');
    }

    public function managedApp()
    {
        return $this->belongsTo(ManagedApp::class);
    }
}
