<?php

namespace App\Models;

use App\Models\Traits\LogsAttributeActivity;
use App\Models\Traits\LogsPivot;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Role extends Model
{
    use LogsAttributeActivity, LogsPivot;

    protected $connection = 'mysql_main';

    protected $fillable = ['name', 'description', 'app_id'];

    protected $logNameToUse = 'admin';

    public function scopeWithAppName($query)
    {
        return $query
            ->select('roles.*')
            ->selectRaw("CASE
                        WHEN roles.app_id IS NULL THEN 'Administration'
                        ELSE COALESCE(apps.name, 'Administration')
                     END AS app_name")
            ->leftJoin('apps', 'roles.app_id', '=', 'apps.id');
    }

    public static function getByName(string $name): self
    {
        return self::whereName($name)->first();
    }

    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class);
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function app(): BelongsTo
    {
        return $this->belongsTo(App::class);
    }
}
