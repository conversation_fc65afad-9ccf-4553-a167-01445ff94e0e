<?php

namespace App\Models;

use App\CustomFilters\IsAdminCustomFilter;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Traits\LogsPivot;
use App\Models\Traits\User\CanImpersonate;
use App\Models\Traits\User\HasRoles;
use App\Models\Traits\User\HasUnits;
use CultureGr\CustomRelation\HasCustomRelation;
use CultureGr\Filterer\Filterable;
use CultureGr\Filterer\FiltersBuilder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Cache;
use Laravel\Passport\HasApiTokens;
use LdapRecord\Laravel\Auth\AuthenticatesWithLdap;
use LdapRecord\Laravel\Auth\LdapAuthenticatable;

class User extends Authenticatable implements LdapAuthenticatable
{
    use AuthenticatesWithLdap,
        CanImpersonate,
        Filterable,
        HasApiTokens,
        HasCustomRelation,
        HasRoles,
        HasUnits,
        LogsAttributeActivity,
        LogsPivot,
        Notifiable,
        SoftDeletes;

    protected array $filterable = ['name'];

    protected array $sortable = ['name'];

    protected array $customFilters = [
        'is_admin' => IsAdminCustomFilter::class,
    ];

    public function getLdapGuidColumn(): string
    {
        return 'objectguid';
    }

    protected static $ignoreChangedAttributes = [
        'name',
        'password',
        'dn',
        'remember_token',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $connection = 'mysql_main';

    protected $fillable = ['name', 'username', 'email', 'password', 'dn', 'primary_unit_id', 'unit_id'];

    protected $hidden = ['password', 'remember_token'];

    protected $logNameToUse = 'admin';

    public function isOnline(): bool
    {
        $userId = $this->id;

        return Cache::has("user_{$userId}_is_online");
    }

    public function lastActivity()
    {
        $lastActivity = Cache::get("user_{$this->id}_is_online");

        return $lastActivity ? $lastActivity->diffForHumans() : null;
    }
}
