<?php

namespace App\Models\Educational;

use App\Models\Region;
use App\Models\Traits\HasTenants;
use App\Models\Unit;
use App\Models\User;
use CultureGr\Filterer\Filterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Action extends Model implements HasMedia
{
    use Filterable, HasTenants, InteractsWithMedia;

    /*
     * ['id' => 1,'name' => 'Εκπαιδευτικό Πρόγραμμα'],
     * ['id' => 2,'name' => 'Επιμορφωτικό σεμινάριο'],
     // * ['id' => 3,'name' => 'Εκπαιδευτικός Φάκελος'],
     // * ['id' => 4,'name' => 'Εκπαιδευτικό Παιχνίδι'],
     * ['id' => 5,'name' => 'Μουσειοσκευή'],
     * ['id' => 6,'name' => 'Θεματική Περιήγηση'],
     * ['id' => 7,'name' => 'Καλλιτεχνική Εκδήλωση'],
     * ['id' => 8,'name' => 'Επιστημονική Συνάντηση'],
     * ['id' => 9,'name' => 'Ψηφιακές Διαδραστικές Εφαρμογές'],
     * ['id' => 10,'name' => 'Εκπαιδευτική Έκθεση'],
     * ['id' => 11,'name' => 'Εκδήλωση Βιβλιοπαρουσίασης'],
     * ['id' => 12,'name' => 'Έντυπο Εκπαιδευτικό'],
     * ['id' => 13,'name' => 'Έντυπο Ενημερωτικό/Προωθητικό'],
     * ['id' => 14,'name' => 'Έκδοση'],
     * ['id' => 15,'name' => 'Εργαστήριο'],
     * ['id' => 16,'name' => 'Βίντεο'],
     */

    /**
     * @var array<string, array<int>>
     */
    public const REQUIRED_FIELDS = [
        'period_id' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'unit_id' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'title' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'type_id' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'involvements' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'ongoing' => [1, 2, 6, 15],
        'is_digital' => [5, 10, 12, 13, 14],
        'context_id' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'started_at' => [1, 2, 6, 7, 8, 11, 15],
        'ended_at' => [1, 2, 6, 7, 8, 11, 15],
        'duration_id' => [1, 2, 6, 7, 8, 11, 15],
        'frequency' => [1, 2, 6, 15],
        'description' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'contributors' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'locations' => [1, 2, 6, 7, 8, 10, 11, 12, 15],
        'targets' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'target_types' => [1, 2, 6, 7, 8, 11, 15],
        'collaborators' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'funds' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'tools' => [1, 5, 9, 12, 15],
        'assessments' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'evaluation' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'disseminations' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'link' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        'attachments' => [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
    ];

    protected static $applicationName = 'educational';

    protected $connection = 'mysql_educational';

    protected $fillable = [
        'period_id',
        'unit_id',
        'region_id',
        'type_id',
        'ongoing',
        'is_digital',
        'title',
        'context_id',
        'started_at',
        'ended_at',
        'duration_id',
        'frequency',
        'description',
        'contributors',
        'evaluation',
        'link',
        'user_id',
    ];

    protected array $filterable = [
        'title',
        'frequency',
        'ongoing',
        'period_id',
        'unit_id',
        'type_id',
        'region_id',
        'context_id',
        'duration_id',
        'locations.id',
        'locations.location_type_id',
        'targets.id',
        'targets.target_type_id',
        'collaborators.collaborator_type_id',
        'funds.id',
        'tools.id',
        'assessments.id',
        'involvements.id',
    ];

    protected array $sortable = [
        'period.name',
        'title',
        'unit.name',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'submitted_at' => 'datetime',
        'is_digital' => 'boolean',
    ];

    public function setOngoingAttribute($value)
    {
        $this->attributes['ongoing'] = filter_var($value, FILTER_VALIDATE_BOOLEAN);
    }

    public function getOngoingAttribute($value)
    {
        return filter_var($value, FILTER_VALIDATE_BOOLEAN);
    }

    public function submit()
    {
        $this->user_id = auth()->id();
        $this->submitted_at = now();
        $this->save();
    }

    public function withdraw()
    {
        $this->user_id = auth()->id();
        $this->submitted_at = null;
        $this->save();
    }

    public function scopeSubmitted(Builder $query): Builder
    {
        return $query->whereNotNull('submitted_at');
    }

    public function scopeUnsubmitted(Builder $query): Builder
    {
        return $query->whereNull('submitted_at');
    }

    public static function getRequiredFieldsForType(int $typeId): array
    {
        return array_keys(array_filter(self::REQUIRED_FIELDS, function ($fields) use ($typeId) {
            return in_array($typeId, $fields);
        }));
    }

    public function period(): BelongsTo
    {
        return $this->belongsTo(Period::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(Type::class);
    }

    public function involvements(): BelongsToMany
    {
        return $this->belongsToMany(Involvement::class);
    }

    public function context(): BelongsTo
    {
        return $this->belongsTo(Context::class);
    }

    public function duration(): BelongsTo
    {
        return $this->belongsTo(Duration::class);
    }

    public function locations(): BelongsToMany
    {
        return $this->belongsToMany(Location::class);
    }

    public function targets(): BelongsToMany
    {
        return $this->belongsToMany(Target::class);
    }

    public function targetTypes(): BelongsToMany
    {
        return $this->belongsToMany(TargetType::class, 'participants')->as('participants')->withPivot('amount');
    }

    public function collaborators(): BelongsToMany
    {
        return $this->belongsToMany(Collaborator::class);
    }

    public function funds(): BelongsToMany
    {
        return $this->belongsToMany(Fund::class);
    }

    public function tools(): BelongsToMany
    {
        return $this->belongsToMany(Tool::class);
    }

    public function assessments(): BelongsToMany
    {
        return $this->belongsToMany(Assessment::class);
    }

    public function disseminations(): BelongsToMany
    {
        return $this->belongsToMany(Dissemination::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
