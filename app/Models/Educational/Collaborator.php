<?php

namespace App\Models\Educational;

use Illuminate\Database\Eloquent\Model;

class Collaborator extends Model
{
    protected $connection = 'mysql_educational';

    protected $fillable = ['name', 'collaborator_type_id'];

    public function actions()
    {
        return $this->belongsToMany(Action::class);
    }

    public function collaboratorType()
    {
        return $this->belongsTo(CollaboratorType::class);
    }
}
