<?php

namespace App\Models\Educational;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Target extends Model
{
    protected $connection = 'mysql_educational';

    protected $fillable = ['name', 'target_type_id'];

    public function actions(): BelongsToMany
    {
        return $this->belongsToMany(Action::class);
    }

    public function targetType(): BelongsTo
    {
        return $this->belongsTo(TargetType::class);
    }
}
