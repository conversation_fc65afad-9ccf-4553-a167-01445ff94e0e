<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\RoleManagement\ManagedRole;

class App extends Model
{
    public $fillable = ['name', 'abbrv', 'icon', 'color'];

    public $timestamps = false;

    public static function getByAbbreviation(string $abbrv): self
    {
        return self::whereAbbrv($abbrv)->firstOrFail();
    }

    public function roles()
    {
        return $this->hasMany(Role::class)->orderBy('description');
    }

    public function managedRoles()
    {
        return $this->hasMany(ManagedRole::class)->orderBy('description');
    }
}
