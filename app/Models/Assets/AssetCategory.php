<?php

namespace App\Models\Assets;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AssetCategory extends Model
{
    use HasFactory;

    // No factory needed as we use the AssetCategorySeeder for predefined values

    protected $connection = 'mysql_assets';

    protected $fillable = [
        'code',
        'description',
        'measure_unit',
        'duration_years',
    ];

    /**
     * Get the assets for the category.
     */
    public function assets(): HasMany
    {
        return $this->hasMany(Asset::class);
    }
}
