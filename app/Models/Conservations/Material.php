<?php

namespace App\Models\Conservations;

use App\Models\Traits\LogsAttributeActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Material extends Model
{
    use SoftDeletes, LogsAttributeActivity;

    protected $connection = 'mysql_conservations';

    protected $fillable = ['name'];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function conservators()
    {
        return $this->belongsToMany(Conservator::class)->withPivot('issued_at', 'issued_record_number');
    }
}
