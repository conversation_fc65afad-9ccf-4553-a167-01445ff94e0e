<?php

namespace App\Models\Conservations;

use App\Models\Prefecture;
use App\Models\Region;
use App\Models\Traits\LogsAttributeActivity;
use App\Models\Unit;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Conservator extends Model
{
    use LogsAttributeActivity, SoftDeletes;

    protected $connection = 'mysql_conservations';

    protected $fillable = [
        'name',
        'surname',
        'fathername',
        'degree_id',
        'school_id',
        'afm',
        'doy',
        'policeid_number',
        'policeid_date',
        'amka',
        'birthdate',
        'street',
        'street_number',
        'postcode',
        'city',
        'prefecture_id',
        'region_id',
        'phonenumber1',
        'phonenumber2',
        'email',
        'registration_number',
        'active',
        'status_date',
        'status_type_id',
        'unit_id',
        'government_position',
    ];

    protected $casts = [
        'policeid_date' => 'datetime',
        'birthdate' => 'datetime',
        'status_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d');
    }

    public static function queryWithFilters($filters)
    {
        $query = static::query();

        if ($filters->has('registration_number')) {
            $query->where('registration_number', '=', $filters['registration_number']);
        }

        if ($filters->has('surname')) {
            $stringToSearch = '%'.trim($filters['surname']).'%';
            $query->where('surname', 'like', $stringToSearch);
        }

        if ($filters->has('amka')) {
            $stringToSearch = '%'.trim($filters['amka']).'%';
            $query->where('amka', 'like', $stringToSearch);
        }

        if ($filters->has('afm')) {
            $stringToSearch = '%'.trim($filters['afm']).'%';
            $query->where('afm', 'like', $stringToSearch);
        }

        if ($filters->has('policeid_number')) {
            $stringToSearch = '%'.trim($filters['policeid_number']).'%';
            $query->where('policeid_number', 'like', $stringToSearch);
        }

        if ($filters->has('degree_id')) {
            $query->where('degree_id', '=', $filters['degree_id']);
        }

        if ($filters->has('degree_level_id')) {
            $query->whereHas('degree', function ($q) use ($filters) {
                $q->where('degree_level_id', '=', $filters['degree_level_id']);
            });
        }

        if ($filters->has('school_id')) {
            $query->where('school_id', '=', $filters['school_id']);
        }

        if ($filters->has('region_id')) {
            $query->where('region_id', '=', $filters['region_id']);
        }

        if ($filters->has('prefecture_id')) {
            $query->where('prefecture_id', '=', $filters['prefecture_id']);
        }

        if ($filters->has('phonenumber1')) {
            $stringToSearch = '%'.trim($filters['phonenumber1']).'%';
            $query->where('phonenumber1', 'like', $stringToSearch);
        }

        if ($filters->has('phonenumber2')) {
            $stringToSearch = '%'.trim($filters['phonenumber2']).'%';
            $query->where('phonenumber2', 'like', $stringToSearch);
        }

        if ($filters->has('email')) {
            $stringToSearch = '%'.trim($filters['email']).'%';
            $query->where('email', 'like', $stringToSearch);
        }

        if ($filters->has('active')) {
            $query->where('active', true);
        } else {
            $query->where('active', false);
        }

        if ($filters->has('status_type_id')) {
            $query->where('status_type_id', '=', $filters['status_type_id']);
        }

        if ($filters->has('unit_id')) {
            debug('filter unit id');
            $query->where('unit_id', '=', $filters['unit_id']);
        }

        if ($filters->has('material_id') && \count($filters['material_id'])) {
            $materials = $filters['material_id'];
            $query->whereHas('materials', function ($q) use ($materials) {
                $q->whereIn('id', $materials);
            });
        }

        return $query;
    }

    public function materials(): BelongsToMany
    {
        return $this->belongsToMany(Material::class)->withPivot('issued_at', 'issued_record_number');
    }

    public function degree(): BelongsTo
    {
        return $this->belongsTo(Degree::class);
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function statusType(): BelongsTo
    {
        return $this->belongsTo(StatusType::class);
    }

    public function prefecture(): BelongsTo
    {
        return $this->belongsTo(Prefecture::class);
    }

    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }
}
