<?php

namespace App\Models\Phonebook;

use App\Models\Personnel\Rank;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EmployeeContact extends Model
{
    protected $connection = 'mysql_personnel';

    protected $table = 'employees';

    protected $with = [
        'workTelephones:id,tel,info,type_id,work,employee_id,unit_id',
        'workTelephones.type:id,name',
        'unit:id,abbrv',
        'ranks:id,display_name,compass_id',
        'users:id',
    ];

    protected $fillable = [
        'name', 'surname', 'email', 'unit_id',
    ];

    protected static function booted()
    {
        static::addGlobalScope('hasUnit', function (Builder $builder) {
            $builder->whereNotNull('unit_id');
        });
    }

    public function scopeOfUnit(Builder $query, int $unitId): Builder
    {
        return $query->where('unit_id', '=', $unitId);
    }

    public function scopeOfTelephone(Builder $query, string $telephone): Builder
    {
        return $query->whereHas('telephones', function ($query) use ($telephone) {
            $query->from('phonebook.telephones')->workTelephones()->where('tel', 'LIKE', '%'.$telephone.'%');
        });
    }

    public function scopeOfName(Builder $query, string $name): Builder
    {
        if (! str_contains($name, ' ')) {
            // Search by name or surrname
            return $query->where('name', 'LIKE', '%'.$name.'%')
                ->orWhere('surname', 'LIKE', '%'.$name.'%');
        } else {
            // Search by fullname
            // if more than two words provided, we keep the first two and we ignore the rest of them
            $fullname = explode(' ', $name);

            return $query
                ->where(function ($query) use ($fullname) {
                    $query->where('name', 'LIKE', '%'.$fullname[0].'%')
                        ->where('surname', 'LIKE', '%'.$fullname[1].'%');
                })
                ->orWhere(function ($query) use ($fullname) {
                    $query->where('name', 'LIKE', '%'.$fullname[1].'%')
                        ->where('surname', 'LIKE', '%'.$fullname[0].'%');
                });
        }
    }

    public function scopeFavoritedByUser(Builder $query, int $userId): Builder
    {
        return $query->whereExists(function ($query) use ($userId) {
            $query->select('*')
                ->from('laravel.users')
                ->join('phonebook.favorite_employee_contacts', 'laravel.users.id', '=', 'phonebook.favorite_employee_contacts.user_id')
                ->whereRaw('personnel.employees.id = phonebook.favorite_employee_contacts.employee_id')
                ->where('laravel.users.id', '=', $userId);
        });
    }

    public function getWorkEmail(): string
    {
        if (strpos($this->email, '@culture.gr') !== false || strpos($this->email, '@gga.gov.gr') !== false) {
            return $this->email;
        }

        return '';
    }

    public function existsInCompass(): bool
    {
        return $this->compass_id !== null;
    }

    public function telephones(): HasMany
    {
        return $this->hasMany(Telephone::class, 'employee_id');
    }

    public function workTelephones(): HasMany
    {
        return $this->telephones()->where('work', '=', 1);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    public function ranks(): BelongsToMany
    {
        return $this->belongsToMany(Rank::class, 'employee_rank', 'employee_id')->withPivot('unit_id');
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'phonebook.favorite_employee_contacts', 'employee_id');
    }
}
