<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Staudenmeir\LaravelAdjacencyList\Eloquent\HasRecursiveRelationships;

class Unit extends Model
{
    use HasRecursiveRelationships;

    public $timestamps = false;

    protected $connection = 'mysql_main';

    public $fillable = [
        'name',
        'abbrv',
        'order',
        'email',
        'unit_type_id', // TODO: UI for creating unit types
        'organogram_id',
        'prefecture_id',
        'parent_id',
        'reference_id', // set when deleting
        'compass_id',
        'started_at',
        'ended_at',
    ];

    protected $casts = [
        'started_at' => 'date:Y-m-d',
        'ended_at' => 'date:Y-m-d',
    ];

    public function scopeWithDepartments(Builder $query, bool $includeDepartments = true): Builder
    {
        if ($includeDepartments === false) {
            return $query->whereNotIn('unit_type_id', [70, 80]);
        }

        return $query;
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->whereNull('ended_at');
    }

    public function scopeInactive(Builder $query): Builder
    {
        return $query->whereNotNull('ended_at');
    }

    public function scopeMinister(Builder $query): Builder
    {
        return $query->whereNull('parent_id');
    }

    public function scopeGdirectorates(Builder $query): Builder
    {
        return $query->where('unit_type_id', '=', 40);
    }

    public function scopeOfOrganogram(Builder $query, Organogram $organogram): Builder
    {
        return $query->where('organogram_id', $organogram->id);
    }

    /**
     * @param  Builder<Unit>  $query
     */
    public function scopeRelatedToEducationalApp(Builder $query): Builder
    {
        return $query->active()
            ->where('parent_id', '=', 2)
            ->whereIn('unit_type_id', [50, 52, 53])
            ->where('id', '!=', 4);
    }

    /**
     * @param  Builder<Unit>  $query
     */
    public function scopeRelatedToPhonebookApp(Builder $query): Builder
    {
        return $query->active()->whereNotIn('unit_type_id', [54, 55, 56, 70, 80]);
    }

    public function isGdirectorate(): bool
    {
        return $this->unit_type_id === 40;
    }

    public function isDepartment(): bool
    {
        return $this->unit_type_id === 70 || $this->unit_type_id === 80;
    }

    public function getParentGdirectorate(): ?self
    {
        // @phpstan-ignore-next-line
        if ($this->ancestors()->gdirectorates()->count() > 1) {
            throw new \Exception('Unit has more than one parent gdirectorates');
        }

        // @phpstan-ignore-next-line
        return $this->ancestors()->gdirectorates()->first();
    }

    public function relatedUnits(): HasMany
    {
        return $this->hasMany(self::class, 'reference_id');
    }

    public function closestRelatedUnit(): HasOne
    {
        return $this->hasOne(self::class, 'reference_id')->orderByDesc('organogram_id')->take(1);
    }

    public function unitType(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(UnitType::class);
    }

    public function organogram(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Organogram::class);
    }

    public function prefecture(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Prefecture::class);
    }
}
