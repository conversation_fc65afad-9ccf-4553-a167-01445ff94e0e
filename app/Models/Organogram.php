<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Organogram extends Model
{
    protected $connection = 'mysql_main';

    protected $guarded = [];

    public $timestamps = false;

    protected $casts = [
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
    ];

    public function scopeActive($query)
    {
        return $query->whereNull('ended_at');
    }
}
