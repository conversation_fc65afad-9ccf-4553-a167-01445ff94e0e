<?php

namespace App\Presenters\Assets;

use App\Models\Assets\AssetCategory;
use CultureGr\Presenter\Presenter;
use Illuminate\Support\Facades\Auth;

/** @mixin AssetCategory */
class AssetCategoryShowPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'description' => $this->description,
            'measure_unit' => $this->measure_unit,
            'duration_years' => $this->duration_years,

            // Include relationships data
            'assets_count' => $this->assets()->count(),

            // Include permissions
            'can' => [
                'update' => Auth::user()->can('assets.update'),
                'delete' => Auth::user()->can('assets.delete') && ($this->assets()->count() === 0),
            ],
        ];
    }
}
