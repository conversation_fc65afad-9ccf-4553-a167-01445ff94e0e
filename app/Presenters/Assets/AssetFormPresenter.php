<?php

namespace App\Presenters\Assets;

use App\Models\Assets\Asset;
use Illuminate\Support\Facades\Auth;

class AssetFormPresenter
{
    /**
     * Transform an asset model to a format suitable for the form
     */
    public static function transform(Asset $asset): array
    {
        $user = Auth::user();

        return [
            'id' => $asset->id,
            'contract_id' => $asset->contract_id,
            'asset_category_id' => $asset->asset_category_id,
            'serial_number' => $asset->serial_number,
            'quantity' => $asset->quantity,
            'date_of_receipt' => $asset->date_of_receipt ? $asset->date_of_receipt->format('Y-m-d') : null,
            'location' => $asset->location,
            'acquisition_cost' => $asset->acquisition_cost_in_cents / 100, // Convert from cents to euros
            'unit_id' => $asset->unit_id,
        ];
    }
}
