<?php

namespace App\Presenters\Assets;

use App\Models\Assets\Contract;
use CultureGr\Presenter\Presenter;
use Illuminate\Support\Facades\Auth;

class ContractListPresenter extends Presenter
{
    /**
     * Transform a contract model to a format suitable for listing
     */
    public function toArray(): array
    {
        /** @var Contract $contract */
        $contract = $this->model;

        return [
            'id' => $contract->id,
            'contract_number' => $contract->contract_number,
            'created_at' => $contract->created_at->format('Y-m-d'),
            'assets_count' => $contract->assets_count ?? $contract->assets()->count(),
            'can' => [
                'update' => Auth::user()->can('assets.update'),
                'delete' => Auth::user()->can('assets.delete') && (($contract->assets_count ?? $contract->assets()->count()) === 0),
            ],
        ];
    }
}
