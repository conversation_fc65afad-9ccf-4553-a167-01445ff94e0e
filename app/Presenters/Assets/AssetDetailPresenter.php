<?php

namespace App\Presenters\Assets;

use App\Models\Assets\Asset;
use CultureGr\Presenter\Presenter;
use Illuminate\Support\Facades\Auth;
use App\Presenters\Assets\AssetCategoryShowPresenter;

/** @mixin Asset */
class AssetDetailPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'serial_number' => $this->serial_number,
            'quantity' => $this->quantity,
            'date_of_receipt' => $this->date_of_receipt->format('d-m-Y'),
            'location' => $this->location,
            'acquisition_cost' => $this->acquisition_cost_formatted,
            'created_at' => $this->created_at->format('d-m-Y H:i'),
            'updated_at' => $this->updated_at->format('d-m-Y H:i'),
            'submitted_at' => $this->submitted_at ? $this->submitted_at->format('d-m-Y H:i') : null,
            'status' => $this->submitted_at ? 'Submitted' : 'Draft',

            // Include relationships data
            'contract' => [
                'id' => $this->whenLoaded('contract')?->id,
                'contract_number' => $this->whenLoaded('contract')?->contract_number ?? '',
            ],
            'asset_category' => $this->whenLoaded('assetCategory') ? AssetCategoryShowPresenter::make($this->assetCategory) : null,
            'user' => [
                'id' => $this->whenLoaded('user')?->id,
                'name' => $this->whenLoaded('user')?->name ?? '',
            ],
            'unit' => [
                'id' => $this->whenLoaded('unit')?->id,
                'name' => $this->whenLoaded('unit')?->name ?? '',
            ],
            'can' => [
                'update' => Auth::user()->can('assets.update'),
                'delete' => Auth::user()->can('assets.delete'),
                'submit' => Auth::user()->can('assets.update') && !$this->submitted_at,
                'withdraw' => Auth::user()->can('assets.update') && $this->submitted_at,
            ],
        ];
    }
}
