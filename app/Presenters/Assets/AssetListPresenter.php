<?php

namespace App\Presenters\Assets;

use App\Models\Assets\Asset;
use CultureGr\Presenter\Presenter;
use Illuminate\Support\Facades\Auth;

/** @mixin Asset */
class AssetListPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'contract_id' => $this->contract_id,
            'asset_category_id' => $this->asset_category_id,
            'serial_number' => $this->serial_number,
            'quantity' => $this->quantity,
            'date_of_receipt' => $this->date_of_receipt->format('d-m-Y'),
            'location' => $this->location,
            'acquisition_cost' => $this->acquisition_cost_formatted,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            // Include relationships when they are loaded
            'contract' => $this->whenLoaded('contract')?->contract_number ?? '',
            'asset_category' => $this->whenLoaded('assetCategory')?->code ?? '',
            'unit_abbrv' => $this->whenLoaded('unit')?->abbrv ?? '',
            'can' => [
                'update' => Auth::user()->can('assets.update'),
                'delete' => Auth::user()->can('assets.delete'),
                'submit' => Auth::user()->can('assets.update') && !$this->submitted_at,
                'withdraw' => Auth::user()->can('assets.update') && $this->submitted_at,
            ],
        ];
    }
}
