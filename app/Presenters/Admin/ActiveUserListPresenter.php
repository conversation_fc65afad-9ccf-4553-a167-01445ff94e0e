<?php

namespace App\Presenters\Admin;

use App\Models\User;
use CultureGr\Presenter\Presenter;

/** @mixin User */
class ActiveUserListPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'primaryUnit' => $this->primaryUnit?->abbrv ?? '',
            'roles' => $this->roles->pluck('name'),
            'isOnline' => $this->isOnline(),
            'isService' => $this->isService(),
            'isAdmin' => $this->isAdmin(),
            'editUrl' => route('admin.user.edit', $this->id),
            'impersonateUrl' => route('admin.impersonated-user.store'),
        ];
    }

    protected function isService(): bool
    {
        $primaryUnitEmail = $this->primaryUnit?->email ?? '';
        $userEmail = $this->email;

        return $primaryUnitEmail === $userEmail;
    }
}
