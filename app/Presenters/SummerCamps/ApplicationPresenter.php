<?php

namespace App\Presenters\SummerCamps;

use App\Models\SummerCamps\Application;
use CultureGr\Presenter\Presenter;

/** @mixin Application */
class ApplicationPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user' => "{$this->user->name} ({$this->user->email})",
            'season_name' => $this->season->name,
            'season_description' => $this->season->description,
            'name' => $this->name,
            'surname' => $this->surname,
            'father_name' => $this->father_name,
            'employment_sector_id' => $this->employment_sector_id,
            'employment_sector' => $this->employmentSector?->name,
            'employmen_type_id' => $this->employment_type_id,
            'employment_type' => $this->employmentType?->name,
            'position' => $this->position,
            'afm' => $this->afm,
            'doy' => $this->doy,
            'personal_phone' => $this->personal_phone,
            'mobile_phone' => $this->mobile_phone,
            'work_phone' => $this->work_phone,
            'phones' => $this->getPhones(),
            'email_address' => $this->email_address,
            'is_submitted' => $this->is_submitted,
            'submitted_at' => $this->submitted_at?->format('d-m-Y'),
            'protocol' => $this->protocol,
            'days' => $this->getChildrenDaysSum(),
            'application_children_count' => $this->application_children_count,
        ];
    }

    protected function getChildrenDaysSum()
    {
        return $this->applicationChildren()->sum('days');
    }

    protected function getPhones()
    {
        $phones = collect([$this->personal_phone, $this->mobile_phone, $this->work_phone])
            ->filter(function ($phone) {
                return !empty($phone);
            })
            ->unique()
            ->toArray();

        return implode(',', $phones);
    }
}
