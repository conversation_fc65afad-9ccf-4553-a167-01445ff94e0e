<?php

namespace App\Presenters\Contractuals;

use CultureGr\Presenter\Presenter;

class RankedPositionPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'specialization_name' => $this->specialization->name,
            'code' => $this->code,
            'amount' => $this->amount,
            'location' => $this->location,
            'unit_abbrv' => $this->unit->abbrv,
            'has_locality' => $this->has_locality,
            'employable_count' => $this->employable_count,
            'accepted_count' => $this->accepted_count,
            'declined_count' => $this->declined_count,
            'vacant_count' => $this->amount - $this->accepted_count,
            'succeededRankings' => SucceededRankingPresenter::collection($this->succeededRankings),
            'rejectedRankings' => RejectedRankingPresenter::collection($this->rejectedRankings),
        ];
    }
}
