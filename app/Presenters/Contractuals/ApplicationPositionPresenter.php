<?php

namespace App\Presenters\Contractuals;

use CultureGr\Presenter\Presenter;

class ApplicationPositionPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'specialization_abbrv' => $this->specialization_shortname,
            'unit_abbrv' => $this->unit_name,
            'location' => $this->location,
            'has_locality' => $this->has_locality,
            'order' => $this->pivot->order, // It works if positions have been eager loaded through the application
            'locality' => (bool) $this->pivot->locality, // It works if positions have been eager loaded through the application
            'auxiliary_level' => $this->pivot->auxiliary_level, // It works if positions have been eager loaded through the application
        ];
    }
}
