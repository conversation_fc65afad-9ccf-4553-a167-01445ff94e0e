<?php

namespace App\Presenters\Contractuals;

use App\Models\Contractuals\Contest;
use Auth;
use CultureGr\Presenter\Presenter;

/** @mixin Contest */
class ContestShowPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'protocol' => $this->protocol_number.'/'.$this->protocol_date->format('d-m-Y'),
            'ada' => $this->ada,
            'start_date' => $this->start_date->format('d/m/Y H:i'),
            'end_date' => $this->end_date->format('d/m/Y H:i'),
            'locked_at' => $this->locked_at,
            'restricted_at' => $this->restricted_at,
            'rated_at' => $this->rated_at,
            'ranked_at' => $this->ranked_at,
            'organization' => $this->organization ?? '',
            'contract_duration' => $this->contract_duration ?? '',
            'undersigned_title_first_row' => $this->undersigned_title_first_row ?? '',
            'undersigned_title_second_row' => $this->undersigned_title_second_row ?? '',
            'undersigned_name' => $this->undersigned_name ?? '',
            'notifications_email' => $this->notifications_email ?? '',
            'can' => [
              'admin' => Auth::user()->can('contractuals.adminUnit'),
            ],
            'has_single_unit' => $this->hasSingleUnit(),
        ];
    }
}
