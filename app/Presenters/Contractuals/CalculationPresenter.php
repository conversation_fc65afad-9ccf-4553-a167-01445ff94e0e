<?php

namespace App\Presenters\Contractuals;

use App\Models\Contractuals\Calculation;
use CultureGr\Presenter\Presenter;

/** @mixin Calculation */
class CalculationPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'description' => $this->description,
            'run_at' => $this->run_at->format('Y-m-d H:i'),
            'distribution_run' => $this->distribution_run,
            'hire_runners_up' => $this->hire_runners_up,
            'is_latest' => $this->isLatest(),
            'is_distribution' => $this->distribution_run,
        ];
    }

    // FIXME: N+1 problem
    protected function isLatest()
    {
        return $this->contest->calculations()->latest('run_at')->first()->id === $this->id;
    }
}
