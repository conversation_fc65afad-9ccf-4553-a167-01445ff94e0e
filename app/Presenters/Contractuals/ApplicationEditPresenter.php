<?php

namespace App\Presenters\Contractuals;

use App\Models\Contractuals\Application;
use CultureGr\Presenter\Presenter;

/** @mixin Application */
class ApplicationEditPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'public_application_id' => $this->public_application_id,
            'protocol' => "$this->protocol_number/{$this->protocol_date->format('d-m-Y')}",
            'applicant_category' => $this->applicant_category,
            'applicant_category_name' => $this->getApplicantCategory(),
            'contest_id' => $this->contest_id,
            'auxiliary_level' => $this->auxiliary_level,
            'name' => $this->name,
            'surname' => $this->surname,
            'fathername' => $this->fathername,
            'mothername' => $this->mothername,
            'policeid_number' => $this->policeid_number,
            'afm' => $this->afm,
            'amka' => $this->amka,
            'birthdate' => $this->birthdate,
            'greek_nationality' => $this->greek_nationality,
            'address' => "$this->street, $this->postcode, $this->city",
            'phonenumber1' => $this->phonenumber1,
            'phonenumber2' => $this->phonenumber2,
            'email' => $this->email,
            'has_eight_months_employment' => $this->has_eight_months_employment,
            'impediment_eight_months' => $this->impediment_eight_months,
            'invalidated' => $this->invalidated,
            'invalidation_description' => $this->invalidation_description,
            'invalidation_description_id' => $this->invalidation_description_id,
            //            "rejected" => null,
            //            "rejection_description" => null,
            'positions' => ApplicationPositionPresenter::collection($this->positions)->toArray(),
            'degrees' => $this->degrees,
            'postgraduates' => $this->postgraduates,
            'doctorates' => $this->doctorates,
            'greekLanguages' => $this->greekLanguages,
            'language_skills' => $this->languageSkills,
            'computer_skills' => $this->computerSkills,
            //
            'experience' => $this->experiences->first(),
            'unemployment' => $this->unemployments->first(),
            'multiChildFamily' => $this->multiChildFamilies->first(),
            'threeChildFamily' => $this->threeChildFamilies->first(),
            'singleParentFamily' => $this->singleParentFamilies->first(),
            'minors' => $this->minors->first(),
            'disability' => $this->disabilities->first(),
            'familyDisability' => $this->familyDisabilities->first(),
        ];
    }

    protected function getApplicantCategory()
    {
        return match ($this->applicant_category) {
            1 => 'ΠΕ',
            2 => 'ΤΕ',
            3 => 'ΔΕ',
            4 => 'ΥΕ',
            default => throw new \InvalidArgumentException("Invalid applicant category: $this->applicant_category"),
        };
    }
}
