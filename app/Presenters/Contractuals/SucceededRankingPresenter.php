<?php

namespace App\Presenters\Contractuals;

use CultureGr\Presenter\Presenter;

class SucceededRankingPresenter extends Presenter
{
    public const AUXILIARY_LEVELS = ['1', 'Α', 'B', 'Γ'];

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'contest_id' => $this->contest_id,
            'application_id' => $this->application_id,
            'position_id' => $this->position_id,
            'protocol_number' => $this->application->protocol_number,
            'surname' => $this->application->surname,
            'name' => $this->application->name,
            'fathername' => $this->application->fathername,
            'policeid_number' => $this->application->policeid_number,
            'impediment_eight_months' => $this->positionRating->applicationRating->impediment_eight_months ? 'ΝΑΙ' : 'ΟΧΙ',
            'auxiliary_level' => $this::AUXILIARY_LEVELS[$this->positionRating->applicationRating->auxiliary_level],
            'score' => $this->positionRating->applicationRating->score,
            'rank' => $this->rank,
            'position_order' => $this->position_order,
            'employable' => $this->employable,
            'employable_in' => $this->employable_in,
            'employable_description' => $this->employable_description,
            'employed_in' => $this->employed_in,
            'accepted' => $this->accepted,
            'was_runner_up' => $this->was_runner_up,
        ];
    }
}
