<?php

namespace App\Presenters\Contractuals;

use App\Models\Contractuals\Contest;
use Auth;
use CultureGr\Presenter\Presenter;

/** @mixin Contest */
class ContestStatePresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'is_rated' => (bool) $this->rated_at,
            'is_ranked' => (bool) $this->ranked_at,
            'is_restricted' => $this->isRestricted(),
            'can' => [
                'admin' => Auth::user()->can('contractuals.admin'),
            ],
        ];
    }
}
