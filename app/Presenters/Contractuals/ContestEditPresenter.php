<?php

namespace App\Presenters\Contractuals;

use App\Models\Contractuals\Contest;
use CultureGr\Presenter\Presenter;

/** @mixin Contest */
class ContestEditPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'type_id' => $this->type_id,
            'protocol_number' => $this->protocol_number,
            'protocol_date' => $this->protocol_date->format('Y-m-d'),
            'description' => $this->description,
            'name' => $this->name,
            'ada' => $this->ada,
            'start_date' => $this->start_date->format('Y-m-d H:i:s'),
            'end_date' => $this->end_date->format('Y-m-d H:i:s'),
            'units' => $this->units->pluck('id'),
            'organization' => $this->organization,
            'contract_duration' => $this->contract_duration,
            'undersigned_title_first_row' => $this->undersigned_title_first_row,
            'undersigned_title_second_row' => $this->undersigned_title_second_row,
            'undersigned_name' => $this->undersigned_name,
            'notifications_email' => $this->notifications_email,
        ];
    }
}
