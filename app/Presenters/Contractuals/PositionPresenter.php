<?php

namespace App\Presenters\Contractuals;

use CultureGr\Presenter\Presenter;

class PositionPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'specialization_name' => $this->specialization_name,
            'specialization_abbrv' => $this->specialization_shortname,
            'code' => $this->code,
            'amount' => $this->amount,
            'location' => $this->location,
            'unit_abbrv' => $this->unit_name,
            'has_locality' => $this->has_locality,
            'employable_count' => $this->employable_count - $this->declined_count,
            'accepted_count' => $this->accepted_count,
            'declined_count' => $this->declined_count,
        ];
    }
}
