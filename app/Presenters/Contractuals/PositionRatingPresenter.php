<?php

namespace App\Presenters\Contractuals;

use CultureGr\Presenter\Presenter;

class PositionRatingPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'application_rating_id' => '0', // TODO implement this
            'application_id' => $this->application_id,
            'unit_name' => $this->unit_name,
            'specialization_name' => $this->specialization_name,
            'position_code' => $this->position_code,
            'position_location' => $this->position_location,
            'position_order' => $this->position_order,
            'locality' => $this->locality ? 'Ναι' : 'OXI',
            'rejected' => $this->rejected ? "❌ $this->rejection_description" : '',
        ];
    }
}
