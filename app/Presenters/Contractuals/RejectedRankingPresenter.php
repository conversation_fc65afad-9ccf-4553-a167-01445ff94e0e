<?php

namespace App\Presenters\Contractuals;

use CultureGr\Presenter\Presenter;

class RejectedRankingPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'contest_id' => $this->contest_id,
            'application_id' => $this->application_id,
            'position_id' => $this->position_id,
            'protocol_number' => $this->application->protocol_number,
            'surname' => $this->application->surname,
            'name' => $this->application->name,
            'fathername' => $this->application->fathername,
            'policeid_number' => $this->application->policeid_number,
            'rejection_description' => $this->positionRating->rejection_description,
        ];
    }
}
