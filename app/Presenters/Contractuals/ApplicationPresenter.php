<?php

namespace App\Presenters\Contractuals;

use App\Models\Contractuals\Application;
use CultureGr\Presenter\Presenter;

/** @mixin Application */
class ApplicationPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'public_application_id' => $this->public_application_id,
            'contest_id' => $this->contest_id,
            'protocol_number' => $this->protocol_number,
            'submitted_at' => $this->submitted_at->format('d-m-Y'),
            'applicant_category' => $this->getApplicantCategoryName(),
            'address' => "$this->street, $this->postcode, $this->city",
            'telephones' => "$this->phonenumber1, $this->phonenumber2",
            'email' => $this->email,
            'name' => $this->name,
            'surname' => $this->surname,
            'fathername' => $this->fathername,
            'birthdate' => $this->birthdate->format('d.m.Y'),
            'policeid_number' => $this->policeid_number,
            'validator' => $this->validator, // Who is going to rate the application
            'locked_at' => $this->locked_at,
            'is_auto_rated' => $this->is_auto_rated,
            'is_distributed' => $this->validating_unit_id !== null,
            'rater' => $this->rater, // Who has rated the application
            'rater_unit' => $this->rater_unit,
            'rated_at' => $this->rated_at?->format('d-m-Y H:i'),
            'score' => $this->score, // TODO pass score from ratings
            'rejected' => $this->rejected,
            'rejection_description' => $this->rejection_description, // TODO: Do I need this?
            'invalidated' => $this->invalidated, // TODO: check differences between 'rejected' and 'invalidated'
            'invalidation_description' => $this->invalidation_description,
            'validating_unit_id' => $this->validating_unit_id,
            'rated_by_unit_id' => $this->rated_by_unit_id,
        ];
    }

    protected function getApplicantCategoryName(): string
    {
        return [1 => 'ΠΕ', 2 => 'ΤΕ', 3 => 'ΔΕ', 4 => 'ΥΕ'][$this->applicant_category];
    }
}
