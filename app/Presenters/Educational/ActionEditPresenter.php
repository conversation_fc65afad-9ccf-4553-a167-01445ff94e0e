<?php

namespace App\Presenters\Educational;

use App\Models\Educational\Action;
use CultureGr\Presenter\Presenter;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/** @mixin Action */
class ActionEditPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'period_id' => $this->period_id,
            'type' => $this->type->name,
            'unit' => $this->unit->abbrv,
            'involvements' => $this->involvements->pluck('id'),
            'ongoing' => $this->ongoing,
            'is_digital' => match ($this->is_digital) {
                false => '0',
                true => '1',
                default => ''
            },
            'title' => $this->title,
            'context_id' => $this->context_id,
            'started_at' => $this->started_at ? $this->started_at->format('Y-m-d') : '',
            'ended_at' => $this->ended_at ? $this->ended_at->format('Y-m-d') : '',
            'duration_id' => $this->duration_id,
            'frequency' => $this->frequency,
            'description' => $this->description,
            'contributors' => $this->contributors,
            'link' => $this->link,
            'locations' => (object)$this->locations
                ->groupBy('location_type_id')
                ->map(function ($model) {
                    return $model->pluck('id');
                })->toArray(),
            'targets' => (object)$this->targets
                ->groupBy('target_type_id')
                ->map(function ($model) {
                    return $model->pluck('id');
                })->toArray(),
            'target_types' => (object)$this->targetTypes
                ->mapWithKeys(function ($targetType) {
                    return [$targetType['id'] => $targetType['participants']['amount']];
                })->toArray(),
            'collaborators' => (object)$this->collaborators
                ->groupBy('collaborator_type_id')
                ->map(function ($model) {
                    return $model->pluck('id');
                })->toArray(),
            'funds' => $this->funds->pluck('id'),
            'tools' => $this->tools->pluck('id'),
            'assessments' => $this->assessments->pluck('id'),
            'evaluation' => $this->evaluation,
            'disseminations' => $this->disseminations->pluck('id'),
            'attachments' => $this->media
                ->map(function (Media $file) {
                    return [
                        'id' => $file->id,
                        'name' => $file->name . '.' . $file->getCustomProperty('extension'),
                        'url' => $file->getUrl(),
                    ];
                }),
        ];
    }
}
