<?php

namespace App\Presenters\Educational;

use CultureGr\Presenter\Presenter;

class ActionIndexPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'period' => $this->period?->name ?? '',
            'unit' => $this->unit->abbrv,
            'type' => $this->type->name,
            'locations' => $this->locations->pluck('name'),
            'targetTypes' => $this->targets->map(function ($target) {
                return $target->targetType->name;
            })->unique(),
            'can' => [
                'update' => auth()->user()->can('educational.update'),
            ],
        ];
    }
}
