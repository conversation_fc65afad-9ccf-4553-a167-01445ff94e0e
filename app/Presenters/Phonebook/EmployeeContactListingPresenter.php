<?php

namespace App\Presenters\Phonebook;

use App\Models\Phonebook\EmployeeContact;
use CultureGr\Presenter\Presenter;

/** @mixin EmployeeContact */
class EmployeeContactListingPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'surname' => $this->surname,
            'rank' => $this->ranks->first()->display_name ?? '',
            'unit' => $this->unit->abbrv,
            'telephones' => $this->workTelephones()->get(['id', 'tel', 'info']),
            'email' => $this->getWorkEmail(),
            'isGGA' => $this->compass_id === null,
            'favorited' => auth()->check() && $this->users->pluck('id')->contains(auth()->id()),
        ];
    }
}
