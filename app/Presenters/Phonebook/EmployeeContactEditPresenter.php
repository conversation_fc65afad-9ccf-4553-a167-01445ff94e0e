<?php

namespace App\Presenters\Phonebook;

use App\Models\Phonebook\EmployeeContact;
use CultureGr\Presenter\Presenter;

/** @mixin EmployeeContact */
class EmployeeContactEditPresenter extends Presenter
{
    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'surname' => $this->surname,
            'rank_id' => $this->ranks->first()->id ?? '',
            'telephones' => $this->workTelephones->map->only(['id', 'tel', 'type_id', 'work', 'info', 'unit_id']),
            'email' => $this->getWorkEmail(),
            'isGGA' => $this->compass_id === null,
            'unit_id' => $this->unit_id,
        ];
    }
}
