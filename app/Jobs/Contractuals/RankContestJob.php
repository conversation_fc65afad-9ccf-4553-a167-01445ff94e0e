<?php

namespace App\Jobs\Contractuals;

use App\Mail\Contractuals\ContestRankingFailedMail;
use App\Mail\Contractuals\ContestRankingSucceededMail;
use App\Models\Contractuals\Contest;
use App\Services\Contractuals\ContestRankingCalculator;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class RankContestJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 6000;

    public int $tries = 1;

    private string $calculationStartedAt;

    public function __construct(
        protected int $contestId,
        protected string $description,
        protected bool $isHirringRunnerUps,
        protected string $notificationsEmail = ''
    ) {}

    public function tags(): array
    {
        return ['rank', 'contest:'.$this->contestId];
    }

    public function handle(ContestRankingCalculator $calculator)
    {
        Cache::forever('calculating_contest', true);

        $this->calculationStartedAt = now()->format('d/m/Y H:i');

        $calculation = $calculator->run(
            $this->contestId,
            $this->description,
            $this->isHirringRunnerUps,
        );

        Cache::forget('calculating_contest');

        Log::info("Contest ranking #{$calculation->id} completed");

        Mail::to($this->notificationsEmail)
            ->cc('<EMAIL>')
            ->queue(new ContestRankingSucceededMail(
                Contest::find($this->contestId),
                $calculation
            ));
    }

    public function failed(\Throwable $exception)
    {
        Cache::forget('calculating_contest');

        Log::error($exception->getMessage());

        Mail::to($this->notificationsEmail)
            ->cc('<EMAIL>')
            ->queue(new ContestRankingFailedMail(
                Contest::find($this->contestId),
                $this->calculationStartedAt,
                $this->description
            ));
    }
}
