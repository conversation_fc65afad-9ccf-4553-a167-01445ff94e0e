<?php

namespace App\Jobs\Contractuals;

use App\Mail\Contractuals\ContestDistributionFailedMail;
use App\Mail\Contractuals\ContestDistributionSucceededMail;
use App\Models\Contractuals\Contest;
use App\Services\Contractuals\ContestDistributionCalculator;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class DistributeContestJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 6000;

    public int $tries = 1;

    public function __construct(
        protected int $contestId,
        protected int $n = 2,
        protected int $limit = 500,
        protected string $notificationsEmail = ''
    ) {
    }

    public function tags(): array
    {
        return ['distribute', 'contest:'.$this->contestId];
    }

    public function handle(ContestDistributionCalculator $calculator)
    {
        Cache::forever('calculating_contest', true);

        $calculation = $calculator->run($this->contestId, $this->n, $this->limit);

        Cache::forget('calculating_contest');

        Log::info("Contest distribution #{$calculation->id} completed");

        Mail::to($this->notificationsEmail)
            ->cc('<EMAIL>')
            ->queue(new ContestDistributionSucceededMail($calculation, Contest::find($this->contestId)));
    }

    public function failed(\Throwable $exception)
    {
        Cache::forget('calculating_contest');

        Log::error($exception->getMessage());

        Mail::to($this->notificationsEmail)
            ->cc('<EMAIL>')
            ->queue(new ContestDistributionFailedMail(Contest::find($this->contestId)));
    }
}
