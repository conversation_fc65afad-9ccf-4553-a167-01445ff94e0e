<?php

namespace App\Jobs\Contractuals;

use App\Exports\Contractuals\TotalRejectedRankingsExport4765;
use App\Mail\Contractuals\RejectedRankingsExportFailed;
use App\Mail\Contractuals\RejectedRankingsExportSucceeded;
use App\Models\Contractuals\Calculation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Throwable;

class CreateRejectedRankingsExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 600;

    public int $tries = 1;

    public function __construct(
        protected Calculation $calculation,
        protected string $notificationsEmail = '',
        protected bool $forPublication = false
    ) {}

    public function tags(): array
    {
        $forPublication = $this->forPublication ? 'true' : 'false';

        return [
            'exporting-rejected',
            "contest:{$this->calculation->contest->id}",
            "calculation:{$this->calculation->id}",
            "publication:{$forPublication}"
        ];
    }

    public function handle()
    {
        Cache::forever('creating_contractuals_exports', true);

        info("Start creating rejected rankings export for contest #{$this->calculation->contest->id} (calculation #{$this->calculation->id})...");

        $temporaryDiskName = 'public';
        $temporaryFileName = "rejected-rankings-{$this->calculation->id}.xlsx";
        $fileName = "ΣΥΓΚΕΝΤΡΩΤΙΚΟΣ ΠΙΝΑΚΑΣ ΑΠΟΡΡΙΠΤΕΩΝ {$this->calculation->run_at->format('d-m-Y H:i')}";
        if ($this->forPublication) {
            $temporaryFileName = 'publishable-'.$temporaryFileName;
            $fileName = "ΣΥΓΚΕΝΤΡΩΤΙΚΟΣ ΠΙΝΑΚΑΣ ΑΠΟΡΡΙΠΤΕΩΝ ΔΙΑΥΓΕΙΑ {$this->calculation->run_at->format('d-m-Y H:i')}";
        }

        info("Creating temporary file '{$temporaryFileName}' on disk {$temporaryDiskName}...");

        $positions = $this->calculation
            ->contest
            ->load([
                'positions:id,contest_id,specialization_id,unit_id,code',
                'positions.contest:id,name,protocol_number,protocol_date,ada,organization,contract_duration,undersigned_title_first_row,undersigned_title_second_row,undersigned_name,notifications_email',
                'positions.unit:id,name',
                'positions.specialization:id,specialization_type_id,name',
                'positions.specialization.specializationType:id,name',
            ])
            ->positions;

        Excel::store(
            new TotalRejectedRankingsExport4765(
                collect($positions),
                $this->calculation->id,
                $this->forPublication
            ),
            $temporaryFileName,
            $temporaryDiskName
        );

        info("Adding temporary file '{$temporaryFileName}' to media collection...");

        $this->calculation->addMediaFromDisk($temporaryFileName, $temporaryDiskName)
            ->withCustomProperties(['extension' => 'xlsx'])
            ->usingName($fileName)
            ->toMediaCollection('exports', 'contractuals_disk');

        Storage::disk($temporaryDiskName)->delete($temporaryFileName);

        info("Rejected rankings export for contest #{$this->calculation->contest->id} (calculation #{$this->calculation->id}) created.");

        Cache::forget('creating_contractuals_exports');

        Mail::to($this->notificationsEmail)
            ->cc('<EMAIL>')
            ->queue(new RejectedRankingsExportSucceeded($this->forPublication, $this->calculation));
    }

    public function failed(Throwable $exception)
    {
        Cache::forget('creating_contractuals_exports');

        Log::error($exception->getMessage());

        Mail::to($this->notificationsEmail)
            ->cc('<EMAIL>')
            ->queue(new RejectedRankingsExportFailed($this->forPublication, $this->calculation));
    }
}
