<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Command\Command as ExitCode;

class DbSyncCommand extends Command
{
    protected $signature = 'db:sync
                            {database : The database to be exported}
                            {--filename= : The name of the output dump file}
                            {--ignore-tables= : The tables to ignore}
                            {--keep-file : Keep the sql dump file}';

    protected $description = 'Sync production database with local database';

    public function handle(): int
    {
        // Disable PHP timeout and memory limit to facilitate restoring big dump files
        ini_set('memory_limit', '-1');
        set_time_limit(0);

        // Ensure that this command cannot run in production environments
        if (! in_array(config('app.env'), ['local', 'staging'])) {
            $this->error('DB sync will only run on local and staging environments');

            return ExitCode::INVALID;
        }

        $remoteHost = config('dbsync.remote_host');
        $remotePort = config('dbsync.remote_port');
        $remoteUsername = config('dbsync.remote_username');
        $remotePassword = config('dbsync.remote_password');
        $localHost = config('dbsync.local_host');
        $localPort = config('dbsync.local_port');
        $localUsername = config('dbsync.local_username');
        $localPassword = config('dbsync.local_password');
        $database = $this->argument('database');
        $fileName = $this->option('filename') ?? $database.'-dump.sql';
        $ignoreTables = $this->option('ignore-tables');
        $keepFileAfterImport = $this->option('keep-file');

        // Dump remote database
        $this->line("▶ Dumping remote database <$database>...");

        // Calculate dump size
        exec("mysql \
            --host $remoteHost \
            --port $remotePort \
            -u $remoteUsername \
            -p'$remotePassword' \
            --silent \
            --skip-column-names \
            -se \"SELECT ROUND(SUM(data_length)) FROM information_schema.TABLES WHERE table_schema='$database';\"
            ", $output, $code);
        if ($code !== 0 || count($output) < 1) {
            $this->error('Could not read the remote database file size');

            return ExitCode::FAILURE;
        }
        $size = (int) $output[0];
        $sizeMB = number_format(round($size / 1024 / 1024, 2));

        $this->comment("Expected mysql dump file size is  $sizeMB MB");

        // Create mysql dump
        exec("
        mysqldump \
        --host $remoteHost \
        --port $remotePort \
        -u $remoteUsername \
        -p'$remotePassword' \
        --set-gtid-purged=OFF \
        --skip-lock-tables \
        --add-drop-table \
        --disable-keys \
        --extended-insert \
        --databases \
        --add-drop-database \
        {$this->getIgnoreString($ignoreTables)} \
        $database | pv --progress --size $size -W -t -e -r -a> $fileName", $output, $code);

        if ($code !== 0) {
            $this->error("Could not dump remote database $database");
            $this->clearMysqlDump($fileName);

            return ExitCode::FAILURE;
        }

        $this->comment("Remote database <$database> dumped successfully!");
        // $this->comment(implode(PHP_EOL, $output));

        // Restore local database
        $this->line('▶ Restoring local database...');

        exec("pv $fileName | mysql \
            -u $localUsername \
            -p'$localPassword' \
            --host $localHost \
            --port $localPort", $output, $code);

        if ($code !== 0) {
            $this->error("Could not restore local database $database");

            return ExitCode::FAILURE;
        }

        $this->comment("Local database <$database> restored successfully!");
        // $this->comment(implode(PHP_EOL, $output));

        // Keep mysql dump if --keep-files option has been provided
        if (! $keepFileAfterImport) {
            $this->clearMysqlDump($fileName);
        }

        $this->info("Finish syncing database $database");

        return ExitCode::SUCCESS;
    }

    private function clearMysqlDump($filename): void
    {
        unlink($filename);

        $this->comment('Dump file has been removed');
    }

    private function getIgnoreString(?string $optionString): string
    {
        if (is_null($optionString)) {
            return '';
        }
        $ignoreString = null;
        $ignoreTables = explode(',', $optionString);
        foreach ($ignoreTables as $name) {
            $ignoreString .= " --ignore-table=$name";
        }

        return $ignoreString;
    }
}
