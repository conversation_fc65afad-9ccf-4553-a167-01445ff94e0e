<?php

namespace App\Console\Commands\Contractuals;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ResetApplicationProtocolNumber extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contractuals:reset-protocol';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset the protocol number sequence';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            DB::connection('mysql_public_contractuals')->table('sequences')->truncate();
        } catch (\Throwable $e) {
            $this->error('Failed reseting FO application protocol numbers. '.$e->getMessage());

            return Command::FAILURE;
        }

        $this->info('Protocol numbers have been reset succesfully!');
        return Command::SUCCESS;
    }
}
