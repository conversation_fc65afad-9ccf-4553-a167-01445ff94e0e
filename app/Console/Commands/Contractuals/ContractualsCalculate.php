<?php

namespace App\Console\Commands\Contractuals;

use App\Models\Contractuals\Contest;
use App\Services\Contractuals\ContestRankingCalculator;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ContractualsCalculate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contractuals:calculate {contest} {description} {--distribution}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate contest rankings';

    protected $distributionRun = false;

    protected $calculator;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(ContestRankingCalculator $calculator)
    {
        parent::__construct();
        $this->calculator = $calculator;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $contest = Contest::find($this->argument('contest'));
        $description = $this->argument('description');

        if (! $contest) {
            $this->error('Contest not found.');

            return 1;
        }

        if (! $description) {
            $this->error('No description given.');

            return 1;
        }

        if ($this->option('distribution')) {
            $this->distributionRun = true;
        }

        // Only distribution run is allowed before locking the application rates
        if (! $contest->isRated() && ! $this->distributionRun) {
            $this->error('The contest is not finalized, not all application have been stored and rated');

            return 1;
        }
        // No distribution run is allowed after locking the application rates
        if ($contest->isRated() && $this->distributionRun) {
            $this->error('Contest finalized. Not possible to distribute for evaluations');

            return 1;
        }
        // We need to unlock ranking to be able to re-rank
        if ($contest->isRanked()) {
            $this->error('The contest is already been ranked');

            return 1;
        }
        if (! $contest->isRated()) {
            $this->error('Contest cannot be marked as rated because it contains unrated applications');

            return 1;
        }

        $this->line('Starting ranking of contest: ' . $contest->name);

        try {
            Cache::forever("calculating_contest", true);
            $calculation = $this->calculator->run($contest, $description, $this->distributionRun);
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
            $this->error($e->getMessage());

            return 1;
        } finally {
            Cache::forget("calculating_contest");
        }


        $this->line('Done!');

        return 0;
    }
}
