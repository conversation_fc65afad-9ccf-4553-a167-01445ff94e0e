<?php

namespace App\Console\Commands\Contractuals;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ContractualsOnlineUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contractuals:online-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Capture count of current front-office online users to database';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $responseA = Http::withHeaders([
                config('public_contractuals.api.header') => config('public_contractuals.api.key'),
            ])
                ->get(config('public_contractuals.url').'/api/backoffice/online-users')
                ->throw();
        } catch (\Throwable $e) {
            Log::error('Unable to capture contractuals front-office online users.');

            return 1;
        }

        $onlineUsers = (int) $responseA->json('online_users');

        DB::connection('mysql_contractuals')
            ->table('online_users')
            ->insert([
                'users' => $onlineUsers,
                'logged_at' => now(),
            ]);

//        $this->line("Online users: $onlineUsers");

        return 0;
    }
}
