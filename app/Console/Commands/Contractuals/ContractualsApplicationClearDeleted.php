<?php

namespace App\Console\Commands\Contractuals;

use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ContractualsApplicationClearDeleted extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contractuals:application-clear-deleted {contest}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove applications which are deleted from the public-apptree database';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $contest = Contest::find($this->argument('contest'));

        if (! $contest) {
            $this->error('Contest not found.');

            return 1;
        }

        if ($contest->type_id === 1) {
            $this->error('Contest type not supported');

            return 1;
        }

        $applicationIds = DB::connection('mysql_public_contractuals')
            ->table('applications')
            ->where('contest_id', $contest->id)
            ->whereNotNull('submitted_at')
            ->pluck('id');

        DB::connection('mysql_contractuals')->beginTransaction();

        try {
            $applicationCount = Application::where('contest_id', $contest->id)
                ->whereNotIn('public_application_id', $applicationIds)
                ->count();

            Application::where('contest_id', $contest->id)
                ->whereNotIn('public_application_id', $applicationIds)
                ->orderBy('id')
                ->lazyById()
                ->each(function ($application) {
                    $application->qualifications()->each(function ($qualification) {
                        $qualification->qualifiable->forceDelete();
                    });
                    $application->qualifications()->forceDelete();
                    $application->evaluations()->forceDelete();
                    $application->positions()->sync([]);
                    $application->ratings()->delete();
                });

            Application::where('contest_id', $contest->id)
                ->whereNotIn('public_application_id', $applicationIds)
                ->delete();

            DB::connection('mysql_contractuals')->commit();

            $this->line("Deleted $applicationCount applications.");
        } catch (\Throwable $e) {
            DB::connection('mysql_contractuals')->rollBack();

            $this->error('Failed deleting applications. '.$e->getMessage());

            return 1;
        }

        return 0;
    }
}
