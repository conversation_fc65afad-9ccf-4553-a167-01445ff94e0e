<?php

namespace App\Console\Commands\Contractuals;

use App\Enums\Contractuals\EvaluationType;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;
use App\Models\User;
use App\Services\Contractuals\ApplicationRaters\ApplicationRaterRegistry;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ContractualsContestRate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contractuals:contest-rate {contestId : The ID of the contest}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create new ratings for all applications of a contest';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(protected ApplicationRaterRegistry $applicationRaterRegistry)
    {
        parent::__construct();
    }

    public function handle(): int
    {
        $contest = Contest::find($this->argument('contestId'));

        if (! $contest) {
            $this->error('Contest not found.');

            return 1;
        }

        $this->line('Starting rating applications for contest '.$contest->id.'...');

        Cache::forever('calculating_contest', true);

        DB::connection('mysql_contractuals')->beginTransaction();

        try {
            $totalApplications = Application::where('contest_id', $contest->id)->count();

            $bar = $this->output->createProgressBar($totalApplications);
            $bar->start();

            Application::where('contest_id', $contest->id)
                ->whereNotNull('rated_at')
                ->orderBy('id')
                ->lazyById()
                ->each(function ($application) use ($bar, $contest) {
                    $this->applicationRaterRegistry->pull($contest->type_id)->rate($application);
                    $this->markAsEvaluated($application);

                    $bar->advance();
                });

            $bar->finish();

            DB::connection('mysql_contractuals')->commit();

            $this->info(PHP_EOL.'Applications rated successfully');

            return 0;
        } catch (\Throwable $e) {
            DB::connection('mysql_contractuals')->rollBack();

            $this->error('Failed rating applications. '.$e->getMessage());

            return 1;
        } finally {
            Cache::forget('calculating_contest');
        }
    }

    protected function markAsEvaluated(Application $application)
    {
        // Fetch the previous applicationRating before the last one ordered by id desc
        $previousRating = $application->applicationRatings()->orderByDesc('id')->skip(1)->first();

//        $application->is_auto_rated = false;
        $application->rated_at = now();
//        $application->rated_by = $previousRating->evaluated_by;
//        $application->rated_by_unit_id = $previousRating->evaluated_by_unit_id;
        $application->save();

        $evaluations = $application->applicationRatings()->count();
        $lastRating = $application->applicationRatings()->orderByDesc('id')->first();
        $lastRating->evaluation_number = $evaluations;
        $lastRating->evaluation_type = EvaluationType::MANUAL;
        $lastRating->evaluated_by = $previousRating->evaluated_by;
        $lastRating->evaluated_by_unit_id = $previousRating->evaluated_by_unit_id;
        $lastRating->evaluated_by_admin = $previousRating->evaluated_by_admin;
        $lastRating->save();
    }
}
