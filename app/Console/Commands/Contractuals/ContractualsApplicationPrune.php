<?php

namespace App\Console\Commands\Contractuals;

use App\Models\Contractuals\Application;
use App\Models\Contractuals\Calculation;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Ranking;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Command\Command as CommandAlias;

class ContractualsApplicationPrune extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contractuals:application-prune
                                {contestId : The ID of the contest}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all applications for a given contest';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): int
    {
        $contest = Contest::find($this->argument('contestId'));

        if (! $contest) {
            $this->error('Contest not found.');

            return CommandAlias::FAILURE;
        }

        $this->line('Starting deleting applications for contest '.$contest->id.'...');

        DB::connection('mysql_contractuals')->beginTransaction();

        try {
            $totalApplications = Application::where('contest_id', $contest->id)->count();

            $bar = $this->output->createProgressBar($totalApplications);
            $bar->start();

            Application::where('contest_id', $contest->id)
                ->orderBy('id')
                ->lazyById()
                ->each(function ($application) use ($bar) {
                    $application->qualifications()->each(function ($qualification) {
                        $qualification->qualifiable->forceDelete();
                    });
                    $application->qualifications()->forceDelete();
                    $application->evaluations()->forceDelete();
                    $application->positions()->sync([]);
                    $application->ratings()->delete();

                    $bar->advance();
                });

            Application::where('contest_id', $contest->id)->delete();

            Ranking::where('contest_id', $contest->id)->delete();

            Calculation::where('contest_id', $contest->id)->delete();

            $contest->markAsNotRanked();

            $bar->finish();

            DB::connection('mysql_contractuals')->commit();

            $this->info(PHP_EOL.'Applications deleted successfully');

            return CommandAlias::SUCCESS;

        } catch (\Throwable $e) {

            DB::connection('mysql_contractuals')->rollBack();

            $this->error('Failed deleting applications. '.$e->getMessage());

            return 1;
        }
    }
}
