<?php

namespace App\Console\Commands\Contractuals;

use App\Exceptions\Contractuals\ApplicationCouldNotBeImportedException;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;
use App\Services\Contractuals\ApplicationImporter;
use App\Services\Contractuals\ApplicationRaters\ApplicationRaterRegistry;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Console\Command\Command as ExitCode;

class ContractualsApplicationImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contractuals:application-import {contest} {--application=} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import new applications from public-apptree database';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(ApplicationImporter $importer, ApplicationRaterRegistry $rater)
    {
        $contest = Contest::find($this->argument('contest'));

        if (! $contest) {
            $this->error('Contest not found.');

            return ExitCode::INVALID;
        }

        if ($contest->type_id === 1) {
            $this->error('Contest type not supported');

            return ExitCode::INVALID;
        }

        if (! $contest->isLocked()) {
            $this->error('Contest is not locked.');

            return ExitCode::INVALID;
        }

        $applicationId = $this->option('application');

        if (is_null($applicationId)) {
            if ($this->option('force')) {
                $applicationIds = DB::connection('mysql_public_contractuals')
                    ->table('applications')
                    ->where('contest_id', $contest->id)
                    ->where('is_submitted', true)
                    ->pluck('id');
            } else {
                $applicationIds = DB::connection('mysql_public_contractuals')
                    ->table('applications')
                    ->where('contest_id', $contest->id)
                    ->where('is_submitted', true)
                    ->where('is_imported', false)
                    ->pluck('id');
            }

            // Double check and update already imported applications.
            if (! $this->option('force')) {
                $importedIds = Application::whereIn('public_application_id', $applicationIds)->pluck('id');
                DB::connection('mysql_public_contractuals')
                    ->table('applications')
                    ->whereIn('id', $importedIds)
                    ->update(['is_imported' => true]);

                $applicationIds = DB::connection('mysql_public_contractuals')
                    ->table('applications')
                    ->where('is_submitted', true)
                    ->where('is_imported', false)
                    ->pluck('id');
            }
        } else {
            $applicationIds = collect([$applicationId]);
        }

        $this->line('Starting importing applications for contest '.$contest->id.'...');
        Log::info('Start importing');

        $start = now();

        $bar = $this->output->createProgressBar($applicationIds->count());
        $bar->start();

        DB::connection('mysql_public_contractuals')
            ->table('applications')
            ->join('users', 'users.id', '=', 'applications.user_id')
            ->select(
                'applications.*',
                'users.first_name',
                'users.last_name',
                'users.father_name',
                'users.mother_name',
                'users.afm',
            )
            ->where('applications.contest_id', $contest->id)
            ->whereIn('applications.id', $applicationIds)
            ->orderBy('applications.id')
            ->lazy()
            ->each(function ($publicApplication) use ($importer, $rater, $bar) {
                try {
                    $importer->run($publicApplication);
                    $success = true;
                } catch (ApplicationCouldNotBeImportedException $e) {
                    $this->error(PHP_EOL.'Failed importing application: '.$publicApplication->id);
                    $success = false;
                }

                if ($success) {
                    // Rate application
                    $application = Application::where('public_application_id', $publicApplication->id)->first();
                    $rater->pull($application->contest->type_id)->rate($application);
                    $application->markAsInitiallyImported();

                    // Mark application as imported
                    DB::connection('mysql_public_contractuals')
                        ->table('applications')
                        ->where('id', $publicApplication->id)
                        ->update(['is_imported' => true]);

                    $bar->advance();

                } else {
                    $this->error(PHP_EOL.'Failed importing application: '.$publicApplication->id);
                }
            });

        $bar->finish();
        $this->info(PHP_EOL.'Applications imported successfully ('.now()->diff($start)->format('%H:%I:%S').')');
        Log::info('Finish importing');

        // If contest has single unit, assign all applications to it.
        // No need to run DistributeContest job at all
        if ($contest->hasSingleUnit()) {
            $unitId = $contest->units()->first()->id;
            $contest->applications()->update(['validating_unit_id' => $unitId]);
        }

        return 0;
    }
}
