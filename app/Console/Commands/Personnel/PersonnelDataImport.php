<?php

namespace App\Console\Commands\Personnel;

use App\Models\Unit;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\Filesystem\Exception\FileNotFoundException;
use Symfony\Component\HttpFoundation\File\Exception\FileException;

class PersonnelDataImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'personnel:data-import {--keep-files}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update personnel data with compass import';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('Start personnel data import at '.now()->toDateTimeString());

        try {
            if (! $this->option('keep-files')) {
                $this->getFiles();
            }

            DB::connection('mysql_personnel')->disableQueryLog();
            $this->execMysqlScript('compass_import_prepare');
            $this->importPersonnel();
            $this->importEpethrida();
            $maxUnitId = Unit::max('id');
            $this->execMysqlScript('compass_import');
            $this->updateUnitTypes($maxUnitId);
            DB::connection('mysql_personnel')->enableQueryLog();
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
            Log::info('Exited personnel data import at '.now()->toDateTimeString());

            return 1;
        }

        Log::info('Finished personnel data import at '.now()->toDateTimeString());

        return 0;
    }

    protected function importPersonnel()
    {
        $this->info('Import data to pers table');

        DB::connection('mysql_personnel')->table('pers')->truncate();

        $handle = fopen(Storage::path('personnel/PERR00030'), 'r');
        if ($handle) {
            $data = [];
            $i = 0;
            while ($line = fgets($handle)) {
                // Process this line and save to database
                $line = rtrim(iconv('WINDOWS-1253', 'utf-8', $line), "\r\n");

                if (! empty($line)) {
                    $i++;
                    $rowData = [];
                    $rowData['AM'] = trim(mb_substr($line, 5, 9 - 5));
                    $rowData['FULLNAME'] = trim(mb_substr($line, 10, 30 - 10));
                    $rowData['CD_EIDIK'] = trim(mb_substr($line, 31, 35 - 31));
                    $rowData['EIDIK_DSCR'] = trim(mb_substr($line, 36, 55 - 36));
                    $rowData['CD_YPHR_COMP'] = trim(mb_substr($line, 56, 60 - 56));
                    $rowData['YPHR_COMP_DSCR'] = trim(mb_substr($line, 61, 103 - 61));
                    $rowData['CD_NOMOS'] = trim(mb_substr($line, 104, 108 - 104));
                    $rowData['NOMOS_DSCR'] = trim(mb_substr($line, 109, 129 - 109));
                    $rowData['CD_EIDIK3'] = trim(mb_substr($line, 130, 134 - 130));
                    $rowData['EIDIK3_DSCR'] = trim(mb_substr($line, 135, 155 - 135));
                    $rowData['LNAME2'] = trim(mb_substr($line, 156, 181 - 156));
                    $rowData['ORGANIKH'] = trim(mb_substr($line, 182, 222 - 182));
                    $rowData['SHIFT_TYPE'] = trim(mb_substr($line, 223, 243 - 223));
                    $rowData['CD_YPHRESIA_FULL'] = trim(mb_substr($line, 244, 248 - 244));
                    $rowData['YPHRESIA_FULL_DSCR'] = trim(mb_substr($line, 249, 269 - 249));
                    $rowData['DT_END'] = $this->parseCompassDate(trim(mb_substr($line, 270, 277 - 270)));
                    $rowData['STOP_FLG'] = trim(mb_substr($line, 279, 282 - 279));
                    $rowData['CD_ETEROAPASX'] = trim(mb_substr($line, 284, 287 - 284));
                    $rowData['ETEROAPASX'] = trim(mb_substr($line, 289, 299 - 289));
                    $rowData['FNAME'] = trim(mb_substr($line, 309, 329 - 309));
                    $rowData['LNAME'] = trim(mb_substr($line, 330, 350 - 330));
                    $rowData['DNAME'] = trim(mb_substr($line, 351, 371 - 351));
                    $rowData['DT_START'] = $this->parseCompassDate(trim(mb_substr($line, 300, 307 - 300)));
                    $rowData['CAT'] = trim(mb_substr($line, 372, 392 - 372));
                    $rowData['DT_TRIM'] = $this->parseCompassDate(trim(mb_substr($line, 393, 400 - 393)));
                    $rowData['AFM'] = trim(mb_substr($line, 402, 422 - 402));
                    $rowData['AMKA'] = trim(mb_substr($line, 423, 448 - 423));
                    $rowData['BANK'] = trim(mb_substr($line, 449, 469 - 449));
                    $rowData['IBAN'] = trim(mb_substr($line, 470, 500 - 470));
                    $rowData['MNAME'] = trim(mb_substr($line, 501, 521 - 501));
                    $rowData['WNAME'] = trim(mb_substr($line, 522, 547 - 522));
                    $rowData['DT_BIRTH'] = $this->parseCompassDate(trim(mb_substr($line, 548, 555 - 548)));
                    $rowData['ADDR_NAME'] = trim(mb_substr($line, 557, 577 - 557));
                    $rowData['ADDR_NO'] = trim(mb_substr($line, 578, 598 - 578));
                    $rowData['ADDR_CITY'] = trim(mb_substr($line, 599, 619 - 599));
                    $rowData['ADDR_DEP'] = trim(mb_substr($line, 620, 640 - 620));
                    $rowData['ADDR_TK'] = trim(mb_substr($line, 641, 661 - 641));
                    $rowData['PHONE'] = preg_replace("[\D]", '', trim(mb_substr($line, 662, 682 - 662)));
                    $rowData['PHONE_B'] = preg_replace("[\D]", '', trim(mb_substr($line, 683, 708 - 683)));
                    $rowData['MOBILE'] = preg_replace("[\D]", '', trim(mb_substr($line, 709, 734 - 709)));
                    $rowData['EMAIL'] = trim(mb_substr($line, 735, 785 - 735));
                    $data[] = $rowData;

                    if (($i + 1) % 500 == 0) {
                        DB::connection('mysql_personnel')->table('pers')->insert($data);
                        $data = [];
                    }
                }
            }
            if (count($data) > 0) {
                DB::connection('mysql_personnel')->table('pers')->insert($data);
                $data = [];
            }
        }
        fclose($handle);
    }

    protected function importEpethrida()
    {
        $this->info('Import data to epethrida table');

        DB::connection('mysql_personnel')->table('epethrida')->truncate();

        $handle = fopen(Storage::path('personnel/PERR00030B'), 'r');
        if ($handle) {
            $data = [];
            $i = 0;
            while ($line = fgets($handle)) {
                // Process this line and save to database
                $line = rtrim(iconv('WINDOWS-1253', 'utf-8', $line), "\r\n");

                if (! empty($line)) {
                    $i++;

                    $rowData = [];
                    $rowData['AM'] = trim(mb_substr($line, 5, 9 - 5));
                    $rowData['FULLNAME'] = trim(mb_substr($line, 10, 30 - 10));
                    $rowData['CD_EIDIK'] = trim(mb_substr($line, 31, 35 - 31));
                    $rowData['EIDIK_DSCR'] = trim(mb_substr($line, 36, 55 - 36));
                    $rowData['CD_YPHR_COMP'] = trim(mb_substr($line, 56, 60 - 56));
                    $rowData['YPHR_COMP_DSCR'] = trim(mb_substr($line, 61, 103 - 61));
                    $rowData['CD_NOMOS'] = trim(mb_substr($line, 104, 108 - 104));
                    $rowData['NOMOS_DSCR'] = trim(mb_substr($line, 109, 129 - 109));
                    $rowData['CD_EIDIK3'] = trim(mb_substr($line, 130, 134 - 130));
                    $rowData['EIDIK3_DSCR'] = trim(mb_substr($line, 135, 155 - 135));
                    $rowData['CD_YPHRESIA'] = trim(mb_substr($line, 156, 160 - 156));
                    $rowData['YPHRESIA_DSCR'] = trim(mb_substr($line, 161, 181 - 161));
                    $rowData['ORGANIKH'] = trim(mb_substr($line, 182, 222 - 182));
                    $rowData['PLACE'] = trim(mb_substr($line, 223, 243 - 223));
                    $rowData['CD_YPHRESIA_FULL'] = trim(mb_substr($line, 244, 248 - 244));
                    $rowData['YPHRESIA_FULL_DSCR'] = trim(mb_substr($line, 249, 269 - 249));
                    $rowData['DT_END'] = $this->parseCompassDate(trim(mb_substr($line, 270, 277 - 270)));
                    $rowData['STOP_FLG'] = trim(mb_substr($line, 279, 282 - 279));
                    $rowData['MANAGER'] = trim(mb_substr($line, 289, 309 - 289));
                    $rowData['ESDD'] = trim(mb_substr($line, 315, 334 - 315));
                    $rowData['ARGIA_DIATHES'] = trim(mb_substr($line, 336, 340 - 336));
                    $rowData['MANAGER4201'] = trim(mb_substr($line, 345, 366 - 345));
                    $rowData['KATHGORIA'] = trim(mb_substr($line,367, 369 - 367));

                    $data[] = $rowData;

                    if (($i + 1) % 500 == 0) {
                        DB::connection('mysql_personnel')->table('epethrida')->insert($data);
                        $data = [];
                    }
                }
            }
            if (count($data) > 0) {
                DB::connection('mysql_personnel')->table('epethrida')->insert($data);
                $data = [];
            }
        }
        fclose($handle);
    }

    /**
     * Copy files to app location.
     */
    protected function getFiles()
    {
        $this->info('Try copping source files...');

        $sourcePath = '/mnt/backup_compass';
        $destinationPath = Storage::path('personnel');
        $sourceFile = $sourcePath.'/'.'PERR00030';
        $sourceFileB = $sourcePath.'/'.'PERR00030B';
        $destinationFile = $destinationPath.'/'.'PERR00030';
        $destinationFileB = $destinationPath.'/'.'PERR00030B';

        $this->info('Checking if source files exist...');

        if (! file_exists($sourceFile)) {
            $this->error('File PERR00030 not found');
            throw new FileNotFoundException('File PERR00030 not found');
        }

        if (! file_exists($sourceFileB)) {
            $this->error('File PERR00030B not found');
            throw new FileNotFoundException('File PERR00030B not found');
        }

        $this->info('Checking if source files are up to date...');

        if (file_exists($destinationFile) && filemtime($sourceFile) <= filemtime($destinationFile)) {
            $this->error('File PERR00030 is not newer than existing file');
            throw new FileException('File PERR00030 is not newer than existing file');
        }

        if (file_exists($destinationFileB) && filemtime($sourceFileB) <= filemtime($destinationFileB)) {
            $this->error('File PERR00030B is not newer than existing file');
            throw new FileException('File PERR00030B is not newer than existing file');
        }

        // FIXME: Don't need this because if the destination file already exists, it will be overwritten see https://www.php.net/manual/en/function.copy.php.
        // remove old files
//        Storage::delete('personnel/PERR00030');
//        Storage::delete('personnel/PERR00030B');

        // overwrite files
        File::copy($sourceFile, $destinationFile);
        File::copy($sourceFileB, $destinationFileB);

        $this->info('Files copied successfully');
    }

    /**
     * Run external mysql script.
     * @param string $script
     */
    protected function execMysqlScript($script)
    {
        $this->info("Running mysql script: $script");

        DB::connection('mysql_personnel')->unprepared(file_get_contents(database_path("scripts/$script.sql")));
    }

    protected function parseCompassDate($string)
    {
        if (empty($string)) {
            return null;
        } else {
            return Carbon::createFromFormat('ymd', $string)->toDateString();
        }
    }

    protected function updateUnitTypes($startId)
    {
        $this->info('Finishing up...');

        Unit::where('id', '>', $startId)->each(function ($unit) {
            is_numeric(Str::after($unit->compass_id, $unit->parent->compass_id)) ? $type = 70 : $type = 80;
            if ($unit->unit_type_id != $type) {
                $unit->update(['unit_type_id' => $type]);
            }
        });
    }
}
