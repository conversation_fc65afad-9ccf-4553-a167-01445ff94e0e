<?php

namespace App\Console\Commands;

use App\Models\Activity;
use App\Models\Log;
use Illuminate\Console\Command;

class ClearLogs extends Command
{
    protected $signature = 'log:clear
                            {--since-days=30 : Clear logs older than x days}';

    protected $description = 'Clear application logs older than 30 days';

    public function handle()
    {
        $since = $this->option('since-days');

        $this->line('Clearing application logs...');

        $numberOfDeletedLogs = $this->clearLogs((int) $since);

        $this->info(number_format($numberOfDeletedLogs).' application logs have been deleted');

        $this->line('Clearing activity logs...');

        $numberOfDeletedActivityLogs = $this->clearActivityLogs();

        $this->info(number_format($numberOfDeletedActivityLogs).' activity logs have been deleted');

        return Command::SUCCESS;
    }

    private function clearLogs(?int $since): int
    {
        return Log::query()
            ->when($since, function ($query) use ($since) {
                $query->where('timestamp', '<=', now()->subDays($since));
            })
            ->delete();
    }

    private function clearActivityLogs(): int
    {
        return Activity::query()
            ->where('created_at', '<=', now()->subYear())
            ->delete();
    }
}
