<?php

namespace App\Rules\Educational;

use App\Models\Educational\Action;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\Rule;

class AtLeastOneParticipantPerTargetType implements Rule, DataAwareRule
{
    protected array $data = [];

    public function __construct(protected Action $action)
    {
    }

    public function passes($attribute, $value): bool
    {
        if (! $this->attributeIsRequiredForThisActionType($attribute)) {
            return true;
        }

        if (empty($this->data['targets'])) {
            return true;
        }

        foreach ($value as $targetTypeId => $amount) {
            if ($amount <= 0) {
                return false;
            }
        }

        return true;
    }

    public function message(): string
    {
        return 'Ο αριθμός συμμετεχόντων πρεπει να είναι μεγαλύτερος του μηδενός για κάθε κατηγορία.';
    }

    public function setData($data)
    {
        $this->data = $data;
        return $this;
    }

    protected function attributeIsRequiredForThisActionType($attribute): bool
    {
        $types = Action::REQUIRED_FIELDS[$attribute];

        return in_array($this->action->type_id, $types);
    }
}
