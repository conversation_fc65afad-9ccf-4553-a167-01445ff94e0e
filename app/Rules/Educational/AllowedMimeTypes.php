<?php

namespace App\Rules\Educational;

use Illuminate\Contracts\Validation\Rule;

class AllowedMimeTypes implements Rule
{
    public function __construct()
    {
        //
    }

    public function passes($attribute, $value)
    {
        foreach ($value as $attachment) {
            if (! in_array($attachment->getMimeType(), [
                'image/png',
                'image/jpeg',
                'application/pdf',
            ])) {
                return false;
            }
        }

        return true;
    }

    public function message()
    {
        return 'Τα επιλεγμένα αρχεία μπορούν να είνα μόνο φωτογραφίες τύπου jpeg, jpg, png ή αρχεία pdf.';
    }
}
