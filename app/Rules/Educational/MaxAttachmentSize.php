<?php

namespace App\Rules\Educational;

use Illuminate\Contracts\Validation\Rule;

class MaxAttachmentSize implements Rule
{
    public function __construct(protected int $limit)
    {
    }

    public function passes($attribute, $value)
    {
        foreach ($value as $attachment) {
            if ($attachment->getSize() > $this->limit * 1048576) {
                return false;
            }
        }

        return true;
    }

    public function message()
    {
        return 'Το μέγεθος του κάθε αρχείου δεν μπορεί να υπερβαίνει το όριο των '.$this->limit.'MB';
    }
}
