<?php

namespace App\Rules\Educational;

use App\Models\Educational\Action;
use Illuminate\Contracts\Validation\ImplicitRule;

class AttributeExistsInModel implements ImplicitRule
{
    public function __construct(protected Action $action)
    {
    }

    public function passes($attribute, $value): bool
    {
        // If attribute is a required field, check if it exists in the model.
        if ($this->attributeIsRequiredForThisActionType($attribute)) {
            return isset($this->action->{$attribute});
        }

        // Otherwise, it is not required and pass the validation.
        return true;
    }

    public function message(): string
    {
        return 'Το πεδίο είναι υποχρεωτικό για την υποβολή της αίτησης.';
    }

    protected function attributeIsRequiredForThisActionType($attribute): bool
    {
        $types = Action::REQUIRED_FIELDS[$attribute];

        return in_array($this->action->type_id, $types);
    }
}
