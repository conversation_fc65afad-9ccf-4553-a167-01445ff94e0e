<?php

namespace App\Rules\Educational;

use Illuminate\Contracts\Validation\Rule;

class AtLeastOnePerCategory implements Rule
{
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  array<int, array>|int  $value
     */
    public function passes($attribute, $value): bool
    {
        foreach ($value as $locationTypeId => $locations) {
            if (empty($locations)) {
                return false;
            }
        }

        return true;
    }

    public function message(): string
    {
        return 'Πρέπει να καταχωρίσετε μία τουλάχιστον επιλογή για κάθε κατηγορία.';
    }
}
