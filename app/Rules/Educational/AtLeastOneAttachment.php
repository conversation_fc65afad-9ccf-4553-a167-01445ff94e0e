<?php

namespace App\Rules\Educational;

use App\Models\Educational\Action;
use Illuminate\Contracts\Validation\ImplicitRule;

class AtLeastOneAttachment implements ImplicitRule
{
    public function __construct(protected Action $action)
    {

    }

    public function passes($attribute, $value)
    {
        return $this->action->getMedia('actions')->count() > 0;
    }

    public function message()
    {
        return 'Η αίτηση πρέπει να έχει τουλάχιστον ένα συνμμένο για να υποβληθεί';
    }
}
