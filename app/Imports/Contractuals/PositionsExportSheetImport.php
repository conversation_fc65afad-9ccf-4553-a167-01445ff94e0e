<?php

namespace App\Imports\Contractuals;

use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Position;
use App\Models\Contractuals\Specialization;
use App\Models\Unit;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class PositionsExportSheetImport implements ToModel, WithCalculatedFormulas, WithBatchInserts, WithChunkReading
{
    protected Contest $contest;
    protected array $unitIds;
    protected array $specializationIds;

    public function __construct(Contest $contest)
    {
        $this->contest = $contest;
        $this->unitIds = Unit::pluck('id')->toArray();
        $this->specializationIds = Specialization::pluck('id')->toArray();
    }

    public function model(array $row)
    {
        for ($i=0; $i<5; $i++) {
            if (!isset($row[$i]) || empty($row[$i]) || $row[$i] == 0 || $row[$i] == '') {
                return null;
            }
        }

        if (!in_array($row[1], $this->unitIds)) {
            Log::error("Invalid unit.id in contractual.positions import. id: {$row[1]}. code: {$row[0]}");
            return null;
        }

        if (!in_array($row[3], $this->specializationIds)) {
            Log::error("Invalid specialization.id in contractual.positions import. id: {$row[3]}. code: {$row[0]}");
            return null;
        }

        return new Position([
            'contest_id' => $this->contest->id,
            'code' => $row[0],
            'unit_id' => $row[1],
            'location' => $row[2],
            'specialization_id' => $row[3],
            'amount' => $row[4],
            'has_locality' => $row[5] ?? 0,
        ]);
    }

    public function batchSize(): int
    {
        return 2;
    }

    public function chunkSize(): int
    {
        return 2;
    }
}
