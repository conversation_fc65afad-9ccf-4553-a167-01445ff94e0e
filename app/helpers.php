<?php

use Carbon\Carbon;
use JetBrains\PhpStorm\Pure;
use Symfony\Component\HttpFoundation\StreamedResponse;

function flash(?string $title = null, ?string $message = null)
{
    $flash = app('App\Http\Flash');

    if (func_num_args() == 0) {
        return $flash;
    }

    return $flash->info($title, $message);
}

//function dateInGreek($format, $timestamp = null): bool|string
//{
//    static $targetFormatPatterns = [
//        // The formats that we need to take care of the Month
//        '%A, %e %B %Y',
//        '%A, %d %B %Y',
//        '%e %B',
//    ];
//
//    static $replacements = [
//        'Ιανουάριος' => 'Ιανουαρίου',
//        'Φεβρουάριος' => 'Φεβρουαρίου',
//        'Μάρτιος' => 'Μαρτίου',
//        'Απρίλιος' => 'Απριλίου',
//        'Μάιος' => 'Μαΐου',
//        'Ιούνιος' => 'Ιουνίου',
//        'Ιούλιος' => 'Ιουλίου',
//        'Αύγουστος' => 'Αυγούστου',
//        'Σεπτέμβριος' => 'Σεπτεμβρίου',
//        'Οκτώβριος' => 'Οκτωβρίου',
//        'Νοέμβριος' => 'Νοεμβρίου',
//        'Δεκέμβριος' => 'Δεκεμβρίου',
//    ];
//
//    $timestamp = $timestamp ?: time();
//
//    $needsReplacement = false;
//    foreach ($targetFormatPatterns as $pattern) {
//        if (strpos($format, $pattern) !== false) {
//            $needsReplacement = true;
//            break;
//        }
//    }
//
//    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
//        setlocale(LC_TIME, 'el');
//        $formatted = iconv('Windows-1253', 'UTF-8', Carbon::parse($timestamp)->formatLocalized($format));
//    } else {
//        setlocale(LC_TIME, 'el_GR.utf8');
//        $formatted = Carbon::parse($timestamp)->formatLocalized($format);
//    }
//
//    return $needsReplacement ? strtr($formatted, $replacements) : $formatted;
//}

function getFileFromResponse(\Illuminate\Http\Client\Response $response): StreamedResponse
{
    $body = $response->toPsrResponse()->getBody();

    $fileResponse = new StreamedResponse(function () use ($body) {
        while (! $body->eof()) {
            echo $body->read(1024);
        }
    });

    $fileResponse->headers->set('Content-Type', $response->header('Content-Type'));
    $fileResponse->headers->set('Content-disposition', $response->header('Content-Disposition'));

    return $fileResponse;
}

function simpleHash(int $input): int
{
    return ((0x0000FFFF & $input) << 16) + ((0xFFFF0000 & $input) >> 16);
}

function array_pick(array $arr, array $keys): array
{
    return array_intersect_key($arr, array_flip($keys));
}
