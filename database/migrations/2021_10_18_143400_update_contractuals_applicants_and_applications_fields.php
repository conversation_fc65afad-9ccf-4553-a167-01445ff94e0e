<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateContractualsApplicantsAndApplicationsFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applicants', function (Blueprint $table) {
            $table->string('policeid_number')->nullable()->default(null)->change();
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applications', function (Blueprint $table) {
            $table->string('policeid_number')->nullable()->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applicants', function (Blueprint $table) {
            $table->string('policeid_number')->nullable(false)->change();
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applications', function (Blueprint $table) {
            $table->string('policeid_number')->nullable(false)->change();
        });

    }
}
