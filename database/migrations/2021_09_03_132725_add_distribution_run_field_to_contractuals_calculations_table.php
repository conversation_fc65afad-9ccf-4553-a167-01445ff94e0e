<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDistributionRunFieldToContractualsCalculationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('calculations', function (Blueprint $table) {
            $table->boolean('distribution_run')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('calculations', function (Blueprint $table) {
            $table->dropColumn('distribution_run');
        });
    }
}
