<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ContractualsMakeApplicantIdOptional extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('applications', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });

        Schema::connection('mysql_contractuals')->table('degrees', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });
        Schema::connection('mysql_contractuals')->table('postgraduates', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });
        Schema::connection('mysql_contractuals')->table('doctorates', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });
        Schema::connection('mysql_contractuals')->table('language_skills', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });
        Schema::connection('mysql_contractuals')->table('computer_skills', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });

        Schema::connection('mysql_contractuals')->table('experiences', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });

        Schema::connection('mysql_contractuals')->table('unemployments', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });

        Schema::connection('mysql_contractuals')->table('minors', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });

        Schema::connection('mysql_contractuals')->table('single_parent_families', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });

        Schema::connection('mysql_contractuals')->table('multi_child_families', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });

        Schema::connection('mysql_contractuals')->table('three_child_families', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });

        Schema::connection('mysql_contractuals')->table('disabilities', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });

        Schema::connection('mysql_contractuals')->table('family_disabilities', function (Blueprint $table) {
            $table->unsignedInteger('applicant_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
//        Schema::connection('mysql_contractuals')->table('degrees', function (Blueprint $table) {
//            $table->unsignedInteger('applicant_id')->nullable(false)->change();
//        });
    }
}
