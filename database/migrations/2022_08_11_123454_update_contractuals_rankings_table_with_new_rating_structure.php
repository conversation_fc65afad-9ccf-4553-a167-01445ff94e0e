<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateContractualsRankingsTableWithNewRatingStructure extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('rankings', function (Blueprint $table) {
            $table->unsignedBigInteger('position_rating_id')->nullable()->default(null);

            $table->string('protocol_number', 100)->nullable()->default(null)->change();
            $table->string('surname', 60)->nullable()->default(null)->change();
            $table->string('name', 40)->nullable()->default(null)->change();
            $table->string('fathername', 100)->nullable()->default(null)->change();
            $table->unsignedSmallInteger('auxiliary_level')->nullable()->default(null)->change();

            $table->unsignedSmallInteger('unemployments_continued')->nullable()->default(null)->change();
            $table->unsignedSmallInteger('unemployments')->nullable()->default(null)->change();
            $table->unsignedSmallInteger('multi_child_families_children')->nullable()->default(null)->change();
            $table->unsignedSmallInteger('multi_child_families_siblings')->nullable()->default(null)->change();
            $table->boolean('multi_child_families_eligibility')->nullable()->default(null)->change();
            $table->unsignedSmallInteger('three_child_families_children')->nullable()->default(null)->change();
            $table->unsignedSmallInteger('three_child_families_siblings')->nullable()->default(null)->change();
            $table->boolean('three_child_families_eligibility')->nullable()->default(null)->change();
            $table->unsignedSmallInteger('minors')->nullable()->default(null)->change();
            $table->unsignedSmallInteger('single_parent_families_children')->nullable()->default(null)->change();
            $table->unsignedSmallInteger('single_parent_families_siblings')->nullable()->default(null)->change();
            $table->boolean('single_parent_families_eligibility')->nullable()->default(null)->change();
            $table->decimal('degrees', 6, 2)->nullable()->default(null)->change();
            $table->boolean('doctorates')->nullable()->default(null)->change();
            $table->boolean('postgraduates')->nullable()->default(null)->change();
            $table->boolean('postgraduates_integrated')->nullable()->default(null)->change();
            $table->boolean('second_degrees')->nullable()->default(null)->change();
            $table->decimal('other_degrees', 6, 2)->nullable()->default(null)->change();
            $table->unsignedSmallInteger('experiences')->nullable()->default(null)->change();
            $table->unsignedSmallInteger('disabilities')->nullable()->default(null)->change();
            $table->boolean('disabilities_eligibility')->nullable()->default(null)->change();
            $table->unsignedSmallInteger('family_disabilities')->nullable()->default(null)->change();
            $table->boolean('family_disabilities_eligibility')->nullable()->default(null)->change();

            $table->decimal('unemployments_continued_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('unemployments_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('multi_child_families_children_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('multi_child_families_siblings_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('multi_child_families_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('three_child_families_children_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('three_child_families_siblings_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('three_child_families_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('minors_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('single_parent_families_children_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('single_parent_families_siblings_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('single_parent_families_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('degrees_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('doctorates_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('postgraduates_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('postgraduates_integrated_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('second_degrees_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('other_degrees_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('experiences_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('disabilities_points', 10, 2)->nullable()->default(null)->change();
            $table->decimal('family_disabilities_points', 10, 2)->nullable()->default(null)->change();

            $table->foreign('position_rating_id')->references('id')->on('position_ratings')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('mysql_contractuals')->table('rankings', function (Blueprint $table) {
            $table->dropForeign(['position_rating_id']);
            $table->dropColumn('position_rating_id');

            $table->string('protocol_number', 100)->nullable(false)->change();
            $table->string('surname', 60)->nullable(false)->change();
            $table->string('name', 40)->nullable(false)->change();
            $table->string('fathername', 100)->nullable(false)->change();
            $table->unsignedSmallInteger('auxiliary_level')->nullable(false)->change();

            $table->unsignedSmallInteger('unemployments_continued')->nullable(false)->default(0)->change();
            $table->unsignedSmallInteger('unemployments')->nullable(false)->change();
            $table->unsignedSmallInteger('multi_child_families_children')->nullable(false)->change();
            $table->unsignedSmallInteger('multi_child_families_siblings')->nullable(false)->change();
            $table->boolean('multi_child_families_eligibility')->nullable(false)->default(false)->change();
            $table->unsignedSmallInteger('three_child_families_children')->nullable(false)->change();
            $table->unsignedSmallInteger('three_child_families_siblings')->nullable(false)->change();
            $table->boolean('three_child_families_eligibility')->nullable(false)->default(false)->change();
            $table->unsignedSmallInteger('minors')->nullable(false)->change();
            $table->unsignedSmallInteger('single_parent_families_children')->nullable(false)->change();
            $table->unsignedSmallInteger('single_parent_families_siblings')->nullable(false)->change();
            $table->boolean('single_parent_families_eligibility')->nullable(false)->default(false)->change();
            $table->decimal('degrees', 6, 2)->nullable(false)->change();
            $table->boolean('doctorates')->nullable(false)->default(false)->change();
            $table->boolean('postgraduates')->nullable(false)->default(false)->change();
            $table->boolean('postgraduates_integrated')->nullable(false)->default(false)->change();
            $table->boolean('second_degrees')->nullable(false)->default(false)->change();
            $table->decimal('other_degrees', 6, 2)->nullable(false)->default(0.00)->change();
            $table->unsignedSmallInteger('experiences')->nullable(false)->change();
            $table->unsignedSmallInteger('disabilities')->nullable(false)->change();
            $table->boolean('disabilities_eligibility')->nullable(false)->default(false)->change();
            $table->unsignedSmallInteger('family_disabilities')->nullable(false)->change();
            $table->boolean('family_disabilities_eligibility')->nullable(false)->default(false)->change();

            $table->decimal('unemployments_continued_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('unemployments_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('multi_child_families_children_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('multi_child_families_siblings_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('multi_child_families_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('three_child_families_children_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('three_child_families_siblings_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('three_child_families_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('minors_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('single_parent_families_children_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('single_parent_families_siblings_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('single_parent_families_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('degrees_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('doctorates_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('postgraduates_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('postgraduates_integrated_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('second_degrees_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('other_degrees_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('experiences_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('disabilities_points', 10, 2)->nullable(false)->default(0.00)->change();
            $table->decimal('family_disabilities_points', 10, 2)->nullable(false)->default(0.00)->change();
        });
    }
}
