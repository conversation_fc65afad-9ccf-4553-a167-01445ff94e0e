<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLogsTable extends Migration
{
    public function up()
    {
        Schema::create('logs', function (Blueprint $table) {
            $table->id();
            $table->enum('level', ['emergency', 'alert', 'critical', 'error', 'warning', 'notice', 'info', 'debug'])->index();
            $table->longText('message');
            $table->json('context')->nullable();
            $table->string('channel')->index();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->ipAddress('remote_addr')->nullable();
            $table->string('user_agent')->nullable();
            $table->string('url')->nullable();
            $table->timestamp('timestamp');
        });
    }

    public function down()
    {
        Schema::dropIfExists('logs');
    }
}
