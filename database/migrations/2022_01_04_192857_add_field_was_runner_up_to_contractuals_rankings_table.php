<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldWasRunnerUpToContractualsRankingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('rankings', function (Blueprint $table) {
            $table->boolean('was_runner_up')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('mysql_contractuals')->table('rankings', function (Blueprint $table) {
            $table->dropColumn('was_runner_up');
        });
    }
}
