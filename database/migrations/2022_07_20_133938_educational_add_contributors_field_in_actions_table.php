<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EducationalAddContributorsFieldInActionsTable extends Migration
{
    public function up()
    {
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->text('contributors')->after('description')->nullable();
        });
    }

    public function down()
    {
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->dropColumn('contributors');
        });
    }
}
