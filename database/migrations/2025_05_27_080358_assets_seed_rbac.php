<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::connection('mysql_main')->transaction(function () {
            // Create Assets application
            $appId = DB::table('apps')->insertGetId([
                'name' => 'Διαχείριση Περιουσιακών Στοιχείων',
                'abbrv' => 'assets',
                'icon' => 'fa-archive',
                'color' => '#2e8b57',
            ]);

            // Create permissions
            $permissions = [
                ['name' => 'assets.create', 'description' => 'Δικαίωμα καταχώρησης'],
                ['name' => 'assets.read', 'description' => 'Δικαίωμα προβολής'],
                ['name' => 'assets.update', 'description' => 'Δικαίωμα επεξεργασίας'],
                ['name' => 'assets.delete', 'description' => 'Δικαίωμα διαγραφής'],
                ['name' => 'assets.admin', 'description' => 'Δικαίωμα διαχείρισης'],
            ];

            $permissionIds = [];
            foreach ($permissions as $permission) {
                $permissionIds[$permission['name']] = DB::table('permissions')->insertGetId($permission);
            }

            // Create roles
            $roles = [
                ['name' => 'assets.subscriber', 'description' => 'Συνδρομητής', 'app_id' => $appId],
                ['name' => 'assets.editor', 'description' => 'Συντάκτης', 'app_id' => $appId],
                ['name' => 'assets.admin', 'description' => 'Διαχειριστής', 'app_id' => $appId],
            ];

            $roleIds = [];
            foreach ($roles as $role) {
                $roleIds[$role['name']] = DB::table('roles')->insertGetId($role);
            }

            // Assign permissions to roles
            $rolePermissions = [
                'assets.subscriber' => ['assets.read'],
                'assets.editor' => ['assets.create', 'assets.read', 'assets.update', 'assets.delete'],
                'assets.admin' => ['assets.admin', 'assets.create', 'assets.read', 'assets.update', 'assets.delete'],
            ];

            $permissionRoleData = [];
            foreach ($rolePermissions as $roleName => $permissions) {
                foreach ($permissions as $permissionName) {
                    $permissionRoleData[] = [
                        'permission_id' => $permissionIds[$permissionName],
                        'role_id' => $roleIds[$roleName],
                    ];
                }
            }

            DB::table('permission_role')->insert($permissionRoleData);

            // Get assets managers
            $assetsManagerIds = $this->getAssetsManagerIds();

            // Define managed roles (roles that can be assigned by managers)
            $managedRoles = ['assets.subscriber', 'assets.editor'];

            // Create manager-role assignments
            $managerRoleData = [];
            foreach ($assetsManagerIds as $managerId) {
                foreach ($managedRoles as $roleName) {
                    $managerRoleData[] = [
                        'user_id' => $managerId,
                        'role_id' => $roleIds[$roleName],
                    ];
                }
            }

            if (! empty($managerRoleData)) {
                DB::table('role_manager')->insert($managerRoleData);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::connection('mysql_main')->transaction(function () {
            // Get the app ID to clean up related data
            $appId = DB::connection('mysql_main')->table('apps')->where('abbrv', 'assets')->value('id');

            if ($appId) {
                // Get role IDs for this app
                $roleIds = DB::connection('mysql_main')->table('roles')->where('app_id', $appId)->pluck('id');

                // Clean up manager_role entries
                DB::connection('mysql_main')->table('role_manager')->whereIn('role_id', $roleIds)->delete();

                // Clean up permission_role entries
                DB::connection('mysql_main')->table('permission_role')->whereIn('role_id', $roleIds)->delete();

                // Delete roles
                DB::connection('mysql_main')->table('roles')->where('app_id', $appId)->delete();

                // Delete permissions (assuming they're app-specific)
                DB::connection('mysql_main')->table('permissions')
                    ->where('name', 'like', 'assets.%')
                    ->delete();

                // Delete the app
                DB::connection('mysql_main')->table('apps')->where('id', $appId)->delete();
            }
        });
    }

    /**
     * Get the IDs of users who can manage assets roles
     */
    private function getAssetsManagerIds(): array
    {
        // Get latest active organogram
        $latestOrganogramId = DB::connection('mysql_main')->table('organograms')
            ->whereNull('ended_at')
            ->value('id');

        if (! $latestOrganogramId) {
            return [];
        }

        // Get active directorates from latest organogram
        $latestOrganogramActiveDirectorates = DB::connection('mysql_main')->table('units')
            ->where('organogram_id', $latestOrganogramId)
            ->whereNull('ended_at')
            ->whereIn('unit_type_id', [50, 51, 52, 53])
            ->pluck('id');

        if ($latestOrganogramActiveDirectorates->isEmpty()) {
            return [];
        }

        // Get manager user IDs
        return DB::connection('mysql_main')->table('users as u')
            ->join('units as d', 'u.unit_id', '=', 'd.id')
            ->whereColumn('u.email', '=', 'd.email')
            ->whereNull('u.deleted_at')
            ->whereIn('d.id', $latestOrganogramActiveDirectorates)
            ->pluck('u.id')
            ->toArray();
    }
};
