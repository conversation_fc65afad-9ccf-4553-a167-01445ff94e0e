<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsToContractualsAssetTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('postgraduates', function (Blueprint $table) {
            $table->boolean('is_integrated')->default(false);
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('unemployments', function (Blueprint $table) {
            $table->boolean('is_continued')->default(false);
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('disabilities', function (Blueprint $table) {
            $table->boolean('is_eligible')->default(false);
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('family_disabilities', function (Blueprint $table) {
            $table->boolean('is_eligible')->default(false);
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('multi_child_families', function (Blueprint $table) {
            $table->boolean('is_eligible')->default(false);
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('single_parent_families', function (Blueprint $table) {
            $table->boolean('is_eligible')->default(false);
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('three_child_families', function (Blueprint $table) {
            $table->boolean('is_eligible')->default(false);
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applications', function (Blueprint $table) {
            $table->unsignedBigInteger('public_application_id')->nullable();
            $table->boolean('is_auto_rated')->default(false);
            $table->renameColumn('healthy', 'meets_general_requirements');
            $table->dropColumn('unrestricted');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('application_position', function (Blueprint $table) {
            $table->boolean('locality')->nullable()->default(null);
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('degrees', function (Blueprint $table) {
            $table->boolean('is_primary')->nullable()->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('postgraduates', function (Blueprint $table) {
            $table->dropColumn('is_integrated');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('unemployments', function (Blueprint $table) {
            $table->dropColumn('is_continued');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('disabilities', function (Blueprint $table) {
            $table->dropColumn('is_eligible');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('family_disabilities', function (Blueprint $table) {
            $table->dropColumn('is_eligible');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('multi_child_families', function (Blueprint $table) {
            $table->dropColumn('is_eligible');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('single_parent_families', function (Blueprint $table) {
            $table->dropColumn('is_eligible');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('three_child_families', function (Blueprint $table) {
            $table->dropColumn('is_eligible');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applications', function (Blueprint $table) {
            $table->dropColumn(['public_application_id', 'is_auto_rated']);
            $table->renameColumn('meets_general_requirements', 'healthy');
            $table->boolean('unrestricted')->default(true);
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('application_position', function (Blueprint $table) {
            $table->dropColumn('locality');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('degrees', function (Blueprint $table) {
            $table->dropColumn('is_primary');
        });
    }
}
