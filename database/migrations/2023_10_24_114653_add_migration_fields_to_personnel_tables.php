<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql_personnel')->table('specializations', function (Blueprint $table) {
            $table->renameColumn('compass_id', 'compass_id_old');
        });

        Schema::connection('mysql_personnel')->table('specialization_types', function (Blueprint $table) {
            $table->renameColumn('compass_id', 'compass_id_old');
        });

        Schema::connection('mysql_personnel')->table('specializations', function (Blueprint $table) {
            $table->string('compass_id', 10)->collation('utf8mb3_bin')->nullable()->after('compass_id_old');
        });

        Schema::connection('mysql_personnel')->table('specialization_types', function (Blueprint $table) {
            $table->string('compass_id', 10)->collation('utf8mb3_bin')->nullable()->after('compass_id_old');
        });

        Schema::connection('mysql_personnel')->table('specialization_types', function (Blueprint $table) {
            $table->dropUnique('specialization_types_compass_id_unique');
            $table->string('compass_id_old', 10)->collation('utf8mb3_bin')->nullable()->default(null)->change();
        });

        Schema::connection('mysql_personnel')->table('specializations', function (Blueprint $table) {
            $table->dropUnique('specializations_compass_id_unique');
            $table->string('compass_id_old', 10)->collation('utf8mb3_bin')->nullable()->default(null)->change();
            $table->dropForeign('specializations_hroffice_id_foreign');
            $table->unsignedInteger('hroffice_id')->nullable()->default(null)->change();
            $table->dropForeign('specializations_department_board_id_foreign');
        });

        Schema::connection('mysql_personnel')->table('positions', function (Blueprint $table) {
            $table->dropForeign('positions_unit_id_foreign');
            $table->dropUnique('positions_unit_id_department_id_specialization_id_unique');
            $table->unique([
                'unit_id',
                'department_id',
                'occupation_id',
                'specialization_id'
            ], 'positions_unique');
            $table->foreign('unit_id')->references('id')->on('laravel.units');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('mysql_personnel')->table('specialization_types', function (Blueprint $table) {
            $table->dropColumn('compass_id');
        });

        Schema::connection('mysql_personnel')->table('specializations', function (Blueprint $table) {
            $table->dropColumn('compass_id');
        });

        Schema::connection('mysql_personnel')->table('specialization_types', function (Blueprint $table) {
            $table->renameColumn('compass_id_old', 'compass_id');
        });

        Schema::connection('mysql_personnel')->table('specializations', function (Blueprint $table) {
            $table->renameColumn('compass_id_old', 'compass_id');
        });
    }
};
