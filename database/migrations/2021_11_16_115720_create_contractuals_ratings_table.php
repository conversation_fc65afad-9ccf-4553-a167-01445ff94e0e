<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContractualsRatingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->create('ratings', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('application_id')->index();
            $table->unsignedInteger('position_id')->index();
            $table->unsignedSmallInteger('position_order')->default(1);
            $table->unsignedSmallInteger('specialization_type_id');
            $table->date('birthdate')->nullable();
            $table->boolean('rejected')->default(false)->index();
            $table->boolean('impediment_eight_months')->default(false);
            $table->boolean('locality')->default(false);
            $table->unsignedSmallInteger('auxiliary_level')->default(1);
            $table->unsignedSmallInteger('unemployments_continued')->default(0);
            $table->unsignedSmallInteger('unemployments')->default(0);
            $table->boolean('multi_child_families_eligibility')->default(false);
            $table->boolean('three_child_families_eligibility')->default(false);
            $table->boolean('single_parent_families_eligibility')->default(false);
            $table->unsignedTinyInteger('minors')->default(0);
            $table->unsignedDecimal('degrees', 6, 2)->default(0);
            $table->unsignedTinyInteger('doctorates')->default(0);
            $table->unsignedTinyInteger('postgraduates')->default(0);
            $table->unsignedTinyInteger('postgraduates_integrated')->default(0);
            $table->boolean('second_degrees')->default(false);
            $table->unsignedDecimal('other_degrees', 6, 2)->default(0);
            $table->unsignedSmallInteger('experiences')->default(0);
            $table->boolean('disabilities_eligibility')->default(false);
            $table->boolean('family_disabilities_eligibility')->default(false);

            $table->unsignedDecimal('unemployments_continued_points', 6, 2)->default(0);
            $table->unsignedDecimal('unemployments_points', 6, 2)->default(0);
            $table->unsignedDecimal('multi_child_families_points', 6, 2)->default(0);
            $table->unsignedDecimal('three_child_families_points', 6, 2)->default(0);
            $table->unsignedDecimal('single_parent_families_points', 6, 2)->default(0);
            $table->unsignedDecimal('minors_points', 6, 2)->default(0);
            $table->unsignedDecimal('degrees_points', 6, 2, 6, 2)->default(0);
            $table->unsignedDecimal('doctorates_points', 6, 2)->default(0);
            $table->unsignedDecimal('postgraduates_points', 6, 2)->default(0);
            $table->unsignedDecimal('postgraduates_integrated_points', 6, 2)->default(0);
            $table->unsignedDecimal('second_degrees_points', 6, 2)->default(0);
            $table->unsignedDecimal('other_degrees_points', 6, 2)->default(0);
            $table->unsignedDecimal('experiences_points', 6, 2)->default(0);
            $table->unsignedDecimal('disabilities_points', 6, 2)->default(0);
            $table->unsignedDecimal('family_disabilities_points', 6, 2)->default(0);

            $table->unsignedDecimal('score', 10, 2)->default(0);
            $table->string('combined_score')->nullable();

            $table->timestamps();

            $table->index(['application_id','position_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->dropIfExists('ratings');
    }
}
