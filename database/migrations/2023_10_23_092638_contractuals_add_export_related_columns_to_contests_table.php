<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('contests', function (Blueprint $table) {
            $table->string('organization')->after('end_date')->nullable();
            $table->string('contract_duration')->after('organization')->nullable();
            $table->string('undersigned_title_first_row')->after('contract_duration')->nullable();
            $table->string('undersigned_title_second_row')->after('undersigned_title_first_row')->nullable();
            $table->string('undersigned_name')->after('undersigned_title_second_row')->nullable();
            $table->string('notifications_email')->after('undersigned_name')->nullable();
        });
    }

    public function down()
    {
        Schema::connection('mysql_contractuals')->table('contests', function (Blueprint $table) {
            $table->dropColumn([
                'organization',
                'contract_duration',
                'undersigned_title_first_row',
                'undersigned_title_second_row',
                'undersigned_name',
                'notifications_email'
            ]);
        });
    }
};
