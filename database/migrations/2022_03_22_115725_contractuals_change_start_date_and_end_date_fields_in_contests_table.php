<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ContractualsChangeStartDateAndEndDateFieldsInContestsTable extends Migration
{
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('contests', function (Blueprint $table) {
            $table->dateTime('start_date')->change();
            $table->dateTime('end_date')->change();
        });
    }

    public function down()
    {
        Schema::connection('mysql_contractuals')->table('contests', function (Blueprint $table) {
            $table->date('start_date')->change();
            $table->date('end_date')->change();
        });
    }
}
