<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateContractualsRankingsTableForRatings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('rankings', function (Blueprint $table) {
            $table->renameColumn('second_degree', 'second_degrees');
            $table->renameColumn('second_degree_points', 'second_degrees_points');
            $table->decimal('other_degrees', 6, 2)->default(0.00)->change();
            $table->boolean('disabilities_eligibility')->default(false)->after('disabilities');
            $table->boolean('family_disabilities_eligibility')->default(false)->after('family_disabilities');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('rankings', function (Blueprint $table) {
            $table->renameColumn('second_degrees', 'second_degree');
            $table->renameColumn('second_degrees_points', 'second_degree_points');
            $table->decimal('other_degrees', 10, 2)->default(0.00)->change();
            $table->dropColumn(['disabilities_eligibility', 'family_disabilities_eligibility']);
        });
    }
}
