<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEightMonthsEmploymentFieldToApplicationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applications', function (Blueprint $table) {
            $table->boolean('has_eight_months_employment')->default(false)
                ->after('meets_general_requirements');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applications', function (Blueprint $table) {
            $table->dropColumn('has_eight_months_employment');
        });
    }
}
