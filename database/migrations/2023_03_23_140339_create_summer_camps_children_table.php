<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql_summer_camps')->create('children', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('application_id');
            $table->string('full_name');
            $table->date('birthdate');
            $table->boolean('has_disability')->default(false);
            $table->unsignedInteger('disability_percentage')->default(0);
            $table->unsignedInteger('days')->default(0);
            $table->text('summer_camps')->nullable();
            $table->timestamps();

            $table->foreign('application_id')->references('id')->on('applications')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('mysql_summer_camps')->dropIfExists('children');
    }
};
