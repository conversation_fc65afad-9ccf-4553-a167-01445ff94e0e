<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRejectionDescriptionFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applications', function (Blueprint $table) {
            $table->string('rejection_description')->nullable()->after('rejected');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('application_position', function (Blueprint $table) {
            $table->json('rejection_description')->nullable()->after('rejected');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applications', function (Blueprint $table) {
            $table->dropColumn('rejection_description');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('application_position', function (Blueprint $table) {
            $table->dropColumn('rejection_description');
        });
    }
}
