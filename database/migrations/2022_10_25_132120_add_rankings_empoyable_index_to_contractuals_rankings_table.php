<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('rankings', function (Blueprint $table) {
            $table->index(['position_id', 'calculation_id', 'employable', 'accepted'], 'rankings_employables_per_position');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('mysql_contractuals')->table('rankings', function (Blueprint $table) {
            $table->dropIndex('rankings_employables_per_position');
        });
    }
};
