<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveAmkaUniqueConstraintFromContractualsApplicantsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applicants', function (Blueprint $table) {
            $table->dropUnique(['amka']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applicants', function (Blueprint $table) {
            $table->unique(['amka']);
        });
    }
}
