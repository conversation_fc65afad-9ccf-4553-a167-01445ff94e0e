<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EducationalCreateDisseminationsTable extends Migration
{
    public function up()
    {
        Schema::connection('mysql_educational')->create('disseminations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });

        Schema::connection('mysql_educational')->create('action_dissemination', function (Blueprint $table) {
            $table->foreignId('action_id')->constrained()->cascadeOnDelete();
            $table->foreignId('dissemination_id')->constrained();
        });
    }

    public function down()
    {
        Schema::connection('mysql_educational')->dropIfExists('action_dissemination');
        Schema::connection('mysql_educational')->dropIfExists('disseminations');
    }
}
