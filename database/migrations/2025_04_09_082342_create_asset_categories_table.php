<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mysql_assets')->create('asset_categories', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->string('measure_unit')->nullable();
            $table->integer('duration_years')->nullable();
            $table->timestamps();
        });

        // Insert predefined asset categories
        $categories = [
            [
                'code' => '3110389001',
                'description' => 'Λοιπά κτίρια',
                'measure_unit' => 'ΤΕΤΡΑΓΩΝΙΚΑ ΜΕΤΡΑ',
                'duration_years' => 80,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3110989001',
                'description' => 'Λοιπές υποδομές',
                'measure_unit' => 'ΤΕΤΡΑΓΩΝΙΚΑ ΜΕΤΡΑ',
                'duration_years' => 80,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120101001',
                'description' => 'Βιομηχανικά μηχανήματα και εργαλεία',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120102001',
                'description' => 'Συσκευές θέρμανσης και κλιματισμού',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120103001',
                'description' => 'Φωτοαντιγραφικά και λοιπές μηχανές γραφείου',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120189001',
                'description' => 'Λοιπά μηχανήματα και εργαλεία',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120201001',
                'description' => 'Οδικά μέσα',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 7,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120202001',
                'description' => 'Πλωτά μέσα',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 7,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120203001',
                'description' => 'Εναέρια μέσα',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 7,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120289001',
                'description' => 'Λοιπά μεταφορικά μέσα',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 7,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120301001',
                'description' => 'Ηλεκτρονικοί υπολογιστές και συναφής εξοπλισμός',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120302001',
                'description' => 'Τηλεπικοινωνιακός εξοπλισμός',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120389001',
                'description' => 'Λοιπός εξοπλισμός πληροφορικής και τηλεπικοινωνιών',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3120989001',
                'description' => 'Λοιπά μηχανήματα και εξοπλισμός',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3140301001',
                'description' => 'Λογισμικό υπολογιστών',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => '3140302001',
                'description' => 'Βάσεις δεδομένων',
                'measure_unit' => 'ΤΕΜΑΧΙΑ',
                'duration_years' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::connection('mysql_assets')->table('asset_categories')->insert($categories);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mysql_assets')->dropIfExists('asset_categories');
    }
};
