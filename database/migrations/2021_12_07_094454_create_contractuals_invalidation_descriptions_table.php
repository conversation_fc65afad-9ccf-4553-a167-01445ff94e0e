<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContractualsInvalidationDescriptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->create('invalidation_descriptions', function (Blueprint $table) {
            $table->smallIncrements('id');
            $table->string('name');
            $table->timestamps();
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applications', function (Blueprint $table) {
            $table->unsignedSmallInteger('invalidation_description_id')->nullable()->after('invalidation_description');
            $table->foreign('invalidation_description_id')->references('id')->on('invalidation_descriptions');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('applications', function (Blueprint $table) {
            $table->dropForeign(['invalidation_description_id']);
            $table->dropColumn('invalidation_description_id');
        });

        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->dropIfExists('invalidation_descriptions');
    }
}
