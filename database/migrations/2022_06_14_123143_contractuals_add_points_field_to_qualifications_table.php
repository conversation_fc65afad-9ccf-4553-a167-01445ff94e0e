<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ContractualsAddPointsFieldToQualificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('qualifications', function (Blueprint $table) {
            $table->unsignedDecimal('points', 10, 2)->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('mysql_contractuals')->table('qualifications', function (Blueprint $table) {
            $table->dropColumn('points');
        });
    }
}
