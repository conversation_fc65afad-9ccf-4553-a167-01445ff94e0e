<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ContractualsMakeIsAutoRatedFieldNullableInApplicationsTable extends Migration
{
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('applications', function (Blueprint $table) {
            $table->boolean('is_auto_rated')->nullable()->change();
        });
    }
    
    public function down()
    {
        Schema::connection('mysql_contractuals')->table('applications', function (Blueprint $table) {
            $table->boolean('is_auto_rated')->nullable(false)->change();
        });
    }
}
