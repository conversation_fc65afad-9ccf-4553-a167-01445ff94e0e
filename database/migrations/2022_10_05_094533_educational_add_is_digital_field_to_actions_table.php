<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->boolean('is_digital')->after('ongoing')->nullable();
        });
    }

    public function down()
    {
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->dropColumn('is_digital');
        });
    }
};
