<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EducationalRemoveScopeIdFromActionsTable extends Migration
{
    public function up()
    {
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->dropConstrainedForeignId('scope_id');
        });
    }

    public function down()
    {
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->foreignId('scope_id')->after('context_id')->nullable()->constrained();
        });
    }
}
