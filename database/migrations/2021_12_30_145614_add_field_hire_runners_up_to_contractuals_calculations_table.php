<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldHireRunnersUpToContractualsCalculationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('calculations', function (Blueprint $table) {
            $table->boolean('hire_runners_up')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('mysql_contractuals')->table('calculations', function (Blueprint $table) {
            $table->dropColumn('hire_runners_up');
        });
    }
}
