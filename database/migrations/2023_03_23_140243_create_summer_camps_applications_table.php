<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.surname
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql_summer_camps')->create('applications', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id');
            $table->unsignedBigInteger('season_id');
            $table->string('name')->nullable();
            $table->string('surname')->nullable();
            $table->string('father_name')->nullable();
            $table->unsignedBigInteger('employment_sector_id')->nullable();
            $table->unsignedBigInteger('employment_type_id')->nullable();
            $table->string('position')->nullable();
            $table->string('afm',9)->nullable();
            $table->string('doy')->nullable();
            $table->string('personal_phone')->nullable();
            $table->string('mobile_phone')->nullable();
            $table->string('work_phone')->nullable();
            $table->string('email_address')->nullable();
            $table->boolean('is_submitted')->default(false);
            $table->dateTime('submitted_at')->nullable();
            $table->string('protocol')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('laravel.users')->onDelete('cascade');
            $table->foreign('season_id')->references('id')->on('seasons')->onDelete('cascade');
            $table->foreign('employment_sector_id')->references('id')->on('employment_sectors')->onDelete('cascade');
            $table->foreign('employment_type_id')->references('id')->on('employment_types')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('mysql_summer_camps')->dropIfExists('applications');
    }
};
