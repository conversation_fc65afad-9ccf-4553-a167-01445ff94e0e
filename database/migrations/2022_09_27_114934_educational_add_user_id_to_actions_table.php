<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->unsignedInteger('user_id')->after('link')->nullable();
            $table->foreign('user_id')->references('id')->on('laravel.users');
        });
    }

    public function down()
    {
        Schema::connection('mysql_educational')->table('actions', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropColumn('user_id');
        });
    }
};
