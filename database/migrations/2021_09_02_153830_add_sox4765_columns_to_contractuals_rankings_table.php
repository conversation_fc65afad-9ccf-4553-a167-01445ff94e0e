<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSox4765ColumnsToContractualsRankingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('rankings', function (Blueprint $table) {
            $table->date('birthdate')->nullable()->after('fathername');
            $table->date('policeid_date')->nullable()->after('policeid_number');
            $table->boolean('locality')->default(false)->after('auxiliary_level');
            $table->boolean('multi_child_families_eligibility')->default(false)->after('multi_child_families_siblings');
            $table->boolean('three_child_families_eligibility')->default(false)->after('three_child_families_siblings');
            $table->boolean('single_parent_families_eligibility')->default(false)->after('single_parent_families_siblings');
            $table->unsignedSmallInteger('doctorates')->default(0)->after('degrees');
            $table->unsignedSmallInteger('postgraduates')->default(0)->after('doctorates');
            $table->unsignedSmallInteger('postgraduates_integrated')->default(0)->after('postgraduates');
            $table->boolean('second_degree')->default(false)->after('postgraduates_integrated');
            $table->decimal('multi_child_families_points', 10, 2)->default(0.00)->after('multi_child_families_siblings_points');
            $table->decimal('three_child_families_points', 10, 2)->default(0.00)->after('three_child_families_siblings_points');
            $table->decimal('single_parent_families_points', 10, 2)->default(0.00)->after('single_parent_families_siblings_points');
            $table->decimal('other_degrees_points', 10, 2)->default(0.00)->after('degrees_points');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('rankings', function (Blueprint $table) {
            $table->dropColumn([
                'birthdate',
                'policeid_date',
                'locality',
                'multi_child_families_eligibility',
                'three_child_families_eligibility',
                'single_parent_families_eligibility',
                'doctorates',
                'postgraduates',
                'postgraduates_integrated',
                'second_degree',
                'multi_child_families_points',
                'three_child_families_points',
                'single_parent_families_points',
                'other_degrees_points',
            ]);
        });
    }
}
