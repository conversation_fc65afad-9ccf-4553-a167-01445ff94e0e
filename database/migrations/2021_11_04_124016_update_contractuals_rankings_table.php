<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateContractualsRankingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('rankings', function (Blueprint $table) {
            $table->unsignedSmallInteger('specialization_type_id')->nullable()->after('calculation_id');
            $table->unsignedSmallInteger('unemployments_continued')->default(0)->after('locality');
            $table->decimal('unemployments_continued_points', 10, 2)->default(0)->after('family_disabilities');
            $table->boolean('doctorates')->default(false)->change();
            $table->boolean('postgraduates')->default(false)->change();
            $table->boolean('postgraduates_integrated')->default(false)->change();
            $table->decimal('other_degrees', 10, 2)->default(0)->after('second_degree');
            $table->decimal('doctorates_points', 10, 2)->default(0)->after('degrees_points');
            $table->decimal('postgraduates_points', 10, 2)->default(0)->after('doctorates_points');
            $table->decimal('postgraduates_integrated_points', 10, 2)->default(0)->after('postgraduates_points');
            $table->decimal('second_degree_points', 10, 2)->default(0)->after('postgraduates_integrated_points');
            $table->string('combined_score')->nullable()->after('score');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('rankings', function (Blueprint $table) {
            $table->dropColumn([
                'specialization_type_id',
                'unemployments_continued',
                'unemployments_continued_points',
                'other_degrees',
                'doctorates_points',
                'postgraduates_points',
                'postgraduates_integrated_points',
                'second_degree_points',
                'combined_score'
            ]);
            $table->unsignedSmallInteger('doctorates')->default(0)->change();
            $table->unsignedSmallInteger('postgraduates')->default(0)->change();
            $table->unsignedSmallInteger('postgraduates_integrated')->default(0)->change();
        });
    }
}
