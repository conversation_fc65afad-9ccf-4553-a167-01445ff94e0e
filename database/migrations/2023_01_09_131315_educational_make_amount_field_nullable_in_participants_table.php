<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::connection('mysql_educational')->table('participants', function (Blueprint $table) {
            $table->unsignedInteger('amount')->nullable()->change();
        });
    }

    public function down()
    {
        Schema::connection('mysql_educational')->table('participants', function (Blueprint $table) {
            $table->unsignedInteger('amount')->nullable(false)->change();
        });
    }
};
