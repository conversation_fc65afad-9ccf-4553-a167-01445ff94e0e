<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ContractualsAddRestrictedAtFieldToContestsTable extends Migration
{
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('contests', function (Blueprint $table) {
            $table->dateTime('restricted_at')->nullable()->after('locked_at');
        });
    }

    public function down()
    {
        Schema::connection('mysql_contractuals')->table('contests', function (Blueprint $table) {
            $table->dropColumn('restricted_at');
        });
    }
}
