<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ContractualsAddDescriptionFieldToContestsTable extends Migration
{
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('contests', function (Blueprint $table) {
            $table->text('description')->nullable()->after('name');
        });
    }

    public function down()
    {
        Schema::connection('mysql_contractuals')->table('contests', function (Blueprint $table) {
            $table->dropColumn('description');
        });
    }
}
