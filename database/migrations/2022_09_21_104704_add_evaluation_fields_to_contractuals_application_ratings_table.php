<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('mysql_contractuals')->table('application_ratings', function (Blueprint $table) {
            $table->dropColumn('calculation_id');
            $table->unsignedInteger('evaluation_number')->nullable(); // to increment per application_id
            $table->string('evaluation_type')->default('import'); // 'import','reset','approve','manual'
            $table->unsignedInteger('evaluated_by')->nullable();
            $table->unsignedInteger('evaluated_by_unit_id')->nullable();
            $table->boolean('evaluated_by_admin')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('mysql_contractuals')->table('application_ratings', function (Blueprint $table) {
            $table->unsignedInteger('calculation_id')->index();
            $table->dropColumn(['evaluation_number', 'evaluation_type', 'evaluated_by', 'evaluated_by_unit_id', 'evaluated_by_admin']);
        });
    }
};
