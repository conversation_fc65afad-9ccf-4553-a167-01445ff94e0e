<?php

use App\Models\App;
use App\Models\Personnel\Position;
use App\Models\Personnel\Specialization;
use App\Models\Prefecture;
use App\Models\Region;
use App\Models\Role;
use App\Models\Unit;
use App\Models\UnitType;
use App\Models\User;
use Illuminate\Support\Str;

/*
|--------------------------------------------------------------------------
| Model Factories for MAIN DB
|--------------------------------------------------------------------------
|
*/
$factory->define(User::class, function (Faker\Generator $faker) {
    $unit = factory(Unit::class)->create();

    return [
        'name' => $faker->name,
        'username' => $faker->unique()->userName,
        'email' => $faker->unique()->safeEmail,
        'password' => bcrypt('secret'),
        'remember_token' => Str::random(10),
        'unit_id' => $unit->id,
        'primary_unit_id' => $unit->id,
    ];
});

$factory->define(Role::class, function (Faker\Generator $faker) {
    return [
        'name' => $faker->name,
        'description' => $faker->name,
        'app_id' => factory(App::class)->create()->id,
    ];
});

$factory->define(App::class, function (Faker\Generator $faker) {
    return [
        'name' => $faker->name,
        'abbrv' => $faker->name,
        'icon' => $faker->word,
        'color' => $faker->hexColor,
    ];
});

$factory->define(Unit::class, function (Faker\Generator $faker) {
    return [
        'name' => $faker->company,
        'abbrv' => $faker->userName,
        'order' => $faker->email,
        'unit_type_id' => fn () => factory(UnitType::class)->create()->id,
        'started_at' => $faker->date(),
    ];
});

$factory->define(UnitType::class, function (Faker\Generator $faker) {
    return [
        'name' => $faker->company,
        'started_at' => $faker->date(),
    ];
});

$factory->define(Region::class, function (Faker\Generator $faker) {
    return [
        'name' => $faker->city,
    ];
});

$factory->define(Prefecture::class, function (Faker\Generator $faker) {
    return [
        'name' => $faker->city,
        'region_id' => random_int(
            \DB::connection('mysql_main')->table('regions')->min('id'),
            \DB::connection('mysql_main')->table('regions')->max('id')
        ),
    ];
});

/*
|--------------------------------------------------------------------------
| Model factories for PERSONNEL DB
|--------------------------------------------------------------------------
|
*/
// Factory for Position model
$factory->define(Position::class, function (Faker\Generator $faker) {
    //Get a random value from array
    $types = ['Γενικός Διευθυντής', 'Διευθυντής', 'Τμηματάρχης'];
    $key = array_rand($types);
    $value = $types[$key];

    return [
        'name' => $value,
        'compass_id' => $faker->numberBetween(),
    ];
});

// Factory for Speicalization model
$factory->define(Specialization::class, function (Faker\Generator $faker) {
    $types = [
        'ΤΕ ΔΙΟΙΚΗΤΙΚΟΥ',
        'ΤΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
        'ΠΕ ΔΙΟΙΚΗΤΙΚΟΥ',
        'ΠΕ ΠΛΗΡΟΦΟΡΙΚΗΣ',
    ];
    $key = array_rand($types);
    $value = $types[$key];

    return [
        'name' => $value,
    ];
});

/*
|--------------------------------------------------------------------------
| Model Factories for CONTRACTUALS DB
|--------------------------------------------------------------------------
|
*/
require base_path('database/factories/contractuals/contractualsFactories.php');
