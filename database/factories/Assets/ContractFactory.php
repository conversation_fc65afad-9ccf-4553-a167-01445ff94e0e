<?php

namespace Database\Factories\Assets;

use App\Models\Assets\Contract;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Assets\Contract>
 */
class ContractFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Contract::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'contract_number' => 'CONTR-' . $this->faker->unique()->numerify('######'),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
