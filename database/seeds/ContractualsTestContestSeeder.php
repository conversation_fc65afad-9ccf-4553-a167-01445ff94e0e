<?php

namespace Database\Seeders;

use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Position;
use App\Models\Unit;
use Illuminate\Database\Seeder;

class ContractualsTestContestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $testContests = Contest::where('name', 'like', 'TEST-%')->count();
        $contestName = 'TEST-'.$testContests+1 . ' Πρόσληψη, με σύμβαση εργασίας ιδιωτικού δικαίου ορισμένου χρόνου, συνολικά πεντακοσίων δεκαπέντε (515) ατόμων για την κάλυψη εποχικών ή παροδικών αναγκών  σε Υπηρεσίες αρμοδιότητας της Γενικής Διεύθυνσης Αρχαιοτήτων & Πολιτιστικής Κληρονομιάς του Υπουργείου Πολιτισμού & Αθλητισμού';
        $contest = Contest::create([
            'type_id' => 2,
            'protocol_number' => 'ΥΠΠΟΑ/'.$contestName,
            'protocol_date' => now()->subDays(20)->toDateString(),
            'name' => $contestName,
            'ada' => 'ΑΔΑ-'.$contestName,
            'start_date' => now()->toDateString(),
            'end_date' => now()->addDays(10)->toDateString(),
        ]);

        // ΓΔΑΠΚ Περιφερειακές & Ειδικές Περιφερειακές
        $units = Unit::where('parent_id', 2)->with('prefecture')->whereIn('unit_type_id', [52, 53])->get();
        $contest->units()->attach($units->pluck('id'));

        $positionCode = 0;
        $units->each(function ($unit) use ($contest, $contestName, &$positionCode) {
            // ΤΕ ΛΟΓΙΣΤΕΣ
            $positionCode++;
            $position = Position::create([
                'contest_id' => $contest->id,
                'specialization_id' => 4,
                'amount' => rand(1, 2),
                'code' => $positionCode,
                'location' => 'Νομός '.$unit->prefecture->name,
                'unit_id' => $unit->id
            ]);
            $position->requirements()->attach([
                6 => ['auxiliary_level' => 0],
                7 => ['auxiliary_level' => 0]
            ]);

            // ΔΕ ΗΜ ΦΥΛΑΚΕΣ
            $positionCode++;
            $position = Position::create([
                'contest_id' => $contest->id,
                'specialization_id' => 1,
                'amount' => rand(2, 6),
                'code' => $positionCode,
                'location' => 'Νομός '.$unit->prefecture->name,
                'unit_id' => $unit->id
            ]);
            $position->requirements()->attach([
                1 => ['auxiliary_level' => 0],
                4 => ['auxiliary_level' => 0],
            ]);
            $position->requirements()->attach([
                2 => ['auxiliary_level' => 1],
                4 => ['auxiliary_level' => 1],
            ]);
            $position->requirements()->attach([
                3 => ['auxiliary_level' => 2],
                4 => ['auxiliary_level' => 2],
            ]);

            // ΔΕ ΝΥΧΤΟΦΥΛΑΚΕΣ
            $positionCode++;
            $position = Position::create([
                'contest_id' => $contest->id,
                'specialization_id' => 2,
                'amount' => rand(1, 3),
                'code' => $positionCode,
                'location' => 'Νομός '.$unit->prefecture->name,
                'unit_id' => $unit->id
            ]);
            $position->requirements()->attach([
                1 => ['auxiliary_level' => 0],
            ]);
            $position->requirements()->attach([
                2 => ['auxiliary_level' => 1],
            ]);
            $position->requirements()->attach([
                3 => ['auxiliary_level' => 2],
            ]);

            // ΥΕ ΚΑΘΑΡΙΟΤΗΤΑΣ
            $positionCode++;
            $position = Position::create([
                'contest_id' => $contest->id,
                'specialization_id' => 3,
                'amount' => rand(1, 2),
                'code' => $positionCode,
                'location' => 'Νομός '.$unit->prefecture->name,
                'unit_id' => $unit->id
            ]);
        });
    }
}
