<?php
namespace Database\Seeders;

use App\Models\Assets\Asset as AssetModel;
use Illuminate\Database\Seeder;

class AssetsAssetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create 10 submitted assets using the factory
        AssetModel::factory()->count(10)->submitted()->create();

        // Create 10 unsubmitted (draft) assets using the factory
        AssetModel::factory()->count(10)->unsubmitted()->create();
    }
}
