<?php

use Illuminate\Database\Seeder;

class DirectoratePrefectureSeeder extends Seeder
{
    /*

    // ΠΕΙΡΦΕΡΕΙΕΣ
    1	Ανατολικής Μακεδονίας και Θράκης
    2	Κεντρικής Μακεδονίας
    3	Δυτική<PERSON>ακεδονίας
    4	Ηπείρου
    5	Θεσσαλίας
    6	Ιονίων Νήσων
    7	Δυτικής Ελλάδας
    8	Στερεάς Ελλάδας
    9	Αττικής
    10	Πελοποννήσου
    11	Βορείου Αιγαίου
    12	Νοτίου Αιγαίου
    13	Κρήτης

    // ΕΦΟΡΕΙΕΣ ΑΡΧΑΙΟΤΗΤΩΝ
    110	Εφορεία Εναλίων Αρχαιοτήτων	ΕΕΝΑ
    331	Εφορεία Αρχαιοτήτων Ανατολικής Αττικής	ΕΦΑ ΑΝΑΤΑΤ
    333	Εφορεία Αρχαιοτήτων Κορινθίας	ΕΦΑ ΚΟΡ
    334	Εφορεία Αρχαιοτήτων Αργολίδας	ΕΦΑ ΑΡΓΟ
    335	Εφορεία Αρχαιοτήτων Αρκαδίας	ΕΦΑ ΑΡΚΑ
    336	Εφορεία Αρχαιοτήτων Λακωνίας	ΕΦΑ ΛΑΚΩ
    337	Εφορεία Αρχαιοτήτων Μεσσηνίας	ΕΦΑ ΜΕΣΣΗ
    338	Εφορεία Αρχαιοτήτων Ηλείας	ΕΦΑ ΗΛΕΙΑΣ
    339	Εφορεία Αρχαιοτήτων Αχαΐας	ΕΦΑ ΑΧΑΙΑΣ
    340	Εφορεία Αρχαιοτήτων Αιτωλοακαρνανίας και Λευκάδος	ΕΦΑ ΑΙΤΩΛ
    341	Εφορεία Αρχαιοτήτων Κέρκυρας	ΕΦΑ ΚΕΡΚ
    343	Εφορεία Αρχαιοτήτων Ζακύνθου	ΕΦΑ ΖΑΚΥΝ
    344	Εφορεία Αρχαιοτήτων Βοιωτίας	ΕΦΑ ΒΟΙΩΤΙ
    345	Εφορεία Αρχαιοτήτων Φωκίδος	ΕΦΑ ΦΩΚΙΔ
    346	Εφορεία Αρχαιοτήτων Ευβοίας	ΕΦΑ ΕΥΒΟΙΑΣ
    347	Εφορεία Αρχαιοτήτων Φθιώτιδος και Ευρυτανίας	ΕΦΑ ΦΘΙ-ΕΥΡΥ
    348	Εφορεία Αρχαιοτήτων Ιωαννίνων	ΕΦΑ ΙΩΑΝ
    349	Εφορεία Αρχαιοτήτων Θεσπρωτίας	ΕΦΑ ΘΕΣΠΡ
    350	Εφορεία Αρχαιοτήτων Πρέβεζας	ΕΦΑ ΠΡΕΒ
    351	Εφορεία Αρχαιοτήτων Άρτας	ΕΦΑ ΑΡΤΑΣ
    352	Εφορεία Αρχαιοτήτων Μαγνησίας	ΕΦΑ ΜΑΓΝΗΣ
    353	Εφορεία Αρχαιοτήτων Λάρισας	ΕΦΑ ΛΑΡΙΣΑΣ
    354	Εφορεία Αρχαιοτήτων Τρικάλων	ΕΦΑ ΤΡΙΚΑΛ
    355	Εφορεία Αρχαιοτήτων Καρδίτσας	ΕΦΑ ΚΑΡΔΙ
    356	Εφορεία Αρχαιοτήτων Καστοριάς	ΕΦΑ ΚΑΣΤΟΡ
    357	Εφορεία Αρχαιοτήτων Φλώρινας	ΕΦΑ ΦΛΩΡΙ
    358	Εφορεία Αρχαιοτήτων Κοζάνης	ΕΦΑ ΚΟΖΑΝΗΣ
    359	Εφορεία Αρχαιοτήτων Γρεβενών	ΕΦΑ ΓΡΕΒΕΝ
    360	Εφορεία Αρχαιοτήτων Πόλης Θεσσαλονίκης	ΕΦΑ ΘΕΣΣΑΛ
    361	Εφορεία Αρχαιοτήτων Περιφέρειας Θεσσαλονίκης	ΕΦΑ ΘΕΣΣΑΛ-ΠΕΡ
    362	Εφορεία Αρχαιοτήτων Κιλκίς	ΕΦΑ ΚΙΛΚΙΣ
    363	Εφορεία Αρχαιοτήτων Χαλκιδικής και Αγίου Όρους	ΕΦΑ ΧΑΛΚΙΔ-ΑΟ
    364	Εφορεία Αρχαιοτήτων Πιερίας	ΕΦΑ ΠΙΕΡΙΑΣ
    365	Εφορεία Αρχαιοτήτων Πέλλας	ΕΦΑ ΠΕΛΛΑΣ
    366	Εφορεία Αρχαιοτήτων Ημαθίας	ΕΦΑ ΗΜΑΘΙΑΣ
    367	Εφορεία Αρχαιοτήτων Σερρών	ΕΦΑ ΣΕΡΡΩΝ
    368	Εφορεία Αρχαιοτήτων Δράμας	ΕΦΑ ΔΡΑΜΑΣ
    369	Εφορεία Αρχαιοτήτων Καβάλας	ΕΦΑ ΚΑΒΑΛΑΣ
    370	Εφορεία Αρχαιοτήτων Ξάνθης	ΕΦΑ ΞΑΝΘΗΣ
    371	Εφορεία Αρχαιοτήτων Ροδόπης	ΕΦΑ ΡΟΔΟΠΗΣ
    372	Εφορεία Αρχαιοτήτων Έβρου	ΕΦΑ ΕΒΡΟΥ
    373	Εφορεία Αρχαιοτήτων Λέσβου	ΕΦΑ ΛΕΣΒΟΥ
    374	Εφορεία Αρχαιοτήτων Χίου	ΕΦΑ ΧΙΟΥ
    375	Εφορεία Αρχαιοτήτων Σάμου & Ικαρίας	ΕΦΑ ΣΑΜΟΥ
    376	Εφορεία Αρχαιοτήτων Κυκλάδων	ΕΦΑ ΚΥΚΛΑΔ
    377	Εφορεία Αρχαιοτήτων Δωδεκανήσου	ΕΦΑ ΔΩΔΕΚ
    378	Εφορεία Αρχαιοτήτων Χανίων	ΕΦΑ ΧΑΝΙΩΝ
    379	Εφορεία Αρχαιοτήτων Ρεθύμνου	ΕΦΑ ΡΕΘΥΜ
    380	Εφορεία Αρχαιοτήτων Ηρακλείου	ΕΦΑ ΗΡΑΚΛ
    381	Εφορεία Αρχαιοτήτων Λασιθίου	ΕΦΑ ΛΑΣΙΘ
    390	Εφορεία Παλαιοανθρωπολογίας - Σπηλαιολογίας	ΕΠΑΣΠΗ
    483	Εφορεία Αρχαιοτήτων Πόλης Αθηνών	ΕΦΑ ΑΘΗΝΩΝ
    484	Εφορεία Αρχαιοτήτων Πειραιώς και Νήσων	ΕΦΑ ΠΕΙΡΑΙΩΣ
    485	Εφορεία Αρχαιοτήτων Κεφαλληνίας και Ιθάκης	ΕΦΑ ΚΕΦΑΛ
    490	Εφορεία Αρχαιοτήτων Δυτικής Αττικής	ΕΦΑ ΔΑ

    // ΜΟΥΣΕΙΑ
    107	Εθνικό Αρχαιολογικό Μουσείο
    486	Νομισματικό Μουσείο
    487	Επιγραφικό Μουσείο
    488	Μουσείο Νεώτερου Ελληνικού Πολιτισμού
    489	Μουσείο Ελληνικών Λαϊκών Μουσικών Οργάνων «Φοίβος Ανωγειανάκης» - Κέντρο Εθνομουσικολογίας
    106	Βυζαντινό & Χριστιανικό Μουσείο
    105	Αρχαιολογικό Μουσείο Θεσσαλονίκης
    114	Μουσείο Βυζαντινού Πολιτισμού
    104	Αρχαιολογικό Μουσείο Ηρακλείου
    113	Μουσείο Ασιατικής Τέχνης

    // ΛΟΙΠΑ ΓΔΠΑΚ
    2	"Γενική Διεύθυνση Αρχαιοτήτων & Πολιτιστικής Κληρονομιάς"
    11	Διεύθυνση Βυζαντινών & Μεταβυζαντινών Αρχαιοτήτων
    26	Διεύθυνση Προϊστορικών & Κλασικών Αρχαιοτήτων
    27	Διεύθυνση Συντήρησης Αρχαίων & Νεωτέρων Μνημείων
    472	Διεύθυνση Αρχαιολογικών Μουσείων, Εκθέσεων και Εκπαιδευτικών Προγραμμάτων
    473	Διεύθυνση Διαχείρισης Εθνικού Αρχείου Μνημείων
    474	Διεύθυνση Τεκμηρίωσης και Προστασίας Πολιτιστικών Αγαθών
    475	Διεύθυνση Νεώτερης Πολιτιστικής Κληρονομιάς


     */
    public function run()
    {
        // ΕΦΟΡΕΙΕΣ ΑΡΧΑΙΟΤΗΤΩΝ
        DB::table('directorates')->where('id', 110)->update(['prefecture_id' => 42]);
        DB::table('directorates')->where('id', 331)->update(['prefecture_id' => 46]);
        DB::table('directorates')->where('id', 333)->update(['prefecture_id' => 50]);
        DB::table('directorates')->where('id', 334)->update(['prefecture_id' => 48]);
        DB::table('directorates')->where('id', 335)->update(['prefecture_id' => 49]);
        DB::table('directorates')->where('id', 336)->update(['prefecture_id' => 51]);
        DB::table('directorates')->where('id', 337)->update(['prefecture_id' => 52]);
        DB::table('directorates')->where('id', 338)->update(['prefecture_id' => 34]);
        DB::table('directorates')->where('id', 339)->update(['prefecture_id' => 33]);
        DB::table('directorates')->where('id', 340)->update(['prefecture_id' => 32]);
        DB::table('directorates')->where('id', 341)->update(['prefecture_id' => 28]);
        DB::table('directorates')->where('id', 343)->update(['prefecture_id' => 27]);
        DB::table('directorates')->where('id', 344)->update(['prefecture_id' => 35]);
        DB::table('directorates')->where('id', 345)->update(['prefecture_id' => 39]);
        DB::table('directorates')->where('id', 346)->update(['prefecture_id' => 36]);
        DB::table('directorates')->where('id', 347)->update(['prefecture_id' => 38]);
        DB::table('directorates')->where('id', 348)->update(['prefecture_id' => 20]);
        DB::table('directorates')->where('id', 349)->update(['prefecture_id' => 19]);
        DB::table('directorates')->where('id', 350)->update(['prefecture_id' => 21]);
        DB::table('directorates')->where('id', 351)->update(['prefecture_id' => 18]);
        DB::table('directorates')->where('id', 352)->update(['prefecture_id' => 24]);
        DB::table('directorates')->where('id', 353)->update(['prefecture_id' => 23]);
        DB::table('directorates')->where('id', 354)->update(['prefecture_id' => 26]);
        DB::table('directorates')->where('id', 355)->update(['prefecture_id' => 22]);
        DB::table('directorates')->where('id', 356)->update(['prefecture_id' => 15]);
        DB::table('directorates')->where('id', 357)->update(['prefecture_id' => 17]);
        DB::table('directorates')->where('id', 358)->update(['prefecture_id' => 16]);
        DB::table('directorates')->where('id', 359)->update(['prefecture_id' => 14]);
        DB::table('directorates')->where('id', 360)->update(['prefecture_id' => 8]);
        DB::table('directorates')->where('id', 361)->update(['prefecture_id' => 8]);
        DB::table('directorates')->where('id', 362)->update(['prefecture_id' => 9]);
        DB::table('directorates')->where('id', 363)->update(['prefecture_id' => 13]);
        DB::table('directorates')->where('id', 364)->update(['prefecture_id' => 11]);
        DB::table('directorates')->where('id', 365)->update(['prefecture_id' => 10]);
        DB::table('directorates')->where('id', 366)->update(['prefecture_id' => 7]);
        DB::table('directorates')->where('id', 367)->update(['prefecture_id' => 12]);
        DB::table('directorates')->where('id', 368)->update(['prefecture_id' => 1]);
        DB::table('directorates')->where('id', 369)->update(['prefecture_id' => 3]);
        DB::table('directorates')->where('id', 370)->update(['prefecture_id' => 5]);
        DB::table('directorates')->where('id', 371)->update(['prefecture_id' => 6]);
        DB::table('directorates')->where('id', 372)->update(['prefecture_id' => 2]);
        DB::table('directorates')->where('id', 373)->update(['prefecture_id' => 53]);
        DB::table('directorates')->where('id', 374)->update(['prefecture_id' => 57]);
        DB::table('directorates')->where('id', 375)->update(['prefecture_id' => 56]);
        DB::table('directorates')->where('id', 376)->update(['prefecture_id' => 65]);
        DB::table('directorates')->where('id', 377)->update(['prefecture_id' => 70]);
        DB::table('directorates')->where('id', 378)->update(['prefecture_id' => 74]);
        DB::table('directorates')->where('id', 379)->update(['prefecture_id' => 73]);
        DB::table('directorates')->where('id', 380)->update(['prefecture_id' => 71]);
        DB::table('directorates')->where('id', 381)->update(['prefecture_id' => 72]);
        DB::table('directorates')->where('id', 390)->update(['prefecture_id' => 42]);
        DB::table('directorates')->where('id', 483)->update(['prefecture_id' => 42]);
        DB::table('directorates')->where('id', 484)->update(['prefecture_id' => 44]);
        DB::table('directorates')->where('id', 485)->update(['prefecture_id' => 29]);
        DB::table('directorates')->where('id', 490)->update(['prefecture_id' => 47]);
        // ΜΟΥΣΕΙΑ
        DB::table('directorates')->where('id', 107)->update(['prefecture_id' => 9]);
        DB::table('directorates')->where('id', 486)->update(['prefecture_id' => 9]);
        DB::table('directorates')->where('id', 487)->update(['prefecture_id' => 9]);
        DB::table('directorates')->where('id', 488)->update(['prefecture_id' => 9]);
        DB::table('directorates')->where('id', 489)->update(['prefecture_id' => 9]);
        DB::table('directorates')->where('id', 106)->update(['prefecture_id' => 9]);
        DB::table('directorates')->where('id', 105)->update(['prefecture_id' => 2]);
        DB::table('directorates')->where('id', 114)->update(['prefecture_id' => 2]);
        DB::table('directorates')->where('id', 104)->update(['prefecture_id' => 13]);
        DB::table('directorates')->where('id', 113)->update(['prefecture_id' => 6]);
        // ΛΟΙΠΑ ΓΔΠΑΚ
        DB::table('directorates')->where('id', 2)->update(['prefecture_id' => 42]);
        DB::table('directorates')->where('id', 11)->update(['prefecture_id' => 42]);
        DB::table('directorates')->where('id', 26)->update(['prefecture_id' => 42]);
        DB::table('directorates')->where('id', 27)->update(['prefecture_id' => 42]);
        DB::table('directorates')->where('id', 472)->update(['prefecture_id' => 42]);
        DB::table('directorates')->where('id', 473)->update(['prefecture_id' => 42]);
        DB::table('directorates')->where('id', 474)->update(['prefecture_id' => 42]);
        DB::table('directorates')->where('id', 475)->update(['prefecture_id' => 42]);
    }
}
