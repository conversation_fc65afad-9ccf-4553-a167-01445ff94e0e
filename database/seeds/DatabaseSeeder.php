<?php

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;


class DatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();

        // Example:
//        if (App::environment('local')) {
//            $this->call(FooSeeder::class);
//        }

        // Asset seeders - using the main Assets seeder
        $this->call('Assets\\AssetsSeeder');

        Model::reguard();
    }
}
