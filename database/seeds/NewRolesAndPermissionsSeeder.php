<?php

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class NewRolesAndPermissionsSeeder extends Seeder
{
    public function run()
    {
        /*
        |--------------------------------------------------------------------------
        | Phonebook
        |--------------------------------------------------------------------------
        |
        */
        // Setup permissions
        Permission::getByName('phonebook.edit')->delete();
        Permission::getByName('phonebook.editAll')->delete();
        Permission::getByName('phonebook.store')->delete();
        Permission::getByName('phonebook.storeAll')->delete();
        $create = tap(Permission::getByName('phonebook.create'))
            ->update([
                'description' => 'Δικαίωμα καταχώρισης'
            ]);
        $createAll = tap(Permission::getByName('phonebook.createAll'))
            ->update([
                'description' => 'Δικαίωμα καταχώρισης σε όλες τις Διευθύνσεις'
            ]);
        $read = tap(Permission::getByName('phonebook.show'))
            ->update([
                'name' => 'phonebook.read',
                'description' => 'Δικαίωμα προβολής'
            ]);
        $readAll = tap(Permission::getByName('phonebook.showAll'))
            ->update([
                'name' => 'phonebook.readAll',
                'description' => 'Δικαίωμα προβολής σε όλες τις Διευθύνσεις'
            ]);
        $update = tap(Permission::getByName('phonebook.update'))
            ->update([
                'description' => 'Δικαίωμα επεξεργασίας'
            ]);
        $updateAll = tap(Permission::getByName('phonebook.updateAll'))
            ->update([
                'description' => 'Δικαίωμα επεξεργασίας σε όλες τις Διευθύνσεις'
            ]);
        $delete = tap(Permission::getByName('phonebook.delete'))
            ->update([
                'description' => 'Δικαίωμα διαγραφής'
            ]);
        $deleteAll = tap(Permission::getByName('phonebook.deleteAll'))
            ->update([
                'description' => 'Δικαίωμα διαγραφής σε όλες τις Διευθύνσεις'
            ]);

        // Setup roles
        $editor = tap(Role::getByName('phonebook.editor'))
            ->update([
                'description' => 'Συντάκτης'
            ])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id
            ]);
        $editorAll = tap(Role::getByName('phonebook.editorAll'))
            ->update([
                'description' => 'Συντάκτης σε όλες τις Διευθύνσεις'
            ])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id,
                $createAll->id, $readAll->id, $updateAll->id, $deleteAll->id
            ]);

        /*
        |--------------------------------------------------------------------------
        | Opendata
        |--------------------------------------------------------------------------
        |
        */
        // Setup permissions
        $create = Permission::create([
            'name' => 'opendata.create',
            'description' => 'Δικαίωμα καταχώρισης'
        ]);
        $createGd = Permission::create([
            'name' => 'opendata.createGd',
            'description' => 'Δικαίωμα καταχώρισης σε όλες τις Διευθύνσεις'
        ]);
        $createAll = Permission::create([
            'name' => 'opendata.createAll',
            'description' => 'Δικαίωμα καταχώρισης στην Γενική Διεύθυνση'
        ]);
        $read = tap(Permission::getByName('opendata.read'))
            ->update([
                'description' => 'Δικαίωμα προβολής'
            ]);
        $readAll = tap(Permission::getByName('opendata.readAll'))
            ->update([
                'description' => 'Δικαίωμα προβολής σε όλες τις Διευθύνσεις'
            ]);
        $readGd = tap(Permission::getByName('opendata.readGd'))
            ->update([
                'description' => 'Δικαίωμα προβολής στην Γενική Διεύθυνση'
            ]);
        $update = tap(Permission::getByName('opendata.edit'))
            ->update([
                'name' => 'opendata.update',
                'description' => 'Δικαίωμα επεξεργασίας'
            ]);
        $updateAll = tap(Permission::getByName('opendata.editAll'))
            ->update([
                'name' => 'opendata.updateAll',
                'description' => 'Δικαίωμα επεξεργασίας σε όλες τις Διευθύνσεις'
            ]);
        $updateGd = tap(Permission::getByName('opendata.editGd'))
            ->update([
                'name' => 'opendata.updateGd',
                'description' => 'Δικαίωμα επεξεργασίας στην Γενική Διεύθυνση'
            ]);
        $delete = tap(Permission::getByName('opendata.delete'))
            ->update([
                'description' => 'Δικαίωμα διαγραφής'
            ]);
        $deleteAll = tap(Permission::getByName('opendata.deleteAll'))
            ->update([
                'description' => 'Δικαίωμα διαγραφής σε όλες τις Διευθύνσεις'
            ]);
        $deleteGd = tap(Permission::getByName('opendata.deleteGd'))
            ->update([
                'description' => 'Δικαίωμα διαγραφής στην Γενική Διεύθυνση'
            ]);
        $admin = tap(Permission::getByName('opendata.admin'))
            ->update([
                'description' => 'Δικαίωμα διαχείρισης'
            ]);

        // Setup roles
        $subscriberAll = tap(Role::getByName('opendata.readAll'))
            ->update([
                'name' => 'opendata.subscriberAll',
                'description' => 'Συνδρομητής σε όλες τις Διευθύνσεις'
            ])
            ->permissions()
            ->sync([$read->id, $readAll->id, $readGd->id]);
        $editor = tap(Role::getByName('opendata'))
            ->update([
                'name' => 'opendata.editor',
                'description' => 'Συντάκτης'
            ])
            ->permissions()
            ->sync([$create->id, $read->id, $update->id, $delete->id]);
        $editorGd = tap(Role::getByName('opendata.gd'))
            ->update([
                'name' => 'opendata.editorGd',
                'description' => 'Συντάκτης στην Γενική Διεύθυνση'
            ])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id,
                $createGd->id, $readGd->id, $updateGd->id, $deleteGd->id,
            ]);
        $editorAll = tap(Role::getByName('opendata.ode'))
            ->update([
                'name' => 'opendata.editorAll',
                'description' => 'Συντάκτης σε όλες τις Διευθύνσεις'
            ])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id,
                $createGd->id, $readGd->id, $updateGd->id, $deleteGd->id,
                $createAll->id, $readAll->id, $updateAll->id, $deleteAll->id,
            ]);
        $admin = tap(Role::getByName('opendata.admin'))
            ->update([
                'description' => 'Διαχειριστής'
            ])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id,
                $createGd->id, $readGd->id, $updateGd->id, $deleteGd->id,
                $createAll->id, $readAll->id, $updateAll->id, $deleteAll->id,
                $admin->id
            ]);

        /*
        |--------------------------------------------------------------------------
        | Personnel
        |--------------------------------------------------------------------------
        |
        */
        // Setup permissions
        $create = tap(Permission::getByName('personnel.create'))
            ->update(['description' => 'Δικαίωμα καταχώρισης']);
        $read = tap(Permission::getByName('personnel.read'))
            ->update(['description' => 'Δικαίωμα προβολής']);
        $update = tap(Permission::getByName('personnel.update'))
            ->update(['description' => 'Δικαίωμα επεξεργασίας']);
        $delete = tap(Permission::getByName('personnel.delete'))
            ->update(['description' => 'Δικαίωμα διαγραφής']);
        $readAll = tap(Permission::getByName('personnel.readAll'))
            ->update(['description' => 'Δικαίωμα προβολής σε όλες τις Διευθύνσεις']);
        $admin = Permission::create(['name' => 'personnel.admin', 'description' => 'Δικαίωμα διαχείρισης']);

        $subscriber = tap(Role::getByName('personnel'))
            ->update(['name' => 'personnel.subscriber', 'description' => 'Συνδρομητής'])
            ->permissions()
            ->sync([$read->id]);
        $subscriberAll = tap(Role::getByName('personnel.readAll'))
            ->update(['name' => 'personnel.subscriberAll', 'description' => 'Συνδρομητής σε όλες τις Διευθύνσεις'])
            ->permissions()
            ->sync([
                $read->id, $readAll->id
            ]);
        $editor = tap(Role::getByName('personnel.editor'))
            ->update(['description' => 'Συντάκτης'])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id
                // FIXME: $readAll is attached in editor role in production. shouldn't we remove it?
            ]);
        $admin = tap(Role::getByName('personnel.admin'))
            ->update(['description' => 'Διαχειριστής'])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id,
                $readAll->id,
                $admin->id
            ]);

        /*
        |--------------------------------------------------------------------------
        | Conservations
        |--------------------------------------------------------------------------
        |
        */
        // Setup permissions
        $create = tap(Permission::getByName('conservations.create'))
            ->update([
                'description' => 'Δικαίωμα καταχώρισης'
            ]);
        $read = tap(Permission::getByName('conservations.read'))
            ->update([
                'description' => 'Δικαίωμα προβολής'
            ]);
        $update = tap(Permission::getByName('conservations.edit'))
            ->update([
                'name' => 'conservations.update',
                'description' => 'Δικαίωμα επεξεργασίας'
            ]);
        $delete = tap(Permission::getByName('conservations.delete'))
            ->update([
                'description' => 'Δικαίωμα διαγραφής'
            ]);
        $admin = tap(Permission::getByName('conservations.admin'))
            ->update([
                'description' => 'Δικαίωμα διαχείρισης'
            ]);

        // Setup roles

        $subscriber = tap(Role::getByName('conservations'))
            ->update([
                'name' => 'conservations.subscriber',
                'description' => 'Συνδρομητής'
            ])
            ->permissions()
            ->sync([
                $read->id,
            ]);
        $editor = tap(Role::getByName('conservations.editor'))
            ->update([
                'description' => 'Συντάκτης'
            ])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id
            ]);
        $admin = tap(Role::getByName('conservations.admin'))
            ->update([
                'description' => 'Διαχειριστής'
            ])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id,
                $admin->id
            ]);

        /*
        |--------------------------------------------------------------------------
        | Registry
        |--------------------------------------------------------------------------
        |
        */
        // Setup permissions
        $create = tap(Permission::getByName('registry.create'))
            ->update([
                'description' => 'Δικαίωμα καταχώρισης'
            ]);
        $read = tap(Permission::getByName('registry.read'))
            ->update([
                'description' => 'Δικαίωμα προβολής'
            ]);
        $update = tap(Permission::getByName('registry.update'))
            ->update([
                'description' => 'Δικαίωμα επεξεργασίας'
            ]);
        $delete = tap(Permission::getByName('registry.delete'))
            ->update([
                'description' => 'Δικαίωμα διαγραφής'
            ]);
        $createAll = tap(Permission::getByName('registry.createAll'))
            ->update([
                'description' => 'Δικαίωμα καταχώρισης σε όλες τις Διευθύνσεις'
            ]);
        $readAll = tap(Permission::getByName('registry.readAll'))
            ->update([
                'description' => 'Δικαίωμα προβολής σε όλες τις Διευθύνσεις'
            ]);
        $updateAll = tap(Permission::getByName('registry.updateAll'))
            ->update([
                'description' => 'Δικαίωμα επεξεργασίας σε όλες τις Διευθύνσεις'
            ]);
        $deleteAll = tap(Permission::getByName('registry.deleteAll'))
            ->update([
                'description' => 'Δικαίωμα διαγραφής σε όλες τις Διευθύνσεις'
            ]);
        $admin = tap(Permission::getByName('registry.admin'))
            ->update([
                'description' => 'Δικαίωμα διαχείρισης'
            ]);

        // Setup roles
        $subscriber = tap(Role::getByName('registry'))
            ->update([
                'name' => 'registry.subscriber',
                'description' => 'Συνδρομητής'
            ])
            ->permissions()
            ->sync([
                $read->id,
            ]);
        $subscriberAll = tap(Role::getByName('registry.readAll'))
            ->update([
                'name' => 'registry.subscriberAll',
                'description' => 'Συνδρομητής σε όλες τις Διευθύνσεις'
            ])
            ->permissions()
            ->sync([
                $read->id,
                $readAll->id
            ]);
        $editor = tap(Role::getByName('registry.editor'))
            ->update([
                'description' => 'Συντάκτης'
            ])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id,
            ]);
        $admin = tap(Role::getByName('registry.admin'))
            ->update([
                'description' => 'Διαχειριστής'
            ])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id,
                $createAll->id, $readAll->id, $updateAll->id, $deleteAll->id,
                $admin->id
            ]);


        /*
        |--------------------------------------------------------------------------
        | Educational
        |--------------------------------------------------------------------------
        |
        */
        // Setup permissions
        $create = Permission::create([
            'name' => 'educational.create',
            'description' => 'Δικαίωμα καταχώρισης',
        ]);
        $read = Permission::create([
            'name' => 'educational.read',
            'description' => 'Δικαίωμα προβολής',
        ]);
        $update = Permission::create([
            'name' => 'educational.update',
            'description' => 'Δικαίωμα επεξεργασίας',
        ]);
        $delete = Permission::create([
            'name' => 'educational.delete',
            'description' => 'Δικαίωμα διαγραφής',
        ]);
        $admin = tap(Permission::getByName('educational.admin'))
            ->update([
                'description' => 'Δικαίωμα διαχείρισης'
            ]);

        // Setup roles
        $subscriber = Role::create([
            'name' => 'educational.subscriber',
            'description' => 'Συνδρομητής'
        ])
            ->permissions()
            ->sync([
                $read->id,
            ]);
        $editor = Role::create([
            'name' => 'educational.editor',
            'description' => 'Συντάκτης'
        ])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id
            ]);
        $admin = tap(Role::getByName('educational.admin'))
            ->update([
                'description' => 'Διαχειριστής'
            ])
            ->permissions()
            ->sync([
                $create->id, $read->id, $update->id, $delete->id,
                $admin->id
            ]);
    }
}
