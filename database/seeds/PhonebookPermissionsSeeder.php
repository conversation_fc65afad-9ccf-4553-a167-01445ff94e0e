<?php

use Database\Seeds\Traits\PermissionSeeder;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PhonebookPermissionsSeeder extends Seeder
{
    use PermissionSeeder;

    public function run()
    {
        $appData = [
            'name' => 'Τηλεφωνικός Κατάλογος',
            'abbrv' => 'phonebook',
            'icon' => 'fa-phone',
            'color' => '#715973',
        ];

        $permissionNames = [
            ['name' => 'phonebook.create', 'description' => 'Δικαίωμα καταχώρησης'],
            ['name' => 'phonebook.read', 'description' => 'Δικαίωμα προβολής'],
            ['name' => 'phonebook.update', 'description' => 'Δικαίωμα επεξεργασίας'],
            ['name' => 'phonebook.delete', 'description' => 'Δικαίωμα διαγραφής'],
            ['name' => 'phonebook.createAll', 'description' => 'Δικαίωμα καταχώρησης σε όλες τις Διευθύνσεις'],
            ['name' => 'phonebook.readAll', 'description' => 'Δικαίωμα προβολής σε όλες τις Διευθύνσεις'],
            ['name' => 'phonebook.updateAll', 'description' => 'Δικαίωμα επεξεργασίας σε όλες τις Διευθύνσεις'],
            ['name' => 'phonebook.deleteAll', 'description' => 'Δικαίωμα διαγραφής σε όλες τις Διευθύνσεις'],
        ];

        $roleNames = [
            ['name' => 'phonebook.editor', 'description' => 'Συντάκτης'],
            ['name' => 'phonebook.editorAll', 'description' => 'Συντάκτης σε όλες τις Διευθύνσεις'],
        ];

        $assignments = [
            'phonebook.editor' => [
                'phonebook.read',
                'phonebook.create',
                'phonebook.update',
                'phonebook.delete',
            ],
            'phonebook.editorAll' => [
                'phonebook.read',
                'phonebook.create',
                'phonebook.update',
                'phonebook.delete',
                'phonebook.readAll',
                'phonebook.createAll',
                'phonebook.updateAll',
                'phonebook.deleteAll',
            ],
        ];

        $managerIds = DB::table('users as u')
            ->join('directorates as d', 'u.current_directorate_id', '=', 'd.id')
            ->whereColumn('u.email', '=', 'd.email')
            ->whereNull('u.deleted_at')
            ->pluck('u.id')->toArray();

        $roleManager = [
            'phonebook.editor' => $managerIds,
        ];

        $this->seedPermissions($appData, $roleNames, $permissionNames, $assignments);

        $this->seedManagedRoles($roleManager);
    }
}
