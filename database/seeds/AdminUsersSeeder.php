<?php

use App\Models\Role;
use App\Models\User;
use App\Models\Permission;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;

class AdminUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create permissions
        $permissionAdmin = Permission::create(array(
            'name' => 'administer',
            'description' => 'Διαχείριση'
        ));

        // Create roles
        $roleAdmin = Role::create(array(
            'name' => 'admin',
            'description' => 'Administrator role'
        ));

        $roleAdmin->permissions()->attach($permissionAdmin);

        // Create admin users
        $user1 = User::create(array(
            'username' => 'apanagiotou',
            'email' => '<EMAIL>',
            'password' => Hash::make(Str::random(20)),
            'name' => 'apanagiotou'
        ));

        $user2 = User::create(array(
            'username' => 'igeorgas',
            'email' => '<EMAIL>',
            'password' => Hash::make(Str::random(20)),
            'name' => 'igeorgas'
        ));

        $user1->roles()->attach($roleAdmin);
        $user2->roles()->attach($roleAdmin);
    }
}
