<?php

use Database\Seeds\Traits\PermissionSeeder;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConservationsPermissionsSeeder extends Seeder
{
    use PermissionSeeder;

    public function run()
    {
        $appData = [
            'name' => 'Μητρώο Συντηρητών',
            'abbrv' => 'conservations',
            'icon' => 'fa-paint-brush',
            'color' => '#488b98',
        ];

        $permissionNames = [
            ['name' => 'conservations.create', 'description' => 'Δικαίωμα καταχώρησης'],
            ['name' => 'conservations.read', 'description' => 'Δικαίωμα προβολής'],
            ['name' => 'conservations.update', 'description' => 'Δικαίωμα επεξεργασίας'],
            ['name' => 'conservations.delete', 'description' => 'Δικαίωμα διαγραφής'],
            ['name' => 'conservations.admin', 'description' => 'Δικαίωμα διαχείρισης'],
        ];

        $roleNames = [
            ['name' => 'conservations.subscriber', 'description' => 'Συνδρομητής'],
            ['name' => 'conservations.editor', 'description' => 'Συντάκτης'],
            ['name' => 'conservations.admin', 'description' => 'Διαχειριστής'],
        ];

        $assignments = [
            'conservations.subscriber' => [
                'conservations.read',
            ],
            'coservations.editor' => [
                'conservations.create',
                'conservations.read',
                'conservations.update',
                'conservations.delete',
            ],
            'conservations.admin' => [
                'conservations.admin',
                'conservations.create',
                'conservations.read',
                'conservations.update',
                'conservations.delete',
            ],
        ];

        $allManagerIds = DB::table('users as u')
            ->join('directorates as d', 'u.current_directorate_id', '=', 'd.id')
            ->whereColumn('u.email', '=', 'd.email')
            ->whereNull('u.deleted_at')
            ->pluck('u.id')->toArray();

        $conservationsManagersIds = [24];

        $roleManager = [
            'conservations.subscriber' => $allManagerIds,
            'conservations.editor' => $conservationsManagersIds,
        ];

        $this->seedPermissions($appData, $roleNames, $permissionNames, $assignments);

        $this->seedManagedRoles($roleManager);
    }
}
