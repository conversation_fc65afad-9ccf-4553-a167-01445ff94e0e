<?php

use App\Models\Contractuals\Language;
use App\Models\Contractuals\LanguageLevel;
use App\Models\Contractuals\Requirement;
use App\Models\Contractuals\RequirementType;
use App\Models\Contractuals\Specialization;
use App\Models\Contractuals\SpecializationType;
use Illuminate\Database\Seeder;

class ContractualsSeederTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::connection(env('DB_CONNECTION_CONTRACTUALS'))->statement("SET foreign_key_checks = 0");

        DB::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('specializations')->truncate();
        DB::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('specialization_types')->truncate();
        DB::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('requirements')->truncate();
        DB::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('requirement_types')->truncate();
        DB::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('languages')->truncate();
        DB::connection(env('DB_CONNECTION_CONTRACTUALS'))->table('language_levels')->truncate();

        DB::connection(env('DB_CONNECTION_CONTRACTUALS'))->statement("SET foreign_key_checks = 1");

        /*
        |--------------------------------------------------------------------------
        | Specializations
        |--------------------------------------------------------------------------
        |
        | Seed specializations and specializationTypes
        |
        */
        $specializations_pe = array(
            'ΠΕ Αρχαιολόγοι',
            'ΠΕ Αρχιτέκτονες',
            'ΠΕ Γεωλόγοι',
            'ΠΕ Ηλεκτρολόγοι Μηχανικοί',
            'ΠΕ Λογιστές',
            'ΠΕ Μηχανικοί',
            'ΠΕ Μηχανικών Αρχιτεκτόνων',
            'ΠΕ Μηχανολοι Μηχανικοί',
            'ΠΕ Οικονομολόγοι',
            'ΠΕ Πληροφορικής',
            'ΠΕ Πολιτικοί Μηχανικοί',
            'ΠΕ Συντηρητές Αρχαιοτήτων',
            'ΠΕ Τοπογράφοι Μηχανικοί',
            'ΠΕ Φυσικοί',
            'ΠΕ Χημικοί',
            'ΠΕ Διοικητικού - Οικονομικού'
        );
        $specializations_te = array(
            'ΤΕ Διοικητικού - Λογιστικού',
            'ΤΕ Λογιστές',
            'ΤΕ Μηχανικοί',
            'ΤΕ Συντηρητές Αρχαιοτήτων',
            'ΤΕ Σχεδιαστές',
            'ΤΕ Τεχνολόγοι Μηχανικοί',
            'ΤΕ Τοπογράφοι Μηχανικοί'
        );
        $specializations_de = array(
            'ΔΕ Διοικητικοί',
            'ΔΕ Ειδικευμένοι Εργάτες',
            'ΔΕ Εργατοτεχνίτες',
            'ΔΕ Ηλεκτρολόγοι',
            'ΔΕ Μαρμαροτεχνίτες',
            'ΔΕ Νυχτοφύλακες',
            'ΔΕ Συντηρητές Αρχαιοτήτων',
            'ΔΕ Σχεδιαστές',
            'ΔΕ Τεχνικοί Συντήρησης Αρχαιοτήτων & Έργων Τέχνης',
            'ΔΕ Φύλακες Αρχαιοτήτων',
            'ΔΕ Φύλακες Εργοταξίου',
            'ΔΕ Χειριστές Η/Υ'
        );
        $specializations_ye = array(
            'ΥΕ Αρχιτεχνίτες',
            'ΥΕ Ειδικευμένοι Εργάτες',
            'ΥΕ Ειδικευμένοι Τεχνίτες',
            'ΥΕ Εργάτες',
            'ΥΕ Νυχτοφύλακες',
            'ΥΕ Προσωπικό Καθαριότητας',
            'ΥΕ Τεχνίτες',
            'ΥΕ Φύλακες Αρχαιοτήτων'
        );

        $st_pe = SpecializationType::create(array('name' => 'ΠΕ'));
        $st_te = SpecializationType::create(array('name' => 'ΤΕ'));
        $st_de = SpecializationType::create(array('name' => 'ΔΕ'));
        $st_ye = SpecializationType::create(array('name' => 'ΥΕ'));

        foreach ($specializations_pe as $specialization) {
            Specialization::create(array(
                'name'                   => $specialization,
                'shortname'              => mb_substr($specialization, 0, 19),
                'specialization_type_id' => $st_pe->id
            ));
        }

        foreach ($specializations_te as $specialization) {
            Specialization::create(array(
                'name'                   => $specialization,
                'shortname'              => mb_substr($specialization, 0, 19),
                'specialization_type_id' => $st_te->id
            ));
        }

        foreach ($specializations_de as $specialization) {
            Specialization::create(array(
                'name'                   => $specialization,
                'shortname'              => mb_substr($specialization, 0, 19),
                'specialization_type_id' => $st_de->id
            ));
        }

        foreach ($specializations_ye as $specialization) {
            Specialization::create(array(
                'name'                   => $specialization,
                'shortname'              => mb_substr($specialization, 0, 19),
                'specialization_type_id' => $st_ye->id
            ));
        }

        /*
        |--------------------------------------------------------------------------
        | Requirements
        |--------------------------------------------------------------------------
        |
        | Seed Requirement and RequirementTypes
        |
        */
        $degree = RequirementType::create(array(
            'name'             => 'Τίτλος Σπουδών',
            'qualifiable_type' => 'degrees',
        ));
        $postgraduate = RequirementType::create(array(
            'name'             => 'Μεταπτυχιακό',
            'qualifiable_type' => 'postgraduates',
        ));
        $doctorate = RequirementType::create(array(
            'name'             => 'Διδακτορικό',
            'qualifiable_type' => 'doctorates',
        ));
        $greekLanguage = RequirementType::create(array(
            'name'             => 'Ελληνομάθεια',
            'qualifiable_type' => 'greek_languages',
        ));
        $languageSkill = RequirementType::create(array(
            'name'             => 'Ξένες Γλώσσες',
            'qualifiable_type' => 'language_skills',
        ));
        $computerSkill = RequirementType::create(array(
            'name'             => 'Γνώσεις Η/Υ',
            'qualifiable_type' => 'computer_skills',
        ));
        $experience = RequirementType::create(array(
            'name'             => 'Εμπειρία',
            'qualifiable_type' => 'experiences',
        ));

        $greekLanguages = array(
            'Πιστοποιητικό Ελληνομάθειας επιπέδου Α ή Α2',
            'Πιστοποιητικό Ελληνομάθειας επιπέδου Β ή Β1',
            'Πιστοποιητικό Ελληνομάθειας επιπέδου Γ ή Β2',
            'Πιστοποιητικό Ελληνομάθειας επιπέδου Δ ή Γ1',
        );
        foreach ($greekLanguages as $requirement) {
            Requirement::create(array(
                'name'                => $requirement,
                'requirement_type_id' => $greekLanguage->id
            ));
        }
        $languageSkills = array(
            'Άριστη γνώση της Αγγλικής, ή Γαλλικής, ή Γερμανικής, ή Ιταλικής γλώσσας.',
            'Πολύ καλή γνώση Αγγλικής ή Γαλλικής ή Γερμανικής ή Ιταλικής γλώσσας.',
            'Καλή γνώση Αγγλικής ή Γαλλικής ή Γερμανικής ή Ιταλικής γλώσσας.',
        );
        foreach ($languageSkills as $requirement) {
            Requirement::create(array(
                'name'                => $requirement,
                'requirement_type_id' => $languageSkill->id
            ));
        }
        $degrees = array(
            'Βεβαίωση σπουδών',
            'Βεβαίωση σπουδών ή αναλυτική βαθμολογία.',
            'Βεβαίωση ομάδας ή αναλυτική βαθμολογία.',
        );
        foreach ($degrees as $requirement) {
            Requirement::create(array(
                'name'                => $requirement,
                'requirement_type_id' => $degree->id
            ));
        }
        $computerSkills = array(
            'Αποδεδειγμένη γνώση χειρισμού Η/Υ στα αντικείμενα: α) επεξεργασίας κειμένων, β) υπολογιστικών φύλλων,  γ) υπηρεσιών διαδικτύου.',
            'Αποδεδειγμένη γνώση χειρισμού Η/Υ στα αντικείμενα: α) επεξεργασίας κειμένων, β) υπολογιστικών φύλλων,  γ) υπηρεσιών διαδικτύου και δ) autocad [εφόσον είναι απαραίτητο για το συγκεκριμένο έργο].',
        );
        foreach ($computerSkills as $requirement) {
            Requirement::create(array(
                'name'                => $requirement,
                'requirement_type_id' => $computerSkill->id
            ));
        }
        $experiences = array(
            'Αποδεδειγμένη Εμπειρία (τουλάχιστον 6μηνη) συναφή με το αντικείμενο της προκηρυσσόμενης θέσης.',
        );
        foreach ($experiences as $requirement) {
            Requirement::create(array(
                'name'                => $requirement,
                'requirement_type_id' => $experience->id
            ));
        }

        /*
        |--------------------------------------------------------------------------
        | Λ
        |--------------------------------------------------------------------------
        |
        | Seed Requirement and RequirementTypes
        |
        */
        Language::create(array('name' => 'Αγγλικά', 'code' => 'en'));
        Language::create(array('name' => 'Γαλλικά', 'code' => 'fr'));
        Language::create(array('name' => 'Γερμανικά', 'code' => 'de'));
        Language::create(array('name' => 'Ιταλικά', 'code' => 'it'));

        LanguageLevel::create(array('name' => 'Άριστα'));
        LanguageLevel::create(array('name' => 'Πολύ Καλά'));
        LanguageLevel::create(array('name' => 'Καλά'));
    }
}
