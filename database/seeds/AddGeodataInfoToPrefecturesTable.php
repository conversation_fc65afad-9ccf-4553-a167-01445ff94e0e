<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AddGeodataInfoToPrefecturesTable extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $mapper = [
            1 => '52000000',
            2 => '71000000',
            3 => '55000000',
            5 => '72000000',
            6 => '73000000',
            7 => '53000000',
            8 => '54000000',
            9 => '57000000',
            10 => '59000000',
            11 => '61000000',
            12 => '62000000',
            13 => '64000000',
            14 => '51000000',
            15 => '56000000',
            16 => '58000000',
            17 => '63000000',
            18 => '31000000',
            19 => '32000000',
            20 => '33000000',
            21 => '34000000',
            22 => '41000000',
            23 => '42000000',
            24 => '43000000',
            26 => '44000000',
            27 => '21000000',
            28 => '22000000',
            29 => '23000000',
            31 => '24000000',
            32 => '01000000',
            33 => '13000000',
            34 => '14000000',
            35 => '03000000',
            36 => '04000000',
            37 => '05000000',
            38 => '06000000',
            39 => '07000000',
            42 => '99000000',
            44 => '96000000',
            46 => '98000000',
            47 => '97000000',
            48 => '11000000',
            49 => '12000000',
            50 => '15000000',
            51 => '16000000',
            52 => '17000000',
            53 => '83000000',
            56 => '84000000',
            57 => '85000000',
            65 => '82000000',
            70 => '81000000',
            71 => '91000000',
            72 => '92000000',
            73 => '93000000',
            74 => '94000000',
        ];

        foreach ($mapper as $id => $geoId) {
            DB::table('prefectures')->where('id', $id)->update(['geoid' => $geoId]);
        }
    }
}
