<?php

use Database\Seeds\Traits\PermissionSeeder;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RegistryPermissionsSeeder extends Seeder
{
    use PermissionSeeder;

    public function run()
    {
        $appData = [
            'name' => 'Μητρώο Υπηρεσιών',
            'abbrv' => 'registry',
            'icon' => 'fa-building',
            'color' => '#c9b60e',
        ];

        $permissionNames = [
            ['name' => 'registry.create', 'description' => 'Δικαίωμα καταχώρησης'],
            ['name' => 'registry.read', 'description' => 'Δικαίωμα προβολής'],
            ['name' => 'registry.update', 'description' => 'Δικαίωμα επεξεργασίας'],
            ['name' => 'registry.delete', 'description' => 'Δικαίωμα διαγραφής'],
            ['name' => 'registry.createAll', 'description' => 'Δικαίωμα καταχώρησης σε όλες τις Διευθύνσεις'],
            ['name' => 'registry.readAll', 'description' => 'Δικαίωμα προβολής σε όλες τις Διευθύνσεις'],
            ['name' => 'registry.updateAll', 'description' => 'Δικαίωμα επεξεργασίας σε όλες τις Διευθύνσεις'],
            ['name' => 'registry.deleteAll', 'description' => 'Δικαίωμα διαγραφής σε όλες τις Διευθύνσεις'],
            ['name' => 'registry.admin', 'description' => 'Δικαίωμα διαχείρισης'],
        ];

        $roleNames = [
            ['name' => 'registry.subscriber', 'description' => 'Συνδρομητής'],
            ['name' => 'registry.subscriberAll', 'description' => 'Συνδρομητής σε όλες τις Διευθύνσεις'],
            ['name' => 'registry.editor', 'description' => 'Συντάκτης'],
            ['name' => 'registry.admin', 'description' => 'Διαχειριστής'],
        ];

        $assignments = [
            'registry.subscriber' => [
                'registry.read',
            ],
            'registry.subscriberAll' => [
                'registry.read',
                'registry.readAll',
            ],
            'registry.editor' => [
                'registry.create',
                'registry.read',
                'registry.update',
                'registry.delete',
            ],
            'registry.admin' => [
                'registry.admin',
                'registry.create',
                'registry.read',
                'registry.update',
                'registry.delete',
                'registry.createAll',
                'registry.readAll',
                'registry.updateAll',
                'registry.deleteAll',
            ],
        ];

        $allManagerIds = DB::table('users as u')
            ->join('directorates as d', 'u.current_directorate_id', '=', 'd.id')
            ->whereColumn('u.email', '=', 'd.email')
            ->whereNull('u.deleted_at')
            ->pluck('u.id')->toArray();

        $roleManager = [
            'registry.subscriber' => $allManagerIds,
            'registry.editor' => $allManagerIds,
        ];

        $this->seedPermissions($appData, $roleNames, $permissionNames, $assignments);

        $this->seedManagedRoles($roleManager);
    }
}
