<?php

use Database\Seeds\Traits\PermissionSeeder;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OpendataPermissionsSeeder extends Seeder
{
    use PermissionSeeder;

    public function run()
    {
        $appData = [
            'name' => 'Σύνολα Δεδομένων',
            'abbrv' => 'opendata',
            'icon' => 'fa-cubes',
            'color' => '#0c90ad',
        ];

        $permissionNames = [
            ['name' => 'opendata.create', 'description' => 'Δικαίωμα καταχώρησης'],
            ['name' => 'opendata.read', 'description' => 'Δικαίωμα προβολής'],
            ['name' => 'opendata.update', 'description' => 'Δικαίωμα επεξεργασίας'],
            ['name' => 'opendata.delete', 'description' => 'Δικαίωμα διαγραφής'],
            ['name' => 'opendata.createGd', 'description' => 'Δικαίωμα καταχώρησης στην Γενική Διεύθυνση'],
            ['name' => 'opendata.readGd', 'description' => 'Δικαίωμα προβολής στην Γενική Διεύθυνση'],
            ['name' => 'opendata.updateGd', 'description' => 'Δικαίωμα επεξεργασίας στην Γενική Διεύθυνση'],
            ['name' => 'opendatadeleteGd.', 'description' => 'Δικαίωμα διαγραφής στην Γενική Διεύθυνση'],
            ['name' => 'opendata.createAll', 'description' => 'Δικαίωμα καταχώρησης σε όλες τις Διευθύνσεις'],
            ['name' => 'opendata.readAll', 'description' => 'Δικαίωμα προβολής σε όλες τις Διευθύνσεις'],
            ['name' => 'opendata.updateAll', 'description' => 'Δικαίωμα επεξεργασίας σε όλες τις Διευθύνσεις'],
            ['name' => 'opendata.deleteAll', 'description' => 'Δικαίωμα διαγραφής σε όλες τις Διευθύνσεις'],
            ['name' => 'opendata.admin', 'description' => 'Δικαίωμα διαχείρισης'],
        ];

        $roleNames = [
            ['name' => 'opendata.subscriberAll', 'description' => 'Συνδρομητής σε όλες τις Διευθύνσεις'],
            ['name' => 'opendata.editor', 'description' => 'Συντάκτης'],
            ['name' => 'opendata.editorGd', 'description' => 'Συντάκτης στην Γενική Διεύθυνση'],
            ['name' => 'opendata.editorAll', 'description' => 'Συντάκτης σε όλες τις Διευθύνσεις'],
            ['name' => 'opendata.admin', 'description' => 'Διαχειριστής'],
        ];

        // TODO: check trello
        $rolePermission = [
            'opendata.subscriberAll' => [
                'opendata.read',
                'opendata.readGd',
                'opendata.readAll',
            ],
            'opendata.editor' => [
                'opendata.create',
                'opendata.read',
                'opendata.update',
                'opendata.delete',
            ],
            'opendata.editorGd' => [
                'opendata.create',
                'opendata.read',
                'opendata.update',
                'opendata.delete',
                'opendata.createGd',
                'opendata.readGd',
                'opendata.updateGd',
                'opendata.deleteGd',
            ],
            'opendata.editorAll' => [
                'opendata.create',
                'opendata.read',
                'opendata.update',
                'opendata.delete',
                'opendata.createGd',
                'opendata.readGd',
                'opendata.updateGd',
                'opendata.deleteGd',
                'opendata.createAll',
                'opendata.readAll',
                'opendata.updateAll',
                'opendata.deleteAll',
            ],
            'opendata.admin' => [
                'opendata.admin',
                'opendata.create',
                'opendata.read',
                'opendata.update',
                'opendata.delete',
                'opendata.createGd',
                'opendata.readGd',
                'opendata.updateGd',
                'opendata.deleteGd',
                'opendata.createAll',
                'opendata.readAll',
                'opendata.updateAll',
                'opendata.deleteAll',
            ],
        ];

        $managerIds = DB::table('users as u')
            ->join('directorates as d', 'u.current_directorate_id', '=', 'd.id')
            ->whereColumn('u.email', '=', 'd.email')
            ->whereNull('u.deleted_at')
            ->pluck('u.id')->toArray();

        $roleManager = [
            'opendata.subscriber' => $managerIds,
        ];

        $this->seedPermissions($appData, $roleNames, $permissionNames, $rolePermission);

        $this->seedManagedRoles($roleManager);
    }
}
