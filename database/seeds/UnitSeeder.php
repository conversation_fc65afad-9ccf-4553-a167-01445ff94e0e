<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UnitSeeder extends Seeder
{
    public function run()
    {
//        DB::connection('mysql_main')->table('organograms')->insert([
//            ['id' => 1, 'name' => 'ΥΠΠΟΑ ΟΡΓΑΝΙΣΜΟΣ ΠΡΟ ΠΔ.104/2014', 'started_at' => '2000-01-01', 'ended_at' => '2014-10-28'],
//            ['id' => 2, 'name' => 'ΥΠΠΟΑ ΟΡΓΑΝΙΣΜΟΣ ΠΔ.104/2014', 'started_at' => '2014-10-29', 'ended_at' => '2016-04-01'],
//            ['id' => 3, 'name' => 'YΠΠΟΑ ΟΡΓΑΝΙΣΜΟΣ ΦΕΚ 49/2016', 'started_at' => '2016-04-02', 'ended_at' => '2018-02-01'],
//            ['id' => 4, 'name' => 'ΥΠΠΟΑ ΟΡΓΑΝΙΣΜΟΣ ΠΔ 4/2018', 'started_at' =>  '2018-02-02', 'ended_at' => null],
//        ]);

        DB::connection('mysql_main')->table('unit_types')->insert([
            ['id' => 32, 'name' => 'Υπουργείο', 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 33, 'name' => 'Γραμματεία', 'started_at' => '2000-01-01',  'ended_at' =>null],
            ['id' => 34, 'name' => 'Γενική Διεύθυνση', 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 1, 'name' => 'Κεντρική Υπηρεσία Πολιτισμού', 'started_at' => '2000-01-01',  'ended_at' =>null],
            ['id' => 4, 'name' => 'Ειδικές Περιφερειακές Υπηρεσίες', 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 30, 'name' => 'Περιφερειακές Υπηρεσίες', 'started_at' => '2000-01-01',  'ended_at' =>null],
            ['id' => 40, 'name' => 'Αυτοτελής Υπηρεσία', 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 38, 'name' => 'Τμήμα', 'started_at' => '2000-01-01',  'ended_at' =>null],
            ['id' => 39, 'name' => 'Γραφείο', 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 2, 'name' => 'ΕΠΚΑ', 'started_at' => '2000-01-01',  'ended_at' =>'2014-10-28'],
            ['id' => 3, 'name' => 'ΕΒΑ', 'started_at' => '2000-01-01',  'ended_at' =>'2014-10-28'],
            ['id' => 5, 'name' => 'Εφορείες Νεωτέρων Μνημείων', 'started_at' => '2000-01-01',  'ended_at' =>'2014-10-28'],
            ['id' => 6, 'name' => 'Εποπτευόμενοι Φορείς Πολιτισμού', 'started_at' => '2000-01-01',  'ended_at' =>'2014-10-28'],
            ['id' => 31, 'name' => 'Δημόσια Μουσεία', 'started_at' => '2000-01-01',  'ended_at' =>'2018-02-01'],
        ]);

        DB::connection('mysql_main')->table('units')->insert([

            // NEW ORGANOGRAM

            // ΥΠ
            ['id' => 1, 'name' => 'Υπουργός Πολιτισμού', 'unit_type_id' => 32, 'abbrv' => '', 'order' => 1, 'parent_id' => null, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΓ
            ['id' => 2, 'name' => 'Γενικός Γραμματέας Πολιτισμού', 'unit_type_id' => 33, 'abbrv' => '', 'order' => 1, 'parent_id' => 1, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 3, 'name' => 'Γενικός Γραμματέας Σύγχρονου Πολιτισμού', 'unit_type_id' => 33, 'abbrv' => '', 'order' => 1, 'parent_id' => 1, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            // Added in new organogram
            ['id' => 4, 'name' => 'Ειδικός Γραμματέας', 'unit_type_id' => 33, 'abbrv' => '', 'order' => 1, 'parent_id' => 1, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2008-01-01', 'ended_at' => null],
            ['id' => 5, 'name' => 'Γραφείο Υπουργού', 'unit_type_id' => 39, 'abbrv' => '', 'order' => 1, 'parent_id' => 1, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΓΠ
            ['id' => 6, 'name' => 'ΓΔΠΑΚ', 'unit_type_id' => 34, 'abbrv' => '', 'order' => 1, 'parent_id' => 2, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 7, 'name' => 'ΓΔΑΜΤΕ', 'unit_type_id' => 34, 'abbrv' => '', 'order' => 1, 'parent_id' => 2, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 8, 'name' => 'Γραφείο Γενικού Γραμματέα', 'unit_type_id' => 39, 'abbrv' => '', 'order' => 1, 'parent_id' => 2, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΔΠΑΚ
            ['id' => 9, 'name' => 'ΔΔΕΑΜ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 6, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 10, 'name' => 'ΔΤΚΠΑ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 6, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            // Changed name in new organogram
            ['id' => 11, 'name' => 'Τμήμα Γραμματείας ΚΑΣ', 'unit_type_id' => 38, 'abbrv' => '', 'order' => 1, 'parent_id' => 6, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΔΑΜΤΕ
            ['id' => 12, 'name' => 'ΔΕΤΥΜΕΑ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 7, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            // Changed type in new organogram (type_id = 30 => type_id = 1)
            ['id' => 13, 'name' => 'ΔΑΑΜ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 7, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 14, 'name' => 'Τμήμα Γραμματείας ΚΣΝΜ', 'unit_type_id' => 38, 'abbrv' => '', 'order' => 1, 'parent_id' => 7, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΓΣΠ
            ['id' => 15, 'name' => 'ΓΔΣΠ', 'unit_type_id' => 34, 'abbrv' => '', 'order' => 1, 'parent_id' => 3, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΔΣΠ
            ['id' => 16, 'name' => 'ΔΠΔΕ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 15, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 17, 'name' => 'ΔΚΕ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 15, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΕΓ
            // Changed parent in new organogram (ΓΓΠ -> ΕΓ)
            ['id' => 18, 'name' => 'ΓΔΟΥ', 'unit_type_id' => 34, 'abbrv' => '', 'order' => 1, 'parent_id' => 4, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            // Changed parent in new organogram  (ΓΓΠ -> ΕΓ)
            ['id' => 19, 'name' => 'ΓΔΔΥΗΔ', 'unit_type_id' => 34, 'abbrv' => '', 'order' => 1, 'parent_id' => 4, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΔΟΥ
            ['id' => 20, 'name' => 'ΔΠΥΑ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 18, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 21, 'name' => 'ΔΠΟΔ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 18, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΔΔΥΗΔ
            ['id' => 22, 'name' => 'ΔΑΑΔ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 19, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 23, 'name' => 'ΔΗΔ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 19, 'reference_id' => null, 'organogram_id' => 4, 'started_at' => '2000-01-01', 'ended_at' => null],

            // OLD ORGANOGRAM

            // ROOT
            ['id' => 24, 'name' => 'Υπουργός Πολιτισμού', 'unit_type_id' => 32, 'abbrv' => '', 'order' => 1, 'parent_id' => null, 'reference_id' => null, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],

            // MINISTER
            ['id' => 25, 'name' => 'Γενικός Γραμματέας Πολιτισμού', 'unit_type_id' => 33, 'abbrv' => '', 'order' => 1, 'parent_id' => 24, 'reference_id' => null, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 26, 'name' => 'Γενικός Γραμματέας Σϋγχρονου Πολιτισμού', 'unit_type_id' => 33, 'abbrv' => '', 'order' => 1, 'parent_id' => 24, 'reference_id' => null, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 28, 'name' => 'Γραφείο Υπουργού', 'unit_type_id' => 39, 'abbrv' => '', 'order' => 1, 'parent_id' => 24, 'reference_id' => null, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΓΠ
            ['id' => 29, 'name' => 'ΓΔΠΑΚ', 'unit_type_id' => 34, 'abbrv' => '', 'order' => 1, 'parent_id' => 25, 'reference_id' => null, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 30, 'name' => 'ΓΔΑΜΤΕ', 'unit_type_id' => 34, 'abbrv' => '', 'order' => 1, 'parent_id' => 25, 'reference_id' => null, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 31, 'name' => 'Γραφείο Γενικού Γραμματέα', 'unit_type_id' => 39, 'abbrv' => '', 'order' => 1, 'parent_id' => 25, 'reference_id' => null, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 41, 'name' => 'ΓΔΟΥ', 'unit_type_id' => 34, 'abbrv' => '', 'order' => 1, 'parent_id' => 25,  'reference_id' => 18,'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 42, 'name' => 'ΓΔΔΥΗΔ', 'unit_type_id' => 34, 'abbrv' => '', 'order' => 1, 'parent_id' => 25,'reference_id' => 19, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΔΠΑΚ
            ['id' => 32, 'name' => 'ΔΔΕΑΜ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 29, 'reference_id' => 9, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 33, 'name' => 'ΔΤΚΠΑ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 29, 'reference_id' => 10, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 34, 'name' => 'Αυτοτελές Τμήμα Γραμματείας ΚΑΣ', 'unit_type_id' => 38, 'abbrv' => '', 'order' => 1, 'parent_id' => 29, 'reference_id' => 11, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΔΑΜΤΕ
            ['id' => 35, 'name' => 'ΔΕΤΥΜΕΑ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 30, 'reference_id' => 12, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 36, 'name' => 'ΔΑΑΜ', 'unit_type_id' => 30, 'abbrv' => '', 'order' => 1, 'parent_id' => 30, 'reference_id' => 13, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 37, 'name' => 'Τμήμα Γραμματείας ΚΣΝΜ', 'unit_type_id' => 38, 'abbrv' => '', 'order' => 1, 'parent_id' => 30, 'reference_id' => 14, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΔΟΥ
            ['id' => 43, 'name' => 'ΔΠΥΑ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 41,'reference_id' => 20, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 44, 'name' => 'ΔΠΟΔ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 41,'reference_id' => 21, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΔΔΥΗΔ
            ['id' => 45, 'name' => 'ΔΑΑΔ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 42, 'reference_id' => 22, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 46, 'name' => 'ΔΗΔ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 42, 'reference_id' => 23, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],

            // ΓΓΣΠ
            ['id' => 38, 'name' => 'ΓΔΣΠ', 'unit_type_id' => 34, 'abbrv' => '', 'order' => 1, 'parent_id' => 26, 'reference_id' => null, 'organogram_id' => 3, 'started_at' => '2007-01-01', 'ended_at' => null],

            // ΓΔΣΠ
            ['id' => 39, 'name' => 'ΔΠΔΕ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 38,'reference_id' => 16, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],
            ['id' => 40, 'name' => 'ΔΚΕ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 38, 'reference_id' => 17,'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => null],

            // CHANGED DATA WITHIN OLD ORGANOGRAM

            // ΓΔΣΠ Changed parent within organogram (from ΓΓΠ to ΓΓΣΠ)
            ['id' => 47, 'name' => 'ΓΔΣΠ', 'unit_type_id' => 34, 'abbrv' => '', 'order' => 1, 'parent_id' => 25, 'reference_id' => 38, 'organogram_id' => 3, 'started_at' => '2007-01-01', 'ended_at' => '2006-01-01'],
            ['id' => 48, 'name' => 'ΔΠΔΕ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 47,'reference_id' => 39, 'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => '2006-01-01'],
            ['id' => 49, 'name' => 'ΔΚΕ', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 47, 'reference_id' => 40,'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => '2006-01-01'],

//            // Deleted w/ same organogram
            ['id' => 50, 'name' => 'Deleted GDDYHD', 'unit_type_id' => 1, 'abbrv' => '', 'order' => 1, 'parent_id' => 42, 'reference_id' => null,  'organogram_id' => 3, 'started_at' => '2000-01-01', 'ended_at' => '2006-01-01'],
        ]);
    }
}
