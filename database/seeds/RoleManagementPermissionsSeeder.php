<?php

use App\Models\Role;
use Database\Seeds\Traits\PermissionSeeder;
use Illuminate\Database\Seeder;

class RoleManagementPermissionsSeeder extends Seeder
{
    use PermissionSeeder;

    public function run()
    {
        $appData = [
            'name' => 'Διαχείριση Ρόλων',
            'abbrv' => 'roleManagement',
            'icon' => 'fa-users',
            'color' => '#a0aec0',
        ];

        $permissionNames = [
            ['name' => 'roleManagement.admin', 'description' => 'Δικαίωμα διαχείρισης'],
        ];

        $roleNames = [
            ['name' => 'roleManagement.admin', 'description' => 'Διαχειριστής'],
        ];

        $assignments = [
            'roleManagement.admin' => [
                'roleManagement.admin',
            ],
        ];

        $this->seedPermissions($appData, $roleNames, $permissionNames, $assignments);

        // Attach roleManagement.admin role to all Directorates
        $allDirectorateIds = DB::table('users as u')
            ->join('directorates as d', 'u.current_directorate_id', '=', 'd.id')
            ->whereColumn('u.email', '=', 'd.email')
            ->whereNull('u.deleted_at')
            ->pluck('u.id')->toArray();

        foreach ($allDirectorateIds as $directorateId) {
            DB::table('role_user')->insert([
                'user_id' => $directorateId,
                'role_id' => Role::getByName('roleManagement.admin')->id,
            ]);
        }
    }
}
