<?php

use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RoleManagementTablesSeeder extends Seeder
{
    public function run()
    {
        // Όλες οι Υπηρεσίες
        $allManagerIds = DB::table('users as u')
            ->join('directorates as d', 'u.current_directorate_id', '=', 'd.id')
            ->whereColumn('u.email', '=', 'd.email')
            ->where('d.active', 1)
            ->pluck('u.id')->toArray();

        // Υπηρεσιές για την εφαρμογή opendata
        $opendataEditorId = Role::getByName('opendata.editor')->id;
        $opendataDirectorateIds = DB::table('role_user as ru')
            ->select('d.id', 'd.name')
            ->join('users as u', 'ru.user_id', '=', 'u.id')
            ->join('directorate_user as du', 'du.user_id', '=', 'u.id')
            ->join('directorates as d', 'd.id', '=', 'du.directorate_id')
            ->whereIn('role_id', [$opendataEditorId])
            ->where('d.active', '=', 1)
            ->distinct()
            ->pluck('d.id')
            ->toArray();

        $opendataManagerIds = DB::table('users as u')
            ->join('directorates as d', 'u.current_directorate_id', '=', 'd.id')
            ->whereColumn('u.email', '=', 'd.email')
            ->whereIn('u.current_directorate_id', $opendataDirectorateIds)
            ->pluck('u.id')
            ->toArray();

        // Διεύθυνση Διαχείρισης Ανθρώπινου Δυναμικού
        $personnelManagerIds = [507];

        // Διεύθυνση Συντήρησης Αρχαίων και Νεωτέρων Μνημείων
        $conservationsManagersIds = [24];

        // Υπηρεσιές για την εφαρμογή registry
        $registrySubscriberId = Role::getByName('registry.subscriber')->id;
        $registryEditorId = Role::getByName('registry.editor')->id;
        $registrySubscriberAllId = Role::getByName('registry.subscriberAll')->id;
        $registryDirectorateIds = DB::table('role_user as ru')
            ->select('d.id', 'd.name')
            ->join('users as u', 'ru.user_id', '=', 'u.id')
            ->join('directorate_user as du', 'du.user_id', '=', 'u.id')
            ->join('directorates as d', 'd.id', '=', 'du.directorate_id')
            ->whereIn('role_id', [$registrySubscriberId, $registryEditorId, $registrySubscriberAllId])
            ->where('d.active', '=', 1)
            ->distinct()
            ->pluck('d.id')
            ->toArray();

        $registryManagerIds = DB::table('users as u')
            ->join('directorates as d', 'u.current_directorate_id', '=', 'd.id')
            ->whereColumn('u.email', '=', 'd.email')
            ->whereIn('u.current_directorate_id', $registryDirectorateIds)
            ->pluck('u.id')
            ->toArray();

        // Υπηρεσιές για την εγαρμογή educational
        $educationalManagerIds = DB::table('users as u')
            ->join('directorates as d', 'u.current_directorate_id', '=', 'd.id')
            ->whereColumn('u.email', '=', 'd.email')
            ->whereNull('u.deleted_at')
            ->where('d.gdirectorate_id', 1)
            ->where('d.id', '!=', 4)
            ->pluck('u.id')->toArray();

        DB::statement('SET foreign_key_checks = 0');

        DB::table('role_manager')->truncate();

        $managedRolesManagersPivotData = [
            'phonebook.editor' => $allManagerIds,
            'opendata.editor' => $opendataManagerIds,
            'personnel.subscriber' => $allManagerIds,
            'personnel.editor' => $personnelManagerIds,
            'conservations.subscriber' => $conservationsManagersIds,
            'conservations.editor' => $conservationsManagersIds,
            'registry.subscriber' => $registryManagerIds,
            'registry.editor' => $registryManagerIds,
            'educational.subscriber' => $educationalManagerIds,
            'educational.editor' => $educationalManagerIds,
        ];

        collect($managedRolesManagersPivotData)->each(function ($managerIds, $managedRoleName) {
            collect($managerIds)->each(function ($managerId) use ($managedRoleName) {
                DB::table('role_manager')->insert([
                    'user_id' => $managerId,
                    'role_id' => Role::getByName($managedRoleName)->id,
                ]);
            });
        });

        DB::statement('SET foreign_key_checks = 1');
    }
}
