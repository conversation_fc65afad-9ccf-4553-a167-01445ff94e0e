<?php

namespace Database\Seeds\Traits;

use App\Models\App;
use App\Models\Permission;
use App\Models\Role;
use App\Models\RoleManagement\ManagedRole;
use Illuminate\Support\Facades\DB;

trait PermissionSeeder
{
    /**
     * Creates new roles and permissions associated with a given app.
     *
     * @param  array  $appData
     * @param  array  $roleNames
     * @param  array  $permissionNames
     * @param  array  $assignments
     */
    public function seedPermissions(array $appData, array $roleNames, array $permissionNames, array $assignments): void
    {
        // Create new application
        $app = App::firstOrCreate($appData);

        // Create permissions
        collect($permissionNames)->map(function ($permission) {
            return Permission::create([
                'name' => $permission['name'],
                'description' => $permission['description'],
            ]);
        });

        // Create Roles
        collect($roleNames)->map(function ($role) use ($app) {
            return Role::create([
                'name' => $role['name'],
                'description' => $role['description'],
                'app_id' => $app->id,
            ]);
        });

        // Attach permissions to roles
        collect($assignments)->each(function ($permissionNames, $roleName) {
            $role = Role::getByName($roleName);
            collect($permissionNames)->map(function ($permissionName) use ($role) {
                return Permission::getByName($permissionName);
            })->each(function ($permission) use ($role) {
                $role->permissions()->attach($permission);
            });
        });
    }

    /**
     * Assign managed roles to a given manager.
     *
     * @param $roleManager
     */
    public function seedManagedRoles($roleManager)
    {
        collect($roleManager)->each(function ($managerIds, $roleName) {
            $role = ManagedRole::getByName($roleName);
            collect($managerIds)->each(function ($managerId) use ($role) {
                DB::table('role_manager')->insert(['role_id' => $role->id, 'user_id' => $managerId]);
            });
        });
    }
}
