<?php

use App\Models\Opendata\Type;
use Illuminate\Database\Seeder;
use App\Models\Opendata\Restriction;
use App\Models\Opendata\UnobtainableReason;

class OpendataSeederTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::connection(env('DB_CONNECTION_OPENDATA'))->statement("SET foreign_key_checks = 0");

        DB::connection(env('DB_CONNECTION_OPENDATA'))->table('types')->truncate();
        DB::connection(env('DB_CONNECTION_OPENDATA'))->table('unobtainable_reasons')->truncate();
        DB::connection(env('DB_CONNECTION_OPENDATA'))->table('restrictions')->truncate();

        DB::connection(env('DB_CONNECTION_OPENDATA'))->statement("SET foreign_key_checks = 1");

        Type::create(array( 'name' => 'Ψηφιακό' ));
        Type::create(array( 'name' => 'Φυσικό' ));

        UnobtainableReason::create(array( 'name' => 'Τεχνικοί Λόγοι' ));
        UnobtainableReason::create(array( 'name' => 'Θεσμικοί Λόγοι' ));
        UnobtainableReason::create(array( 'name' => 'Οικονομικοί Λόγοι' ));
        UnobtainableReason::create(array( 'name' => 'Πνευματικά Δικαιώματα' ));
        UnobtainableReason::create(array( 'name' => 'Άλλο', 'open' => true ));
        // UnobtainableReason::create(array( 'name' => 'Άλλο' ));

        Restriction::create(array( 'name' => 'Θέματα Εθνικής Ασφάλειας' ));
        Restriction::create(array( 'name' => 'Φορολογικό Απόρρητο' ));
        Restriction::create(array( 'name' => 'Προστασία Πολιτιστικής Κληρονομιάς' ));
        Restriction::create(array( 'name' => 'Άλλο', 'open' => true ));


    }
}
