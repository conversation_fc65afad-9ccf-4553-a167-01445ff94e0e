<?php

use Database\Seeds\Traits\PermissionSeeder;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EducationalPermissionsSeeder extends Seeder
{
    use PermissionSeeder;

    public function run()
    {
        $appData = [
            'name' => 'e-Πλουτώ',
            'abbrv' => 'educational',
            'icon' => 'fa-graduation-cap',
            'color' => '#7a1565',
        ];

        $permissionNames = [
            ['name' => 'educational.create', 'description' => 'Δικαίωμα καταχώρησης'],
            ['name' => 'educational.read', 'description' => 'Δικαίωμα προβολής'],
            ['name' => 'educational.update', 'description' => 'Δικαίωμα επεξεργασίας'],
            ['name' => 'educational.delete', 'description' => 'Δικαίωμα διαγραφής'],
            ['name' => 'educational.admin', 'description' => 'Δικαίωμα διαχείρισης'],
        ];

        $roleNames = [
            ['name' => 'educational.subscriber', 'description' => 'Συνδρομητής'],
            ['name' => 'educational.editor', 'description' => 'Συντάκτης'],
            ['name' => 'educational.admin', 'description' => 'Διαχειριστής'],
        ];

        $assignments = [
            'educational.subscriber' => [
                'educational.read',
            ],
            'educational.editor' => [
                'educational.create',
                'educational.read',
                'educational.update',
                'educational.delete',
            ],
            'educational.admin' => [
                'educational.admin',
                'educational.create',
                'educational.read',
                'educational.update',
                'educational.delete',
            ],
        ];

        $educationalManagerIds = DB::table('users as u')
            ->join('directorates as d', 'u.current_directorate_id', '=', 'd.id')
            ->whereColumn('u.email', '=', 'd.email')
            ->whereNull('u.deleted_at')
            ->where('d.gdirectorate_id', 1)
            ->where('d.id', '!=', 4)
            ->pluck('u.id')->toArray();

        $roleManager = [
            'educational.subscriber' => $educationalManagerIds,
            'educational.editor' => $educationalManagerIds,
        ];

        $this->seedPermissions($appData, $roleNames, $permissionNames, $assignments);

        $this->seedManagedRoles($roleManager);
    }
}
