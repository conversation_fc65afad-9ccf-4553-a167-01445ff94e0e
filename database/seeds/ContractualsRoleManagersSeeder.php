<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ContractualsRoleManagersSeeder extends Seeder
{
    public const GDAPK = 2;

    public const PERIFERIAKES = 52;

    public const EIDIKES_PERIFERIAKES = 53;

    public function run()
    {
        $roleManagerIds = DB::table('users')
            ->leftJoin('units', 'users.unit_id', '=', 'units.id')
            ->where('units.parent_id', '=', self::GDAPK)
            ->whereIn('units.unit_type_id', [self::PERIFERIAKES, self::EIDIKES_PERIFERIAKES])
            ->whereColumn('users.email', '=', 'units.email')
            ->select('users.id')
            ->pluck('id');

        $subscriberId = Role::getByName('contractuals.subscriber')->id;

        $editorId = Role::getByName('contractuals.editor')->id;

        $data = [];
        foreach ($roleManagerIds as $roleManagerId) {
            $data[] = ['user_id' => $roleManagerId, 'role_id' => $subscriberId];
            $data[] = ['user_id' => $roleManagerId, 'role_id' => $editorId];
        }

        DB::table('role_manager')->insert($data);
    }
}
