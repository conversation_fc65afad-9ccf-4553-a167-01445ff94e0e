<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;

class ContractualsAdminUnitRoleSeeder extends Seeder
{
    public function run()
    {
        $contractualsAppId = 6;
        $contractualsCreatePermissionId = 68;
        $contractualsReadPermissionId = 69;
        $contractualsUpdatePermissionId = 70;
        $contractualsDeletePermissionId = 71;

        $contractualsAdminUnitPermission = Permission::create([
            'name' => 'contractuals.adminUnit',
            'description' => 'Δικαίωμα διαχείρισης στην εφαρμογή contractuals - πρόσβαση μόνο στα δεδομένα της υπηρεσίας του',
        ]);

        $role = Role::create([
            'name' => 'contractuals.adminUnit',
            'description' => 'Διαχειριστής στην εφαρμογή contractuals - πρόσβαση μόνο στα δεδομένα της υπηρεσίας του',
            'app_id' => $contractualsAppId,
        ]);

        $role->permissions()->attach([
            $contractualsCreatePermissionId,
            $contractualsReadPermissionId,
            $contractualsUpdatePermissionId,
            $contractualsDeletePermissionId,
            $contractualsAdminUnitPermission->id,
        ]);
    }
}
