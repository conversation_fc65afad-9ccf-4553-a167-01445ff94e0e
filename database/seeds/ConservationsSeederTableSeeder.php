<?php

use Illuminate\Database\Seeder;
use App\Models\Conservations\Material;
use App\Models\Conservations\StatusType;

class ConservationsSeederTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::connection(env('DB_CONNECTION_CONSERVATIONS'))->statement("SET foreign_key_checks = 0");

        DB::connection(env('DB_CONNECTION_CONSERVATIONS'))->table('materials')->truncate();
        DB::connection(env('DB_CONNECTION_CONSERVATIONS'))->table('status_types')->truncate();

        DB::connection(env('DB_CONNECTION_CONSERVATIONS'))->statement("SET foreign_key_checks = 1");

        // Seed Specializations
        $statusTypes = array(
            'Πειθαρχική εκκρεμότητα',
            'Σχέση με το Δημόσιο',
            'Αποθανών',
            'Προσωπική επιλογή'
        );

        $materials = array(
            'ΤΟΙΧΟΓΡΑΦΙΑ',
            'ΕΙΚΟΝΑ',
            'ΛΙΘΟΣ',
            'ΧΑΡΤΩΟ ΥΛΙΚΟ',
            'ΦΩΤΟΓΡΑΦΙΑ',
            'ΨΗΦΙΔΩΤΟ',
            'ΞΥΛΟ',
            'ΥΦΑΣΜΑ',
            'ΜΕΤΑΛΛΟ',
            'ΚΕΡΑΜΙΚΟ',
            'ΑΝΑΣΚΑΦΙΚΑ_ΟΡΓΑ',
            'ΓΥΑΛΙ',
            'ΕΡΓΑ ΖΩΓΡΑΦΙΚΗΣ',
            'ΚΟΝΙΑΜΑΤΑ',
            'ΟΣΤΕΙΝΑ'
        );

        foreach($statusTypes as $statusType) {
            StatusType::create(array(
                'name' => $statusType
            ));
        }

        foreach($materials as $material) {
            Material::create(array(
                'name' => $material
            ));
        }

    }
}