<?php

use App\Models\Role;
use Illuminate\Database\Seeder;

class AppsTableSeeder extends Seeder
{
    public function run()
    {
        $apps = [
            [
                'id' => 1,
                'name' => 'Τηλεφωνικός Κατάλογος',
                'abbrv' => 'phonebook',
                'icon' => 'fa-phone',
                'color' => '#715973'
            ],
            [
                'id' => 2,
                'name' => 'Ανοιχτά Δεδομένα',
                'abbrv' => 'opendata',
                'icon' => 'fa-cubes',
                'color' => '#0C90AD'
            ],
            [
                'id' => 3,
                'name' => 'Οργανικές Θέσεις',
                'abbrv' => 'personnel',
                'icon' => 'fa-users',
                'color' => '#1B78BE'
            ],
            [
                'id' => 5,
                'name' => 'Μητρώο Συντηρητών',
                'abbrv' => 'conservations',
                'icon' => 'fa-paint-brush',
                'color' => '#488B98'
            ],
            [
                'id' => 6,
                'name' => 'Διαγωνισμοί ΙΔΟΧ',
                'abbrv' => 'contractuals',
                'icon' => 'fa-bar-chart',
                'color' => '#5EBED2'
            ],
            [
                'id' => 7,
                'name' => 'Μητρώο Υπηρεσιών',
                'abbrv' => 'registry',
                'icon' => 'fa-building',
                'color' => '#C9B60E'
            ],
            [
                'id' => 8,
                'name' => 'Εκπαιδευτικές Δράσεις',
                'abbrv' => 'educational',
                'icon' => 'fa-graduation-cap',
                'color' => '#6C6080'
            ],
        ];

        DB::statement("SET foreign_key_checks = 0");

        // Seed `apps` table
        DB::table('apps')->truncate();
        DB::table('apps')->insert($apps);

        // Define in which app a role belongs to.
        collect($apps)->each(function ($app) {
            Role::where('name', 'like', $app['abbrv'].'%')
                ->update(['app_id' => $app['id']]);
        });

        DB::statement("SET foreign_key_checks = 1");
    }
}
