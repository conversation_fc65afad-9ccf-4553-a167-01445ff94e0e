<?php

use Illuminate\Database\Seeder;
use App\Models\Registry\UtilityType;
use App\Models\Registry\VehicleType;
use App\Models\Registry\AssociateType;
use App\Models\Registry\BuildingUsage;
use App\Models\Registry\OwnershipType;
use App\Models\Registry\PhoneProvider;
use App\Models\Registry\RentalProcess;
use App\Models\Registry\UtilityProvider;

class RegistryTableSeeder extends Seeder
{
    public function run()
    {
        DB::connection(env('DB_CONNECTION_REGISTRY'))->statement("SET foreign_key_checks = 0");

        DB::connection(env('DB_CONNECTION_REGISTRY'))->table('building_usages')->truncate();
        DB::connection(env('DB_CONNECTION_REGISTRY'))->table('ownership_types')->truncate();
        // DB::connection(env('DB_CONNECTION_REGISTRY'))->table('phone_types')->truncate();
        DB::connection(env('DB_CONNECTION_REGISTRY'))->table('utility_types')->truncate();
        DB::connection(env('DB_CONNECTION_REGISTRY'))->table('phone_providers')->truncate();
        DB::connection(env('DB_CONNECTION_REGISTRY'))->table('utility_providers')->truncate();
        DB::connection(env('DB_CONNECTION_REGISTRY'))->table('vehicle_types')->truncate();
        DB::connection(env('DB_CONNECTION_REGISTRY'))->table('associate_types')->truncate();

        DB::connection(env('DB_CONNECTION_REGISTRY'))->statement("SET foreign_key_checks = 1");

        $buildingUsages = array(
            'Διοικητικές υπηρεσίες',
            'Αποθήκες',
            'Πωλητήρια',
            // 'Άλλο'
        );

        $ownershipTypes = array(
            'Ιδιοκτησία ΥΠΠΟΑ',
            'Ενοικιαζόμενο'
        );

        $rentalProcesses = array(
            'Κτηματική Υπηρεσία',
            'Ειδικός Λογαριασμός',
            'ΤΑΠ',
            'Δωρεάν Παραχώρηση',
            'Άλλο'
        );

        // $phoneTypes = array(
        //     'Σταθερή τηλεφωνία',
        //     'Κινητή τηλεφωνία'
        // );

        $utilityTypes = array(
            'Ύδρευση',
            'Ηλεκτρικό ρεύμα',
            'Φυσικό αέριο'
        );

        $phoneProviders = array(
            'ΟΤΕ',
        );

        $utilityProviders = array(
            ['ΕΥΔΑΠ', '1'],
            ['ΔΕΗ', '2'],
        );

        $vehicleTypes = array(
            'Επιβατικό',
            'Φορτηγό',
            'Ρυμουλκό',
            'Δίκυκλο / Τρίκυκλο',
            // 'Άλλο'
        );

        $associateTypes = array(
            'Ιδιοκτήτης',
            'Διαχειριστής',
            'Κάτοχος Κλειδιών'
        );

        foreach ($buildingUsages as $buildingUsage) {
            BuildingUsage::create(array(
                'name' => $buildingUsage
            ));
        }
        BuildingUsage::create(array(
            'name' => 'Άλλο',
            'is_other' => true
        ));

        foreach ($ownershipTypes as $ownershipType) {
            OwnershipType::create(array(
                'name' => $ownershipType
            ));
        }

        foreach ($rentalProcesses as $rentalProcess) {
            RentalProcess::create(array(
                'name' => $rentalProcess
            ));
        }

        foreach ($utilityTypes as $utilityType) {
            UtilityType::create(array(
                'name' => $utilityType
            ));
        }

        foreach ($phoneProviders as $phoneProvider) {
            PhoneProvider::create(array(
                'name' => $phoneProvider
            ));
        }

        foreach ($utilityProviders as $utilityProvider) {
            UtilityProvider::create(array(
                'name' => $utilityProvider[0],
                'utility_type_id' => $utilityProvider[1]
            ));
        }

        foreach ($vehicleTypes as $vehicleType) {
            VehicleType::create(array(
                'name' => $vehicleType
            ));
        }
        VehicleType::create(array(
            'name' => 'Άλλο',
            'is_other' => true
        ));


        foreach ($associateTypes as $associateType) {
            AssociateType::create(array(
                'name' => $associateType
            ));
        }
    }
}
