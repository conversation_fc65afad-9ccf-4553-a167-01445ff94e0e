<?php

namespace Database\Seeders;

use Database\Seeds\Traits\PermissionSeeder;
use Illuminate\Database\Seeder;

class ContractualsPermissionsSeeder extends Seeder
{
    use PermissionSeeder;

    public function run()
    {
        $appData = [
            'id' => 6,
            'name' => 'Διαγωνισμοί ΙΔΟΧ',
            'abbrv' => 'contractuals',
            'icon' => 'fa-bar-chart',
            'color' => '#5EBED2',
        ];

        $permissionNames = [
            ['name' => 'contractuals.create', 'description' => 'Δικαίωμα καταχώρησης'],
            ['name' => 'contractuals.read', 'description' => 'Δικαίωμα προβολής'],
            ['name' => 'contractuals.update', 'description' => 'Δικαίωμα επεξεργασίας'],
            ['name' => 'contractuals.delete', 'description' => 'Δικαίωμα διαγραφής'],
            // ['name' => 'contractuals.admin', 'description' => 'Δικαίωμα διαχείρισης'],
        ];

        $roleNames = [
            ['name' => 'contractuals.subscriber', 'description' => 'Συνδρομητής'],
            ['name' => 'contractuals.editor', 'description' => 'Συντάκτης'],
            // ['name' => 'contractuals.admin', 'description' => 'Διαχειριστής']
        ];

        $assignments = [
            'contractuals.subscriber' => [
                'contractuals.read',
            ],
            'contractuals.editor' => [
                'contractuals.create',
                'contractuals.read',
                'contractuals.update',
                'contractuals.delete',
            ],
            'contractuals.admin' => [
                // 'contractuals.admin',
                'contractuals.create',
                'contractuals.read',
                'contractuals.update',
                'contractuals.delete',
            ],
        ];

//        $educationalManagerIds = DB::table('users as u')
//            ->join('directorates as d', 'u.current_directorate_id', '=', 'd.id')
//            ->whereColumn('u.email', '=', 'd.email')
//            ->whereNull('u.deleted_at')
//            ->where('d.gdirectorate_id', 1)
//            ->where('d.id', '!=', 4)
//            ->pluck('u.id')->toArray();
//
//        $roleManager = [
//            'contractuals.subscriber' => $educationalManagerIds,
//            'contractuals.editor' => $educationalManagerIds,
//        ];

        $this->seedPermissions($appData, $roleNames, $permissionNames, $assignments);

//        $this->seedManagedRoles($roleManager);
    }
}
