<?php

namespace Database\CustomFactories\Contractuals;

use App\Models\Contractuals\Applicant;

class ApplicantFactory
{
    /**
     * Create an applicant profile.
     *
     * @param array $overrides An associative array, where the keys consist of
     *                         the Applicant model's property names and relationship name
     * @return mixed
     */
    public static function create($overrides = [])
    {
        /**
         * Examples of $overrides for creating an applicant profile with attached assets.
         *
         [
                'degrees' => [
                    [
                        'id'       => '',
                        'name'     => 'Degree A',
                        'issuer'   => 'University A',
                        'mark'     => '8.2',
                        'year'     => '2005',
                        'verified' => false,
                    ],
                    [
                        'id'       => '',
                        'name'     => 'Degree B',
                        'issuer'   => 'University B',
                        'mark'     => '7.5',
                        'year'     => '2006',
                        'verified' => false,
                    ],
                ],
                'postgraduates' => [
                    [
                        'id'       => '',
                        'name'     => 'Postgraduate A',
                        'issuer'   => 'University A',
                        'year'     => '2007',
                        'verified' => false,
                    ],
                ],
                'doctorates' => [
                    [
                        'id'       => '',
                        'name'     => 'Doctorate Α',
                        'issuer'   => 'University A',
                        'year'     => '2008',
                        'verified' => false,
                    ],
                ],
                'greekLanguages'        => [
                    [
                        'id'                => '',
                        'name'              => 'Greek Knowledge',
                        'issuer'            => 'Ministry of Education',
                        'level'             => 'A',
                        'verified'          => false,
                    ],
                ],
                'languageSkills' => [
                    [
                        'id'                => '',
                        'name'              => 'Proficiency',
                        'issuer'            => 'British Council',
                        'language_id'       => $this->language->id,
                        'language_level_id' => $this->languageLevel->id,
                        'verified'          => false,
                    ],
                ],
                'computerSkills' => [
                    [
                        'id'              => '',
                        'name'            => 'ECDL',
                        'verified'        => false,
                        'has_attachments' => false,
                        'media'           => [],
                    ],
                ],
                'experiences'            => [
                    [
                        'id'              => '',
                        'name'            => 'Job A',
                        'issuer'          => 'Company A',
                        'months'          => '',
                        'started_at'      => '01-01-2017',
                        'ended_at'        => '30-06-2017',
                        'verified'        => false,
                        'has_attachments' => false,
                        'media'           => [],
                    ],
                    [
                        'id'              => '',
                        'name'            => 'Job B',
                        'issuer'          => 'Company B',
                        'months'          => '',
                        'started_at'      => '01-07-2017',
                        'ended_at'        => '31-12-2017',
                        'verified'        => false,
                        'has_attachments' => false,
                        'media'           => [],
                    ],
                ],
                'unemployments' => [
                    [
                        'id'       => '',
                        'months'   => 2,
                        'verified' => false,
                    ],
                ],
                'multiChildFamilies' => [
                    [
                        'id'       => '',
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'threeChildFamilies' => [
                    [
                        'id'       => '',
                        'children' => true,
                        'siblings' => true,
                        'verified' => false,
                    ],
                ],
                'singleParentFamilies' => [
                    [
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'minors' => [
                    [
                        'id'       => '',
                        'amount'   => 2,
                        'verified' => false,
                    ],
                ],
                'disabilities' => [
                    [
                        'id'           => '',
                        'percentage'   => 60,
                        'verified'     => false,
                    ],
                ],
                'familyDisabilities' => [
                    [
                        'id'           => '',
                        'percentage'   => 65,
                        'verified'     => false,
                    ],
                ],
            ]
         */
        $assetsList = [
            'degrees',
            'postgraduates',
            'doctorates',
            'greekLanguages',
            'languageSkills',
            'computerSkills',
            'experiences',
            'unemployments',
            'multiChildFamilies',
            'threeChildFamilies',
            'singleParentFamilies',
            'minors',
            'disabilities',
            'familyDisabilities',
        ];

        $applicantParams = collect($overrides)->except($assetsList);

        $assetsParams = collect($overrides)->only($assetsList); // ['degrees' => [0 => [], 1 => []]]

        $applicant = factory(Applicant::class)->create($applicantParams->toArray());

        $assetsParams->each(function ($assets, $type) use ($applicant) {
            $applicant->addAssets($type, $assets);
        });

        return $applicant;
    }
}
