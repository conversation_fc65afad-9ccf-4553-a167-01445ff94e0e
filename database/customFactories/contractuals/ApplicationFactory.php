<?php

namespace Database\CustomFactories\Contractuals;

use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Application;

class ApplicationFactory
{
    /**
     * Create a new application for given contest.
     *
     * @param Contest $contest The given contest
     * @param array $overrides Some overrides
     * @return mixed
     */
    public static function createForContest($contest, $overrides = [])
    {
        if (!isset($overrides['applicant_id'])) {
            $applicant = ApplicantFactory::create(self::applicantParams($overrides));
        }

        $application = factory(Application::class)->create(
            array_merge(
                [
                    'applicant_id'       => $applicant->id ?? '',
                    'contest_id'         => $contest->id,
                    'applicant_category' => $contest->positions->first()->specialization->specialization_type_id,
                ],
                self::applicationParams($overrides)
            )
        );

        // TODO: always create qualifications rows for unemployments, multi_child_families, three_child_families, single_parent_families, disabilities, family_disabilities,
        // Add application qualifications
        collect(self::qualificationParams($overrides))->each(function ($qualificationArr, $type) use ($application) {
            $application->setQualifications($type, $application->applicant->$type);
        });

        // Set selected positions
        $application->setPositions(self::positionParams($overrides));

        return $application;
    }

    private static function applicantParams($overrides)
    {
        return collect($overrides)->only([
            'name',
            'surname',
            'fathername',
            'mothername',
            'birthdate',
            'eu_citizen',
            'greek_nationality',
            'policeid_number',
            'policeid_date',
            'afm',
            'doy',
            'amka',
            'street',
            'street_number',
            'postcode',
            'city',
            'phonenumber1',
            'phonenumber2',
            'email',
            'degrees',
            'postgraduates',
            'doctorates',
            'greekLanguages',
            'languageSkills',
            'computerSkills',
            'experiences',
            'unemployments',
            'multiChildFamilies',
            'threeChildFamilies',
            'singleParentFamilies',
            'minors',
            'disabilities',
            'familyDisabilities',
        ])->toArray();
    }

    private static function applicationParams($overrides)
    {
        return collect($overrides)->only([
            'applicant_id',
            'name',
            'surname',
            'fathername',
            'mothername',
            'birthdate',
            'eu_citizen',
            'greek_nationality',
            'policeid_number',
            'policeid_date',
            'afm',
            'doy',
            'amka',
            'street',
            'street_number',
            'postcode',
            'city',
            'phonenumber1',
            'phonenumber2',
            'email',
            'protocol_number',
            'protocol_date',
            'healthy',
            'unrestricted',
            'invalidated',
            'impediment_eight_months',
            'invalidation_description',
            'submitted_at',
            'rejected',
            'locked_at',
            'rated_at',
        ])->toArray();
    }

    private static function qualificationParams($overrides)
    {
        return collect($overrides)->only([
            'degrees',
            'postgraduates',
            'doctorates',
            'greekLanguages',
            'languageSkills',
            'computerSkills',
            'experiences',
            'unemployments',
            'multiChildFamilies',
            'threeChildFamilies',
            'singleParentFamilies',
            'minors',
            'disabilities',
            'familyDisabilities',
        ])->toArray();
    }

    private static function positionParams($overrides)
    {
        return collect($overrides)->only('positions')['positions'];
    }
}
