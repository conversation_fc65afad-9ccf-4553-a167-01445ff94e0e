<?php

namespace Database\CustomFactories\Contractuals;

use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Requirement;
use App\Models\Contractuals\RequirementType;
use App\Models\Contractuals\Specialization;
use App\Models\Contractuals\SpecializationType;
use App\Models\Unit;

class ContestFactory
{
    /**
     * Create a dummy contest.
     *
     * The required specialization for participating in the contest is `PE ARXAIOLOGON`.
     * The contest will have one (1) `position` with two (2) `requirements` (degree & experience)
     *
     * @param  array  $overrides
     *
     * @return mixed
     */
    public static function create($overrides = [])
    {
        \DB::connection('mysql_contractuals')->statement("SET FOREIGN_KEY_CHECKS=0;");

        $contestParams = collect($overrides)->except('positions');
        $positionsParams = collect($overrides)->only('positions')['positions'];

        $contest = factory(Contest::class)->create($contestParams->toArray());

        collect($positionsParams)->each(function ($positionParam) use ($contest) {
            // extract specialization
            $specialization_type_name = mb_substr($positionParam['specialization'], 0, 2);
            $specialization_type_id = ['PE' => 1, 'TE' => 2, 'DE' => 3, 'YE' => 4][$specialization_type_name];
            $specialization_name = $positionParam['specialization'];

            // Check for duplicate entries
            $specialization_type = SpecializationType::where('name', $specialization_type_name)->first()
                ?? factory(SpecializationType::class)->create([
                    'id' => $specialization_type_id,
                    'name' => $specialization_type_name
                ]);

            $specialization = Specialization::where('name', $specialization_name)->first()
                ?? factory(Specialization::class)->create([
                    'name' => $specialization_name,
                    'shortname' => mb_substr(str_replace(' ', '', $specialization_name), 0, 6),
                    'specialization_type_id' => $specialization_type->id,
                ]);

            // create position
            $position = $contest->positions()->create([
                'amount' => $positionParam['amount'],
                'location' => $positionParam['location'],
                'code' => $positionParam['code'] ?? rand(pow(10, 3 - 1), pow(10, 3) - 1),
                'specialization_id' => $specialization->id,
                'contest_id' => $contest->id,
                'unit_id' => factory(Unit::class)->create()->id,
            ]);

            // attach requirements to positions
            collect($positionParam['requirements'])
                ->each(function ($requirementSet, $auxiliary_level) use ($position) {
                    collect($requirementSet)->each(function ($requirementParam) use ($position, $auxiliary_level) {
                        $requirementType = factory(RequirementType::class)->create([
                            'name' => $requirementParam['requirement_type'],
                            'qualifiable_type' => $requirementParam['requirement_type'],
                        ]);

                        $requirement = factory(Requirement::class)->create([
                            'name' => $requirementParam['name'],
                            'requirement_type_id' => $requirementType->id,
                        ]);

                        $position->requirements()->attach([
                            $requirement->id => ['auxiliary_level' => $auxiliary_level],
                        ]);
                    });
                });
        });

        \DB::connection('mysql_contractuals')->statement("SET FOREIGN_KEY_CHECKS=1;");

        return $contest;
    }
}
