# noinspection SqlResolveForFile

use personnel;
set @organogram_id := (select id from laravel.organograms where ended_at is null order by id desc limit 1);

-- ** hardcoded for doctor Δ265 ΓΕΩΡΓΙΟΥ ΑΙΚΑΤΕΡΙΝΗ **
update epethrida
set KATHGORIA = 11
where AM = 'Δ265';

--  Μηδενίζω τιμές για υπηρετούντες, προσωρινές και διαθέσιμους
UPDATE positions X SET epet_yphr_nr = 0, epet_diath_nr = 0, epet_pros_nr = 0;

--  προσθήκη νέων τμημάτων
insert into tmhmata (cd, dscr, cd_parent_yphr, cd_parent_yphr_comp)
select a.cd_yphr_comp, a.yphr_comp_dscr, b.cd_yphr, b.cd_yphr_comp from (
select distinct cd_yphr_comp, yphr_comp_dscr from epethrida where cd_yphr_comp not in (select cd_yphr_comp from compass_yphr) and char_length(cd_yphr_comp)=4
and cd_yphr_comp not in ('1371','ΚΩ28', 'ΚΩ74','Φ32Δ')
and cd_yphr_comp not like 'ΚΩ%' and cd_yphr_comp not like 'Φ%'
and cd_yphr_comp not in (select cd from tmhmata)
) a inner join compass_yphr b on left(a.cd_yphr_comp, 3) = b.cd_yphr_comp
 order by a.cd_yphr_comp;

insert into tmhmata (cd, dscr, cd_parent_yphr, cd_parent_yphr_comp)
select a.cd_yphresia_full, a.yphresia_full_dscr, b.cd_yphr, b.cd_yphr_comp from (
select distinct cd_yphresia_full, yphresia_full_dscr from epethrida where cd_yphresia_full not in (select cd_yphr_comp from compass_yphr) and char_length(cd_yphresia_full)=4
and cd_yphresia_full not in ('1371','ΚΩ28', 'ΚΩ74','Φ32Δ')
and cd_yphresia_full not like 'ΚΩ%' and cd_yphr_comp not like 'Φ%'
and cd_yphresia_full not in (select cd from tmhmata)
) a inner join compass_yphr b on left(a.cd_yphresia_full, 3) = b.cd_yphr_comp
 order by a.cd_yphresia_full;

-- update laravel.departments
insert into laravel.units (`name`, abbrv, `order`, parent_id, compass_id, organogram_id, unit_type_id)
select DSCR, ABBRV, ORDER_NR, CD_PARENT_YPHR, CD, @organogram_id, 80
from personnel.tmhmata where CD_PARENT_YPHR is not null and CD_PARENT_YPHR != '' and
        CD not in (select compass_id from laravel.units)
order by CD;

--  Νέες εγγραφές Υπηρεσιών στις Οργανικές
insert into positions (compass_unit_id, unit_id, occupation_id, compass_specialization_id, org_nr, epet_yphr_nr, epet_pros_nr, created_at)
select c.cd_yphr_comp, c.cd_yphr, o.id, e.cd_eidik, SUM(IF(e.place='ΟΡΓΑΝΙΚΗ',1,0)), count(am), SUM(IF(e.place='ΠΡΟΣΩΠΟΠΑΓΗΣ',1,0)), now()
from epethrida e
 inner join compass_yphr c on e.cd_yphr_comp = c.cd_yphr_comp
 inner join occupations o on e.kathgoria = o.compass_id
where e.cd_yphr_comp not in (select compass_unit_id from positions where positions.compass_specialization_id = e.cd_eidik and positions.occupation_id = o.id and positions.department_id is null)
  and e.cd_yphr_comp not in (select cd from tmhmata)
  AND e.ARGIA_DIATHES NOT LIKE 'Δ%'
group by c.cd_yphr_comp, c.cd_yphr, o.id, e.cd_eidik;


--  Νέες εγγραφές Τμημάτων στις Οργανικές
insert into positions (compass_unit_id, unit_id, occupation_id, compass_specialization_id, compass_department_id, org_nr, epet_yphr_nr, epet_pros_nr, created_at)
select t.cd_parent_yphr_comp, t.cd_parent_yphr, o.id, e.cd_eidik, t.cd, SUM(IF(e.place='ΟΡΓΑΝΙΚΗ',1,0)), count(am), SUM(IF(e.place='ΠΡΟΣΩΠΟΠΑΓΗΣ',1,0)), now()
from epethrida e
 inner join tmhmata t on e.cd_yphr_comp = t.cd
 inner join occupations o on e.kathgoria = o.compass_id
where e.cd_yphr_comp not in (select compass_department_id from positions where positions.compass_specialization_id = e.cd_eidik and positions.occupation_id = o.id and positions.compass_department_id is not null)
  AND e.ARGIA_DIATHES NOT LIKE 'Δ%'
group by t.cd_parent_yphr_comp, t.cd_parent_yphr, o.id, e.cd_eidik, t.cd;

--  Ενημέρωση νέων πεδίων από συμπληρωματικούς πίνακες ON SERVER
update positions p
left join laravel.units d on p.compass_department_id = d.compass_id
set p.department_id = d.id
where p.compass_department_id is not null;

update positions p
left join specializations s on p.compass_specialization_id = s.compass_id
set p.specialization_id = s.id
where p.compass_specialization_id is not null;


--  Διόρθωση Parent Ειδικοτήτων για όλους
update positions p inner join specializations s on p.specialization_id = s.id
set p.compass_specialization_parent_id = s.compass_specialization_parent_id,
    p.specialization_parent_id = s.specialization_parent_id
where specialization_id in (select id from specializations s where s.specialization_parent_id is not null)
  and p.compass_specialization_parent_id is null;



--  Καταμέτρηση Υπηρετούντων, Προσωρινές, Διαθεσιμότητα  από πίνακα EPETHRIDA
--  Όχι όσοι είναι σε χώρους - θα γίνουν με άλλο query
--  Όχι όσοι είναι σε γραφεία (πχ. Υπουργού, Γ.Γ., ΓΔΔΥ κτλ) - θα γίνουν με άλλο query
update positions p
    inner join (
        SELECT e.CD_YPHR_COMP, o.id occupation_id, e.CD_EIDIK, count(e.AM) YPHR_NR, SUM(IF(e.PLACE='ΠΡΟΣΩΠΟΠΑΓΗΣ',1,0)) PROS_NR, SUM(IF(e.ARGIA_DIATHES LIKE 'Δ%',1,0)) DIATH_NR
        FROM epethrida e
            inner join occupations o on e.KATHGORIA = o.compass_id
        group by e.CD_YPHR_COMP, o.id, e.CD_EIDIK
    ) x
set p.EPET_YPHR_NR = x.YPHR_NR, p.EPET_PROS_NR = x.PROS_NR, p.EPET_DIATH_NR = x.DIATH_NR
where x.CD_EIDIK = p.compass_specialization_id
  and x.CD_YPHR_COMP = p.compass_unit_id
  and x.occupation_id = p.occupation_id
  and p.compass_department_id is null;

--  Καταμέτρηση Υπηρετούντων, Προσωρινές, Διαθεσιμότητα από πίνακα EPETHRIDA όσων είναι σε Χώρους
update positions p
    inner join (
        SELECT e.CD_YPHR_COMP, o.id occupation_id, e.CD_EIDIK, count(e.AM) YPHR_NR, SUM(IF(e.PLACE='ΠΡΟΣΩΠΟΠΑΓΗΣ',1,0)) PROS_NR, SUM(IF(e.ARGIA_DIATHES LIKE 'Δ%',1,0)) DIATH_NR
        FROM epethrida e
            inner join occupations o on e.KATHGORIA = o.compass_id
        group by e.CD_YPHR_COMP, o.id, e.CD_EIDIK
    ) x
set p.EPET_YPHR_NR = x.YPHR_NR, p.EPET_PROS_NR = x.PROS_NR, p.EPET_DIATH_NR = x.DIATH_NR
where x.CD_EIDIK = p.compass_specialization_id
  and x.CD_YPHR_COMP = p.compass_department_id
  and x.occupation_id = p.occupation_id
  and p.compass_department_id is not null;


UPDATE positions SET EPET_DIATH_NR = 0 WHERE EPET_DIATH_NR IS NULL;

UPDATE positions SET EPET_PROS_NR = 0 WHERE EPET_PROS_NR IS NULL;


-- update employees

--  Διαγράφω τους υπαλλήλους που βγήκαν από την επετηρίδα
delete from employees where compass_id not in
(select AM from epethrida) and compass_id is not null;



--  Περνάω τους νέους υπαλλήλους
insert into employees (compass_id, fullname, name, surname)
select e.AM, e.FULLNAME, p.FNAME, p.LNAME
from epethrida e inner join pers p on e.AM = p.AM
where e.AM not in (select compass_id from employees where compass_id is not null) ;


--  update stop_flg
update employees empl
inner join epethrida e on empl.compass_id = e.am
set empl.stop_flg = e.stop_flg
where e.stop_flg != '';


--  update esdd
update employees empl
inner join epethrida e on empl.compass_id = e.am
set empl.esdd = e.esdd
where e.esdd != '';


--  update argia_diathes
update employees empl
inner join epethrida e on empl.compass_id = e.am
set empl.suspension = e.argia_diathes
where e.argia_diathes != '';


--  update personal data
update employees empl
left join epethrida e on empl.compass_id = e.AM
left join pers p on empl.compass_id = p.AM
set empl.fullname = e.FULLNAME, empl.name = p.FNAME, empl.surname = TRIM(CONCAT(p.LNAME, ' ', IFNULL(p.LNAME2,''))),
    empl.fathername = p.DNAME, empl.mothername = p.MNAME, empl.email = p.EMAIL,
empl.birthdate = date_format(p.DT_BIRTH, '%Y-%m-%d'),
empl.amka = p.AMKA, empl.afm = p.AFM, empl.place = e.place
where empl.compass_id is not null;

--  set employee working departments
update employees empl
inner join (
select e.AM, u.id as unit_id, d.id as department_id,
   left(e.CD_YPHRESIA_FULL, 3) as compass_unit_id, e.CD_YPHRESIA_FULL as compass_department_id, e.YPHRESIA_FULL_DSCR as compass_department_name, ifnull(c.yphr_comp_dscr, yphresia_full_dscr) as compass_unit_name
from epethrida e
     left join (select * from laravel.units where unit_type_id in (70,80)) d on e.cd_yphresia_full = d.compass_id
     left join (select * from laravel.units where unit_type_id not in (70,80)) u on d.parent_id = u.id
     left join compass_yphr c on left(e.cd_yphresia_full, 3) = c.cd_yphr_comp
where char_length(e.cd_yphresia_full)=4 AND e.CD_YPHRESIA_FULL NOT in ('Α691', 'Α692')) x
on empl.compass_id = x.AM
set empl.unit_id = x.unit_id, empl.department_id = x.department_id, empl.compass_unit_id = x.compass_unit_id, empl.compass_department_id = x.compass_department_id,
empl.compass_unit_name = x.compass_unit_name, empl.compass_department_name = x.compass_department_name where empl.compass_id is not null;

-- remove working departments if no longer present
update employees
set department_id = null, compass_department_id = null, compass_department_name = null
where department_id is not null and compass_id not in (
    select e.AM
    from epethrida e
             inner join (select * from laravel.units where unit_type_id in (70,80)) d on e.cd_yphresia_full = d.compass_id
    where char_length(e.cd_yphresia_full)=4 AND e.CD_YPHRESIA_FULL NOT in ('Α691', 'Α692'));

--  set employee working units
update employees empl
    inner join (
        select e.AM, d.id as unit_id, e.CD_YPHRESIA_FULL as compass_unit_id, ifnull(c.yphr_comp_dscr, e.YPHRESIA_FULL_DSCR) as compass_unit_name
        from epethrida e
                 left join compass_yphr c on e.cd_yphresia_full = c.cd_yphr_comp
                 left join (select * from laravel.units where unit_type_id not in (70,80)) d on c.cd_yphr = d.id
        where char_length(e.cd_yphresia_full)<4 or e.CD_YPHRESIA_FULL in ('Α691', 'Α692')) x
    on empl.compass_id = x.AM
set empl.unit_id = x.unit_id, empl.compass_unit_id = x.compass_unit_id, empl.compass_unit_name = x.compass_unit_name
where empl.compass_id is not null;


--  set employee position departments
update employees empl
    inner join (
        select e.AM, u.id as position_unit_id, d.id as position_department_id,
               left(e.CD_YPHR_COMP, 3) as compass_position_unit_id, e.CD_YPHR_COMP as compass_position_department_id,
               e.YPHR_COMP_DSCR as compass_position_department_name,
               ifnull(c.yphr_comp_dscr, e.YPHRESIA_DSCR) as compass_position_unit_name
        from epethrida e
                 left join (select * from laravel.units where unit_type_id in (70,80)) d on e.cd_yphr_comp = d.compass_id
                 left join (select * from laravel.units where unit_type_id not in (70,80)) u on d.parent_id = u.id
                 left join compass_yphr c on left(e.cd_yphr_comp, 3) = c.cd_yphr_comp
        where char_length(e.CD_YPHR_COMP)=4 AND e.CD_YPHRESIA_FULL NOT in ('Α691', 'Α692')) x
    on empl.compass_id = x.AM
set empl.position_unit_id = x.position_unit_id, empl.position_department_id = x.position_department_id, empl.compass_position_unit_id = x.compass_position_unit_id,
    empl.compass_position_department_id = x.compass_position_department_id,
    empl.compass_position_unit_name = x.compass_position_unit_name, empl.compass_position_department_name = x.compass_position_department_name where empl.compass_id is not null;

-- remove employee position departments if no longer present
update employees
set position_department_id = null, compass_position_department_id = null, compass_position_department_name = null
where position_department_id is not null and compass_id not in (
    select e.AM
    from epethrida e
             inner join (select * from laravel.units where unit_type_id in (70,80)) d on e.cd_yphr_comp = d.compass_id
    where char_length(e.CD_YPHR_COMP)=4 AND e.CD_YPHR_COMP NOT in ('Α691', 'Α692'));

--  set employee position units
update employees empl
    inner join (
        select e.AM, d.id as position_unit_id, e.CD_YPHR_COMP as compass_position_unit_id,
               ifnull(c.yphr_comp_dscr, e.YPHR_COMP_DSCR) as compass_position_unit_name
        from epethrida e
                 left join compass_yphr c on e.CD_YPHR_COMP = c.cd_yphr_comp
                 left join (select * from laravel.units where unit_type_id not in (70,80)) d on c.cd_yphr = d.id
        where char_length(e.CD_YPHR_COMP)<4 or e.CD_YPHR_COMP in ('Α691', 'Α692')) x
    on empl.compass_id = x.AM
set empl.position_unit_id = x.position_unit_id, empl.compass_position_unit_id = x.compass_position_unit_id,
    empl.compass_position_unit_name = x.compass_position_unit_name where empl.compass_id is not null;
-- -- --

--  set employee specializations
update employees empl
    inner join (
        select am, cd_eidik, s.id as specialization_id, o.id as occupation_id from epethrida e
        inner join specializations s on e.cd_eidik = s.compass_id
        inner join occupations o on e.kathgoria = o.compass_id
    ) x  on empl.compass_id = x.am
set empl.specialization_id = x.specialization_id, empl.occupation_id = x.occupation_id;

-- sync employees ranks

-- delete records that will be brought from compass
delete from employee_rank where unit_id is null
and employee_id in (select id from employees where compass_id is not null);

-- insert ranks from compass (without overriding old values or doing double-entry)
insert into employee_rank (employee_id, rank_id)
select empl.id, x.id from employees empl inner join
(
	select e.AM, r.id from epethrida e
	inner join ranks r
	on trim(left(e.manager,3)) = r.compass_id
	where e.manager is not null and e.manager != ''
) x on empl.compass_id = x.AM
where empl.id not in (select employee_id from employee_rank where rank_id = x.id);

-- insert ranks from field 4201 from compass (without overriding old values or doing double-entry)
insert into employee_rank (employee_id, rank_id)
select empl.id, x.id from employees empl inner join
(
	select e.AM, r.id from epethrida e
	inner join ranks r
	on trim(left(e.manager4201,3)) = r.compass_id
	where e.manager4201 is not null and e.manager4201 != ''
) x on empl.compass_id = x.AM
where empl.id not in (select employee_id from employee_rank where rank_id = x.id);


--  set employee telephones (type 3: pesrsonal 1)
insert into phonebook.telephones (employee_id, type_id, work, tel, unit_id)
select empl.id, 3, 0, trim(p.phone), empl.unit_id
from employees empl
inner join pers p on empl.compass_id = p.am
where char_length(trim(p.phone)) <= 10 and trim(p.phone) > 5 and empl.unit_id is not null
and empl.id not in (select employee_id from phonebook.telephones where type_id = 3) and empl.compass_id is not null;


--  set employee telephones (type 4: pesrsonal 2)
insert into phonebook.telephones (employee_id, type_id, work, tel, unit_id)
select empl.id, 4, 0, trim(p.phone_b), empl.unit_id
from employees empl
inner join pers p on empl.compass_id = p.am
where char_length(trim(p.phone_b)) <= 10 and trim(p.phone_b) > 5 and empl.unit_id is not null
and empl.id not in (select employee_id from phonebook.telephones where type_id = 4) and empl.compass_id is not null;


--  set employee telephones (type 5: pesrsonal 3)
insert into phonebook.telephones (employee_id, type_id, work, tel, unit_id)
select empl.id, 5, 0, trim(p.mobile), empl.unit_id
from employees empl
inner join pers p on empl.compass_id = p.am
where char_length(trim(p.mobile)) <= 10 and trim(p.mobile) > 5 and empl.unit_id is not null
and empl.id not in (select employee_id from phonebook.telephones where type_id = 5 and empl.compass_id is not null);


--  delete employees without specialization - these are from errors in compass
delete from employees where specialization_id is null and compass_id is not null;




-- ----------------------- sync assignments --------------------------------

truncate table assignments;


ALTER TABLE assignments AUTO_INCREMENT = 1;


--  those that are assigned in their position (unit or department)
INSERT INTO assignments
(employee_id,fullname,name,surname,fathername,mothername,birthdate,amka,afm,email,occupation_id,specialization_id,unit_id,department_id,
position_unit_id,position_department_id,place,stop_flg,esdd,suspension,compass_id,compass_unit_id,compass_department_id,compass_position_unit_id,
compass_position_department_id,compass_unit_name,compass_department_name,compass_position_unit_name,compass_position_department_name,print_unit_id,flg)
select
id,fullname,name,surname,fathername,mothername,birthdate,amka,afm,email,occupation_id,specialization_id,unit_id,department_id,
position_unit_id,position_department_id,place,stop_flg,esdd,suspension,compass_id,compass_unit_id,compass_department_id,compass_position_unit_id,
compass_position_department_id,compass_unit_name,compass_department_name,compass_position_unit_name,compass_position_department_name,unit_id, 1
from employees where compass_id is not null and unit_id is not null and unit_id = position_unit_id and (department_id is null or department_id = position_department_id);


--  show apospasmenoi when listing by position
INSERT INTO assignments
(employee_id,fullname,name,surname,fathername,mothername,birthdate,amka,afm,email,occupation_id,specialization_id,unit_id,department_id,
position_unit_id,position_department_id,place,stop_flg,esdd,suspension,compass_id,compass_unit_id,compass_department_id,compass_position_unit_id,
compass_position_department_id,compass_unit_name,compass_department_name,compass_position_unit_name,compass_position_department_name,print_unit_id,flg)
select
id,fullname,name,surname,fathername,mothername,birthdate,amka,afm,email,occupation_id,specialization_id,unit_id,department_id,
position_unit_id,position_department_id,place,stop_flg,esdd,suspension,compass_id,compass_unit_id,compass_department_id,compass_position_unit_id,
compass_position_department_id,compass_unit_name,compass_department_name,compass_position_unit_name,compass_position_department_name,position_unit_id, 1
from employees where compass_id is not null and position_unit_id is not null and (unit_id is null or unit_id != position_unit_id or department_id != position_department_id);


--  show apospasmenoi when listing by assigned unit
INSERT INTO assignments
(employee_id,fullname,name,surname,fathername,mothername,birthdate,amka,afm,email,occupation_id,specialization_id,unit_id,department_id,
position_unit_id,position_department_id,place,stop_flg,esdd,suspension,compass_id,compass_unit_id,compass_department_id,compass_position_unit_id,
compass_position_department_id,compass_unit_name,compass_department_name,compass_position_unit_name,compass_position_department_name,print_unit_id,flg)
select
id,fullname,name,surname,fathername,mothername,birthdate,amka,afm,email,occupation_id,specialization_id,unit_id,department_id,
position_unit_id,position_department_id,place,stop_flg,esdd,suspension,compass_id,compass_unit_id,compass_department_id,compass_position_unit_id,
compass_position_department_id,compass_unit_name,compass_department_name,compass_position_unit_name,compass_position_department_name,unit_id, 2
from employees where compass_id is not null and unit_id is not null and (unit_id != position_unit_id or position_unit_id is null);


-- update log
insert into updates (logging, AM, FULLNAME, CD_EIDIK, EIDIK_DSCR, CD_YPHR_COMP, YPHR_COMP_DSCR, CD_YPHRESIA, YPHRESIA_DSCR, CD_YPHRESIA_FULL, YPHRESIA_FULL_DSCR, ARGIA_DIATHES, created_at)
select 1, e.AM, e.FULLNAME, e.CD_EIDIK, e.EIDIK_DSCR, e.CD_YPHR_COMP, e.YPHR_COMP_DSCR, e.CD_YPHRESIA, e.YPHRESIA_DSCR, e.CD_YPHRESIA_FULL, e.YPHRESIA_FULL_DSCR, e.ARGIA_DIATHES, now()
from epethrida e where e.AM not in (SELECT AM from epethrida_old);

insert into updates (logging, AM, FULLNAME, CD_EIDIK_OLD, EIDIK_DSCR_OLD, CD_YPHR_COMP_OLD, YPHR_COMP_DSCR_OLD, CD_YPHRESIA_OLD, YPHRESIA_DSCR_OLD, CD_YPHRESIA_FULL_OLD, YPHRESIA_FULL_DSCR_OLD, ARGIA_DIATHES_OLD, created_at)
select 2, o.AM, o.FULLNAME, o.CD_EIDIK, o.EIDIK_DSCR, o.CD_YPHR_COMP, o.YPHR_COMP_DSCR, o.CD_YPHRESIA, o.YPHRESIA_DSCR, o.CD_YPHRESIA_FULL, o.YPHRESIA_FULL_DSCR, o.ARGIA_DIATHES, now()
from epethrida_old o where o.AM not in (SELECT AM from epethrida);

insert into updates (logging, AM, FULLNAME, CD_EIDIK, EIDIK_DSCR, CD_YPHR_COMP, YPHR_COMP_DSCR, CD_YPHRESIA, YPHRESIA_DSCR, CD_YPHRESIA_FULL, YPHRESIA_FULL_DSCR, ARGIA_DIATHES,
                     CD_EIDIK_OLD, EIDIK_DSCR_OLD, CD_YPHR_COMP_OLD, YPHR_COMP_DSCR_OLD, CD_YPHRESIA_OLD, YPHRESIA_DSCR_OLD, CD_YPHRESIA_FULL_OLD, YPHRESIA_FULL_DSCR_OLD, ARGIA_DIATHES_OLD, created_at)
select 3, e.AM, e.FULLNAME, e.CD_EIDIK, e.EIDIK_DSCR, e.CD_YPHR_COMP, e.YPHR_COMP_DSCR, e.CD_YPHRESIA, e.YPHRESIA_DSCR, e.CD_YPHRESIA_FULL, e.YPHRESIA_FULL_DSCR, e.ARGIA_DIATHES,
       o.CD_EIDIK, o.EIDIK_DSCR, o.CD_YPHR_COMP, o.YPHR_COMP_DSCR, o.CD_YPHRESIA, o.YPHRESIA_DSCR, o.CD_YPHRESIA_FULL, o.YPHRESIA_FULL_DSCR, o.ARGIA_DIATHES, now()
from epethrida e inner join epethrida_old o on e.am = o.am
where (e.CD_EIDIK != o.CD_EIDIK or e.CD_YPHR_COMP != o.CD_YPHR_COMP or e.CD_YPHRESIA != o.CD_YPHRESIA or e.CD_YPHRESIA_FULL != o.CD_YPHRESIA_FULL or e.ARGIA_DIATHES != o.ARGIA_DIATHES);

drop table epethrida_old;
