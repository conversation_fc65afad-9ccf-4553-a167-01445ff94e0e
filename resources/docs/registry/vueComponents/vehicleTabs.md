# vehicleTabs


## buttons
---
delete: deleteResource(onDelete)
print: printout()
tab-collection

## props
---
vehicle
inside_modal
(select options)

## created
---
if we have a model
populates models -> instantiateResources
and sets permissions

## watch
---
if model changes we update permissions

## methods
---
prepareModels: Fill data models used in forms

updateResource: updates resource models -> instantiateVehicle
    and emits event (resource-updated)

addVehicleInsurance: adds new insurance to insurance-tab collection
updateVehicleInsurance: updates insurances collection
    and emits event (resource-updated)

deleteVehicleInsurance: removes insurance from insurance-tab collection

deleteResource: deletes resource item and then:
    emits event or returns home -> onDelete()

printout: calls url to print model

