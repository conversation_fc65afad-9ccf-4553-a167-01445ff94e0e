# vehicleInsuranceForm (form used in tab template as part of a collection tab)

## buttons
---
submit: onSubmit()
cancel: editOff()
edit: editOn()
delete: removeInsurance

## props
---
vehicle_insurance
index
permissions

## created
---
sets create or show mode based on if we have a model
and populates model and form

## methods
---
editOn: opens form for editing

editOff: resets form, or returns home

onSubmit: does a post or put to specified url and then:
    updates model & form, emits event and sets editing off -> onUpdate()

deleteResource:
if saved, deletes resource item and then:
    emits events deleted
else
    emits events deleted
