# mobileForm (simple form and main resource template)

## buttons
---
submit: onSubmit()
cancel: editOff()
edit: editOn()
delete: deleteResource(onDelete)
print: printout()

## props
---
mobile
inside_modal
(select options)

## created
---
sets create or show mode based on if we have a model
and populates model and form
and sets permissions

## watch
---
if model changes we update permissions and url/submit method

## methods
---
editOn: opens form for editing

editOff: resets form, or returns home

onSubmit: does a post or put to specified url and then:
    updates model & form, emits event and sets editing off -> onUpdate()

deleteResource: deletes resource item and then:
    emits event or returns home -> onDelete()

printout: calls url to print model

