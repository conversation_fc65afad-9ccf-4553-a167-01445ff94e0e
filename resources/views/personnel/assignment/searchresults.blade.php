@extends('layouts.personnel')

<?php
  //this are the fields we check for printing tables
  $print_unit_id_old = '';
  $assignment_id_old = '';
?>

@section('contentheader_description')
Σύνθεση Υπηρεσιών
@endsection

@section('main-content')
<div class="row">
    <div class="col-xs-12">
        <h2>Προβολή Σύνθεσης Υπηρεσιών<a href="{{ URL::route('personnel.assignment.index') }}" class="btn btn-info pull-right"><i class="fa fa-search"></i> Νέα Αναζήτηση</a></h2>
        <br>
    </div>
</div>

@foreach ($assignments as $assignment)
    @if ( ($assignment->print_unit_id != $print_unit_id_old) && ($assignment_id_old != '') )
    {{-- Change of one of critical fields AND we have an id so we need to close the table --}}
                            </tbody>
                        </table>
                    </div> <!-- box-body -->
                </div> <!-- box -->
                <br>
            </div> <!-- col -->
        </div> <!-- row -->

    @endif

    @if ($assignment->print_unit_id != $print_unit_id_old)
    {{-- change of yphresia. Need to show yphresia header --}}
        <div class="row">
            <div class="col-xs-12">
                <div class="box box-primary" id="{{ $assignment->print_unit_id }}_box">
                    <div class="box-header with-border">
                        <h3 class="box-title">{{ $assignment->printUnit->name }}</h3>
                    </div>
                    <div class="box-body">
                        <table class="table table-bordered table-striped table-hover apptree-dt" id="{{ $assignment->print_unit_id }}_tbl"  style="width:100%">
                    <thead>
                        <tr>
                          <th>Κ.Ε.</th>
                          <th>ΕΙΔΙΚΟΤΗΤΑ</th>
                          <th>ΣΧ. ΕΡΓ.</th>
                          <th>AM</th>
                          <th>ΟΝΟΜΑΤΕΠΩΝΥΜΟ</th>
                          <th>ΥΠΗΡ/ΟΡΓ</th>
                          <th>ΠΑΡΑΤΗΡΗΣΕΙΣ</th>
                        </tr>
                    </thead>
                    <tbody>
    @endif
    <?php
      $print_unit_id_old = $assignment->print_unit_id;
      $assignment_id_old = $assignment->id;
    ?>
                        <tr id="{{ $assignment->id }}">
                            <td class="right">{{ $assignment->specialization->compass_id }}</td>
                            <td>{{ $assignment->specialization->fullname }}</td>
                            <td>{{ $assignment->occupation->name }}</td>
                            <td class="center">{{ $assignment->compass_id }}</td>
                            <td>{{ $assignment->fullname }}</td>
                            <td>
                            @if ($assignment->flg == 1 && $assignment->unit_id != $assignment->print_unit_id)
                                Υπηρετεί: {{ $assignment->compass_unit_name . $assignment->compass_department_name }}
{{--                            @elseif ($assignment->flg == 1 && $assignment->department_id != $assignment->printdepartment_id)--}}
{{--                                Υπηρετεί: {{ $assignment->compass_department_name }}--}}
                            @endif
                            @if ($assignment->flg == 2)
                            Οργ.Ένταξη: {{ $assignment->compass_position_unit_name . $assignment->compass_position_department_name }}
                            @endif
                            </td>
                            <td>
                            @foreach ($assignment->employee->ranks()->orderBy('compass_id')->get() as $rank)
                                {{ $rank->name . ' ' }}
                            @endforeach
                            {{ $assignment->esdd }}
                            </td>
                        </tr>
@endforeach
                    </tbody>
                </table>
            </div> <!-- box-body -->
        </div> <!-- box -->.
        <br>
    </div> <!-- col -->
</div> <!-- row -->

<div class="row">
    <div class="col-sm-3">
        <a class="btn btn-default btn-block" href="{{ URL::route('personnel.home') }}"><i class="fa fa-arrow-circle-left"></i> Επιστροφή</a>
    </div>
    <div class="col-sm-3 col-sm-offset-6">
        <a class="btn btn-info btn-block" href="{{ URL::route('personnel.assignment.export-pdf', Request::all()) }}"><i class="fa fa-file-pdf-o"></i> Εκτύπωση PDF</a>
    </div>
</div>
        </div>
    </div>
@endsection
