@extends('personnel.pdf')

@section('main')
<?php
    //this are the fields we check for printing tables
    $flg_old = '';
    $place_old = '';
    $specialization_id_old = '';
    $print_unit_id_old = '';
    $assignment_id_old = '';
?>
  <h3>ΚΑΤΑΣΤΑΣΗ ΠΡΟΣΩΠΙΚΟΥ</h3>
  <div>
    @foreach ($assignments as $assignment)

      @if (
        ( ($assignment->flg != $flg_old) ||
          ($assignment->place != $place_old) ||
          ($assignment->specialization_id != $specialization_id_old) ||
          ($assignment->print_unit_id != $print_unit_id_old)
        ) &&
        ($assignment_id_old != '')
      )
      {{-- Change of one of critical fields AND we have an id so we need to close the table --}}
          </tbody>
        </table>
        <br><br>
      @endif

      @if (($assignment->print_unit_id != $print_unit_id_old) || ($assignment->specialization_id != $specialization_id_old) || ($assignment->flg != $flg_old) || ($assignment->place != $place_old))
      {{-- change of yphresia, eidikotita, flag or place. Need to start the table! --}}

        @if ($assignment->print_unit_id != $print_unit_id_old)
        {{-- change of yphresia. Need to show yphresia header --}}
            @if ($assignment_id_old != '')
            {{-- This is not the 1st Yphresia --}}
              <h3 class="newpage">{{ $assignment->printUnit->name }}</h3>
            @else
              <h3>{{ $assignment->printUnit->name }}</h3>
            @endif
        @endif



          <table class="table table-bordered table-striped table-hover"  cellspacing="0">
            <thead>
                <tr>
                    <th class="text-left" colspan="5">
                        {{ $assignment->specialization->fullname }}&nbsp;
                        @if ($assignment->specialization->occupation_id == 1)
                            (ΜΟΝΙΜΟ ΠΡΟΣΩΠΙΚΟ).
                        @else
                            (Ι.Δ.Α.Χ.).
                        @endif
                        @if ($assignment->flg == '2')
                            ΑΠΟ ΑΛΛΗ ΥΠΗΡΕΣΙΑ
                        @else
                            {{ $assignment->place }} ΘΕΣΗ
                        @endif
                    </th>
                </tr>
                <tr>
                    <th width="25">A/A</th>
                    <th width="40">AM</th>
                    <th width="130">ΟΝΟΜΑΤΕΠΩΝΥΜΟ</th>
                    <th width="145">ΥΠΗΡ/ΟΡΓ</th>
                    <th>ΠΑΡΑΤΗΡΗΣΕΙΣ</th>
                </tr>
            </thead>
            <tbody>
            <?php
                $i=0; //table counter
            ?>
      @endif
        <?php
          $flg_old = $assignment->flg;
          $place_old = $assignment->place;
          $specialization_id_old = $assignment->specialization_id;
          $print_unit_id_old = $assignment->print_unit_id;
          $assignment_id_old = $assignment->id;
          $i++;
        ?>
          <tr id="{{ $assignment->id }}">
              <td class="text-right">{{ $i }}</td>
              <td class="text-center">{{ $assignment->compass_id }}</td>
              <td>{{ $assignment->fullname }}</td>
              <td>
                @if ($assignment->flg == 1 && $assignment->unit_id != $assignment->print_unit_id)
                  Υπηρετεί: {{ $assignment->compass_unit_name . $assignment->compass_department_name }}
{{--                @elseif ($assignment->flg == 1 && $assignment->department_id != $assignment->printdepartment_id)--}}
{{--                    Υπηρετεί: {{ $assignment->compass_department_name }}--}}
                @endif
                @if ($assignment->flg == 2)
                  Οργ.Ένταξη: {{ $assignment->compass_position_unit_name . $assignment->compass_position_department_name }}
                @endif
              </td>
              <td>
                @foreach ($assignment->employee->ranks()->orderBy('compass_id')->get() as $rank)
                    {{ $rank->name . ' ' }}
                @endforeach
                {{ $assignment->ESDD }}
              </td>
          </tr>
    @endforeach
    {{-- last table close --}}
        </tbody>
      </table>
  </div>

@endsection
