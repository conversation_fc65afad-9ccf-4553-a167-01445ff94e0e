@extends('layouts.personnel')

@section('contentheader_description')
Καταγραφή Ενημέρωσης Στοιχείων Υπαλλήλων Aπό Compass
@endsection

@section('main-content')
<div class="row">
    <div class="col-xs-12">
        <div class="box box-primary">
            <div class="box-header">
                <h3 class="box-title">Παρακολούθηση αλλαγών δεδομένων εισαγωγής από Compass</h3>
            </div>

            <div class="box-body">
                <table class="table table-bordered table-striped table-hover" id="updates_tbl">
                    <thead>
                        <tr>
                            <th><i class="fa fa-cog"></i></th>
                            <th>ΑΜ</th>
                            <th>Ονοματεπώνυμο</th>
                            <th>Αλλαγή</th>
                            <th>Εισαγωγή</th>
                            <th>Επικύρωση</th>
                            <th>Χρήστης</th>
                        </tr>
                    </thead>
                    <tbody>
                    @foreach($updates as $update)
                        @if ($update->status)
                        <tr id="{{$update->id}}" class="done">
                            <td class="actions">
                                <a href="{{ URL::route('personnel.update.show', $update->id) }}" id="updateDetails_{{$update->id}}" class="personnel-modal btn btn-sm btn-info" data-toggle="modal" data-remote="false" data-target="#myModal"><i class="fa fa-info"></i> info</a>
                                <a href="#" disabled class="btn btn-sm btn-success">done</a>
                            </td>
                        @else
                        <tr id="{{$update->id}}">
                            <td class="actions">
                                <a href="{{ URL::route('personnel.update.show', $update->id) }}" id="updateDetails_{{$update->id}}" class="update-modal btn btn-sm btn-info" data-toggle="modal" data-remote="false" data-target="#myModal"><i class="fa fa-info"></i> info</a>
                                <a onClick="task_done('{{$update->id}}');" class="toggle btn btn-sm btn-success"><i class="fa fa-check"></i> ok</a>
                            </td>
                        @endif
                            <td id="span_{{$update->id}}">{{$update->AM}}</td>
                            <td>{{$update->FULLNAME}}</td>
                            <td>
                                @if ($update->logging == 1)
                                    νέα εγγραφή
                                @elseif ($update->logging == 2)
                                    διαγραφή από επετηρίδα
                                @else
                                    τροποποίηση εγγραφής
                                @endif
                            </td>
                            <td>{{ $update->created_at->format('d-m-Y') }}</td>
                            <td class="updateDate">
                            @if ($update->updated_at)
                                {{ $update->updated_at->format('d-m-Y') }}
                            @endif
                            </td>
                            <td class="updateUser">{{ $update->user->name ?? '' }}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div><!-- box-body -->
            <div class="box-footer">
                <div class="row">
                    <div class="col-md-12 text-center">
                        {!! $updates->render() !!}
                    </div>
                </div>
            </div>
        </div><!-- box -->
    </div><!-- col-xs-12 -->
</div> <!-- row -->
<div class="row">
    <div class="col-sm-3">
        <a class="btn btn-default btn-block" href="{{ route('personnel.home') }}"><i class="fa fa-arrow-circle-left"></i> Επιστροφή</a>
    </div>
</div>

<!-- Bootstrap modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
    </div>
  </div>
</div>

@endsection

@section('page-scripts')
<script>
    var task_done = function(id){
        //$.get("/staff/epethridalog/done/"+id, function(data) {
            //if(data=="OK"){
                //alert('it is done!');
        // $.getJSON("/personnel/update/"+id, function(data) {
        //     if (data.statusMsg=="OK"){
        //         $("#"+id).addClass("done");
        //         $("#"+id+" td.actions a.toggle").remove();
        //         $("#"+id+" td.actions").append('<a href="#" disabled class="btn default">done</a>');
        //         $("#"+id+" td.updateDate").html(data.saveDate);
        //         $("#"+id+" td.updateUser").html(data.user);
        //     }
        // });

        $.ajax({
            url: "/personnel/update/"+id,
            method: 'PUT',
            success: function(result) {
                if (result.statusMsg === "OK"){
                    // swal("Επιτυχία", "Ενημέρωση Ιστορικού", "success");
                    swal({
                        title: "Επιτυχία!",
                        text: "Επιτυχής ενημέρωση ιστορικού!",
                        type: "success",
                        timer: 1000,
                        showConfirmButton: false
                    });
                    $("#"+id).addClass("done");
                    $("#"+id+" td.actions a.toggle").remove();
                    $("#"+id+" td.actions").append('<a href="#" disabled class="btn btn-sm btn-success">done</a>');
                    $("#"+id+" td.updateDate").html(result.savedDate);
                    $("#"+id+" td.updateUser").html(result.user);
                }
            }
        });

        /*
            $("#"+id).addClass("done");
            $("#"+id+" td.actions a.toggle").remove();
            $("#"+id+" td.actions").append('<a href="#" disabled class="btn default"><i class="fa fa-check-circle"></i> done</a>');
        */
    };

    $(document).ready(function(){
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': window.Laravel.csrfToken
            }
        });
        // Fill modal with content from link href
        $("#myModal").on("show.bs.modal", function(e) {
            var link = $(e.relatedTarget);
            $(this).find(".modal-content").load(link.attr("href"));
        });
    });
</script>
@endsection
