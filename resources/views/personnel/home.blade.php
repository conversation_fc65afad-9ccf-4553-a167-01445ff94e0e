@extends('layouts.personnel')

@section('contentheader_description')

@endsection

@section('main-content')
    <div class="row">
        @include('partials.appIconButton', [
            'url' => route('personnel.assignment.index'),
            'title' => 'Σύνθεση Υπηρεσιών',
            'color' => '1b78be',
            'icon' => 'ion-ios-list'
        ])
        @include('partials.appIconButton', [
            'url' => route('personnel.position.index'),
            'title' => 'Οργανικές Θέσεις',
            'color' => '58AAE7',
            'icon' => 'fa-building'
        ])
    </div>
    <div class="row">
        <div class="col-sm-12">
            @canany(['personnel.create','personnel.readAll', 'personnel.update'])
                <div class="col-sm-12">
                    <h3 class="home-section__title">Διαχείριση Εφαρμογής</h3>
                </div>
            @endcanany
        </div>
    </div>
    <div class="row">
        @can('personnel.create')
            @include('partials.appIconButton', [
                'url' => route('personnel.position.create'),
                'title' => 'Δημιουργία Θέσεων',
                'color' => '34676d',
                'icon' => 'fa-plus-square-o'
            ])
        @endcan
        @can('personnel.readAll')
            @include('partials.appIconButton', [
                'url' => route('personnel.employee.index'),
                'title' => 'Αναζήτηση Υπαλλήλων',
                'color' => '45888f',
                'icon' => 'fa-users'
            ])
        @endcan
        @can('personnel.update')
            @include('partials.appIconButton', [
                'url' => route('personnel.position.overfilled'),
                'title' => 'Υπερκάλυψη Θέσεων',
                'color' => '58a6af',
                'icon' => 'fa-exclamation-circle'
            ])
        @endcan
        @can('personnel.update')
            @include('partials.appIconButton', [
                'url' => route('personnel.update.index'),
                'title' => 'Παρακολούθηση Αλλαγών',
                'description' => 'Ενημέρωση από Compass',
                'color' => '7bb9bf',
                'icon' => 'fa-history'
            ])
        @endcan
    </div>
@endsection
