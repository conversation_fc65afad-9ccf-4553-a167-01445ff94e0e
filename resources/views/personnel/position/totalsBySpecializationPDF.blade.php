@extends('personnel.pdf')
@section('main')
<?php
  $occupation_id_old = '';
  $org = $pros = $yphr = $unfilled = $desm = $diath = 0;
?>
    <h3>ΘΕΣΕΙΣ-ΥΠΗΡΕΤΟΥΝΤΕΣ ΑΝΑ ΚΛΑΔΟ</h3>
  <div>
        @foreach ($positions as $position)
        @if ($position->occupation_id != $occupation_id_old)
        {{-- change of occupation --}}

          @if ($occupation_id_old != '')
          {{-- not starting up - we have old data --}}
                <tr class="table-footer">
                  <td class="nums" colspan="2">Σύνολο</td>
                  <td class="nums">{{ $org }}</td>
                  <td class="nums">{{ $pros }}</td>
                  <td class="nums">{{ $yphr }}</td>
                  <td class="nums">{{ $unfilled }}</td>
                  <td class="nums">{{ $desm }}</td>
{{--                  <td class="nums">{{ $diath }}</td>--}}
                </tr>
              </tbody>
            </table>
            <?php $org = $pros = $yphr = $unfilled = $desm = $diath = 0; ?>
            @if ($position->occupation_id == 1)
              <h4 class="newpage">ΜΟΝΙΜΟ ΠΡΟΣΩΠΙΚΟ</h4>
            @else
              <h4 class="newpage">ΙΔΑΧ</h4>
            @endif
          @else
            @if ($position->occupation_id == 1)
              <h4>ΜΟΝΙΜΟ ΠΡΟΣΩΠΙΚΟ</h4>
            @else
              <h4>ΙΔΑΧ</h4>
            @endif
          @endif

          <table class="table table-bordered table-striped table-hover" cellspacing="0">
            <thead>
                <tr>
                    <th>ΚΩΔ</th>
                    <th>Κλάδος/Ειδικότητα</th>
                    <th>Οργανικές</th>
                    <th>Προσωρινές</th>
                    <th>Υπηρετ</th>
                    <th>Κενές</th>
                    <th>Δεσμευ</th>
{{--                    <th>Διαθεσ</th>--}}
                </tr>
            </thead>
        @endif
        <?php
          $occupation_id_old = $position->occupation_id;
          $org = $org + ($position->org_nr);
          $pros = $pros + ($position->epet_pros_nr);
          $yphr = $yphr + ($position->epet_yphr_nr);
          $unfilled = $unfilled + ($position->unfilled);
          $desm = $desm + ($position->desm_nr);
          $diath = $diath + $position->epet_diath_nr;
        ?>
        <tbody>
          <tr orgcd="{{ $position->id }}" id="{{ $position->id }}">
              <td class="org_dscr">{{ $specializationNames[$position->specialization_id]['compass_id'] }}</td>
              <td class="org_dscr">{{ $specializationNames[$position->specialization_id]['fullname'] }}</td>
              <td class="org_nr nums">{{ $position->org_nr }}</td>
              <td class="pros_nr nums">{{ $position->epet_pros_nr }}</td>
              <td class="yphr_nr nums">{{ $position->epet_yphr_nr }}</td>
              <td class="unfilled_nr nums">{{ $position->unfilled }}</td>
              <td class="desm_nr nums">{{ $position->desm_nr}}</td>
{{--              <td class="nums">{{ $position->epet_diath_nr }}</td>--}}
          </tr>
    @endforeach
    {{-- last totals --}}
          <tr class="table-footer">
            <td class="nums" colspan="2">Σύνολο</td>
            <td class="nums">{{ $org }}</td>
            <td class="nums">{{ $pros }}</td>
            <td class="nums">{{ $yphr }}</td>
            <td class="nums">{{ $unfilled }}</td>
            <td class="nums">{{ $desm }}</td>
{{--            <td class="nums">{{ $diath }}</td>--}}
          </tr>
        </tbody>
      </table>

  </div>

@endsection
