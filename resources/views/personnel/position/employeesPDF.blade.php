@extends('personnel.pdf')

@section('main')
<?php
$i=1;
$PAGE_NUM=1;
?>
<h3>ΚΑΤΑΣΤΑΣΗ ΠΡΟΣΩΠΙΚΟΥ</h3>
<h4>
    {{ $position->unit->name ?? ''}} {{ $position->department->name ?? ''}}<br>
    {{  $position->occupation->name ?? ''}} {{ $position->specialization->field ?? ''}} {{ $position->specialization->name ?? ''}}
</h4>
<div>
    @if (!$employees->isEmpty())
    <table class="table table-bordered table-striped table-hover" cellspacing="0">
        <thead>
            <tr>
                <th colspan="4">ΟΡΓΑΝΙΚΗ ΘΕΣΗ</th>
            </tr>
            <tr>
                <th>AA</th>
                <th>Α/Μ</th>
                <th>Ονοματεπώνυμο</th>
                <th>Παρατηρήσεις</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($employees as $employee)
                <tr>
                    <td class="text-right">{{ $i++ }}</td>
                    <td class="text-right">{{ $employee->compass_id }}</td>
                    <td>{{ $employee->fullname }}</td>
                    <td>
                    @if ($employee->unit_id  != $employee->position_unit_id)
                        Υπηρετεί: {{ $employee->compass_unit_name . $employee->compass_department_name }}
                    @endif
                    @if (!empty($employee->rank_id))
                        {{ $employee->rank->name }}
                    @endif
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    @if (!$temporaryEmployees->isEmpty())
    <br>
    <table class="table table-bordered table-striped table-hover" cellspacing="0">
        <thead>
            <tr>
                <th colspan="4">ΠΡΟΣΩΡΙΝΗ ΘΕΣΗ</th>
            </tr>
            <tr>
                <th>AA</th>
                <th>Α/Μ</th>
                <th>Ονοματεπώνυμο</th>
                <th>Παρατηρήσεις</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($temporaryEmployees as $employee)
                <tr>
                    <td class="text-right">{{ $i++ }}</td>
                    <td class="text-right">{{ $employee->compass_id }}</td>
                    <td>{{ $employee->fullname }}</td>
                    <td>
                    @if ($employee->unit_id  != $employee->position_unit_id)
                        Υπηρετεί: {{ $employee->compass_unit_name . $employee->compass_department_name }}
                    @endif
                    @if (!empty($employee->rank_id))
                        {{ $employee->rank->name }}
                    @endif
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    @if (!$notPlacedEmployees->isEmpty())
        <br>
        <table class="table table-bordered table-striped table-hover" cellspacing="0">
            <thead>
            <tr>
                <th colspan="4">ΧΩΡΙΣ ΧΑΡΑΚΤΗΡΙΣΜΟ ΘΕΣΗΣ</th>
            </tr>
            <tr>
                <th>AA</th>
                <th>Α/Μ</th>
                <th>Ονοματεπώνυμο</th>
                <th>Παρατηρήσεις</th>
            </tr>
            </thead>
            <tbody>
            @foreach ($notPlacedEmployees as $employee)
                <tr>
                    <td class="text-right">{{ $i++ }}</td>
                    <td class="text-right">{{ $employee->compass_id }}</td>
                    <td>{{ $employee->fullname }}</td>
                    <td>
                        @if ($employee->unit_id  != $employee->position_unit_id)
                            Υπηρετεί: {{ $employee->compass_unit_name . $employee->compass_department_name }}
                        @endif
                        @if (!empty($employee->rank_id))
                            {{ $employee->rank->name }}
                        @endif
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>
    @endif

    @if (!$secondedEmployees->isEmpty())
    <br>
    <table class="table table-bordered table-striped table-hover" cellspacing="0">
        <thead>
            <tr>
                <th colspan="4">ΑΠΟ ΑΛΛΗ ΥΠΗΡΕΣΙΑ</th>
            </tr>
            <tr>
                <th>AA</th>
                <th>ΑΜ</th>
                <th>Ονοματεπώνυμο</th>
                <th>Παρατηρήσεις</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($secondedEmployees as $employee)
                <tr>
                    <td class="text-right">{{ $i++ }}</td>
                    <td class="text-right">{{ $employee->compass_id }}</td>
                    <td>{{ $employee->fullname }}</td>
                    <td>
                    Οργ.Εντ.: {{ $employee->compass_position_unit_name . $employee->compass_position_department_name }}
                    @if (!empty($employee->rank_id))
                        -- {{ $employee->rank->name }}
                    @endif
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    @endif
</div>
@endsection
