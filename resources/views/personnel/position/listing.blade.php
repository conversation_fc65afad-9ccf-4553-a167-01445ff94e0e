@extends('layouts.personnel')



@section('main-content')

  <div class="row">
    <div class="col-xs-12">
      <div class="box box-primary" id="edit_positions_box" style="display:none">
        <div class="box-header with-border">
          <h3 class="box-title" id="edit_form_dscr">Επεξεργασία</h3>
        </div>
        <form id="edit_positions_frm" action="{{ route('personnel.position.update', ['0']) }}" method="POST">
          @csrf
          @method('PUT')
          <div class="box-body">
            <input type="hidden" name="edit_position_id" value="" id="edit_position_id">
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label for="edit_organikes_nr" class="control-label" id="test1">Οργανικές:</label>
                  <input type="number" name="edit_organikes_nr" id="edit_organikes_nr" class="form-control" min="0"
                         max="1000">
                </div>
              </div>
              <div class="col-sm-6">
                <div class="form-group">
                  <label for="edit_desmevmenes_nr" class="control-label">Δεσμευμένες:</label>
                  <input type="number" name="edit_desmevmenes_nr" id="edit_desmevmenes_nr" class="form-control" min="0"
                         max="1000">
                </div>
              </div>
            </div>
          </div>
          <div class="box-footer">
            <div class="row">
              <div class="col-sm-3">
                <button type="reset" class="btn btn-default btn-block">
                  <i class="fa fa-arrow-circle-left"></i> Ακύρωση
                </button>
              </div>
              <div class="col-sm-6 col-sm-offset-3">
                <button type="submit" class="btn btn-warning btn-block">
                  <i class="fa fa-check-circle"></i> Αποθήκευση
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-xs-12">
      <div class="box box-primary">
        <div class="box-header">
          <h3 class="box-title">Προβολή Οργανικών Θέσεων Προσωπικού</h3>
          <div class="box-tools pull-right">
            <a href="{{ URL::route('personnel.position.index') }}" class="btn btn-info pull-right"><i
                class="fa fa-search"></i> Νέα Αναζήτηση</a>
          </div>
        </div>

        <div class="box-body">
          <table class="table small table-bordered table-striped table-hover" id="searchresults_tbl" style="width:100%">
            <thead>
            <tr>
              <th>Υπηρεσία</th>
              <th>Κατηγορία</th>
              <th>Κλάδος</th>
              <th>Οργανικές</th>
              <th>Προσωρινές</th>
              <th>Υπηρετ</th>
              <th>Κενές</th>
              <th>Δεσμευ</th>
              {{--                            <th>Διαθεσ</th>--}}
              <th style="width: 120px;"><i class="fa fa-cog"></i></th>
            </tr>
            </thead>
            <tbody>
            @foreach ($positions as $position)
              <tr orgcd="{{ $position->id }}" id="{{ $position->id }}">
                <td class="org_yp">
                  {{ $position->unit->abbrv ?? '' }}
                  {!! isset($position->department->name) ? '<br>'.$position->department->name : '' !!}
                </td>
                <td class="org_kat">
                  {{ $position->occupation->name ?? '' }}
                </td>
                <td class="org_dscr">
                  {{ $position->specialization->fullname ?? '' }}

                </td>
                <td class="org_nr">{{ $position->org_nr }}</td>
                <td class="pros_nr">{{ $position->epet_pros_nr }}</td>
                <td class="yphr_nr">{{ $position->epet_yphr_nr }}</td>
                <td class="kenes_nr {{ ($position->unfilled < 0 ? 'danger' : '') }}">{{ $position->unfilled }}</td>
                <td class="desm_nr">{{ $position->desm_nr ?? '0'}}</td>
                <td style="width: 150px;display: flex;justify-content: space-around;">
                  @can('personnel.update')
                    <a onClick="setupEditPositionsFrm('{{$position->id}}');" class="action-button"><i
                        class="fa fa-2x fa-edit"></i></a>
                  @endcan
                  <a href="{{ URL::route('personnel.position.employees-modal', $position->id) }}"
                     id="personnelDetails_{{$position->id}}" class="action-button personnel-modal"
                     data-toggle="modal" data-remote="false" data-target="#myModal"><i
                      class="fa fa-2x fa-user"></i></a>
                  <a href="{{ URL::route('personnel.position.employees-pdf', $position->id) }}"
                     target="_blank" class="action-button"><i class="fa fa-2x fa-file-pdf-o"></i></a>
                  @can('personnel.delete')
                    @include('partials.btnDelete', ['title' => 'DEL', 'path' => route('personnel.position.destroy', $position->id )])
                  @endcan
                </td>
              </tr>
            @endforeach
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  @yield("footer_links")

  <!-- Bootstrap Modal placeholder for showing employees of position -->
  <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
      </div>
    </div>
  </div>
@endsection

@section('page-scripts')
  <script>
    // Set values and text of edit form
    var setupEditPositionsFrm = function (id) {
      var org = parseInt($("tr#" + id + " td.org_nr").text(), 10);
      var desm = parseInt($("tr#" + id + " td.desm_nr").text(), 10);
      var dscr = $("tr#" + id + " td.org_yp").text() + ': ' + $("tr#" + id + " td.org_kat").text() + ' ' + $("tr#" + id + " td.org_dscr").text();
      $("#edit_position_id").val(id);
      $("#edit_organikes_nr").val(org);
      $("#edit_desmevmenes_nr").val(desm);
      $("#edit_form_dscr").text(dscr);
      //show_form('edit_organikes');
      $('#edit_positions_box').hide();
      $('#edit_positions_box').show("slow");
    };

    $(document).ready(function () {
      // Setup ajax headers to include csrf token
      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': window.Laravel.csrfToken
        }
      });

      // Fill modal with content from link href
      $("#myModal").on("show.bs.modal", function (e) {
        var link = $(e.relatedTarget);
        $(this).find(".modal-content").load(link.attr("href"));
      });

      // Update DB from pop-up form of position numbers.
      $('form#edit_positions_frm').on('submit', function (event) {
        // Stop form from submitting normally
        event.preventDefault();
        var org_id = $('#edit_position_id').val();
        var org_nr = $('#edit_organikes_nr').val();
        var desm_nr = $('#edit_desmevmenes_nr').val();
        var pros_nr = $("tr#" + org_id + " td.pros_nr").text();
        var yphr_nr = $("tr#" + org_id + " td.yphr_nr").text();
        var kenes_nr = parseInt(org_nr, 10) + parseInt(pros_nr, 10) - parseInt(yphr_nr, 10) - parseInt(desm_nr, 10);

        if ((!isNaN(org_nr) && !isNaN(desm_nr)) && (org_nr && desm_nr)) {
          //ajax post the form
          $.ajax({
            url: "/personnel/position/" + org_id,
            type: 'PUT',
            data: {org: org_nr, desm: desm_nr},
            success: function (data) {
              // Show a success messagge
              swal({
                title: "Επιτυχία!",
                text: "Ο αριθμός θέσεων ενημερώθηκε επιτυχώς.",
                type: "success",
                timer: 1700,
                showConfirmButton: false
              });

              // Update the table
              $("tr#" + org_id + " td.org_nr").text(org_nr);
              $("tr#" + org_id + " td.desm_nr").text(desm_nr);
              $("tr#" + org_id + " td.kenes_nr").text(kenes_nr);
              if (kenes_nr < 0) {
                $("tr#" + org_id + " td.kenes_nr").addClass('danger');
              } else {
                $("tr#" + org_id + " td.kenes_nr").removeClass('danger');
              }

              // Hide the form
              $('#edit_positions_box').hide("slow");
            }
          });
        } else {
          swal("Δεν δώσατε αριθμό θέσεων!");
        }
      });

      $('form#edit_positions_frm').bind("reset", function (e) {
        $('#edit_positions_box').hide("slow");
      });

      // Datatable
      $('#searchresults_tbl').dataTable({
        dom: "<'row'<'col-sm-6'l><'col-sm-6 text-right'B>>frtip",
        buttons: [
          {
            text: '<i class="fa fa-print" aria-hidden="true"></i> Εκτύπωση',
            extend: 'print',
            exportOptions: {
              columns: '0,1,2,3,4,5,6,7'
            },
            className: 'btn btn-default btn-flat btn-xs',
          },
          {
            text: '<i class="fa fa-file-excel-o" aria-hidden="true"></i> Excel',
            extend: 'excel',
            exportOptions: {
              columns: '0,1,2,3,4,5,6,7'
            },
            className: 'btn btn-default btn-flat btn-xs',
          },
          {
            text: '<i class="fa fa-file-pdf-o" aria-hidden="true"></i> PDF',
            extend: 'pdf',
            exportOptions: {
              columns: '0,1,2,3,4,5,6,7'
            },
            className: 'btn btn-default btn-flat btn-xs',
          }
        ],
      });
    });

  </script>
@endsection
