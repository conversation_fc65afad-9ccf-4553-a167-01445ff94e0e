@extends('personnel.position.listing')

@section('contentheader_description')
Οργανικές Θέσεις
@endsection

@section('footer_links')
<div class="row">
    <div class="col-sm-3">
        <a class="btn btn-default btn-block" href="{{ URL::route('personnel.home') }}"><i class="fa fa-arrow-circle-left"></i> Επιστροφή</a>
    </div>

    @canany(['personnel.readAll', 'personnel.update'])
        <div class="col-sm-3 col-sm-offset-3">
            <a class="btn btn-info btn-block" href="{{ URL::route('personnel.position.totalsByUnit-pdf', Request::all()) }}"><i class="fa fa-file-pdf-o"></i> Θέσεις Ανά Υπηρεσία και Κλάδο</a>
        </div>
        <div class="col-sm-3">
            <a class="btn btn-info btn-block" href="{{ URL::route('personnel.position.totalsBySpecialization-pdf', Request::all()) }}"><i class="fa fa-file-pdf-o"></i> Θέσεις Ανά Κλάδο</a>
        </div>
    @else
        <div class="col-sm-6 col-sm-offset-3">
            <a class="btn btn-info btn-block" href="{{ URL::route('personnel.position.totalsBySpecialization-pdf', Request::all()) }}"><i class="fa fa-file-pdf-o"></i> Θέσεις Ανά Κλάδο</a>
        </div>
    @endcanany
</div>
@endsection
