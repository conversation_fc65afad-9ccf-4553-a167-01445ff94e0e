@extends('layouts.personnel')

@section('contentheader_description')
  Οργανικές Θέσεις
@endsection

@section('main-content')
  <div>
    <div class="row">
      <div class="col-sm-12">
        <!-- Search Form -->
        <div class="box box-primary box-solid">
          <div class="box-header">
            <h3 class="box-title">{{ $title }}</h3>
          </div>
          <form action="{{ route($resultRoute) }}" method="POST">
            @csrf
            <div class="box-body">
              <h4 class="form-section">Ειδικότητα</h4>
              <div class="row">
                <div class="col-md-3">
                  <div class="form-group">
                    <label for="occupation" class="control-label">Σχέση Εργασίας:</label>
                    <select
                      name="occupation"
                      id="occupation"
                      class="form-control select2">
                      <option value="">Επιλέξτε...</option>
                      @foreach(\App\Models\Personnel\Occupation::pluck('name', 'id') as $id => $name)
                        <option value="{{ $id }}">
                          {{ $name }}
                        </option>
                      @endforeach
                    </select>
                  </div>
                </div>
                @canany(['personnel.readAll','personnel.update'])
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="hroffice" class="control-label">Γραφείο:</label>
                      <select
                        name="hroffice"
                        id="hroffice"
                        class="form-control select2">
                        <option value="">Επιλέξτε...</option>
                        @foreach(\App\Models\Personnel\Hroffice::where('occupation_id', '!=', 2)->pluck('name', 'id') as $id => $name)
                          <option value="{{ $id }}">
                            {{ $name }}
                          </option>
                        @endforeach
                      </select>
                    </div>
                  </div>
                @endcanany
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="specialization" class="control-label">Κλάδος/Ειδικότητα:</label>
                    <select
                      name="specialization"
                      id="specialization"
                      class="form-control select2">
                      <option value="">Επιλέξτε...</option>
                      @foreach(\App\Models\Personnel\Specialization::orderBy('compass_id', 'asc')->get()->pluck('compass_name', 'id') as $id => $name)
                        <option value="{{ $id }}">
                          {{ $name }}
                        </option>
                      @endforeach
                    </select>
                  </div>
                </div>
                <div class="col-md-3">
                  <br>
                  <div class="form-group">
                    <div class="checkbox">
                      <label>
                        @canany(['personnel.readAll', 'personnel.update'])
                          <input
                            type="checkbox"
                            name="guards_hidden"
                            id="guards_hidden"
                            value="1"
                            checked
                            class="is-iCheck"
                          />
                          <strong>Απόκρυψη Φυλάκων</strong>
                        @else
                          <input
                            type="checkbox"
                            name="guards_hidden"
                            id="guards_hidden"
                            value="1"
                            @checked(old('guards_hidden', 1))
                            class="is-iCheck"
                          >
                          <strong>Απόκρυψη Φυλάκων</strong>
                        @endcanany
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12">
                  <tree-view-input
                    input-name="unit_id"
                    :show-collapsed="{{json_encode(false)}}"
                    :input-value="{{Auth::user()->can('personnel.readAll') ? collect([])->toJson() : collect([Auth::user()->unit])->toJson()}}"
                    authorize-for="personnel"
                  ></tree-view-input>
                </div>
              </div>
            </div>
            <div class="box-footer">
              <div class="row">
                <div class="col-sm-3">
                  <a class="btn btn-default btn-block" href="{{ url('personnel') }}">
                    <i class="fa fa-arrow-circle-left"></i> Επιστροφή
                  </a>
                </div>
                <div class="col-sm-6 col-sm-offset-3">
                  <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block">
                      <i class="fa fa-search"></i> Αναζήτηση
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection

@section('page-scripts')
  @parent
{{--  <script src="{{mix('js/admin/user/index.js')}}"></script>--}}
  <script>
    // $(document).ready(function() {
    //
    //     $('#occupation').change(function() {
    //         // Update specializations select options based on occupation
    //         var $url = '/personnel/specialization/select_by_occupation/' + $('#occupation').val();
    //         updateSelectOptions($('#occupation').val(), $('#specialization'), $url, 'id', 'compass_name');
    //
    //         // Update hroffices select options based on occupation
    //         $url = '/personnel/hroffice/select_by_occupation/' + $('#occupation').val();
    //         updateSelectOptions($('#occupation').val(), $('#hroffice'), $url);
    //     });
    //
    //     $('#hroffice').change(function() {
    //         // Update specialization select options based on hroffice
    //         var $url = '/personnel/specialization/select_by_hroffice/' + $('#hroffice').val();
    //         updateSelectOptions($('#hroffice').val(), $('#specialization'), $url, 'id', 'compass_name');
    //     });
    // });
    // export default {
    //   components: {HTTPOrganogramTreeView},
    // };
  </script>
@endsection
