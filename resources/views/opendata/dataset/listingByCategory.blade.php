@extends('layouts.opendata')

@section('contentheader_description')
  Σύνολα Δεδομένων ανά Κατηγορία
@endsection

@section('main-content')
  <div>
    <div class="row">
      <div class="col-sm-12">
        <form action="{{ route('opendata.dataset.bycategory') }}" method="post">
          <div class="box box-primary">
            <div class="box-header">
              <h3 class="box-title">Επιλογή Υπηρεσίας</h3>
            </div>
            <div class="box-body">
              <tree-view-input
                input-name="filtered_unit_id"
                :allow-showing-departments="false"
                :show-collapsed="true"
                authorize-for="opendata"
                :input-value="{{json_encode($filteredUnits)}}"
              ></tree-view-input>
            </div>
            <div class="box-footer">
              <div class="row">
                <div class="col-sm-4 col-sm-offset-8">
                  <button type="submit" class="btn btn-primary btn-block">Αναζήτηση</button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">A. Ανοιχτά Μηχαναγνώσιμα</h3>
          </div>
          <div class="box-body">
            @if (count($datasets['cat_a']) > 0)
              @include('opendata.dataset._listingTable', ['datasets' => $datasets['cat_a']])
            @else
              <p>Δε βρέθηκαν Σύνολα Δεδομένων.</p>
            @endif
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">B. Ανοιχτά χωρίς δικτυακή διάθεση</h3>
          </div>
          <div class="box-body">
            @if (count($datasets['cat_b']) > 0)
              @include('opendata.dataset._listingTable', ['datasets' => $datasets['cat_b']])
            @else
              <p>Δε βρέθηκαν Σύνολα Δεδομένων.</p>
            @endif
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">Γ. Διαθέσιμα με Τέλη ή Αδειοδότηση</h3>
          </div>
          <div class="box-body">
            @if (count($datasets['cat_c']) > 0)
              @include('opendata.dataset._listingTable', ['datasets' => $datasets['cat_c']])
            @else
              <p>Δε βρέθηκαν Σύνολα Δεδομένων.</p>
            @endif
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">Δ. Κλειστά (μη διαθέσιμα)</h3>
          </div>
          <div class="box-body">
            @if (count($datasets['cat_d']) > 0)
              @include('opendata.dataset._listingTable', ['datasets' => $datasets['cat_d']])
            @else
              <p>Δε βρέθηκαν Σύνολα Δεδομένων.</p>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>
@endsection

@section('page-scripts')
  <script src="{{ mix('js/opendata/dataset_table.js') }}" type="text/javascript"></script>
  <script src="{{ mix('js/opendata/index.js') }}" type="text/javascript"></script>
@endsection
