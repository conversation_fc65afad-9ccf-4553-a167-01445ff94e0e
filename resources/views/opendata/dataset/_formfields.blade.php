<div class="row">
    <div class="col-sm-6 col-xs-12">
        <div class="form-group">
          <label for="name" class="control-label">Τίτλος: *</label>
          <input type="text" name="name" id="name" class="form-control" required value="{{ old('name', $dataset->name) }}">
        </div>
        <div class="form-group">
          <label for="description" class="control-label">Περιγραφή: *</label>
          <textarea name="description" id="description" rows="4" class="form-control" required>{{ old('description', $dataset->description) }}</textarea>
        </div>
    </div>
    <div class="col-sm-6 col-xs-12">
        <div class="form-group">
          <label for="location" class="control-label">Θέση Αρχείου/Κάτοχος: *</label>
            <button type="button" class="btn btn-info btn-circle btn-xs" data-container="body" data-toggle="popover" data-placement="right" data-content="Η Θέση/κάτοχος όπου βρίσκεται το σύνολο δεδομένων. Πχ. Υπηρεσία, Διεύθυνση, Κτίριο.">
              <i class="fa fa-info"></i>
            </button>
          <input type="text" name="location" id="location" class="form-control" required value="{{ old('location', $dataset->location) }}">

        </div>
        <div class="form-group">
          <label for="records" class="control-label">Αριθμός Εγγραφών:</label>
            <button type="button" class="btn btn-info btn-circle btn-xs" data-container="body" data-toggle="popover" data-placement="right" data-content="Ο αριθμός των εγγραφών κατά προσέγγιση. Αν δεν τον γνωρίζετε, αφήστε το κενό.">
              <i class="fa fa-info"></i>
            </button>
          <input type="text" name="records" id="records" class="form-control" value="{{ old('records', $dataset->records) }}">
        </div>
        <div class="form-group">
          <label for="updaterate_id" class="control-label">Ρυθμός Ανανέωσης:</label>
          <select name="updaterate_id" id="updaterate_id" class="form-control select2">
            <option value="">Επιλέξτε...</option>
            @foreach($updaterates as $value => $label)
              <option value="{{ $value }}" @selected(old('updaterate_id', $dataset->updaterate_id) == $value)>
                {{ $label }}
              </option>
            @endforeach
          </select>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-6 col-xs-12">
        <div class="form-group">
          <label for="type_id" class="control-label">Τύπος: *</label>
          <select name="type_id" id="type_id" class="form-control select2" required>
            <option value="">Επιλέξτε...</option>
            @foreach(\App\Models\Opendata\Type::all() as $type)
              <option value="{{ $type->id }}" @selected(old('type_id', $dataset->type_id ?? null) == $type->id)>
                {{ $type->name }}
              </option>
            @endforeach
          </select>
        </div>
        <div id="digital_details" style="display: none">
            <div class="form-group">
              <label for="filetypes" class="control-label">Μορφή Αρχείου(ων): *</label>
              <select name="filetypes[]" id="filetypes" class="form-control select2" multiple style="width: 100%">
                @foreach($filetypes as $value => $label)
                  <option
                    value="{{ $value }}"
                    @selected(in_array($value, old('filetypes', $dataset->filetypes->pluck('id')->toArray() ?? [])))
                  >
                    {{ $label }}
                  </option>
                @endforeach
              </select>
            </div>
            <div class="form-group">
              <label for="digital_distribution" class="control-label">Τρόπος Διάθεσης (αν διατίθεται):</label>
              <input type="text" name="digital_distribution" id="digital_distribution" class="form-control" value="{{ old('digital_distribution', $dataset->digital_distribution) }}">
            </div>
            <div class="form-group">
              <label for="url" class="control-label">Url:</label>
              <input type="text" name="url" id="url" class="form-control" value="{{ old('url', $dataset->url ?? null) }}">
            </div>
            <div class="form-group">
              <label for="api" class="control-label">Api:</label>
              <input type="text" name="api" id="api" class="form-control" value="{{ old('api', $dataset->api ?? null) }}">
            </div>
        </div>
        <div id="physical_details" style="display: none;">
            <div class="form-group">
              <label for="physical_format" class="control-label">Μορφή (πχ. αρχείο εγγράφων Α4, αρχείο φωτογραφιών): *</label>
              <input type="text" name="physical_format" id="physical_format" class="form-control" value="{{ old('physical_format', $dataset->physical_format ?? null) }}">
            </div>
            <div class="form-group">
              <label for="physical_distribution" class="control-label">Τρόπος Διάθεσης (αν διατίθεται):</label>
              <input type="text" name="physical_distribution" id="physical_distribution" class="form-control" value="{{ old('physical_distribution', $dataset->physical_distribution ?? null) }}">
            </div>
            <div class="form-group">
                <div class="checkbox">
                  <label>
                    <input type="checkbox" name="e_application" id="e_application" class="is-iCheck" value="1"
                      @checked(old('e_application', $dataset->e_application ?? false))>
                    <strong>Διάθεση Μέσω Ηλεκτρονικής Αίτησης</strong>
                  </label>
                </div>
            </div>
            <div class="form-group">
                <div class="checkbox">
                  <label>
                    <input type="checkbox" name="application" id="application" class="is-iCheck" value="1"
                      @checked(old('application', $dataset->application ?? false))>
                    <strong>Διάθεση Μέσω Έντυπης Αίτησης</strong>
                  </label>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-xs-12">
        <div class="form-group">
            <label>Διάθεση
                <button type="button" class="btn btn-info btn-circle btn-xs" data-container="body" data-toggle="popover" data-placement="right" data-content="Επιλέξτε ΝΑΙ αν το σύνολο διατίθεται ήδη, ή μπορεί να διατεθεί άμεσα. Αν όχι επιλέξτε το λόγο από τα παρακάτω.">
                  <i class="fa fa-info"></i>
                </button>
            </label>
            <br>
          <input
            type="checkbox"
            name="obtainable"
            id="obtainable"
            class="switch"
            data-group-cls="btn-group-sm"
            value="1"
            @checked(old('obtainable', $dataset->obtainable ?? false))
          >
        </div>
        <div class="form-group" id="obtainable_details">
            <label>Αν Όχι, Αιτιολόγηση: *</label>
          @foreach($unobtainable_reasons as $unobtainable_reason)
            <div class="checkbox">
              @if ($unobtainable_reason->open)
                <label>
                  <input type="checkbox"
                         name="unobtainable_reasons[]"
                         id="unobtainable_reason_{{ $unobtainable_reason->id }}"
                         value="{{ $unobtainable_reason->id }}"
                         class="has_other is-iCheck"
                    @checked(in_array($unobtainable_reason->id, old('unobtainable_reasons', $dataset->unobtainable_reasons->pluck('id')->toArray() ?? [])))>
                  {{ $unobtainable_reason->name }}.
                </label>
                <input type="text"
                       name="unobtainable_reason_other"
                       id="unobtainable_reason_other"
                       class="input_other"
                       value="{{ old('unobtainable_reason_other', $dataset->unobtainable_reason_other ?? null) }}">
              @else
                <label>
                  <input type="checkbox"
                         name="unobtainable_reasons[]"
                         id="unobtainable_reason_{{ $unobtainable_reason->id }}"
                         value="{{ $unobtainable_reason->id }}"
                         class="is-iCheck"
                    @checked(in_array($unobtainable_reason->id, old('unobtainable_reasons', $dataset->unobtainable_reasons->pluck('id')->toArray() ?? [])))>
                  {{ $unobtainable_reason->name }}.
                </label>
              @endif
            </div>
          @endforeach
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-6 col-xs-12">
      <div class="form-group">
        <label>Εμπεριέχονται στο Σύνολο Προσωπικά Δεδομένα</label>
        <br>
        <input type="checkbox"
               name="personaldata"
               id="personaldata"
               class="switch"
               data-group-cls="btn-group-sm"
               value="1"
          @checked(old('personaldata', $dataset->personaldata ?? false))>
      </div>

      <div class="form-group" id="personaldata_details" style="display: none">
        <label for="personaldata_info" class="control-label">Περιγραφή/Αιτιολόγηση: *</label>
        <input type="text"
               name="personaldata_info"
               id="personaldata_info"
               class="form-control"
               value="{{ old('personaldata_info', $dataset->personaldata_info ?? null) }}">
      </div>
    </div>
    <div class="col-sm-6 col-xs-12">
        <div class="form-group">
            <label>Συντρέχουν οι λοιποί περιορισμοί του Αρ.3 του Ν.4305/14</label>
            <br>
          <input
            type="checkbox"
            name="restricted"
            id="restricted"
            class="switch"
            data-group-cls="btn-group-sm"
            value="1"
            @checked(old('restricted', $dataset->restricted ?? false))
          >
        </div>
        <div class="form-group" id="restricted_details" style="display: none">
            <label>Αν Ναι, Αιτιολόγηση: *</label>
          @foreach($restrictions as $restriction)
            <div class="checkbox">
              @if ($restriction->open)
                <label>
                  <input type="checkbox"
                         name="restrictions[]"
                         id="restriction_{{ $restriction->id }}"
                         value="{{ $restriction->id }}"
                         class="has_other is-iCheck"
                    @checked(in_array($restriction->id, old('restrictions', $dataset->restrictions->pluck('id')->toArray() ?? [])))>
                  {{ $restriction->name }}.
                </label>
                <input type="text"
                       name="restriction_other"
                       id="restriction_other"
                       class="input_other"
                       value="{{ old('restriction_other', $dataset->restriction_other ?? null) }}">
              @else
                <label>
                  <input type="checkbox"
                         name="restrictions[]"
                         id="restriction_{{ $restriction->id }}"
                         value="{{ $restriction->id }}"
                         class="is-iCheck"
                    @checked(in_array($restriction->id, old('restrictions', $dataset->restrictions->pluck('id')->toArray() ?? [])))>
                  {{ $restriction->name }}.
                </label>
              @endif
            </div>
          @endforeach
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-6 col-xs-12">
        <div class="form-group">
            <label>Διάθεση Μέσω Τελών</label>
            <br>
          <input
            type="checkbox"
            name="fees"
            id="fees"
            class="switch"
            data-group-cls="btn-group-sm"
            value="1"
            @checked(old('fees', $dataset->fees ?? false))
          >
        </div>
    </div>
    <div class="col-sm-6 col-xs-12">
        <div class="form-group">
            <label>Διάθεση Μέσω Αδειοδότησης</label>
            <div class="checkbox">
              <input
                type="checkbox"
                name="licenced"
                id="licenced"
                class="switch"
                data-group-cls="btn-group-sm"
                value="1"
                @checked(old('licenced', $dataset->licenced ?? false))
              >
            </div>
        </div>
        <div class="form-group" id="licence_details">
          <label for="licence_id" class="control-label">Άδεια:</label>
          <select name="licence_id" id="licence_id" class="form-control select2">
            <option value="">Επιλέξτε...</option>
            @foreach($licences as $value => $label)
              <option value="{{ $value }}" @selected(old('licence_id', $dataset->licence_id ?? null) == $value)>
                {{ $label }}
              </option>
            @endforeach
          </select>
        </div>
    </div>
</div>
<div class="row">
  <div class="col-sm-6 col-xs-12">
    <div class="form-group">
      <label for="comments" class="control-label">Λοιπά σχόλια:</label>
      <textarea
        name="comments"
        id="comments"
        rows="4"
        class="form-control">{{ old('comments', $dataset->comments ?? null) }}
      </textarea>
    </div>
  </div>

  <div class="col-sm-6 col-xs-12">
    <div class="form-group">
      <label for="user_id" class="control-label">Καταχώρηση Από Χρήστη:</label><br>
      {{ Auth::user()->username }} ({{ Auth::user()->name }}).
      <input type="hidden" name="user_id" value="{{ Auth::user()->id }}">
    </div>
  </div>
</div>
<div class="row">
    <div class="col-sm-12">
        @if (auth()->user()->can('opendata.update'))
            <tree-view-input
                input-name="unit"
                :input-value="{{ json_encode($unit ?? [])}}"
                :allow-selecting-multiple-units="false"
                :allow-showing-departments="false"
                authorize-for="opendata"
            ></tree-view-input>
        @else
          <label>Υπηρεσία:</label>
          <p>{{ auth()->user()->unit->name }}</p>
          <input type="hidden" name="unit" value="{{ json_encode([auth()->user()->unit_id]) }}">
        @endif
    </div>
</div>
