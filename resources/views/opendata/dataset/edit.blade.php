@extends('layouts.opendata')

@section('contentheader_description')
Επεξεργασία Συνόλου Δεδομένων
@endsection


@section('main-content')
    <div>
	<div class="row">
		<div class="col-xs-12">
	        <div class="box box-primary">
	            <div class="box-header">
	                <h3 class="box-title">Επεξεργασία Συνόλου Δεδομένων</h3>
	            </div>
	            <div class="box-body">
                <form action="{{ route('opendata.dataset.update', $dataset->id) }}" method="POST">
                  @csrf
                  @method('PUT')
			            @include('opendata.dataset._formfields')
                    <br>
                    <div class="row">
                        <div class="col-sm-4">
                            <a class="btn btn-block btn-default" href="{{ route('opendata.home') }}">Επιστροφή</a>
                        </div>
                        <div class="col-sm-4 col-sm-offset-4">
                          <button type="submit" class="btn btn-block btn-primary">Ενημέρωση</button>
                        </div>
                    </div>
                </form>
	            </div>
	        </div>
		</div>
	</div>
    </div>
@endsection

@section('page-scripts')
<script src="{{ mix('js/opendata/dataset_form.js') }}" type="text/javascript"></script>
<script src="{{ mix('js/opendata/index.js') }}" type="text/javascript"></script>
@endsection
