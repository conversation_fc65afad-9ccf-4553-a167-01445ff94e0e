@extends('layouts.opendata')

@section('main-content')
    <div class="row">
        @include('partials.appIconButton', [
            'url' => route('opendata.dataset.directoratelisting'),
            'title' => 'Σύνολα Δεδομένων Υπηρεσίας',
            'description' => auth()->user()->unit->abbrv,
            'color' => '52615C',
            'icon' => 'fa-cubes'
        ])
        @include('partials.appIconButton', [
            'url' => route('opendata.dataset.bycategory'),
            'title' => 'Σύνολα Δεδομένων ανά Κατηγορία',
            'description' => '',
            'color' => '52615C',
            'icon' => 'fa-list-alt'
        ])
        @include('partials.appIconButton', [
            'url' => route('opendata.dataset.create'),
            'title' => 'Καταχώρηση Συνόλου Δεδομένων',
            'description' => '',
            'color' => '9CB0AD',
            'icon' => 'fa-plus-square-o'
        ])
        @include('partials.appIconButton', [
            'url' => route('opendata.dataset.search'),
            'title' => 'Αναζήτηση Συνόλου Δεδομένων',
            'description' => '',
            'color' => 'CCD6D7',
            'icon' => 'fa-search'
        ])
    </div>
    @can('opendata.admin')
        <div class="row">
            <div class="col-sm-12">
                <div class="col-sm-12">
                    <h3 class="home-section__title">Διαχείριση Εφαρμογής</h3>
                </div>
            </div>
        </div>
        <div class="row">
            @include('partials.appIconButton', [
                'url' => route('opendata.filetype.index'),
                'title' => 'Είδη Ψηφιακών Αρχείων',
                'description' => '',
                'color' => 'EB9887',
                'icon' => 'fa-files-o'
            ])
            @include('partials.appIconButton', [
                'url' => route('opendata.updaterate.index'),
                'title' => 'Ρυθμοί Ανανέωσης Συνόλων',
                'description' => '',
                'color' => 'EB9887',
                'icon' => 'fa-refresh'
            ])
            @include('partials.appIconButton', [
                'url' => route('opendata.licence.index'),
                'title' => 'Άδειες Διάθεσης Συνόλων',
                'description' => '',
                'color' => 'EB9887',
                'icon' => 'fa-certificate'
            ])
        </div>
    @endcan
@endsection
