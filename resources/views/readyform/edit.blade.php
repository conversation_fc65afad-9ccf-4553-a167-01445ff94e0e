@extends($bladeLayout)

@section('main-content')
  <div class="row">
    <div class="col-sm-12 col-lg-10 col-lg-offset-1">
      <div class="box box-primary box-solid">
        <div class="box-header">
          <h3 class="box-title">{{ $title }} - Επεξεργασία</h3>
        </div>

        <form
          action="{{ route("$route.update", $entity->id) }}"
          method="POST"
        >
          @csrf
          @method('PATCH')

          <div class="box-body">
            <div class="row">
              <div class="col-sm-12">
                @include('readyform.form')
              </div>
            </div>
          </div>
          <div class="box-footer">
            <div class="row">
              {{-- Back to resource index --}}
              <div class="col-sm-2">
                <a href="{{ route("$route.index") }}" class="btn btn-default btn-block">
                  <i class='fa fa-arrow-circle-left'></i> Επιστροφή
                </a>
              </div>

              {{-- Cancel and go back to resource show --}}
              <div class="col-sm-2">
                <a href="{{ route("$route.show", $entity) }}" class="btn btn-default btn-block">
                  <i class='fa fa-ban'></i> Ακύρωση
                </a>
              </div>

              {{-- Submit --}}
              <div class="col-sm-8">
                <button type="submit" class="btn btn-primary btn-block">
                  <i class='fa fa-check-circle'></i> Αποθήκευση
                </button>
              </div>

            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
@endsection
