@extends($bladeLayout)

@section('main-content')

  <div class="row">
    <div class="col-md-10 col-md-offset-1">
      <div class="box box-primary">
        <div class="box-header">
          <h3 class="box-title">{{ $title }} - Λίστα Εγγραφών</h3>
          @can($createPermission)
            <a class="box-tools pull-right">
              <a href="{{ route("$route.create" ) }}" class="btn btn-info pull-right">
                <i class="fa fa-plus"></i> Νέα εγγραφή
              </a>
            </a>
          @endcan
        </div>
        <div class="box-body">
          <table class="table table-condensed apptree-dt">
            <thead>
            <tr>
              @foreach ($fields as $field)
                <th>{{$field['label']}}</th>
              @endforeach
              @if ($withTrashed)
                <th>Ανενεργό από</th>
              @endif
              <th class="text-center" style="white-space: nowrap;width: 150px">Ενέργειες</th>
            </tr>
            </thead>
            <tbody>
            @foreach ($entities as $entity)
              <tr>
                @foreach ($fields as $field)
                  {{-- <td>{{ $entity->{$field['name']} }}</td> --}}
                  <td>@include( "readyform.displays.{$field['type']}")</td>
                @endforeach

                @if ($withTrashed)
                  <td>{{ !empty($entity->deleted_at) ? $entity->deleted_at->format('d-m-Y') : '' }}</td>
                @endif

                <td class="text-center" style="white-space: nowrap;width: 150px">
                  @if (empty($entity->deleted_at))
                    {{-- Show --}}
                    <a href="{{ route("$route.show", $entity->id ) }}" class="btn btn-info">
                      <i class="fa fa-info-circle"></i>
                    </a>

                    @can($updatePermission)
                      {{-- Update --}}
                      <a href="{{ route("$route.edit", $entity->id ) }}" class="btn btn-warning">
                        <i class="fa fa-edit"></i>
                      </a>
                    @endcan

                    @can($deletePermission)
                      {{-- Delete --}}
                      <form
                        action="{{ route("$route.destroy", $entity->id) }}"
                        method="POST"
                        class="inline"
                      >
                        @csrf
                        @method('DELETE')
                        <button class="btn btn-danger delete-btn" type="submit">
                          <i class="fa fa-remove"></i>
                        </button>
                      </form>

                    @endcan
                  @elseif ($withTrashed)
                    @can($updatePermission)
                      {{-- Restore SoftDeleted --}}
                      <form
                        action="{{ route("$route.restore", $entity->id) }}"
                        method="POST"
                        class="inline"
                      >
                        @csrf
                        @method('PUT')
                        <button class="btn btn-success" type="submit">
                          <i class="fa fa-level-up"></i> Επαναφορά
                        </button>
                      </form>
                    @endcan
                  @endif
                </td>
              </tr>
            @endforeach
            </tbody>
          </table>
        </div>
        <div class="box-footer">
          <div class="row">
            {{-- Back to resource index --}}
            <div class="col-sm-3">
              <a href="{{ route(substr($route, 0, strpos($route, ".")).'.home') }}" class="btn btn-default btn-block">
                <i class='fa fa-arrow-circle-left'></i> Επιστροφή
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

@endsection
