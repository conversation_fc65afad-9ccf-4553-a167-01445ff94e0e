<!DOCTYPE html>
<html lang="el">

@include('layouts.partials.htmlheader')                                         {{-- @yield('htmlheader_title',) --}}

<body class="skin-gray sidebar-mini fixed" id="app_body">

{{-- Use this script to toggle admin-lte sidebar --}}
<script>
    if (localStorage.getItem('sidebar-collapse-toggle') === '1') {
        document.getElementById("app_body").className += " sidebar-collapse";
    }
</script>

<div id="app" class="wrapper" @yield('wrapper-attributes')>

    @include('layouts.partials.mainheader')

    @include('layouts.partials.sidebar')

    {{--Content Wrapper. Contains page content--}}
    <div class="content-wrapper">

        @include('layouts.partials.contentheader') {{-- @yield('contentheader_title'),  @yield('contentheader_description')  --}}

        {{--Main content--}}
        <section class="content">

            @include('layouts.partials.errors')
            @include('layouts.partials._flash')

            {{--Your Page Content Here--}}
            @yield('main-content')                                             {{-- @yield('main-content') --}}

        </section>

    </div>

    {{-- @include('layouts.partials.controlsidebar') --}}

    @include('layouts.partials.footer')

    <portal-target name="modal-container"></portal-target>
</div>

@include('layouts.partials.scripts')

@yield('page-scripts')
</body>
</html>
