<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <title>apptree | @yield('htmlheader_title', 'Your title here')</title>

        {{-- Font Awesome --}}
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

        <style>
            @font-face {
                font-family: 'OpenSans';
                font-style: normal;
                font-weight: 400;
                src: url({{ public_path('fonts/OpenSans-Regular.ttf') }})  format('truetype');
            }
            @font-face {
                font-family: 'OpenSans';
                font-style: normal;
                font-weight: bold;
                src: url({{ public_path('fonts/OpenSans-Bold.ttf') }})  format('truetype');
            }
            @font-face {
                font-family: 'OpenSans';
                font-style: italic;
                font-weight: 400;
                src: url({{ public_path('fonts/OpenSans-Italic.ttf') }})  format('truetype');
            }

            @font-face {
                font-family: 'RobotoCondensed';
                font-style: normal;
                font-weight: 400;
                src: url({{ public_path('fonts/RobotoCondensed-Regular.ttf') }})  format('truetype');
            }
            @font-face {
                font-family: 'RobotoCondensed';
                font-style: normal;
                font-weight: bold;
                src: url({{ public_path('fonts/RobotoCondensed-Bold.ttf') }})  format('truetype');
            }
            @font-face {
                font-family: 'RobotoCondensed';
                font-style: italic;
                font-weight: 400;
                src: url({{ public_path('fonts/RobotoCondensed-Italic.ttf') }})  format('truetype');
            }
        </style>
        <link href="{{ public_path('css/pdf.css') }}" rel="stylesheet" type="text/css"/>
        @if (isset($snappy) && $snappy == true)
            {{--TODO: should I use public_path()  instead of asset() in snappy pdf as well--}}
            <link href="{{ public_path('css/snappy.css') }}" rel="stylesheet" type="text/css"/>
        @endif
    </head>
    <body>
        @yield('body')
    </body>
</html>
