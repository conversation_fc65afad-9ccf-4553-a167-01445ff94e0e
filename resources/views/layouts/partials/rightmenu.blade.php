<!-- Navbar Right Menu -->
<div class="navbar-custom-menu">
    <ul class="nav navbar-nav">
        @if (Auth::guest())
            <li><a href="{{ url('/login') }}"><i class="fa fa-sign-in" aria-hidden="true"></i> Σύνδεση</a></li>
        @else
            <!-- User Account Menu -->
            <li class="dropdown user user-menu">
                <!-- Menu Toggle Button -->
                @if(Auth::user()->isImpersonating())
                    <a href="#" class="dropdown-toggle text-teal" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                        <i class='fa fa-user-secret text-teal'></i> {{ Auth::user()->name }}
                        <i class="fa fa-at" aria-hidden="true"></i>{{ Auth::user()->unit ? Auth::user()->unit->abbrv : ' '}}
                        <span class="caret"></span>
                    </a>
                @else
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true">
                        <i class='fa fa-user'></i> {{ Auth::user()->name }}
                        <i class="fa fa-at" aria-hidden="true"></i>{{ Auth::user()->unit ? Auth::user()->unit->abbrv : ' ' }}
                        <span class="caret"></span>
                    </a>
                @endif
                <ul class="dropdown-menu">
                    @if(Auth::user()->isImpersonating())
                        <li class="user-footer">
                            <form method="POST" action="{{ route('admin.impersonated-user.destroy', Auth::user()) }}" class="inline">
                                @csrf
                                @method('DELETE')
                                <button class="btn btn-default btn-flat bg-teal" type="submit">
                                    Stop Impersonating
                                </button>
                            </form>
                        </li>
                    @endif
                    @if(Auth::user()->units()->count()>1)
                        <li class="user-footer">
                            <a href="{{ route('userSelectedUnit.edit', Auth::user()) }}" class="btn btn-default btn-flat">
                                <i class="fa fa-exchange"></i> Αλλαγή Υπηρεσίας
                            </a>
                        </li>
                    @endif
                    <!-- Menu Footer-->
                    <li class="user-footer">
                        <form method="POST" action="{{ url('/logout') }}" class="inline">
                            {{ csrf_field() }}
                            <button class="btn btn-default btn-flat" type="submit"><span class="glyphicon glyphicon-log-out"></span> Αποσύνδεση</button>
                        </form>
                    </li>
                </ul>
            </li>
        @endif
        <!-- Control Sidebar Toggle Button -->
    </ul>
</div>
