<head>
    <meta charset="UTF-8">
    <title> apptree - @yield('htmlheader_title', 'Your title here') </title>
    <meta content='width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' name='viewport'>

    {{-- CSRF protection --}}
    <script>
      window.Laravel = <?php echo json_encode([
          'csrfToken' => csrf_token(),
      ]); ?>
    </script>

    {{-- Fonts --}}
    <link
            href='https://fonts.googleapis.com/css?family=Open+Sans:400,300,300italic,400italic,600,600italic,700|Open+Sans+Condensed:300,700,300italic|Noto+Serif:400,400italic,700|Exo+2:400,600|Roboto+Mono:400,700&subset=greek,latin'
            rel='stylesheet'
    >
    <style type="text/css">
        @font-face {
            font-family: Cennerik;
            src: url('{{asset('/fonts/Cennerik-Plain.ttf')}}');
        }

        @font-face {
            font-family: Cennerik-Bold;
            src: url('{{asset('/fonts/Cennerik-Bold.ttf')}}');
        }
    </style>

    {{-- Icons --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

    {{-- Bootstrap 3.3.5 + admin-lte stylesheet --}}
    <link href="{{mix('/css/apptree.css')}}" rel="stylesheet" type="text/css"/>

  {{-- Taiwind CSS --}}
  <link href="{{mix('/css/tailwind.css')}}" rel="stylesheet" type="text/css"/>

  {{-- Custom styles --}}
  <link href="{{mix('/css/myStyles.css')}}" rel="stylesheet" type="text/css"/>

    {{-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries --}}
    {{-- WARNING: Respond.js doesn't work if you view the page via file:// --}}
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->

    <!--[if IE]>
    <style>
    .ie_only {
        display: block !important;
        visibility: visible !important;
    }
    </style>
    <![endif]-->

    <style>
        @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
            /* IE10+ CSS styles go here */
            .ie_only {
                display: block !important;
                visibility: visible !important;
            }
        }

        @supports (-ms-accelerator:true) {
            /* IE Edge 12+ CSS styles go here */
            .ie_only {
                display: block !important;
                visibility: visible !important;
            }
        }
    </style>

    {{--  Style duplicate queries in php debugbar  --}}
    @if (app()->has('debugbar') && app('debugbar')->isEnabled())
            <style>
                div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item.phpdebugbar-widgets-sql-duplicate {
                    background-color: #ecc94b !important;
                }
            </style>
    @endif

        {{--Yield your own css after page reload--}}
    @yield('page-styles')

</head>
