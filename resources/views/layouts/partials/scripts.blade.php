<!-- REQUIRED JS SCRIPTS -->

{{-- Required base scripts (jQuery + Bootstrap + AdminLte) --}}
<script src="/js/jquery-2.2.3.min.js"></script>
<script src="/js/bootstrap-3.3.4.min.js"></script>
<script src={{mix("/js/apptree.js")}}></script>

@include('layouts.partials.flash-scripts')

{{-- Toggle duplicate queries in php debugbar --}}
@if (app()->has('debugbar') && app('debugbar')->isEnabled())
    <script>
        window.toggleSqlDuplicate = function () {
            $('<a>')
                .text(' | Toggle Duplicates |')
                .on('click', function () {
                    $('div.phpdebugbar-widgets-sqlqueries li')
                        .not('.phpdebugbar-widgets-sql-duplicate')
                        .not('.phpdebugbar-widgets-table-list-item')
                        .toggle();
                })
                .appendTo($('.phpdebugbar-widgets-sqlqueries .phpdebugbar-widgets-status'));
        };
        setTimeout(function(){
            toggleSqlDuplicate();
            $('.phpdebugbar-datasets-switcher').change(function(){
                toggleSqlDuplicate()
            });
        },1000);
    </script>
@endif

