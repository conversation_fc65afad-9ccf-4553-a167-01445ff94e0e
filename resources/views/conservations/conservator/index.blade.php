@extends('layouts.conservations')

@section('contentheader_description')
  Αναζήτηση Συντηρητή
@endsection

@section('main-content')
  <div>
    <div class="row">
      <div class="col-sm-12 col-lg-10 col-lg-offset-1">
        @can('conservations.create')
          {{--Create conservator--}}
          <a href="{{ route('conservations.conservator.create') }}">
            <div class="box box-primary box-solid">
              <div class="box-header">
                <h3 class="box-title">Προσθήκη νέου Συντηρητή</h3>
              </div>
            </div>
          </a>
        @endcan
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">Συντηρητές</h3>
          </div>
          <div class="box-body">
            <table class="table table-striped table-condensed apptree-dt">
              <thead>
              <tr>
                <th>ΑΜ</th>
                <th>Όνομα</th>
                <th>ΑΦΜ</th>
                <th>Περιφέρεια</th>
                <th>Ενεργός</th>
                <th>Υλικά</th>
                <th class="text-center" style="white-space: nowrap">Ενέργειες</th>
              </tr>
              </thead>
              <tbody>
              @foreach ($conservators as $conservator)
                <tr>
                  <td>{{ $conservator->registration_number }}</td>
                  <td>{{ $conservator->surname . ' ' . $conservator->name }}</td>
                  <td>{{ $conservator->afm }}</td>
                  <td>{{ $conservator->region->name ?? '' }}</td>
                  <td style="white-space: nowrap">
                    @if ($conservator->active)
                      <span class="label label-info"><i class="fa fa-check"></i></span>
                  @endif
                  <td>
                    @foreach ($conservator->materials as $material)
                      <span class="label bg-light-teal">{{ $material->name }}</span>
                    @endforeach
                  </td>
                  <td class="text-center" style="white-space: nowrap">
                    <a href="{{ route("conservations.conservator.show", $conservator->id ) }}" class="btn btn-info">
                      <i class="fa fa-info-circle"></i>
                    </a>

                    @can('conservations.update')
                      <a href="{{ route("conservations.conservator.edit", $conservator->id ) }}"
                         class="btn btn-warning">
                        <i class="fa fa-edit"></i>
                      </a>
                    @endcan

                    @can('conservations.delete')
                      {{-- Delete --}}
                      <form
                        action="{{ route('conservations.conservator.destroy', $conservator->id) }}"
                        method="POST"
                        class="inline"
                      >
                        @csrf
                        @method('DELETE')
                        <button class="btn btn-danger delete-btn" type="submit">
                          <i class="fa fa-remove"></i>
                        </button>
                        <!-- ... existing code ... -->
                      </form>
                    @endcan

                  </td>
                </tr>
              @endforeach
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
@endsection

@section('page-scripts')
  <script>
    ;(function () {
      "use strict";

      // ################################
      //  Functions
      // ################################

      /**
       * This function enable collapsing/expanding the resource filters
       * by clicking on the box header
       * @param selector
       */
      function toggleFiltersBox(selector) {
        // Add pointer cursor to filter box header
        $(selector).find('div.box-header').css('cursor', 'pointer');
        // Register event handler
        $(selector).on('click', 'div.box-header', function (e) {
          if (e.target == e.currentTarget) {
            $(selector + ' > div.box-header > div > button').trigger('click');
          }
        });
        // Register event handler
        $(selector).on('click', 'div.box-header > h3', function (e) {
          if (e.target == e.currentTarget) {
            $(selector + ' > div.box-header > div > button').trigger('click');
          }
        });
      }

      // ********************************
      // Helper functions
      // ********************************

      // ################################
      //  On document ready
      // ################################

      $(function () {
        // toggleFiltersBox('#filtersBox');
      });
    }());

  </script>
@endsection
