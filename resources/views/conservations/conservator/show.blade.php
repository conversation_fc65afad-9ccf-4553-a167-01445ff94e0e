@extends('layouts.conservations')

@section('contentheader_description')
Προβολή Συντηρητή
@endsection

@section('main-content')
<div>
<div class="row">
    <div class="col-sm-12 col-lg-10 col-lg-offset-1">
        <div class="box box-primary">
            <div class="box-header">
                <div class="row">
                    <div class="col-sm-6">
                        <h3 class="box-title">Στοιχεία Συντηρητή</h3>
                    </div>
                    <div class="col-sm-6 header-buttons">
                        <div id="exportConservatorBtn" class="tooltip-btn" tooltip="Εκτύπωση Στοιχείων">
                            <a href="{{route("conservations.conservator.export", $conservator)}}">
                                <i class="text-blue hover-text-maroon fa fa-file-pdf-o fa-2x grow"></i></a>
                        </div>
                        @can('conservations.update')
                            <div id="editConservatorBtn" class="tooltip-btn" tooltip="Επεξεργασία συντηρητή">
                                <a href="{{route('conservations.conservator.edit', $conservator->id)}}">
                                    <i class="text-blue hover-text-yellow fa fa-edit fa-2x grow"></i></a>
                            </div>
                        @endcan
                        @can('conservations.delete')
                            <div tooltip="Διαγραφή συντηρητή" class="tooltip-btn">
                              <form method="POST" action="{{ route('conservations.conservator.destroy', $conservator->id) }}">
                                @csrf
                                @method('DELETE')
                                <button type="submit" id="deleteConservatorBtn" class="delete-btn text-blue hover-text-red grow">
                                  <i class="fa fa-times fa-2x"></i>
                                </button>
                              </form>
                            </div>
                        @endcan
                    </div>
                </div>
            </div>
            <div class="box-body">
                @include('conservations.conservator._card')
            </div>
        </div>
    </div>
</div>
</div>
@endsection

@section('page-styles')
<style>
    .row {
        margin-bottom:6px;
    }
</style>
@endsection
