<div class="row">
    <div class="col-sm-2">
        <strong>Αρ. Μητρώου:</strong> {{$conservator->registration_number}}
    </div>
    <div class="col-sm-3">
        <strong>Όνομα:</strong> {{$conservator->name}}
    </div>
    <div class="col-sm-4">
        <strong>Επώνυμο:</strong> {{$conservator->surname}}
    </div>
    <div class="col-sm-3">
        <strong>Πατρώνυμο:</strong> {{$conservator->fathername}}
    </div>
</div>

<hr>

<div class="row">
    <div class="col-sm-12">

        <h4 class="form-header">Προσωπικά Στοιχεία</h4>
    </div>
</div>

<div class="row">
    <div class="col-sm-4">
        <strong>Πτυχίο:</strong> {{$conservator->degree->name_with_level ?? ''}}
    </div>
    <div class="col-sm-8">
        <strong>Εκπαιδευτικό Ίδρυμα:</strong> {{$conservator->school->name ?? ''}}
    </div>
</div>
<div class="row">
    <div class="col-sm-4">
        <strong>ΑΜΚΑ:</strong> {{$conservator->amka}}
    </div>
    <div class="col-sm-4">
        <strong>ΑΦΜ:</strong> {{$conservator->afm}}
    </div>
    <div class="col-sm-4">
        <strong>ΔΟΥ:</strong> {{$conservator->doy}}
    </div>
</div>

<div class="row">
    <div class="col-sm-4">
        <strong>Ημ. Γέννησης:</strong> {{$conservator->birthdate ? $conservator->birthdate->format('d-m-Y') : ''}}
    </div>
    <div class="col-sm-4">
        <strong>Αρ. Δελτίου Ταυτότητας:</strong> {{$conservator->policeid_number}}
    </div>
    <div class="col-sm-4">
        <strong>Ημ. Ταυτότητας:</strong> {{$conservator->policeid_date ? $conservator->policeid_date->format('d-m-Y') : ''}}
    </div>
</div>

<hr>

<div class="row">
    <div class="col-sm-12">
        <h4 class="form-header">Στοιχεία Επικοινωνίας</h4>
    </div>
</div>

<div class="row">
    <div class="col-sm-4">
        <strong>Οδός:</strong> {{$conservator->street}}
    </div>
    <div class="col-sm-4">
        <strong>Αριθμός:</strong> {{$conservator->street_number}}
    </div>
    <div class="col-sm-4">
        <strong>Τ.Κ.:</strong> {{$conservator->postcode}}
    </div>
</div>

<div class="row">
    <div class="col-sm-4">
        <strong>Πόλη:</strong> {{$conservator->city}}
    </div>
    <div class="col-sm-4">
        <strong>Περιφέρεια:</strong> {{$conservator->region->name ?? ''}}
    </div>
    <div class="col-sm-4">
        <strong>Περιφερειακή Ενότητα:</strong> {{$conservator->prefecture->name ?? ''}}
    </div>
</div>

<div class="row">
    <div class="col-sm-4">
        <strong>Αρ. Τηλεφώνου 1:</strong> {{$conservator->phonenumber1}}
    </div>
    <div class="col-sm-4">
        <strong>Αρ. Τηλεφώνου 2:</strong> {{$conservator->phonenumber2}}
    </div>
    <div class="col-sm-4">
        <strong>E-mail:</strong> {{$conservator->email}}
    </div>
</div>

<hr>

<div class="row">
    <div class="col-sm-12">
        <h4 class="form-header">Κατάσταση</h4>
    </div>
</div>

<div class="row">
    <div class="col-sm-2">
        <strong>Ενεργός:</strong>&nbsp;
        @if ($conservator->active)
            <span class="label label-info">NAI</span>
        @else
            OXI
        @endif
    </div>
    <div class="col-sm-3">
        <strong>Ημ Κατάστασης:</strong> {{$conservator->status_date ? $conservator->status_date->format('d-m-Y') : ''}}
    </div>
    <div class="col-sm-7">
        <strong>Αιτιολογία:</strong> {{$conservator->statusType->name ?? ''}}
        @if ($conservator->unit)
            <br>
            <strong>Υπηρεσία:</strong> {{$conservator->unit->name}}
        @endif
        @if (!empty($conservator->government_position))
            <br>
            <strong>Υπηρεσία:</strong> {{$conservator->government_position}}
        @endif
    </div>
</div>

<hr>

<div class="row">
    <div class="col-sm-12">
        <h4 class="form-header">Υλικά</h4>
    </div>
</div>
<div class="row">
    <div class="col-sm-12">
        <table class="table table-condensed">
            <thead>
                <tr class="bg-primary">
                    <th>Υλικό</th>
                    <th>Ημ. Χορήγησης</th>
                    <th>Αρ. Πρακτικού</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($conservator->materials as $material)
                <tr>
                    <td>{{$material->name}}</td>
                    <td>{{!empty($material->pivot->issued_at) ? \Carbon\Carbon::parse($material->pivot->issued_at)->format('d-m-Y') : ''}}</td>
                    <td>{{$material->pivot->issued_record_number}}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="3">Δεν έχουν καταχωρηθεί υλικά μέχρι στιγμής</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
