@extends('conservations.pdf')

@section('main')
    <h3 class="text-center">Συντηρητές ανά Υλικό</h3>

    @foreach($conservatorsByMaterial as $material => $conservators)
        <table class="table table-bordered table-striped table-hover @if (!$loop->first) page-break @endif"  cellspacing="0">
            <thead>
                <tr>
                    <th colspan="10">{{ $material }}</th>
                </tr>
                <tr class="headings">
                    <th>ONOMA</th>
                    <th>ΠΑΤΡΩΝΥΜΟ</th>
                    <th>ΑΡ. ΜΗΤΡΩΟΥ</th>
                    @if (in_array('address', $columns))
                        <th>ΟΔΟΣ</th>
                        <th>ΠΟΛΗ</th>
                        <th>ΤΚ</th>
                    @endif
                    <th>ΠΕΡΙΦΕΡΕΙΑ</th>
                    @if (in_array('phone', $columns))
                        <th>ΤΗΛΕΦΩΝΟ</th>
                        <th>ΤΗΛΕΦΩΝΟ</th>
                    @endif
                    @if (in_array('email', $columns))
                        <th>Ε-MAIL</th>
                    @endif
                </tr>
            </thead>
            <tbody>
                @foreach($conservators as $conservator)
                    <tr>
                        <td>{{ $conservator->surname }} {{ $conservator->name }}</td>
                        <td>{{ $conservator->fathername }}</td>
                        <td>{{ $conservator->registration_number }}</td>
                        @if (in_array('address', $columns))
                            <td>{{ $conservator->street }} {{ $conservator->street_number }}</td>
                            <td>{{ $conservator->city }}</td>
                            <td>{{ $conservator->postcode }}</td>
                        @endif
                        <td>{{ $conservator->region_name }}</td>
                        @if (in_array('phone', $columns))
                            <td>{{ $conservator->phonenumber1 }}</td>
                            <td>{{ $conservator->phonenumber2 }}</td>
                        @endif
                        @if (in_array('email', $columns))
                            <td>{{ $conservator->email }}</td>
                        @endif
                    </tr>
                @endforeach
            </tbody>
        </table>
    @endforeach
@endsection