@extends('layouts.conservations')

@section('main-content')
    <div class="row">
        @include('partials.appIconButton', [
            'url' => route('conservations.conservator.index'),
            'title' => 'Αρχείο Συντηρητών',
            'description' => '',
            'color' => '3e6774',
            'icon' => 'fa fa-database'
        ])
        @can('conservations.create')
            @include('partials.appIconButton', [
                'url' => route('conservations.conservator.create'),
                'title' => 'Καταχώρηση Συντηρητή',
                'description' => '',
                'color' => '488b98',
                'icon' => 'fa fa-user-plus'
            ])
        @endcan
        @include('partials.appIconButton', [
            'url' => route('conservations.conservator.search'),
            'title' => 'Αναζήτηση Συντηρητή',
            'description' => '',
            'color' => 'c3cf85',
            'icon' => 'fa fa-search'
        ])
        @include('partials.appIconButton', [
            'url' => route('conservations.conservator.searchByMaterial'),
            'title' => 'Συντηρητές ανά Υλικό',
            'description' => '',
            'color' => 'bbc971',
            'icon' => 'fa fa-list-alt'
        ])
    </div>
    <div class="row">
        <div class="col-sm-12">
            <div class="col-sm-12">
                <h3 class="home-section__title">Διαχείριση Εφαρμογής</h3>
            </div>
        </div>
    </div>
    <div class="row">
        @include('partials.appIconButton', [
            'url' => route('conservations.material.index'),
            'title' => 'Υλικά',
            'description' => '',
            'color' => 'd0bdc6',
            'icon' => 'fa fa-flask'
        ])
        @include('partials.appIconButton', [
            'url' => route('conservations.status-type.index'),
            'title' => 'Καταστάσεις Συντηρητών',
            'description' => '',
            'color' => 'b8a1af',
            'icon' => 'fa fa-exchange'
        ])
        @include('partials.appIconButton', [
            'url' => route('conservations.degree.index'),
            'title' => 'Πτυχία',
            'description' => '',
            'color' => '8c7284',
            'icon' => 'fa fa-graduation-cap'
        ])
        @include('partials.appIconButton', [
            'url' => route('conservations.school.index'),
            'title' => 'Εκπαιδευτικά Ιδρύματα',
            'description' => '',
            'color' => '63495c',
            'icon' => 'fa fa-university '
        ])
    </div>
@endsection
