@extends('layouts.contractuals')

@section('contentheader_description')
    Στατιστικά Back Office
@endsection

@section('main-content')

    <div class="row">
        <div class="col-sm-12">
            <form action="{{ route('contractuals.statistics.back-office') }}" method="GET">
                <div class="form-group">
                    <label for="contest__dropdown">Επιλογή διαγωνισμού</label>
                    <div class="row">
                        <div class="col-sm-3">
                            <select name="contest_id" id="contest__dropdown" class="form-control">
                                @foreach($contests as $contest)
                                    <option
                                        value="{{$contest->id}}"
                                        @if($contest->id == $contestId) selected @endif
                                    >
                                        {{$contest->name}}
                                        ({{$contest->start_date->format('d-m-Y')}}
                                        - {{$contest->end_date->format('d-m-Y')}}
                                        )
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-sm-3">
                            <button type="submit" class="btn btn-primary">Επιλογή</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div>
        <backoffice-stats :unit-data="{{ json_encode($unitStats) }}" ></backoffice-stats>
    </div>

    <div class="box box-primary">
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    <canvas id="pending-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-sm-4">
            <a href="{{route('contractuals.contests.show', $contestId)}}" class="btn btn-default btn-block">
                <i class="fa fa-arrow-left"></i> Επιστροφή στην προβολή διαγωνισμού
            </a>
        </div>
    </div>
@endsection

@section('page-scripts')
    @parent
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.6.0/chart.min.js"></script>
    <script>
        $(function () {
            const pendingData = {
                labels: @json($data['units']),
                datasets: [
                    {
                        label: 'Ελέγχθηκαν',
                        data: @json($data['elegthikan']),
                        backgroundColor: '#48bb78',
                        borderColor: '#48bb78',
                        borderWidth: 1,
                    },
                    {
                        label: 'Υπό έλεγχο',
                        data: @json($data['ypo_elegxo']),
                        backgroundColor: '#ecc94b',
                        borderColor: '#ecc94b',
                        borderWidth: 1,
                    },
                    {
                        label: 'Προς έλεγχο',
                        data: @json($data['pros_elegxo']),
                        backgroundColor: '#429ae1',
                        borderColor: '#429ae1',
                        borderWidth: 1,
                    },
                ],
            };
            const myChart1 = new Chart(document.getElementById('pending-chart'), {
                type: 'bar',
                data: pendingData,
                options: {
                    interaction: {
                        intersect: true,
                        mode: 'index',
                    },
                    plugins: {
                        title: {
                            display: false,
                            text: 'Αριθμός νέων/υποβληθεισών αιτήσεων (ανά ημέρα)',
                        },
                        tooltip: {
                            callbacks: {
                                footer: (tooltipItems) => {
                                    let totalPending = tooltipItems[1].parsed.y + tooltipItems [2].parsed.y;

                                    return 'Σύνολο εκκρεμοτήτων ' + totalPending;
                                },
                            },
                        },
                    },
                    responsive: true,
                    scales: {
                        x: {
                            stacked: true,
                            title: {
                                display: true,
                                text: 'Υπηρεσίες',
                            },
                        },
                        y: {
                            beginAtZero: true,
                            stacked: true,
                            title: {
                                display: true,
                                text: 'Αριθμός αιτήσεων',
                            },
                        },
                    },
                },
            });
        });
    </script>
@endsection
