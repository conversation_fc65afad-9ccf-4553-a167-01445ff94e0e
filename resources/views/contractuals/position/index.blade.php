@extends('layouts.contractuals')

@section('contentheader_description')
    Προβολή Θέσεων Διαγωνισμού
@endsection

@section('main-content')
    <div>
        @if(Session::has('success'))
            <p class="alert alert-success">{{ Session::get('success') }}</p>
        @endif

            <div class="row">
                <div class="col-sm-12">
                    <position-index
                        :contest="{{ json_encode($contest) }}"
                        :positions="{{ $positions }}"
                    ></position-index>
                </div>
            </div>

{{--        <div class="row">--}}
{{--            <!--=============================================>>>>>--}}
{{--            = Στοιχεία διαγωνισμού =--}}
{{--            ===============================================>>>>-->--}}
{{--            <div class="col-sm-12">--}}
{{--                @component('components.box', [--}}
{{--                    'title'=> "Θέσεις διαγωνισμού",--}}
{{--                    'type' => 'box-primary',--}}
{{--                    'buttons' => []--}}
{{--                ])--}}
{{--                    <div class="row" style="margin-top: 1.5rem; margin-bottom: 1rem">--}}
{{--                        <div class="col-sm-9 text-dark-gray">--}}
{{--                            <h3>{{ $contest->name }}</h3>--}}
{{--                            <p>{{ $contest->description }}</p>--}}
{{--                            <p>ΑΔΑ: {{$contest->ada}} - Αρ.Πρωτ: {{$contest->protocol_number}}/{{ $contest->protocol_date->format('d-m-Y') }}</p>--}}
{{--                            <p><i class="fa fa-calendar"></i> Περίοδος αιτήσεων: {{$contest->start_date->format('d-m-Y H:i')}} - {{$contest->end_date->format('d-m-Y H:i')}}</p>--}}
{{--                        </div>--}}
{{--                    </div>--}}
                    {{-- Export total accepted/rejected tables --}}
{{--                    @if($contest->isRanked() && auth()->user()->can('contractuals.admin') )--}}
{{--                        <div class="row">--}}
{{--                            <div class="col-sm-12 text-right">--}}
{{--                                <a href="{{route('contractuals.calculation-employable-diff', [$contest, $calculation])}}" class="btn btn-primary btn-sm">--}}
{{--                                    <i class="fa fa-calculator"></i> Υπολογισμός διαφορών με προηγούμενο πίνακα</a>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <br>--}}
{{--                        <div class="row">--}}
{{--                            <div class="col-sm-12">--}}
{{--                                <div class="row">--}}
{{--                                    <div class="col-sm-12 text-right">--}}
{{--                                        <a--}}
{{--                                            href="{{ "/contractuals/ranked-positions/export-accepted?publication=1&contest_id={$contest->id}&calculation_id={$calculation->id}" }}"--}}
{{--                                            class="action-button"--}}
{{--                                        >--}}
{{--                                            <i class="fa fa-send"></i> ΣΥΓΚΕΝΤΡΩΤΙΚΟΙ ΠΙΝΑΚΕΣ ΕΠΙΤΥΧΟΝΤΩΝ--}}
{{--                                        </a>--}}
{{--                                    </div>--}}
{{--                                    <div class="col-sm-12 text-right" style="margin-top: 0.5rem;">--}}
{{--                                        <a--}}
{{--                                            href="{{ "/contractuals/ranked-positions/export-rejected?publication=1&contest_id={$contest->id}&calculation_id={$calculation->id}" }}"--}}
{{--                                            class="action-button"--}}
{{--                                        >--}}
{{--                                            <i class="fa fa-send"></i> ΣΥΓΚΕΝΤΡΩΤΙΚΟΙ ΠΙΝΑΚΕΣ ΑΠΟΡΡΙΠΤΕΩΝ--}}
{{--                                        </a>--}}
{{--                                    </div>--}}
{{--                                    <div class="col-sm-12 text-right" style="margin-top: 10px">--}}
{{--                                        <a--}}
{{--                                            href="{{ "/contractuals/ranked-positions/export-accepted?publication=0&contest_id={$contest->id}&calculation_id={$calculation->id}" }}"--}}
{{--                                            class="action-button"--}}
{{--                                        >--}}
{{--                                            <i class="fa fa-download"></i> ΣΥΓΚΕΝΤΡΩΤΙΚΟΙ ΠΙΝΑΚΕΣ ΕΠΙΤΥΧΟΝΤΩΝ--}}
{{--                                        </a>--}}
{{--                                    </div>--}}
{{--                                    <div class="col-sm-12 text-right" style="margin-top: 0.5rem;">--}}
{{--                                        <a--}}
{{--                                            href="{{ "/contractuals/ranked-positions/export-rejected?publication=0&contest_id={$contest->id}&calculation_id={$calculation->id}" }}"--}}
{{--                                            class="action-button"--}}
{{--                                        >--}}
{{--                                            <i class="fa fa-download"></i> ΣΥΓΚΕΝΤΡΩΤΙΚΟΙ ΠΙΝΑΚΕΣ ΑΠΟΡΡΙΠΤΕΩΝ--}}
{{--                                        </a>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <br>--}}
{{--                    @endif--}}
{{--                    <div class="row">--}}
                        <!----------------------------------------------=>>>>>
                        = Contest units =
                        ----------------------------------------------=>>>>-->
                    {{--                        <div class="col-sm-4">--}}
                    {{--                            <h5 class="text-dark-gray">ΑΡΜΟΔΙΕΣ ΥΠΗΡΕΣΙΕΣ</h5>--}}
                    {{--                            <ul>--}}
                    {{--                                @foreach ($contest->units as $unit)--}}
                    {{--                                    <li>{{ $unit->name }}</li>--}}
                    {{--                                @endforeach--}}
                    {{--                            </ul>--}}
                    {{--                        </div>--}}

                    <!----------------------------------------------=>>>>>
                        = Contest position listing =
                        ----------------------------------------------=>>>>-->
{{--                        <div class="col-sm-12">--}}
{{--                            @if (count($positions))--}}
{{--                                <table class="table table-hover apptree-dt">--}}
{{--                                    <thead>--}}
{{--                                    <th style="text-align: center">Κωδ.</th>--}}
{{--                                    <th>Ειδικότητα</th>--}}
{{--                                    <th style="text-align: center">Αρ. Θέσεων</th>--}}
{{--                                    @if ($contest->isRanked())--}}
{{--                                        <th style="text-align: center">Κενές</th>--}}
{{--                                        <th style="text-align: center">Προσληπτέοι</th>--}}
{{--                                        <th style="text-align: center">Αποδέχτηκαν</th>--}}
{{--                                        <th style="text-align: center">Δεν Αποδέχτηκαν</th>--}}
{{--                                    @endif--}}
{{--                                    <th>Περιοχή</th>--}}
{{--                                    <th>Υπηρεσία</th>--}}
{{--                                    <th>Εντοπιότητα</th>--}}
{{--                                    <th class="text-right">Ενέργειες</th>--}}
{{--                                    </thead>--}}
{{--                                    <tbody>--}}
{{--                                    @foreach($positions as $position)--}}
{{--                                        <tr>--}}
{{--                                            <td style="text-align: center">{{ $position->code }}</td>--}}
{{--                                            <td style="text-align: left">{{ $position->specialization->name }}</td>--}}
{{--                                            <td style="text-align: center">{{ $position->amount }}</td>--}}
{{--                                            @if ($contest->isRanked())--}}
{{--                                                <td style="text-align: center">{{ $position->amount - ($position->employable_count - $position->declined_count) }}</td>--}}
{{--                                                <td style="text-align: center">{{ $position->employable_count }}</td>--}}
{{--                                                <td style="text-align: center">{{ $position->accepted_count }}</td>--}}
{{--                                                <td style="text-align: center">{{ $position->declined_count }}</td>--}}
{{--                                            @endif--}}
{{--                                            <td>{{ $position->location }}</td>--}}
{{--                                            <td>{{ $position->unit->abbrv }}--}}
{{--                                            <td>{{ $position->has_locality ? 'NAI' : '' }}</td>--}}
{{--                                            <td style="display: flex;justify-content: space-around;width: 110px;"--}}
{{--                                                class="pull-right">--}}
{{--                                                @if (!$contestHasRankings)--}}
{{--                                                    <a href="{{ route("contractuals.positions.show", [$contest->id, $position->id]) }}"--}}
{{--                                                       class="action-button">--}}
{{--                                                        <i class="fa fa-2x fa-info-circle"></i>--}}
{{--                                                    </a>--}}
{{--                                                @endif--}}
{{--                                                @if (!$contest->isLocked())--}}

{{--                                                    <a href="{{ route("contractuals.positions.edit", [$contest->id, $position->id]) }}"--}}
{{--                                                       class="action-button">--}}
{{--                                                        <i class="fa fa-2x fa-edit"></i>--}}
{{--                                                    </a>--}}

{{--                                                    {!! Form::model($position, [--}}
{{--                                                    'route' => ['contractuals.positions.destroy', $contest->id, $position->id],--}}
{{--                                                    'method' => 'DELETE',--}}
{{--                                                    'class' => 'inline'--}}
{{--                                                    ]) !!}--}}
{{--                                                    <button class="delete-btn action-button" type="submit">--}}
{{--                                                        <i class="fa fa-2x fa-trash"></i>--}}
{{--                                                    </button>--}}
{{--                                                    {!! Form::close() !!}--}}
{{--                                                @endif--}}
{{--                                                @if ($contestHasRankings)--}}
{{--                                                    <a href="{{ route("contractuals.ranked-positions.show", [$position->id]) }}"--}}
{{--                                                       class="action-button">--}}
{{--                                                        <i class="fa fa-2x fa-bar-chart"></i>--}}
{{--                                                    </a>--}}
{{--                                                    <a href="{{ "/contractuals/ranked-positions/".$position->id."/export-accepted?calculation_id=".$calculation->id }}"--}}
{{--                                                       class="action-button">--}}
{{--                                                        <i class="fa fa-download fa-2x"></i>--}}
{{--                                                    </a>--}}
{{--                                                @endif--}}
{{--                                            </td>--}}
{{--                                        </tr>--}}
{{--                                    @endforeach--}}
{{--                                    </tbody>--}}
{{--                                    @if (!$contest->isLocked())--}}
{{--                                        <tfoot>--}}
{{--                                        <tr>--}}
{{--                                            <td colspan="5">--}}
{{--                                                <a href="{{route('contractuals.positions.create', ['contest' => $contest->id])}}"--}}
{{--                                                   class="btn btn-link btn-block">--}}
{{--                                                    <i class="fa fa-plus" aria-hidden="true"></i>--}}
{{--                                                    Προσθήκη Θέσης / Ειδικότητας--}}
{{--                                                </a>--}}
{{--                                            </td>--}}
{{--                                        </tr>--}}
{{--                                        </tfoot>--}}
{{--                                    @endif--}}
{{--                                </table>--}}
{{--                            @else--}}
{{--                                <div class="callout callout-danger text-center">--}}
{{--                                    <h4><i class="icon fa fa-warning"></i> Προσοχή!</h4>--}}
{{--                                    <p>Δεν έχουν προστεθεί θέσεις στον διαγωνισμό</p>--}}
{{--                                </div>--}}
{{--                                <a href="{{route('contractuals.positions.create', ['contest' => $contest->id])}}"--}}
{{--                                   class="btn btn-link btn-block">--}}
{{--                                    <i class="fa fa-plus" aria-hidden="true"></i>--}}
{{--                                    Προσθήκη Θέσης / Ειδικότητας--}}
{{--                                </a>--}}
{{--                            @endif--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                @endcomponent--}}
{{--            </div>--}}
{{--        </div>--}}

{{--        <div class="row">--}}
{{--            <div class="col-sm-4">--}}
{{--                <a href="/contractuals/contests/{{ $contest->id }}" class="btn btn-default btn-block">--}}
{{--                    <i class="fa fa-arrow-left"></i> Επιστροφή στην προβολή διαγωνισμού--}}
{{--                </a>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}
@endsection
