@extends('layouts.contractuals')

@section('contentheader_description')
  Προβολή διαφορών αιτήσεων
@endsection

@section('main-content')
  <div class="row">
    <div class="col-sm-12">
      <div class="box box-primary">
        <div class="box-header">
          <h3 class="box-title">Κενές θέσεις</h3>
        </div>
        <div class="box-body">
          <table class="table apptree-dt">
            <thead>
            <th>Κωδ</th>
            <th>Χώρος</th>
            <th>Κενές</th>
            <th>Δεν έχουν απαντήσει</th>
            </thead>
            <tbody>
            @foreach($unfilledPositions as $unfilledPosition)
              <tr>
                <td>
                  <a href="{{ route("contractuals.ranked-positions.show", [$contest, $unfilledPosition['id']]) }}">
                    ΘΕΣΗ {{ $unfilledPosition['code'] }}
                  </a>
                </td>
                <td>{{ $unfilledPosition['location'] }}</td>
                <td>{{ $unfilledPosition['vacant'] }}</td>
                <td>{{ $unfilledPosition['toBeFilled'] }}</td>
              </tr>
            @endforeach
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-sm-6">
      <a href="{{ route('contractuals.ranked-positions.index', $contest) }}" class="btn btn-default">
        <i class="fa fa-arrow-left"></i> Επιστροφή στην προβολή θέσεων του διαγωνισμού
      </a>
    </div>
  </div>
@endsection
