@extends('layouts.contractuals')

@section('contentheader_description')
    Εισαγωγή Θέσεων Διαγωνισμού Από Αρχείο
@endsection

@section('main-content')
    <div>
        <div class="row">
            <div class="col-sm-12 col-lg-10 col-lg-offset-1">
                @component('components.box', [
                    'title'=> "Εισαγωγή Αρχείου Θέσεων Διαγωνισμού",
                    'type' => 'box-primary',
                    'buttons' => [],
                ])
                    <div class="row" style="margin-top: 1.5rem; margin-bottom: 1rem">
                        <div class="col-sm-12">
                            <h3>Διαγωνισμός: <strong>{{ $contest->name }}</strong></h3>
                            <p>{{ $contest->description }}</p>
                            <p>ΑΔΑ: {{$contest->ada}} - Αρ.Πρωτ: {{$contest->protocol_number}}/{{ $contest->protocol_date->format('d-m-Y') }}</p>
                        </div>

                        <div class="col-sm-12" style="margin-top:10px">
                            <form method="POST" enctype="multipart/form-data" action="{{ route('contractuals.import-positions.store', ['contest' => $contest->id]) }}">
                                @csrf
                                <div class="row">
                                    <div class="col-sm-6">
                                        <input class="form-control" type="file" id="myFile" name="csv_import_file" required>
                                        <br>
                                        @if ($contest->positions()->exists())
                                            <p class="text-red"><i class="fa fa-warning"></i> Προσοχή: Οι θέσεις θα δημιουργηθούν από την αρχή διαγράφοντας τις θέσεις έχουν ήδη καταχωρηθεί!</p>
                                        @endif
                                        <button class="btn btn-primary btn-block" type="submit">
                                            <i class="fa fa-upload"></i> Μεταφόρτωση Αρχείου
                                        </button>
                                    </div>
                                    <div class="col-sm-6">
                                        <h4>Οδηγίες</h4>
                                        <p>Στο πρότυπο αρχείο excel συμπληρώνουμε το 1ο φύλλο με όλες τις θέσεις του διαγωνισμού.</p>
                                        <p class="text-red">Προσοχή! Δεν πρέπει να γίνουν αλλαγές σε κανένα άλλο φύλλο του αρχείου excel.</p>
                                        <a href="/tools/contractuals_positions_template.xlsx"><i class="fa fa-file-excel-o"></i> Πρότυπο αρχείο excel</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3">
                            <a class="btn btn-default btn-block" href="{{ route('contractuals.positions.index', $contest) }}">
                                <i class="fa fa-arrow-left"></i> Επιστροφή
                            </a>
                        </div>
                    </div>
                @endcomponent
            </div>
        </div>
    </div>
@endsection
