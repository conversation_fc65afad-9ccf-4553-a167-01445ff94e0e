@can('contractuals.admin')
@if (! $contest->isRated() && $contest->applications()->exists())
<div class="row" style="max-width: 1400px; margin-top: 25px;">
    <div class="col-sm-12">
        <div class="stats">
            <canvas id="myChart"></canvas>
        </div>
    </div>
</div>
<div class="row" style="max-width: 1400px; margin-top: 25px;">
    <div class="col-sm-12">
        <div style="display: flex; justify-content: space-between">
            @include('partials.statsBox', [
            'number' => $totalApplications,
            'description' => 'ΣΥΝΟΛΟ',
            'r' => 0,
            'g' => 0,
            'b' => 0,
            ])
            @include('partials.statsBox', [
            'number' => $totalRatedApplications,
            'description' => 'ΕΛΕΓΧΘΗΚΑΝ',
            'r' => 72,
            'g' => 187,
            'b' => 120,
            ])
            @include('partials.statsBox', [
            'number' => $totalNotRatedApplications,
            'description' => 'ΥΠΟ ΕΛΕΓΧΟ',
            'r' => 236,
            'g' => 201,
            'b' => 75,
            ])
            @include('partials.statsBox', [
            'number' => $totalDistributedApplications,
            'description' => 'ΠΡΟΣ ΕΛΕΓΧΟ',
            'r' => 66,
            'g' => 154,
            'b' => 225,
            ])
            @include('partials.statsBox', [
            'number' => $totalNotDistributedApplications,
            'description' => 'ΜΗ ΔΙΑΜΟΙΡΑΣΜΕΝΕΣ',
            'r' => 121,
            'g' => 131,
            'b' => 141,
            ])
            @include('partials.statsBox', [
            'number' => $totalAutoRejectedApplications,
            'description' => 'ΑΥΤ ΑΠΟΡΡΙΨΗ',
            'r' => 245,
            'g' => 101,
            'b' => 101,
            ])
        </div>
    </div>
</div>
@elseif ($contest->isRanked())
<div class="row" style="margin-top: 25px;">
    <div class="col-sm-7">
        <div style="display: flex; justify-content: space-between">
            @include('partials.statsBox', [
            'number' => $totalApplications,
            'description' => 'ΑΙΤΗΣΕΙΣ',
            'r' => 0,
            'g' => 0,
            'b' => 0,
            ])
            @include('partials.statsBox', [
            'number' => $totalPositions,
            'description' => 'ΘΕΣΕΙΣ',
            'r' => 66,
            'g' => 154,
            'b' => 225,
            ])
            @include('partials.statsBox', [
            'number' => $totalPositions - $totalEmployed,
            'description' => 'ΚΕΝΕΣ',
            'r' => 245,
            'g' => 101,
            'b' => 101,
            ])
            @include('partials.statsBox', [
            'number' => $totalEmployables,
            'description' => 'ΠΡΟΣΛΗΠΤΕΟΙ',
            'r' => 72,
            'g' => 187,
            'b' => 120,
            ])
            @include('partials.statsBox', [
            'number' => $totalEmployed,
            'description' => 'ΑΠΟΔΕΧΤΗΚΑΝ',
            'r' => 12,
            'g' => 144,
            'b' => 173,
            ])
            @include('partials.statsBox', [
            'number' => $totalDeclined,
            'description' => 'ΔΕΝ ΑΠΟΔΕΧΤΗΚΑΝ',
            'r' => 245,
            'g' => 101,
            'b' => 101,
            ])
            @include('partials.statsBox', [
            'number' => $totalRejectedApplications,
            'description' => 'ΑΠΟΡΡΙΠΤΕΟΙ',
            'r' => 168,
            'g' => 71,
            'b' => 71,
            ])
        </div>
    </div>
    @if($unfilledPositions->isNotEmpty())
    <div class="col-sm-5">
        <h4 style="margin-top: 0">Θέσεις χωρίς κάλυψη: <strong>{{ $totalVacant }}</strong></h4>
        <ul style="padding-left: 15px;">
            @foreach($unfilledPositions as $position)
            <li>
                <a href="/contractuals/ranked-positions/{{ $position['id'] }}">
                    {{ $position['code'] }}. {{ $position['location'] }} : {{ $position['vacant_amount'] }}
                </a>
            </li>
            @endforeach
        </ul>
    </div>
    @endif
</div>
@endif
@endcan