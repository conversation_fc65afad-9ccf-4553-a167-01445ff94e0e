<div class="row">
  <!----------------------------------------------=>>>>>
  = Contest units =
  ----------------------------------------------=>>>>-->
  <div class="col-sm-4">
    <h5 class="text-dark-gray">ΑΡΜΟΔΙΕΣ ΥΠΗΡΕΣΙΕΣ</h5>
    <ul>
      @foreach ($contest->units as $unit)
        <li>{{ $unit->name }}</li>
      @endforeach
    </ul>
  </div>

  <!----------------------------------------------=>>>>>
  = Contest position listing =
  ----------------------------------------------=>>>>-->
  <div class="col-sm-8">
    <h5 class="text-dark-gray">ΘΕΣΕΙΣ</h5>
    @if (count($contest->positions))
      <table class="table table-hover">
        <thead>
        <th style="text-align: center">Κωδ.</th>
        <th>Ειδικότητα</th>
        <th style="text-align: center">Αρ. Θέσεων</th>
        <th>Περιοχή</th>
        <th>Υπηρεσία</th>
        <th>Εντοπιότητα</th>
        <th class="pull-right">Ενέργειες</th>
        </thead>
        <tbody>
        @foreach($contest->positions as $position)
          <tr>
            <td style="text-align: center">{{ $position->code }}</td>
            <td style="text-align: left">{{ $position->specialization->name }}</td>
            <td style="text-align: center">{{ $position->amount }}</td>
            <td>{{ $position->location }}</td>
            <td>{{ $position->unit->abbrv }}
            <td>{{ $position->has_locality ? 'NAI' : '' }}</td>
            <td style="display: flex;justify-content: space-around;width: 110px;"
                class="pull-right">
              <a href="{{ route("contractuals.positions.show", [$contest->id, $position->id]) }}"
                 class="action-button">
                <i class="fa fa-2x fa-info-circle"></i>
              </a>
              @if (!$contest->isLocked() && Auth::user()->can('contractuals.admin'))

                <a href="{{ route("contractuals.positions.edit", [$contest->id, $position->id]) }}"
                   class="action-button">
                  <i class="fa fa-2x fa-edit"></i>
                </a>
                <form
                  method="POST"
                  action="{{ route('contractuals.positions.destroy', [$contest->id, $position->id]) }}"
                  class="inline"
                >
                  @csrf
                  @method('DELETE')
                  <button class="delete-btn action-button" type="submit">
                    <i class="fa fa-2x fa-trash"></i>
                  </button>
                </form>
              @endif
            </td>
          </tr>
        @endforeach
        </tbody>
      </table>
    @else
      <div class="row">
        <div class="col-sm-12">
          <div class="callout callout-danger text-center">
            <h4><i class="icon fa fa-warning"></i> Προσοχή!</h4>
            <p>Δεν έχουν προστεθεί θέσεις στον διαγωνισμό</p>
          </div>
        </div>
      </div>
    @endif
    @can('contractuals.admin')
      <div class="row">
        <div class="col-sm-6">
          <a href="{{route('contractuals.positions.create', ['contest' => $contest->id])}}"
             class="btn btn-link btn-block">
            <i class="fa fa-plus" aria-hidden="true"></i>
            Προσθήκη Θέσης / Ειδικότητας
          </a>
        </div>
        <div class="col-sm-6">
          <a href="{{ route('contractuals.import-positions.create', ['contest' => $contest->id]) }}"
             class="btn btn-link btn-block">
            <i class="fa fa-upload" aria-hidden="true"></i>
            Εισαγωγή Θέσεων Από Αρχείο
          </a>
        </div>
      </div>
    @endcan
  </div>
</div>
