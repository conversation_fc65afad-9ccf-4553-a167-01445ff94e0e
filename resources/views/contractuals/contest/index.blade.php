@extends('layouts.contractuals')

@section('contentheader_description')
    Προκηρύξεις Διαγωνισμών
@endsection

@section('page-styles')
    @parent
    <style>
        .contest-box {
            height: auto;
        }

        .contest-box.bg-paper > .small-box-footer {
            color: #3c3e42;
        }

        .contest-box .info-box-number {
            font-size: 14px;
            font-weight: 600;
        }

        .contest-box .info-box-text {
            display: block;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
@endsection


@section('main-content')
    <div class="row at-contractuals-contest-index">
        <div class="col-sm-12">
            {{-- Show open contests --}}
            <div class="row">
                <div class="col-sm-12">
                    <h3>Ανοιχτοί διαγωνισμοί</h3>
                </div>
            </div>

            <div class="row">
                @foreach($activeContests as $contest)
                    <div class="col-sm-12">
                        <div class="small-box contest-box bg-light-blue">
                            <div class="inner">
                                <h4>{{ $contest->name }}</h4>
                                <h5>{{ $contest->description }}</h5>
                                <span class="info-box-text">
                                    Αρ.Πρωτ.: {{ $contest->protocol_number }}/{{ $contest->protocol_date->format('d-m-Y') }} - Α.Δ.Α.: {{ $contest->ada }}
                                </span>
                                <span class="info-box-number">
                                    Θέσεις: {{ $contest->positions->sum('amount') }} - Αιτήσεις: {{ $contest->applications->count() }}
                                </span>
                            </div>
                            <div class="icon">
                                <i class="fa fa-folder-open" aria-hidden="true"></i>
                            </div>
                            <a class="small-box-footer" href="{{route('contractuals.contests.show', $contest->id)}}">
                                Προβολή
                                <i class="fa fa-arrow-circle-right"></i>
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

          <br>

            {{-- Show completed contests --}}
            <div class="row">
                <div class="col-sm-12">
                    <h3>Ολοκληρωμένοι διαγωνισμοί</h3>
                </div>
            </div>

            <div class="row">
                @foreach($completedContests as $contest)
                    <div class="col-sm-12">
                        <div class="small-box contest-box bg-gray">
                            <div class="inner">
                                <h4>{{ $contest->name }}</h4>
                                <h5>{{ $contest->description }}</h5>
                                <span class="info-box-text">
                                    Αρ.Πρωτ.: {{ $contest->protocol_number }}/{{ $contest->protocol_date->format('d-m-Y') }} - Α.Δ.Α.: {{ $contest->ada }}
                                </span>
                                <span class="info-box-number">
                                    Θέσεις: {{ $contest->positions->sum('amount') }} - Αιτήσεις: {{ $contest->applications->count() }}
                                </span>
                            </div>
                            <div class="icon">
                                <i class="fa fa-folder-open" aria-hidden="true"></i>
                            </div>
                            <a class="small-box-footer" href="{{route('contractuals.contests.show', $contest->id)}}">
                                Προβολή
                                <i class="fa fa-arrow-circle-right"></i>
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
@endsection
