@extends('layouts.contractuals')

@section('main-content')
    <div class="row">
        @can('contractuals.read')
            @include('partials.appIconButton', [
                'url' => route('contractuals.contests.index'),
                'title' => 'Διαγωνισμοί',
                'description' => '',
                'color' => '5EBED2',
                'icon' => 'fa fa-folder-open'
            ])
        @endcan
        @can('contractuals.adminUnit')
          @include('partials.appIconButton', [
              'url' => route('contractuals.contests.create'),
              'title' => 'Καταχώριση νέου Διαγωνισμού',
              'description' => '',
              'color' => '5EBED2',
              'icon' => 'fa fa-plus'
          ])
        @endcan
    </div>
    @can('contractuals.admin')
        <div class="row">
            <div class="col-sm-12">
                <div class="col-sm-12">
                    <h3 class="home-section__title">Διαχείριση Εφαρμογής</h3>
                </div>
            </div>
        </div>
        <div class="row">
            @include('partials.appIconButton', [
                'url' => route('contractuals.specializations.index'),
                'title' => 'Ειδικότητες',
                'description' => '',
                'color' => 'CBBBBA',
                'icon' => 'fa fa-random'
            ])
            @include('partials.appIconButton', [
                'url' => route('contractuals.requirements.index'),
                'title' => 'Ειδικά Προσόντα',
                'description' => '',
                'color' => 'CBBBBA',
                'icon' => 'fa fa-graduation-cap'
            ])
            @include('partials.appIconButton', [
                'url' => route('contractuals.languages.index'),
                'title' => 'Ξένες Γλώσσες',
                'description' => '',
                'color' => 'CBBBBA',
                'icon' => 'fa fa-language'
            ])
            @include('partials.appIconButton', [
                'url' => route('contractuals.invalidation-descriptions.index'),
                'title' => 'Λόγοι Απόρριψης',
                'description' => '',
                'color' => 'CBBBBA',
                'icon' => 'fa fa-ban'
            ])
        </div>
    @endcan
    @can('contractuals.admin')
        <div class="row">
            <div class="col-sm-12">
                <div class="col-sm-12">
                    <h3 class="home-section__title">Στατιστικά</h3>
                </div>
            </div>
        </div>
        <div class="row">
            @include('partials.appIconButton', [
                'url' => route('contractuals.statistics.front-office'),
                'title' => 'Στατιστικά Front Office',
                'description' => '',
                'color' => '5795a8',
                'icon' => 'fa fa-line-chart'
            ])
            @include('partials.appIconButton', [
                'url' => route('contractuals.statistics.back-office'),
                'title' => 'Στατιστικά Back Office',
                'description' => '',
                'color' => '3b6c7b',
                'icon' => 'fa fa-line-chart'
            ])
        </div>
    @endcan
    <div class="row" style="margin-top: 20px;">
        <div class="col-sm-12">
            <a href="docs/contractuals_user_guide_v2.1.pdf" class="home-section__note">
                <i class="fa fa-lg fa-file-pdf-o text-red" aria-hidden="true"></i>
                Εγχειρίδιο χρήστη εφαρμογής
            </a>
        </div>
        @can('contractuals.adminUnit')
        <div class="col-sm-12">
            <a href="docs/contractuals_admin_guide_v1.pdf" class="home-section__note">
                <i class="fa fa-lg fa-file-pdf-o text-red" aria-hidden="true"></i>
                Εγχειρίδιο διαχειριστή εφαρμογής
            </a>
        </div>
        @endcan
        <div class="col-sm-12">
            <a href="{{ route('contractuals.instructional-videos') }}" class="home-section__note">
                <i class="fa fa-lg fa-file-movie-o" aria-hidden="true"></i>
                Βίντεο οδηγιών χρήσης front-office
            </a>
        </div>
        <div class="col-sm-12">
            <a href="{{ asset('storage/egxeiridio_moriodotisis.pdf') }}" class="home-section__note">
                <i class="fa fa-lg fa-file-pdf-o text-red" aria-hidden="true"></i>
                Εγχειρίδιο μοριοδότησης σύμφωνα με το παράρτημα του ΑΣΕΠ με ημερομηνία 19/02/2025
            </a>
        </div>
    </div>
@endsection
