@extends('layouts.auth')

@section('htmlheader_title')
    Είσοδος Χρήστη
@endsection

@section('content')
    <body class="hold-transition login-page">

    {{-- Extra css for Christmas period with animated snow --}}
    @if(config('app.christmas_mode'))
        <div class="sf-snow-flake"></div>
    @endif

    <div class="login-box animation flipInX">

        <div class="login-box-header">

            <div class="logo-wrapper">
                <svg class="logo" width="36" height="36" viewBox="0 0 512 512">

                    <defs>
                        <linearGradient id="apptree-logo-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%"/>
                            <stop offset="100%"/>
                        </linearGradient>
                    </defs>
                    <path fill="url(#apptree-logo-gradient)" d="M26.55,161.483c-10.174,10.644-12.44,28.681-5.259,41.792c7.5,13.69,22.881,19.797,39.508,17.195
                    c6.013-0.94,13.33-0.338,18.672,2.325c33.31,16.599,64.187,36.71,90.597,63.428c4.705,4.76,8.87,9.81,12.616,15.087
                    c-23.609-15.57-51.257-24.437-80.722-25.114c-1.09-0.026-2.037,0.16-2.912,0.437c-1.088-2.343-2.495-4.515-4.187-6.475
                    c0.16-2.11-0.602-4.315-2.733-6.21c-14.427-12.819-37.201-12.467-50.214,2.373c-12.219,13.935-8.917,36.902,4.751,48.505
                    c14.208,12.062,36.906,8.229,48.337-5.704c3.831-4.667,5.97-10.188,6.641-15.793c0.109,0.008,0.204,0.037,0.316,0.04
                    c39.42,0.908,76.257,18.509,101.24,48.757c1.009,2.81,1.985,5.63,2.897,8.477c1.847,5.768,3.483,11.602,5.042,17.461
                    c-12.874-6.396-25.744-12.786-38.618-19.178c-0.189-0.095-0.379-0.135-0.568-0.216c1.144-6.595-0.059-13.599-3.855-19.338
                    c-0.452-0.684-0.966-1.237-1.505-1.733c-0.451-0.813-1.039-1.604-1.884-2.344c-10.112-8.811-28.565-10.622-37.734,1.046
                    c-8.074,10.273-9.962,27.141,0.9,36.615c9.905,8.632,25.305,10.211,35.621,1.163c0.13-0.117,0.243-0.244,0.371-0.361
                    c17.266,8.571,34.53,17.145,51.796,25.72c0.291,0.146,0.576,0.219,0.863,0.328c1.191,5.112,2.361,10.225,3.534,15.341
                    c0.634,28.908-3.064,57.692-10.757,85.827c-1.126,4.121-0.419,7.653,1.33,10.425c0.103,1.995,2.398,5.391,4.22,6.035
                    c21.562,7.612,43.297,4.525,64.859-0.226c0.842-0.186,1.852-1.47,2.58-2.956c3.582-2.893,5.488-7.517,3.36-13.351
                    c-9.202-25.237-16.013-51.085-20.126-77.537c1.644-8.618,6.198-16.926,10.01-25.223c46.658-3.164,89.635-30.472,111.951-71.98
                    c7.069,1.738,14.678,1.28,21.845-2.012c14.397-6.61,21.879-20.185,21.245-35.106c0.729-2.179,0.612-4.624-0.85-6.879
                    c-0.029-0.14-0.044-0.282-0.073-0.419c-0.557-2.585-1.967-4.206-3.698-5.15c-5.221-10.423-15.282-17.124-27.446-16.763
                    c-3.346,0.098-6.563,0.921-9.544,2.28c-3.961,0.397-7.896,1.607-11.689,3.881c-13.905,8.334-21.544,27.582-12.974,42.548
                    c2.133,3.717,4.865,6.974,8.003,9.696c-18.453,33.499-51.205,56.02-88.319,61.623c22.011-45.115,51.873-84.528,89.944-117.988
                    c13.771-12.102,27.389-24.302,40.904-36.371c4.625-1.72,9.321-3.068,14.103-3.99c24.443,18.49,37.616,20.378,52.178,9.146
                    c12.911-9.958,17.298-27.253,10.786-42.521c-6.275-14.719-21.776-22.781-38.885-20.231c-16.602,2.475-24.99,13.104-28.31,36.961
                    c-3.438,0.71-6.826,1.654-10.192,2.707c-3.969-18.609-7.937-37.219-11.905-55.829c1.96-1.866,4.504-3.695,6.497-5.04
                    c12.521-8.465,19.024-22.644,16.081-36.673c-3.291-15.666-13.709-25.646-29.48-28.237c-14.149-2.325-27.836,4.005-35.288,16.313
                    c-11.883,19.631-2.602,45.746,19.499,51.613c1.986,0.529,3.658,1.181,5.138,1.932c3.812,17.882,7.627,35.763,11.438,53.644
                    c-2.396,10.575-8.323,19.966-18.264,28.277c-12.63,10.558-23.431,23.289-35.846,34.134c-3.601,3.146-7.372,4.665-10.669,4.653
                    c-1.849-3.87-3.696-7.739-5.543-11.609c11.729-7.193,19.083-22.01,14.455-35.722c-5.182-15.353-20.574-28.562-37.617-27.367
                    c-6.198-0.331-12.547,0.856-18.194,3.655c-14.324,7.099-21.652,23.271-18.1,39.949c3.695,17.348,15.666,24.829,44.212,27.278
                    c0.762,1.589,1.52,3.182,2.281,4.774c1.757,9.43,0.977,18.796-5.211,26.678c-14.806,18.857-29.76,37.73-46.205,55.129
                    c-13.421,14.202-24.222,12.227-35.697-3.891c-4.627-6.504-8.672-13.22-12.198-20.122c9.407-5.899,16.692-14.691,20.529-25.292
                    c7.498,0.112,15.034-2.475,19.833-8.17c5.34-6.333,7.807-14.263,6.593-21.761c-0.324-9.905-6.362-19.248-16.536-22.973
                    c-11.777-4.312-28.343,0.146-33.966,12.255c-5.622,12.105-3.582,27.155,7.648,35.405c-2.299,5.899-6.137,10.99-11.161,14.685
                    c-6.65-17.293-10.421-35.572-11.891-54.543c-0.647-8.403-1.241-16.836-1.657-25.271c0.474-0.193,0.952-0.441,1.433-0.776
                    c32.314-22.403,67.308-43.159,97.061-68.955c22.793-19.762,40.415-43.597,43.961-73.893c24.881-2.059,49.566-23.463,45.623-47.487
                    c0.995-4.115,0.558-8.477-2.044-11.953c-1.734-2.321-3.729-4.548-5.882-6.661c-0.423-0.579-0.933-1.107-1.596-1.534
                    C359.339,2.754,341.294-4.153,324.852,2.76c-19.801,8.327-33.342,25.53-32.435,47.773c0.212,5.167,2.271,9.11,5.266,11.865
                    c2.146,3.854,5.141,7.506,9.084,10.808c4.609,3.859,9.836,6.296,15.319,7.602c-3.92,34.301-30.099,59.105-57.003,78.254
                    c-23.911,17.018-48.166,33.565-72.304,50.262c-0.095-6.653-0.032-13.305,0.296-19.933c0.589-12.033,4.532-22.753,9.98-32.821
                    c8.65-9.157,17.302-18.318,25.952-27.476c28.896,2.703,39.931-2.249,46.573-19.532c6.086-15.833,0.289-33.692-13.824-42.576
                    c-13.888-8.746-31.904-6.935-43.582,4.376c-13.629,13.202-14.059,29.007-1.393,50.678c-5.805,6.147-11.613,12.295-17.418,18.442
                    c-3.739,1.505-7.743,1.737-12.521,0.834c-13.285-7.772-27.29-13.967-41.935-18.522c-0.776-3.206-1.621-6.417-1.6-9.619
                    c0.131-19.084-10.546-33.004-28.445-36.359c-16.679-3.131-33.27,5.709-39.268,20.916c-6.596,16.722-0.401,35.168,14.772,43.986
                    c13.833,8.038,29.844,5.954,41.881-4.675c16.154,4.34,31.389,10.685,45.848,19.08c2.635,5.517,3.962,11.77,3.718,19.131
                    c-0.699,21.212,0.879,42.499,1.425,63.755c0.098,3.936,0.346,7.926-0.156,11.803c-1.469,11.351-6.178,14.672-15.761,8.705
                    c-25.366-15.786-50.287-32.34-74.705-49.558c-3.899-2.752-6.501-10.769-5.878-15.888c2.069-17.058-3.746-31.563-17.765-39.097
                    C54.945,147.435,37.41,150.133,26.55,161.483z"></path>
                </svg>

                @if(config('app.christmas_mode'))
                    <svg id="santa-hat-login" x="0px" y="0px" width="30" height="30" viewBox="0 0 845 845">
                        <path fill-rule="evenodd" clip-rule="evenodd" fill="#BE1E2D"
                              d="M685.821,319.775c-17.455,41.828-24.402,47.809-62.296,40.043  c-18.85-3.864-37.886-11.942-56.523-16.63c-4.88-1.229-31.213-10.293,35.498,100.875c-32.263,8.149-73.158,15.141-104.294,24.624  c-11.537,3.514-22.947,11.242-31.508,19.919c-15.586,15.79-31.323,25.118-54.913,20.501c-25.878-5.064-47.569,10.448-70.497,19.206  c-5.85,2.233-12.011,4.021-18.191,4.948c-11.618,1.746-23.338,2.854-35.043,3.944c-4.424,0.411-8.942-0.238-13.382,0.083  c-12.025,0.869-24.033,1.995-36.046,3.025c-6.968,0.598-13.926,1.506-20.906,1.697c-3.952,0.107-8.081-1.905-11.883-1.333  c-27.929,4.203-55.798,8.818-86.01,13.679c4.815-101.209-2.249-122.356,9.698-152.897c13.842-62.953,56.648-121.539,112.992-152.852  c64.75-35.987,134.079-53.246,208.66-54.371c39.308-0.592,92.35,15.242,124.587,37.082  C617.1,259.323,641.746,284.042,685.821,319.775z"/>
                        <g>
                            <path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF"
                                  d="M41.206,670.285c0-13.803,4.046-27.704,2.73-41.376   c-1.587-16.489,15.669-45.907,36.103-50.942c16.731-4.126,33.028-9.985,49.653-14.591c3.888-1.079,8.364-0.585,12.488-0.112   c4.164,0.479,8.457-2.781,12.299-3.416c28.305-4.671,56.522-3.893,84.742-9.072c3.434-0.631,6.728-2.954,10.081-2.941   c24.003,0.091,48.005,0.584,72.005,0.953c2.133,0.031,4.502,0.722,6.358,0.027c23.237-8.688,46.266-17.962,69.65-26.231   c9.71-3.433,20.161-6.873,30.235-6.761c27.148,0.296,42.45-22.822,68.282-35.261c6.449-3.105,12.584-3.521,19.405-4.104   c17.53-1.499,31.115-8.659,46.296-17.462c14.289-8.283,35.165-5.438,53.193-6.861c3.953-0.312,8.892,2.231,12.022,2.814   c31.354,5.839,31.354,5.839,44.062,20.615c11.249,13.079,24.964,34.427,30.564,52.226c1.031,3.28,1.482,7.84,0.923,10.36   c-7.299,32.903-1.066,35.761-6.813,52.583c-1.414,4.137-8.441,6.374-12.919,9.437c-9.479,6.481-19.065,12.806-28.414,19.467   c-2.771,1.973-4.585,6.546-7.364,7.062c-38.885,7.191-66.51,33.28-95,57.253c-13.68,11.513-26.773,15.763-44.666,13.461   c-10.668-1.372-27.975,1.099-32.525,8.152c-19.629,30.398-47.825,29.129-78.096,29.209c-16.209,0.041-34.234,4.188-48.176,12.176   c-22.203,12.722-44.279,16.007-69.215,15.123c-16.923-0.596-34.035,5.853-51.152,8.595c-14.909,2.391-29.921,5.307-44.928,5.569   c-13.316,0.234-27.473-6.506-39.899-3.839c-47.864-5.732-64.827-17.352-87.933-50.021c-4.799-6.785-10.926-18-13.367-26.831   c-2.423-8.779-0.408-17.125-3.708-25.662C39.483,670.017,39.845,670.149,41.206,670.285z"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF"
                                  d="M735.527,309.197c14.838,5.398,34.563,6.642,43.454,17.096   c16.395,19.282,28.969,43.026,37.854,66.879c3.509,9.416-4.872,25.619-11.92,35.946c-7.032,10.307-21.538,15.523-28.541,25.841   c-11.481,16.92-22.574,7.072-35.071,3.923c-20.571-5.186-42.315-6.659-62.103-13.767c-21.193-7.616-25.259-24.5-14.724-44.142   c1.641-3.059,5.371-7.84,4.485-8.98c-14.001-17.976,5.537-26.257,9.725-39.219c7.552-23.398,31.45-23.919,45.115-37.987   c1.997-2.054,6.582-1.591,9.97-2.294C734.357,311.394,734.944,310.297,735.527,309.197z"/>
                        </g>
                        <g>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M91.279,555.544c8.94-47.98,16.044-99.03,28.232-148.837   c25.926-105.959,96.126-170.779,198.764-201.072c113.367-33.458,220.159-20.802,315.313,53.576   c17.704,13.84,33.266,31.398,46.498,49.646c9.703,13.381,15.277,14.61,25.219,2.113c17.62-22.149,43.145-12.149,63.198-9.673   c12.339,1.524,22.56,22.454,33.149,35.157c7.468,8.962,13.594,19.035,20.961,28.089c13.592,16.705,10.568,34.925,3.844,52.406   c-5.763,14.982-11.101,31.545-31.735,34.146c-3.628,0.458-5.855,8.453-9.67,11.94c-4.195,3.838-9.327,8.909-14.313,9.216   c-15.807,0.976-32.447,2.798-47.432-0.987c-17.831-4.502-34.608-13.828-51.238-22.255c-18.588-9.422-20.047-26.956-18.394-45.066   c1.05-11.504,2.778-22.945,4.36-35.702c-33.538-2.695-63.412-5.096-95.416-7.668c21.059,31.622,39.009,58.849,57.396,85.782   c1.894,2.772,6.402,5.368,9.674,5.34c32.703-0.285,51.193,20.844,66.605,45.118c5.531,8.71,8.604,20.208,9.15,30.625   c1.575,30.055,1.593,60.416-19.636,85.207c-3.008,3.514-9.451,4.177-14.389,5.919c-5.012,1.77-12.779,1.712-14.738,5.107   c-8.704,15.101-21.848,18.449-37.573,24.076c-22.013,7.876-39.345,27.941-60.237,40.119c-16.339,9.526-34.504,17.267-52.851,21.638   c-19.789,4.717-37.319,9.414-53.233,24.393c-10.479,9.864-28.441,12.709-43.606,16.451c-25.665,6.333-51.75,10.969-77.695,16.142   c-9.115,1.817-18.445,4.87-27.489,4.318c-27.037-1.658-52.378,2.03-78.587,10.441c-16.209,5.203-36.417-2.013-54.823-3.726   c-9.931-0.924-19.921-3.332-29.758-2.786c-54.736,3.042-90.402-28.753-103.906-82.966c-10.907-43.783,0.027-80.971,20.902-116.7   C63.345,565.624,79.23,562.23,91.279,555.544z M680.821,319.775c-40.292-29.636-79.722-60.452-121.057-88.456   c-32.237-21.84-69.279-30.674-108.587-30.082c-74.581,1.125-143.91,21.384-208.66,57.371   c-56.344,31.313-90.15,79.899-103.992,142.852c25.42-43.033,54.085-82.577,104.607-100.243   c-79.631,66.631-118.491,151.931-123.306,253.14c30.212-4.86,58.081-9.476,86.01-13.679c3.803-0.572,7.932,1.44,11.883,1.333   c6.98-0.191,13.938-1.1,20.906-1.697c12.013-1.03,24.021-2.156,36.046-3.025c4.439-0.321,8.958,0.328,13.382-0.083   c11.705-1.091,23.425-2.198,35.043-3.944c6.18-0.928,12.341-2.715,18.191-4.948c22.927-8.758,44.618-24.271,70.497-19.206   c23.59,4.617,39.327-4.711,54.913-20.501c8.561-8.677,19.971-16.405,31.508-19.919c31.136-9.483,63.031-16.475,95.294-24.624   c-16.844-56.992-64.553-80.035-109.836-105.499c1.084-1.94,2.167-3.881,3.25-5.822c16.121,3.103,32.241,6.205,48.64,9.361   c-6.973-10.456-13.581-20.365-20.188-30.277c2.152-2.005,4.306-4.013,6.456-6.019c10.787,8.824,21.614,17.599,32.34,26.498   c4.361,3.618,7.961,9.654,12.841,10.883c18.638,4.688,37.674,7.766,56.523,11.63C661.419,362.584,663.366,361.603,680.821,319.775z    M55.471,667.027c-1.282-0.128,3.018,15.534,5.303,23.81c2.3,8.325,0.535,18.896,5.059,25.291   c21.779,30.794,46.41,56.601,90.426,47.15c11.713-2.515,25.057,3.839,37.609,3.618c14.146-0.248,28.295-2.996,42.35-5.25   c16.134-2.585,32.265-8.662,48.216-8.101c23.504,0.832,44.313-2.265,65.242-14.255c13.142-7.53,30.131-11.438,45.41-11.478   c28.533-0.075,55.112,1.122,73.612-27.532c4.29-6.648,20.604-8.978,30.66-7.685c16.863,2.171,29.207-1.836,42.102-12.688   c26.854-22.597,52.894-47.188,89.545-53.967c2.621-0.486,4.329-4.798,6.942-6.657c8.812-6.278,17.849-12.24,26.783-18.349   c4.222-2.887,10.844-4.996,12.177-8.896c5.418-15.856,26.942-30.061,6.423-49.564c-1.766-1.676,0.103-6.673-0.869-9.766   c-5.279-16.777-6.896-36.899-17.5-49.228c-11.979-13.928-28.469-31.12-52.843-19.432c-2.706,1.298-7.607-2.947-11.333-2.652   c-16.994,1.341-36.671-1.341-50.14,6.467c-14.31,8.298-27.115,15.047-43.639,16.459c-6.43,0.551-13.867,5.373-18.291,10.468   c-17.228,19.839-38.771,26.917-64.363,26.638c-9.497-0.104-19.347,3.138-28.5,6.374c-22.043,7.794-43.749,16.535-65.652,24.725   c-1.75,0.655-3.983,0.004-5.993-0.025c-22.623-0.348-45.248-0.812-67.872-0.898c-3.162-0.012-6.266,2.179-9.502,2.772   c-26.601,4.882-53.197,9.805-79.877,14.207c-3.622,0.598-7.668-1.983-11.593-2.436c-3.887-0.446-8.106-0.911-11.771,0.105   c-15.67,4.342-31.032,9.864-46.802,13.754c-19.261,4.746-43.067,32.475-41.571,48.018   C56.458,640.914,55.471,654.017,55.471,667.027z M735.686,313.658c-0.551,1.036-1.104,2.07-1.655,3.106   c-3.193,0.663-7.515,0.226-9.396,2.163c-12.881,13.26-35.407,13.751-42.526,35.806c-3.946,12.218-22.363,20.023-9.166,36.968   c0.835,1.075-2.682,5.582-4.228,8.464c-9.931,18.515-6.099,34.429,13.879,41.608c18.651,6.7,39.146,8.089,58.537,12.977   c11.779,2.969,22.235,12.251,33.059-3.698c6.601-9.725,20.273-14.642,26.901-24.357c6.644-9.734,14.544-25.008,11.236-33.883   c-8.376-22.483-20.228-44.864-35.682-63.039C768.265,319.918,749.671,318.747,735.686,313.658z"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M335.812,688.653c-24.833,45.719-193.443,57.298-235.615,15.654   C181.986,725.363,257.787,704.805,335.812,688.653z"/>
                        </g>
                    </svg>
                @endif

                <h1 class="logo-title">app<span>tree</span></h1>
            </div>

            <h5 class="logo-subtitle">Πλατφόρμα διαδικτυακών εφαρμογών του Υπουργείου Πολιτισμού</h5>
        </div>

        @if (count($errors) > 0)
            <div class="alert alert-danger">
                Παρουσιάστηκε σφάλμα κατά τη σύνδεση.<br>
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <div class="login-box-body">
            <p class="login-box-msg">Είσοδος χρήστη</p>
            <form action="{{ url('/login') }}" method="post">
                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                <div class="form-group has-feedback">
                    <input type="text" class="form-control" placeholder="Όνομα Χρήστη" name="username"/>
                    <span class="glyphicon glyphicon-user form-control-feedback"></span>
                </div>
                <div class="form-group has-feedback">
                    <input type="password" class="form-control" placeholder="Κωδικός" name="password"/>
                    <span class="glyphicon glyphicon-lock form-control-feedback"></span>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fa fa-sign-in" aria-hidden="true"></i> Σύνδεση
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="login-box-footer">
            <i class="fa fa-phone" aria-hidden="true"></i> <a href="/phonebook/public/employee-contact"> Τηλεφωνικός
                κατάλογος</a>
        </div>
    </div>
    </div>

    @include('layouts.partials.scripts_auth')

    <script>
        $(function() {
            $('input').iCheck({
                checkboxClass: 'icheckbox_square-blue',
                radioClass: 'iradio_square-blue',
                increaseArea: '20%', // optional
            });
        });
    </script>
    </body>

@endsection

@section('scripts')
    {{-- Extra css for Christmas period with animated snow --}}
    @if(config('app.christmas_mode'))
        <script src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/130527/h5ab-snow-flurry.js"></script>
        <script>
            jQuery(document).ready(function($) {
                $(document).snowFlurry({
                    maxSize: 10,
                    numberOfFlakes: 150,
                    minSpeed: 10,
                    maxSpeed: 15,
                    color: '#fff',
                    timeout: 0,
                });
            });
        </script>
    @endif
@endsection
