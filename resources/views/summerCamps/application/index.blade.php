@extends('layouts.summerCamps')

@section('contentheader_description')
  Αιτήσεις
@endsection

@section('main-content')
  <div class="row">
    <div class="col-sm-12 col-md-10 col-md-offset-1">
      <div class="box box-primnary">
        <div class="box-body">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>Σεζόν</th>
                <th>Περίοδος Αιτήσεων</th>
                <th>Αιτήσεις</th>
              </tr>
            </thead>
            <tbody>
            @foreach($seasons as $season)
              <tr>
                <td>{{ $season->description }}</td>
                <td>{{ $season->start_date->format('d-m-Y H:i') }} - {{ $season->end_date->format('d-m-Y H:i') }}</td>
                <td>
                  @if ($season->applications->count())
                    @if ($season->status->value == 'upcoming')
                      <a class="btn btn-default" href="{{ route('summer-camps.applications.show', ['application' => $season->applications->first()->id]) }}">
                        Προβολή Αίτησης
                      </a>
                    @elseif ($season->status->value == 'running')
                      <a class="btn btn-primary" href="{{ route('summer-camps.applications.show', ['application' => $season->applications->first()->id]) }}">
                        Προβολή Αίτησης
                      </a>
                    @elseif ($season->status->value == 'ended')
                      <a class="btn btn-default" href="{{ route('summer-camps.applications.show', ['application' => $season->applications->first()->id]) }}">
                        Προβολή Αίτησης
                      </a>
                    @endif
                  @else
                    @if ($season->status->value == 'upcoming')
                      Η προθεσμία υποβολής αιτήσεων αρχίζει στις {{ $season->start_date->format('d-m-Y H:i') }}
                    @elseif ($season->status->value == 'running')
                      <a class="btn btn-primary" href="{{ route('summer-camps.applications.create', ['season_id' => $season->id]) }}">
                        Καταχώρηση Αίτησης
                      </a>
                    @elseif ($season->status->value == 'ended')
                      Η προθεσμία υποβολής αιτήσεων έληξε
                    @endif
                  @endif
                </td>
              </tr>
            @endforeach
            </tbody>
          </table>
        </div>
        <div class="box-footer">
          <div class="row">
            <div class="col-sm-6">
              <a class="btn btn-default btn-block" href="{{ route('summer-camps.home') }}">
                <i class="fa fa-arrow-left"></i> Επιστροφή
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
@endsection
