@extends('layouts.summerCamps')

@section('main-content')
  <div class="row">
    @include('partials.appIconButton', [
        'url' => route('summer-camps.applications.index'),
        'title' => 'Αιτήσεις',
        'description' => '',
        'color' => 'F38A7C',
        'icon' => 'fa fa-folder-open'
    ])
    @if (\App\Models\SummerCamps\Season::ofType('payment')->exists())
      @include('partials.appIconButton', [
          'url' => route('summer-camps.payment-applications.index'),
          'title' => 'Αιτήσεις Αποπληρωμής',
          'description' => '',
          'color' => 'f5a196',
          'icon' => 'fa fa-folder-open'
      ])
    @endif
  </div>
  @can('summerCamps.admin')
    <br><br>
    <hr>
    <div class="row">
      <div class="col-sm-12">
        <h3 class="home-section__title">Διαχείριση Εφαρμογής</h3>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <h4 class="home-section__subtitle">Αιτήσεις Συμμετοχής</h4>
      </div>
    </div>
    <div class="row">
      @foreach(\App\Models\SummerCamps\Season::ofType('main')->orderByDesc('start_date')->get() as $season)
        @include('partials.appIconButton', [
            'url' => route('summer-camps.application-admin.index', $season),
            'title' => 'Προβολή Αιτήσεων '.$season->year,
            'description' => '',
            'color' => 'D88CF3',
            'icon' => 'fa fa-table'
        ])
      @endforeach
    </div>
    @if (\App\Models\SummerCamps\Season::ofType('payment')->exists())
      <div class="row">
        <div class="col-sm-12">
          <h4 class="home-section__subtitle">Αιτήσεις Αποπληρωμής</h4>
        </div>
      </div>
      <div class="row">
        @foreach(\App\Models\SummerCamps\Season::ofType('payment')->orderByDesc('start_date')->get() as $season)
          @include('partials.appIconButton', [
              'url' => route('summer-camps.payment-application-admin.index', $season),
              'title' => 'Προβολή Αιτήσεων Αποπληρωμής '.$season->year,
              'description' => '',
              'color' => 'dfa3f5',
              'icon' => 'fa fa-table'
          ])
        @endforeach
      </div>
    @endif
  @endcan
  <div class="row" style="margin-top: 20px;">
    <div class="col-sm-12">
      <a href="docs/summer_camps_user_guide_v1.2.pdf" class="home-section__note">
        <i class="fa fa-lg fa-file-pdf-o text-red" aria-hidden="true"></i>
        Εγχειρίδιο χρήστη εφαρμογής
      </a>
    </div>
    {{--        @can('contractuals.admin')--}}
    {{--        <div class="col-sm-12">--}}
    {{--            <a href="docs/summer_camps_admin_guide_v1.pdf" class="home-section__note">--}}
    {{--                <i class="fa fa-lg fa-file-pdf-o text-red" aria-hidden="true"></i>--}}
    {{--                Εγχειρίδιο διαχειριστή εφαρμογής--}}
    {{--            </a>--}}
    {{--        </div>--}}
    {{--        @endcan--}}
  </div>
@endsection
