@extends('registry.pdf')

@section('main')
    <h3 class="text-center">ΠΡΟΒΟΛΗ ΑΚΙΝΗΤΟΥ</h3>

    <div class="row">
        <div class="col-sm-12">
            <h4 class="form-header">Στοιχεία Ακινήτου</h4>
        </div>
        <div class="col-sm-3">
            <label>Αρ. Μητρώου:</label> {{ $building->id }}
        </div>
        <div class="col-sm-3">
            <label>Διεύθυνση:</label> {{ $building->street }} {{ $building->street_number}}, {{ $building->city}} {{ $building->postcode }}
        </div>
        <div class="col-sm-3">
            <label>Όροφοι:</label> {{ $building->floor }}
        </div>
        <div class="col-sm-3">
            <label>Περιφέρεια:</label> {{ $building->region->name ?? '' }} {{ $building->prefecture->name ?? '' }}
        </div>
        <div class="col-sm-3">
            <label>Τ.μ. ωφέλιμου χώρου:</label> {{ $building->interior_area }}
        </div>
        <div class="col-sm-3">
            <label>Τ.μ. περιβάλλοντος χώρου:</label> {{ $building->exterior_area }}
        </div>
        <div class="col-sm-3">
            <label>Ιδιοκτησιακό καθεστώς:</label> {{ $building->ownershipType->name }}
        </div>
        <div class="col-sm-3">
            <label>Περιγραφή:</label> {{ $building->description }}
        </div>
        <div class="col-sm-3">
            <label>Υπηρεσίες:</label>
            <ul>
                @foreach ($building->hostedUnits as $unit)
                    <li>{{ $unit->name }}</li>
                @endforeach
            </ul>
        </div>
        <div class="col-sm-3">
            <label>Χρήση:</label> {{ $building->buildingUsage->name }} {{ $building->building_usage_other }}
        </div>
        <div class="col-sm-3">
            <label>Έναρξη Χρήσης:</label> {{ $building->valid_from }}
        </div>
        <div class="col-sm-3">
            <label>Λήξη Χρήσης:</label> {{ $building->valid_to }}
        </div>
        <div class="col-sm-3">
            <label>Καταχώρηση από:</label> {{ $building->unit->name }}
        </div>
        <div class="col-sm-3">
            <label>Τελευταία Ενημέρωση:</label> {{ $building->updated_at->format('d-m-Y, H:i') }}
        </div>
    </div>

    <hr>

    <div class="row">
        <div class="col-sm-12">
            <h4 class="form-header">Προσωπικό Ακινήτου</h4>
        </div>
        <div class="col-sm-12">
            <table class="table table-condensed">
                <thead>
                    <tr class="bg-primary">
                        <th>Ειδικότητα</th>
                        @foreach ($staffUnits as $staffUnit)
                            <th>{{ $staffUnit[0]['unit']['abbrv'] }}</th>
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach ($staffSpecializations as $specialization)
                        <tr>
                            <td>{{ $specialization['title'] }}</td>
                            @foreach ($staffUnits as $staffUnit)
                                <td class="nums">
                                    {{ $staffUnit[0][$specialization['field']] ?? 0 }}
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

    <hr>

    <div class="row">
        <div class="col-sm-12">
            <h4 class="form-header">Εκμισθώσεις Ακινήτου</h4>
        </div>
        <div class="col-sm-12">
            <table class="table table-condensed">
                <thead>
                    <tr class="bg-primary">
                        <th>Σύμβαση</th>
                        <th>Διαδικασία Μίσθωσης</th>
                        <th>Μηνιαίο Μίσθωμα</th>
                        <th>Από</th>
                        <th>Έως</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($building->rentals as $rental)
                        <tr>
                            <td>{{ $rental->contract }}</td>
                            <td>{{ $rental->rentalProcess->name }}</td>
                            <td class="nums">{{ $rental->monthly_rent}}</td>
                            <td class="nums">{{ $rental->valid_from }}</td>
                            <td class="nums">{{ $rental->valid_to }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5">Δεν έχουν καταχωρηθεί εκμισθώσεις.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

    <hr>

    <div class="row">
        <div class="col-sm-12">
            <h4 class="form-header">Συσχετιζόμενα Άτομα</h4>
        </div>
        <div class="col-sm-12">
            <table class="table table-condensed">
                <thead>
                    <tr class="bg-primary">
                        <th>Σχέση</th>
                        <th>Ονοματεπώνυμο</th>
                        <th>ΑΦΜ</th>
                        <th>ΔΟΥ</th>
                        <th>IBAN</th>
                        <th>Διεύθυνση</th>
                        <th>Τηλέφωνο</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($building->associates as $associate)
                        <tr>
                            <td>{{ $associate->associateType->name }}</td>
                            <td>{{ $associate->surname }} {{ $associate->name }}</td>
                            <td class="nums">{{ $associate->afm }}</td>
                            <td>{{ $associate->doy }}</td>
                            <td class="nums">{{ $associate->iban }}</td>
                            <td>{{ $associate->address }}</td>
                            <td class="nums">{{ $associate->phone }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7">Δεν έχουν καταχωρηθεί συσχετιζόμενα άτομα.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

    <hr>

    <div class="row">
        <div class="col-sm-12">
            <h4 class="form-header">Τηλεφωνία Ακινήτου</h4>
        </div>
        <div class="col-sm-12">
            <table class="table table-condensed">
                <thead>
                    <tr class="bg-primary">
                        <th>Αρ. Τηλ. Γραμμής</th>
                        <th>Αρ. ΕΣΥΠ</th>
                        <th>Σύνδεση Internet</th>
                        <th>Internet εντός Σύζευξης</th>
                        <th>Σύνδεση ΚΕΛΕΣΣ</th>
                        <th>Πληρωμή Κεντρικά (ΓΔΟΥ)</th>
                        <th>Υπηρεσία</th>
                        <th>Έναρξη Χρήσης</th>
                        <th>Λήξη Χρήσης</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($building->phones as $phone)
                        <tr>
                            <td>{{ $phone->number }}</td>
                            <td>{{ $phone->supply_number }}</td>
                            <td>{{ $phone->has_internet ? 'NAI' : 'OXI' }}</td>
                            <td>{{ $phone->by_syzefxis ? 'NAI' : 'OXI' }}</td>
                            <td>{{ $phone->keles_linked ? 'NAI' : 'OXI' }}</td>
                            <td>{{ $phone->paid_centrally ? 'NAI' : 'OXI' }}</td>
                            <td>{{ $phone->unit->abbrv }}</td>
                            <td class="nums">{{ $phone->valid_from }}</td>
                            <td class="nums">{{ $phone->valid_to }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9">Δεν έχουν καταχωρηθεί στοιχεία τηλεφωνίας.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

    <hr>

    <div class="row">
        <div class="col-sm-12">
            <h4 class="form-header">Παροχές Ακινήτου</h4>
        </div>
        <div class="col-sm-12">
            <table class="table table-condensed">
                <thead>
                    <tr class="bg-primary">
                        <th>Είδος Παροχής</th>
                        <th>Πάροχος</th>
                        <th>Αρ. Παροχής/Μετρητή</th>
                        <th>Πληρωμή Κεντρικά (ΓΔΟΥ)</th>
                        <th>Από</th>
                        <th>Έως</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($building->utilities as $utility)
                        <tr>
                            <td>{{ $utility->utilityType->name }}</td>
                            <td>{{ $utility->utilityProvider->name }}</td>
                            <td class="nums">{{ $utility->supply_number}}</td>
                            <td>{{ $utility->paid_centrally ? 'NAI' : 'OXI' }}</td>
                            <td class="nums">{{ $utility->valid_from }}</td>
                            <td class="nums">{{ $utility->valid_to }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6">Δεν έχουν καταχωρηθεί παροχές ύδρευσης, ηλεκτρισμού και θέρμανσης.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
@endsection
