@extends('registry.pdf')

@section('main')
    <h3 class="text-center">ΠΡΟΒΟΛΗ ΟΧΗΜΑΤΟΣ</h3>

    <div class="row">
        <div class="col-sm-12">
            <h4 class="form-header">Στοιχεία Οχήματος</h4>
        </div>
        <div class="col-sm-3">
            <label>Αρ. Μητρώου:</label> {{ $vehicle->id }}
        </div>
        <div class="col-sm-3">
            <label>Μάρκα:</label> {{ $vehicle->brand }}
        </div>
        <div class="col-sm-3">
            <label>Μοντέλο:</label> {{ $vehicle->model }}
        </div>
        <div class="col-sm-3">
            <label>Είδος Οχήματος:</label> {{ $vehicle->vehicleType->name }}
        </div>
        <div class="col-sm-3">
            <label>Αρ. Κυκλοφορίας:</label> {{ $vehicle->registration_number }}
        </div>
        <div class="col-sm-3">
            <label>Κυβικά Εκατοστά:</label> {{ $vehicle->cc }}
        </div>
        <div class="col-sm-3">
            <label>Αρ. Πλαισίου:</label> {{ $vehicle->frame_number }}
        </div>
        <div class="col-sm-3">
            <label>Ημερομηνία 1ης Άδειας:</label> {{ $vehicle->license_date }}
        </div>
        <div class="col-sm-3">
            <label>Αριθμός Άδειας:</label> {{ $vehicle->license_number }}
        </div>
        <div class="col-sm-3">
            <label>Υποχρέωση Ασφάλισης:</label> {{ $vehicle->insurance_obligation ? 'ΝΑΙ' : 'ΟΧΙ' }}
        </div>
        <div class="col-sm-3">
            <label>Χρήστης/Οδηγός (υπαλ. ΥΠΠΟΑ):</label> {{ $vehicle->employee->surname ?? '' }} {{ $vehicle->employee->name ?? '' }}
        </div>
        <div class="col-sm-3">
            <label>Χρήστης/Οδηγός (άλλος):</label> {{ $vehicle->employee_other }}
        </div>
        <div class="col-sm-3">
            <label>Έναρξη Χρήσης:</label> {{ $vehicle->valid_from }}
        </div>
        <div class="col-sm-3">
            <label>Λήξη Χρήσης:</label> {{ $vehicle->valid_to }}
        </div>
        <div class="col-sm-3">
            <label>Υπηρεσία:</label> {{ $vehicle->unit->name }}
        </div>
        <div class="col-sm-3">
            <label>Τελευταία Ενημέρωση:</label> {{ $vehicle->updated_at->format('d-m-Y, H:i') }}
        </div>
    </div>

    <hr>

    <div class="row">
        <div class="col-sm-12">
            <h4 class="form-header">Ασφαλίσεις Οχήματος</h4>
        </div>
        <div class="col-sm-12">
            <table class="table table-condensed">
                <thead>
                    <tr class="bg-primary">
                        <th>Ασφαλ. Εταιρεία</th>
                        <th>Αρ. Συμβολαίου</th>
                        <th>Από</th>
                        <th>Έως</th>
                        <th>Ποσό</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($vehicle->vehicleInsurances as $insurance)
                        <tr>
                            <td>{{ $insurance->provider }}</td>
                            <td class="nums">{{ $insurance->contract_number }}</td>
                            <td class="nums">{{ $insurance->valid_from }}</td>
                            <td class="nums">{{ $insurance->valid_to }}</td>
                            <td class="nums">{{ $insurance->amount}} €</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5">Δεν έχουν καταχωρηθεί ασφαλίσεις.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
@endsection
