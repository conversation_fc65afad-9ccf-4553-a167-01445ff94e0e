@extends('layouts.registry')

@section('main-content')
    <div class="row">
        <div class="col-sm-12">
            <h3 class="home-section__subtitle">Ακίνητα Υπηρεσιών</h3>
        </div>
    </div>
    <div class="row">
        @can('registry.create')
            @include('partials.appIconButton', [
                'url' => route('registry.building.create'),
                'title' => 'Καταχώρηση Ακινήτου',
                'description' => '',
                'color' => 'c9b60e',
                'icon' => 'fa fa-building'
            ])
        @endcan
        @include('partials.appIconButton', [
            'url' => route('registry.building.search'),
            'title' => 'Αναζήτηση Ακινήτων',
            'description' => '',
            'color' => 'dbcd4f',
            'icon' => 'fa fa-search'
        ])
    </div>
    <div class="row">
        @include('partials.appIconButton', [
            'url' => route('registry.rental.search'),
            'title' => 'Προβολή Εκμισθώσεων Ακινήτων',
            'description' => '',
            'color' => '535766',
            'icon' => 'fa fa-calendar-check-o'
        ])

        @include('partials.appIconButton', [
            'url' => route('registry.phone.search'),
            'title' => 'Προβολή Σταθερών Τηλεφώνων',
            'description' => '',
            'color' => '6f76a7',
            'icon' => 'fa fa-phone'
        ])
        @include('partials.appIconButton', [
            'url' => route('registry.utility.search'),
            'title' => 'Προβολή Παροχών Ακινήτων',
            'description' => '',
            'color' => 'acb4c4',
            'icon' => 'fa fa-bolt'
        ])
    </div>
    <div class="row">
        <div class="col-sm-12">
            <h3 class="home-section__subtitle">Κινητά Τηλέφωνα</h3>
        </div>
    </div>
    <div class="row">
        @can('registry.create')
            @include('partials.appIconButton', [
                'url' => route('registry.mobile.create'),
                'title' => 'Καταχώρηση Κινητού Τηλεφώνου',
                'description' => '',
                'color' => '909769',
                'icon' => 'fa fa-mobile'
            ])
        @endcan
        @include('partials.appIconButton', [
            'url' => route('registry.mobile.search'),
            'title' => 'Αναζήτηση Κινητών Τηλεφώνων',
            'description' => '',
            'color' => 'c5c88e',
            'icon' => 'fa fa-search'
        ])
    </div>
    <div class="row">
        <div class="col-sm-12">
            <h3 class="home-section__subtitle">Οχήματα Υπηρεσιών</h3>
        </div>
    </div>
    <div class="row">
        @can('registry.create')
            @include('partials.appIconButton', [
                'url' => route('registry.vehicle.create'),
                'title' => 'Καταχώρηση Οχήματος',
                'description' => '',
                'color' => '63495C',
                'icon' => 'fa fa-car'
            ])
        @endcan
        @include('partials.appIconButton', [
            'url' => route('registry.vehicle.search'),
            'title' => 'Αναζήτηση Οχημάτων',
            'description' => '',
            'color' => '8C7284',
            'icon' => 'fa fa-search'
        ])
        @include('partials.appIconButton', [
            'url' => route('registry.vehicle-insurance.search'),
            'title' => 'Προβολή Ασφαλίσεων Οχημάτων',
            'description' => '',
            'color' => 'B8A1AF',
            'icon' => 'fa fa-life-ring'
        ])
    </div>
    @can('registry.admin')
        <div class="row">
            <div class="col-sm-12">
                <div class="col-sm-12">
                    <h3 class="home-section__title">Διαχείριση Εφαρμογής</h3>
                </div>
            </div>
        </div>
        <div class="row">
            @include('partials.appIconButton', [
                'url' => route('registry.building-usage.index'),
                'title' => 'Χρήσεις Κτιρίων',
                'description' => '',
                'color' => '434343',
                'icon' => 'fa fa-building-o'
            ])
            @include('partials.appIconButton', [
                'url' => route('registry.ownership-type.index'),
                'title' => 'Ιδιοκτησιακό Καθεστώς',
                'description' => '',
                'color' => '616161',
                'icon' => 'fa fa-building'
            ])
            @include('partials.appIconButton', [
                'url' => route('registry.rental-process.index'),
                'title' => 'Διαδικασίες Μίσθωσης Κτιρίων',
                'description' => '',
                'color' => '838383',
                'icon' => 'fa fa-exchange'
            ])
            @include('partials.appIconButton', [
                'url' => route('registry.associate-type.index'),
                'title' => 'Συσχετιζόμενοι Κτιρίων',
                'description' => '',
                'color' => 'a3a3a3',
                'icon' => 'fa fa-id-card-o'
            ])
            @include('partials.appIconButton', [
                'url' => route('registry.utility-type.index'),
                'title' => 'Είδη Παροχών Κτιρίων',
                'description' => '(εκτός τηλεφωνίας)',
                'color' => '592a31',
                'icon' => 'fa fa-wrench '
            ])
            @include('partials.appIconButton', [
                'url' => route('registry.utility-provider.index'),
                'title' => 'Πάροχοι',
                'description' => '(εκτός τηλεφωνίας)',
                'color' => '9c3e4b',
                'icon' => 'fa fa-bolt '
            ])
            @include('partials.appIconButton', [
                'url' => route('registry.phone-provider.index'),
                'title' => 'Πάροχοι Τηλεφωνίας',
                'description' => '',
                'color' => 'ad606b',
                'icon' => 'fa fa-phone '
            ])
            @include('partials.appIconButton', [
                'url' => route('registry.mobile-provider.index'),
                'title' => 'Πάροχοι Κινητής Τηλεφωνίας',
                'description' => '',
                'color' => 'c28d95',
                'icon' => 'fa fa-mobile '
            ])
            @include('partials.appIconButton', [
                'url' => route('registry.vehicle-type.index'),
                'title' => 'Τύποι Οχημάτων',
                'description' => '',
                'color' => '434343',
                'icon' => 'fa fa-car '
            ])
        </div>
    @endcan
    <div class="row" style="margin-top: 20px;">
        <div class="col-sm-12">
            <a href="docs/registry_user_guide_v1.0.1.pdf" class="home-section__note">
                <i class="fa fa-lg fa-file-pdf-o text-red" aria-hidden="true"></i>
                Εγχειρίδιο <strong>χρήστη</strong> εφαρμογής ΜΗΤΡΩΟ ΥΠΗΡΕΣΙΩΝ ΥΠΠΟΑ
            </a>
            <a href="docs/registry_admin_guide_v1.0.1.pdf" class="home-section__note">
                <i class="fa fa-lg fa-file-pdf-o text-red" aria-hidden="true"></i>
                Εγχειρίδιο <strong>διαχειριστή</strong> εφαρμογής ΜΗΤΡΩΟ ΥΠΗΡΕΣΙΩΝ ΥΠΠΟΑ
                (<em>αφορά ΜΟΝΟ υπαλλήλους της ΓΔΟΥ</em>)
            </a>
        </div>
    </div>
@endsection
