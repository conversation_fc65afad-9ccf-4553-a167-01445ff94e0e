@extends('layouts.admin')

@section('contentheader_description')
  Διαχείριση Χρηστών
@endsection

@section('main-content')
  <div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">Test vue</h3>
          </div>
          <div class="box-body">
            <active-users-list :users="{{ json_encode($paginatedUsers)  }}"></active-users-list>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Current Users -->
{{--  <div class="row">--}}
{{--    <div class="col-sm-12">--}}
{{--      <div class="box box-primary">--}}
{{--        <div class="box-header">--}}
{{--          <h3 class="box-title">Χρήστες Εφαρμογής--}}
{{--            @if($onlineUsers > 0)--}}
{{--              <span class="badge" style="background-color: #78b159">{{ $onlineUsers }} online</span>--}}
{{--            @endif--}}
{{--          </h3>--}}
{{--          <div class="box-tools pull-right">--}}
{{--            <a href="{{ route('admin.user.create') }}">--}}
{{--              <i class='fa fa-plus fa-2x'></i>--}}
{{--            </a>--}}
{{--          </div>--}}
{{--        </div>--}}
{{--        @if (count($users) > 0)--}}
{{--          <div class="box-body">--}}
{{--            <table class="table table-condensed table-striped apptree-dt-unordered" style="width:100%">--}}
{{--              <thead>--}}
{{--              <th>&nbsp;</th>--}}
{{--              <th>Username</th>--}}
{{--              <th>Όνομα</th>--}}
{{--              <th>E-mail</th>--}}
{{--              <th>Υπηρεσία</th>--}}
{{--              <th>Ρόλοι</th>--}}
{{--              <th style="width: 140px;">&nbsp</th>--}}
{{--              </thead>--}}
{{--              <tbody>--}}
{{--              @foreach ($users as $user)--}}
{{--                <tr>--}}
{{--                  <td class="table-text" style="width: 10px">--}}
{{--                    @if($user->isOnline())--}}
{{--                      <span data-toggle="tooltip" title='{{$user->lastActivity()}}'>🟢</span>--}}
{{--                    @endif--}}
{{--                  </td>--}}
{{--                  <td class="table-text">--}}
{{--                    <div>{{ $user->username }}</div>--}}
{{--                  </td>--}}
{{--                  <td class="table-text">--}}
{{--                    <div>{{ $user->name }}</div>--}}
{{--                  </td>--}}
{{--                  <td class="table-text">--}}
{{--                    <div>{{ $user->email }}</div>--}}
{{--                  </td>--}}
{{--                  <td class="table-text">--}}
{{--                    @if ($user->primaryUnit)--}}
{{--                      <div>{{ $user->primaryUnit->abbrv }}</div>--}}
{{--                    @endif--}}
{{--                  </td>--}}
{{--                  <td class="table-text">--}}
{{--                    <div>--}}
{{--                      <ul>--}}
{{--                        @foreach ($user->roles as $role)--}}
{{--                          <li>{{ $role->name }}</li>--}}
{{--                        @endforeach--}}
{{--                      </ul>--}}
{{--                    </div>--}}
{{--                  </td>--}}
{{--                  <td style="display: flex;justify-content: space-around;width: 140px;">--}}
{{--                    <x-action-button method="GET" action="{{ route('admin.user.edit', $user) }}">--}}
{{--                      <i class='fa fa-2x fa-edit'></i>--}}
{{--                    </x-action-button>--}}
{{--                    <x-action-button action="{{ route('admin.impersonated-user.store') }}"--}}
{{--                                     :data="['user_id' => $user->id]">--}}
{{--                      <i class='fa fa-2x fa-user-secret'></i>--}}
{{--                    </x-action-button>--}}
{{--                    <x-action-button method="DELETE" action="{{ route('admin.user.destroy', $user) }}">--}}
{{--                      <i class="fa fa-2x fa-trash"></i>--}}
{{--                    </x-action-button>--}}
{{--                  </td>--}}
{{--                </tr>--}}
{{--              @endforeach--}}
{{--              </tbody>--}}
{{--            </table>--}}
{{--          </div>--}}
{{--        @endif--}}
{{--      </div>--}}

{{--      @if (count($deletedUsers) > 0)--}}
{{--        <div class="box box-danger">--}}
{{--          <div class="box-header">--}}
{{--            <h3 class="box-title">Απενεργοποιημένοι Χρήστες</h3>--}}
{{--          </div>--}}

{{--          <div class="box-body">--}}
{{--            <table class="table table-striped apptree-dt" style="width:100%">--}}
{{--              <thead>--}}
{{--              <th>Username</th>--}}
{{--              <th>Όνομα</th>--}}
{{--              <th>E-mail</th>--}}
{{--              <th style="width: 75px;">&nbsp;</th>--}}
{{--              </thead>--}}
{{--              <tbody>--}}
{{--              @foreach ($deletedUsers as $deletedUser)--}}
{{--                <tr>--}}
{{--                  <td class="table-text">--}}
{{--                    <div>{{ $deletedUser->username }}</div>--}}
{{--                  </td>--}}
{{--                  <td class="table-text">--}}
{{--                    <div>{{ $deletedUser->name }}</div>--}}
{{--                  </td>--}}
{{--                  <td class="table-text">--}}
{{--                    <div>{{ $deletedUser->email }}</div>--}}
{{--                  </td>--}}
{{--                  <td style="display: flex;justify-content: space-around;width: 100px;">--}}
{{--                    <x-action-button method="PUT" action="{{ route('admin.user.restore', $deletedUser->id) }}">--}}
{{--                      <i class="fa fa-2x fa-repeat"></i>--}}
{{--                    </x-action-button>--}}
{{--                    <x-action-button method="DELETE" action="{{ route('admin.user.force-destroy', $deletedUser->id) }}">--}}
{{--                      <i class="fa fa-2x fa-trash"></i>--}}
{{--                    </x-action-button>--}}
{{--                  </td>--}}
{{--                </tr>--}}
{{--              @endforeach--}}
{{--              </tbody>--}}
{{--            </table>--}}
{{--          </div>--}}
{{--        </div>--}}
{{--      @endif--}}
{{--    </div>--}}
{{--  </div>--}}
@endsection

{{--@section('page-scripts')--}}
{{--  @parent--}}
{{--  <script>--}}
{{--    $('table.apptree-dt-unordered').dataTable({--}}
{{--      dom: '<\'row\'<\'col-sm-6\'l><\'col-sm-6 text-right\'B>>frtip',--}}
{{--      buttons: [--}}
{{--        {--}}
{{--          text: '<i class="fa fa-print" aria-hidden="true"></i> Εκτύπωση',--}}
{{--          extend: 'print',--}}
{{--          className: 'btn btn-default btn-xs',--}}
{{--        },--}}
{{--        {--}}
{{--          text: '<i class="fa fa-file-excel-o" aria-hidden="true"></i> Excel',--}}
{{--          extend: 'excel',--}}
{{--          className: 'btn btn-default btn-xs',--}}
{{--        },--}}
{{--        {--}}
{{--          text: '<i class="fa fa-file-pdf-o" aria-hidden="true"></i> PDF',--}}
{{--          extend: 'pdf',--}}
{{--          className: 'btn btn-default btn-xs',--}}
{{--        },--}}
{{--      ],--}}
{{--      paging: true,--}}
{{--      lengthChange: true,--}}
{{--      searching: true,--}}
{{--      ordering: true,--}}
{{--      order: [[0, 'desc'], [1, 'asc']],--}}
{{--      info: true,--}}
{{--      autoWidth: false,--}}
{{--    });--}}
{{--  </script>--}}
{{--@endsection--}}
