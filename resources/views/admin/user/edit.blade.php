@extends('layouts.admin')

@section('contentheader_description')
  Επεξεργασία Χρήστη
@endsection


@section('main-content')
  <div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">Επεξεργασία Χρήστη</h3>
          </div>
          <form
            method="POST"
            action="{{ route('admin.user.update', $user) }}"
            accept-charset="UTF-8"
          >
            @method('PUT')
            @csrf
            <div class="box-body">
              <div class="row">
                <div class="col-sm-6">
                  <h4>Στοιχεία Χρήστη:</h4>
                  <div class="form-group">
                    <label for="name" class="control-label">Όνομα:</label>
                    <input
                      name="name"
                      type="text"
                      value="{{ old('name', $user->name) }}"
                      id="name"
                      class="form-control"
                    >
                  </div>
                  <div class="form-group">
                    <label for="username" class="control-label">Username:</label>
                    <input
                      name="username"
                      type="text"
                      value="{{ old('username', $user->username) }}"
                      id="username"
                      class="form-control"
                    >
                  </div>
                  <div class="form-group">
                    <label for="email" class="control-label">Email:</label>
                    <input
                      name="email"
                      type="text"
                      value="{{ old('email', $user->email) }}"
                      id="email"
                      class="form-control"
                    >
                  </div>
                  <div class="col-sm-12">
                    <h4>Κύρια Υπηρεσία:</h4>
                    <unit-tree-field-http
                      input-name="primary_unit_id"
                      :input-value="{{json_encode($primaryUnit)}}"
                      :allow-selecting-multiple-units="false"
                    ></unit-tree-field-http>
                    <h4>Δευτερεύουσες Υπηρεσίες</h4>
                    <unit-tree-field-http
                      input-name="secondary_unit_id"
                      :input-value="{{json_encode($secondaryUnits)}}"
                      :allow-multiple-units-check="true"
                    ></unit-tree-field-http>

                    {{--                  @if(! $roles->isEmpty())--}}
                    {{--                    <div class="form-group tw:grid tw:grid-cols-4 tw:gap-6">--}}
                    {{--                      @foreach($roles as $appName => $appRoles)--}}
                    {{--                        <div class="tw:bg-gray-50 tw:rounded tw:p-4 tw:border tw:border-gray-300">--}}
                    {{--                          <h3 class="tw:font-light tw:text-lg tw:mb-6">{{ $appName }}</h3>--}}
                    {{--                          @foreach($appRoles as $role)--}}
                    {{--                            <!-- component -->--}}

                    {{--                            <div class="tw:flex tw:items-start tw:py-4 tw:ml-2">--}}
                    {{--                              <input--}}
                    {{--                                id="{{ "role-".$role->id  }}" type="checkbox" class="tw:hidden tw:peer"--}}
                    {{--                                name="roles[]"--}}
                    {{--                                value="{{ $role->id }}" {{ $user->hasRole($role->name) ? 'checked' : '' }}--}}
                    {{--                              >--}}
                    {{--                              <label--}}
                    {{--                                for="{{ "role-".$role->id  }}"--}}
                    {{--                                class="tw:inline-flex tw:items-center tw:flex-1 tw:justify-between tw:p-2 tw:font-medium tw:border tw:rounded-lg tw:cursor-pointer tw:bg-white tw:border-cyan-600 tw:peer-checked:border-violet-400 tw:peer-checked:bg-cyan-600 tw:peer-checked:text-white tw:peer-checked:font-semibold"--}}
                    {{--                              >--}}
                    {{--                                <p class="tw:flex tw:items-center tw:justify-center tw:w-full tw:flex-col tw:gap-1">--}}
                    {{--                                  <span class="tw:font-semibold">{{ $role->name }}</span>--}}
                    {{--                                  <span class="tw:text-sm">{{ $role->description }}</span>--}}
                    {{--                                </p>--}}
                    {{--                              </label>--}}
                    {{--                            </div>--}}

                    {{--                          @endforeach--}}
                    {{--                        </div>--}}
                    {{--                      @endforeach--}}
                    {{--                    </div>--}}
                    {{--                  @endif--}}
                  </div>
                </div>
                <div class="col-sm-6">
                  <h4>Ρόλοι Χρήστη:</h4>
                  @if(! $roles->isEmpty())
                    <div class="form-group tw:grid tw:grid-cols-1 tw:gap-8">
                      @foreach($roles as $roleGroup)
                        <div class="tw:bg-gray-50 tw:rounded tw:p-4 tw:border tw:border-gray-300">
                          <h3 class="tw:font-bold tw:text-lg tw:mb-4">{{ $roleGroup['app']['name'] }}</h3>
                          <div class="tw:flex tw:flex-col tw:gap-2 role-group">
                            @foreach($roleGroup['roleGroup'] as $roleType)
                              {{--                              <div class="tw:rounded tw:p-2">--}}
                              {{--                              <h4 class="tw:text-sm">{{ $roleType['type'] }}</h4>--}}
                              <div class="tw:grid tw:gap-2 tw:grid-cols-3 role-type">

                                @foreach($roleType['roles'] as $role)
                                  <div class="tw:flex">
                                    <input
                                      id="{{ "role-".$role->id }}"
                                      type="checkbox"
                                      class="tw:hidden tw:peer role-checkbox"
                                      name="roles[{{$role->app_id.$roleType['type']}}]"
                                      value="{{ $role->id }}"
                                      {{ $user->hasRole($role->name) ? 'checked' : '' }}
                                      data-role-group="{{$role->app_id.$roleType['type']}}"
                                    >
                                    <label
                                      for="{{ "role-".$role->id }}"
                                      class="tw:mb-0 tw:inline-flex tw:items-center tw:flex-1 tw:justify-between tw:p-2 tw:font-medium tw:border tw:rounded-lg tw:cursor-pointer tw:bg-white tw:border-cyan-600 tw:peer-checked:border-violet-400 tw:peer-checked:bg-cyan-600 tw:peer-checked:text-white tw:peer-checked:font-semibold"
                                    >
                                      <p class="tw:flex tw:items-center tw:justify-center tw:w-full tw:flex-col tw:gap-1">
                                        <span class="tw:text-center tw:text-sm">{{ $role->description }}</span>
                                        <span class="tw:font-semibold tw:text-sm tw:text-gray-400">{{ $role->name }}</span>
                                      </p>
                                    </label>
                                  </div>
                                @endforeach
                              </div>
                              {{--                              </div>--}}
                            @endforeach
                          </div>
                        </div>
                      @endforeach
                    </div>
                  @endif
                </div>
              </div>
            </div>
            <div class="box-footer">
              <div class="row">
                <div class="col-sm-4">
                  <a class="btn btn-default btn-block"
                     href="{{ route('admin.user.index') }}">Επιστροφή</a>
                </div>
                <div class="col-sm-4">
                  {{--                  <input type="reset" value="Επαναφορά πεδίων" class="btn btn-default btn-block">--}}
                </div>
                <div class="col-sm-4">
                  <input type="submit" value="Καταχώρηση" class="btn btn-primary btn-block">
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection

@section('page-scripts')
  @parent
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const roleCheckboxes = document.querySelectorAll('.role-checkbox');

      roleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
          if (this.checked) {
            // Find the parent role type container
            const roleTypeContainer = this.closest('.role-type');

            // Uncheck all other checkboxes in this container
            roleTypeContainer.querySelectorAll('.role-checkbox').forEach(otherCheckbox => {
              if (otherCheckbox !== this) {
                otherCheckbox.checked = false;
              }
            });
          }
        });
      });
    });
  </script>
@endsection
