@extends('layouts.admin')

@section('contentheader_description')
  Δημιουργία Νέου Χρήστη
@endsection

@section('main-content')
  <div class="row">
    <div class="col-sm-6 col-sm-offset-3">
      <div class="box box-primary">
        <div class="box-header">
          <h3 class="box-title">Δημιουργία Νέου Χρήστη</h3>
        </div>
        <div class="box-body">
          <form action="{{ route('admin.user.store') }}" method="POST}}">
            <div class="form-group">
              <label for="name" class="control-label">Όνομα:</label>
              <input
                type="text"
                id="name"
                name="name"
                value="{{ old('name') }}"
                placeholder="Όνομα"
                class="form-control"
              >
            </div>
            <div class="form-group">
              <label for="username" class="control-label">Username:</label>
              <input type="text" name="username" class="form-control" id="username" placeholder="Username"
                     value="{{ old('username') }}">
            </div>
            <div class="form-group">
              <label for="email" class="control-label">Email:</label>
              <input type="email" name="email" class="form-control" id="email" placeholder="Email"
                     value="{{ old('email') }}">
            </div>
            <div class="form-group">
              <label for="password" class="control-label">Password:</label>
              <input type="password" name="password" class="form-control" id="password" placeholder="Password"
                     value="{{ old('password') }}">
            </div>
            <div class="form-group">
              <label for="password-confirmation" class="control-label">Password:</label>
              <input type="password" name="password_confirmation" class="form-control" id="password-confirmation"
                     placeholder="Password"
                     value="{{ old('password_confirmation') }}">
            </div>
            <div class="row" style="margin-top: 1rem;">
              <div class="col-sm-4">
                <a class="btn btn-default btn-block"
                   href="{{ route('admin.user.index') }}">Επιστροφή</a>
              </div>
              <div class="col-sm-4">
                <button type="reset" class="btn btn-default btn-block">Επαναφορά πεδίων</button>
              </div>
              <div class="col-sm-4">
                <button type="submit" class="btn btn-primary btn-block">Καταχώρηση</button>
              </div>
            </div>
          </form>
        </div>
      </div>

    </div>
  </div>
@endsection
