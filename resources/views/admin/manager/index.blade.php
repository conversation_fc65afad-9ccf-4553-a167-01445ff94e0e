@extends('layouts.admin')

@section('contentheader_description')
    Διαχείριση Role Managers
@endsection

@section('main-content')
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-primary">
                <div class="box-header">
                    <h3 class="box-title">Role Managers</h3>
                </div>
                <div class="box-body">
                    <table class="table table-striped apptree-dt">
                        <thead>
                        <th>Όνομα</th>
                        <th>Username</th>
                        <th>E-mail</th>
                        <th>Υπηρεσία</th>
                        <th>Διαχειρίσιμοι Ρόλοι</th>
                        <th>&nbsp;</th>
                        </thead>
                        <tbody>
                        @foreach ($managers as $manager)
                            <tr>

                                <td class="table-text">
                                    <div>{{ $manager->name }}</div>
                                </td>
                                <td class="table-text">
                                    <div>{{ $manager->username }}</div>
                                </td>
                                <td class="table-text">
                                    <div>{{ $manager->email }}</div>
                                </td>
                                <td class="table-text">
                                    @if ($manager->unit)
                                        <div>{{ $manager->unit->abbrv }}</div>
                                    @endif
                                </td>
                                <td class="table-text">
                                    <div>
                                        <ul>
                                            @foreach ($manager->managedRoles as $managedRole)
                                                <li>{{ $managedRole->name }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </td>
                                <td>
                                    <div class="pull-right">
                                        <a href="{{ route('admin.managers.edit', $manager->id) }}"
                                           class="action-button">
                                            <i class='fa fa-2x fa-edit'></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

