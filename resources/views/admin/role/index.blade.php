@extends('layouts.admin')

@section('contentheader_description')
    Ρόλοι Χρηστών (roles)
@endsection

@section('main-content')
    <!-- Current Roles -->
    <div class="row">
        <div class="col-sm-12 col-md-8 col-md-offset-2">

            <div class="box box-primary">
                <div class="box-header">
                    <h3 class="box-title">Ρόλοι Χρηστών</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('admin.role.create') }}">
                            <i class='fa fa-plus fa-2x'></i>
                        </a>
                    </div>
                </div>

                @if (count($roles) > 0)

                    <div class="box-body">
                        <table class="table table-striped apptree-dt" style="width:100%">

                            <!-- Table Headings -->
                            <thead>
                            <th>Δικαίωμα</th>
                            <th>Περιγραφή</th>
                            <th style="width: 100px;">&nbsp;</th>
                            </thead>

                            <!-- Table Body -->
                            <tbody>
                            @foreach ($roles as $role)
                                <tr>
                                    <!-- Role Name -->
                                    <td class="table-text">
                                        <div>{{ $role->name }}</div>
                                    </td>

                                    <!-- Role Description -->
                                    <td class="table-text">
                                        <div>{{ $role->description }}</div>
                                    </td>

                                    <!-- Actions -->
                                    <td style="display: flex;justify-content: space-around;width: 100px;">
                                        <a href="{{ route('admin.role.edit', $role->id ) }}" class="action-button"><i
                                                    class='fa fa-2x fa-edit'></i></a>
                                        @include('partials.btnDelete', ['title' => 'Διαγραφή', 'path' => route('admin.role.destroy', $role->id )])
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>

                @endif

            </div>
        </div>
    </div>
@endsection
