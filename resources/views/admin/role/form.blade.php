<div class="tw:mb-4">
  <label for="name" class="control-label">Ρόλος</label>
  <input type="text" name="name" value="{{ old('name', $role->name ?? '') }}" class="form-control">
{{--  {!! Form::label('name', 'Ρόλος:', ['class' => "control-label"]) !!}--}}
{{--  {!! Form::text('name', old('name'), ['class' => "form-control"]) !!}--}}
</div>

<div class="form-group">
  <label for="description" class="control-label">Περιγραφή</label>
  <input type="text" name="description" value="{{ old('description', $role->description ?? '') }}"
         class="form-control">
{{--  {!! Form::label('description', 'Περιγραφή:', ['class' => "control-label"]) !!}--}}
{{--  {!! Form::text('description', old('description'), ['class' => "form-control"]) !!}--}}
</div>

<div class="form-group">
  <label for="app_id" class="control-label">Αφορά στην εφαρμογή:</label>
  <select name="app_id" id="app_id" class="form-control select2">
    <option value="">Select an option</option>
    @foreach($apps ?? [] as $key => $value)
      <option value="{{ $key }}"
        {{ old('app_id', $role->app_id ?? null) == $key ? 'selected' : '' }}>
        {{ $value }}
      </option>
    @endforeach
  </select>
</div>
