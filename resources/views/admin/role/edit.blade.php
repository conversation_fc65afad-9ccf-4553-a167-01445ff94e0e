@extends('layouts.admin')

@section('contentheader_description')
  Επεξεργασία Ρόλου Χρηστών
@endsection


@section('main-content')
  <div class="row">
    <div class="col-sm-12 col-md-8 col-md-offset-2">
      <div class="panel panel-primary">
        <div class="panel-heading">
          <h3>Επεξεργασία Ρόλου Χρηστών</h3>
        </div>

        <div class="panel-body">
          <form method="post" action="{{ route('admin.role.update', $role->id) }}">
            @csrf
            @method('PUT')

            @include('admin.role.form')

            @if(! $permissions->isEmpty())
              <h4 class="tw:font-bold">Δικαιώματα Πρόσβασης</h4>
              <div class="form-group">
                @foreach($permissions as $permission)
                  <div class="checkbox">
                    <label>
                      <input
                        type="checkbox"
                        name="permissions[]"
                        value="{{ $permission->id }}"
                        class="is-iCheck"
                        {{ in_array($permission->id, old('permissions', $role->permissions?->pluck('id')->toArray() ?? [])) ? 'checked' : '' }}
                      >
                      {{ $permission->name }}. {{ $permission->description }}.
                    </label>
                  </div>
                @endforeach
              </div>
            @endif
            <div class="form-group">
              <button type="submit" class="btn btn-success">Καταχώρηση</button>
              <button type="reset" class="btn btn-warning">Καθαρισμός πεδίων</button>
              <a class="btn btn-default" href="{{ route('admin.role.index') }}">Επιστροφή</a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
@endsection
