@extends('layouts.admin')

@section('contentheader_description')
    Λίστα Εφαρμογών
@endsection

@section('main-content')
    <div class="row">
        <div class="col-sm-12 col-md-8 col-md-offset-2">
            <div class="box box-primary">
                <div class="box-header">
                    <h3 class="box-title">Εφαρμογές</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('admin.apps.create') }}">
                            <i class='fa fa-plus fa-2x'></i>
                        </a>
                    </div>
                </div>


                <div class="box-body">
                    <table class="table table-striped apptree-dt" style="width:100%">
                        <!-- Table Headings -->
                        <thead>
                        <th>Όνομα</th>
                        <th>Συντομογραφία</th>
                        <th>Logo</th>
                        <th>Χρώμα</th>
                        <th></th>
                        </thead>
                        <tbody>
                        @foreach ($apps as $app)
                            <tr>
                                <td class="table-text">
                                    {{ $app['name'] }}
                                </td>
                                <td class="table-text">
                                    {{ $app['abbrv'] }}
                                </td>
                                <td class="table-text">
                                    <i class="fa {{$app['icon']}} fa-2x"></i>
                                </td>
                                <td>
                                    <div style="width: 28px;height: 28px;border-radius: 4px; background-color: {{$app['color']}}"></div>
                                </td>
                                <td>
                                    <div>
                                        <a href="{{ route('admin.apps.edit', $app['id'] ) }}" class="action-button">
                                            <i class='fa fa-2x fa-edit'></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
