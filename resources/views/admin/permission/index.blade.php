@extends('layouts.admin')

@section('contentheader_description')
    Διαχείριση Δικαιωμάτων Χρήσης (permissions)
@endsection

@section('main-content')
    <div class="row">
        <div class="col-sm-12 col-md-8 col-md-offset-2">
            <div class="box box-primary">
                <div class="box-header">
                    <h3 class="box-title">Δικαιώματα Χρήσης</h3>
                    <div class="box-tools pull-right">
                        <a href="{{ route('admin.permission.create') }}">
                            <i class='fa fa-plus fa-2x'></i>
                        </a>
                    </div>
                </div>

                @if (count($permissions) > 0)

                    <div class="box-body">
                        <table class="table table-striped apptree-dt" style="width:100%">

                            <!-- Table Headings -->
                            <thead>
                            <th>Δικαίωμα</th>
                            <th>Περιγραφή</th>
                            <th style="width: 100px;">&nbsp;</th>
                            </thead>

                            <!-- Table Body -->
                            <tbody>
                            @foreach ($permissions as $permission)
                                <tr>
                                    <!-- Permission Name -->
                                    <td class="table-text">
                                        <div>{{ $permission->name }}</div>
                                    </td>

                                    <!-- Permission Description -->
                                    <td class="table-text">
                                        <div>{{ $permission->description }}</div>
                                    </td>

                                    <!-- Actions -->
                                    <td style="display: flex;justify-content: space-around;width: 100px;">
                                        <a href="{{ route('admin.permission.edit', $permission->id ) }}"
                                           class="action-button">
                                            <i class='fa fa-2x fa-edit'></i>
                                        </a>
                                        @include('partials.btnDelete', ['title' => 'Διαγραφή', 'path' => route('admin.permission.destroy', $permission->id )])
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif

            </div>
        </div>
    </div>
@endsection
