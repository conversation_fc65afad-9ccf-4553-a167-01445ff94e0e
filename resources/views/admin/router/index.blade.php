@extends('layouts.admin')

@section('contentheader_description')
Route ls
@endsection

@section('page-styles')
    <style>
        table td:first-child {
            vertical-align: inherit !important;
            text-align: left !important;
        }

        table td:first-child span{
            display: block;
            width: 100%;
        }

    </style>
@endsection

@section('main-content')
	<div class="row">
    <div class="col-sm-12">
      <div class="box">
        <div class="box-body">
          <table class="table table-striped apptree-dt" style="width:100%">
            <thead class="text-left">
            <tr>
              <th>method</th>
              <th>uri</th>
              <th>name</th>
              <th>controller</th>
            </tr>
            </thead>
            <tbody>
            @foreach($routes as $route)
                            <tr>
                                <td><span class="label {{$route['label']}}">{{$route['method']}}</span></td>
                                <td>{{ $route['uri']}}</td>
                                <td>{{ $route['name']}}</td>
                                <td>{{ $route['controller']}}</td>
                                {{--<td>{{ $route['controller']}}</td>--}}
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
		</div>
	</div>
@endsection

@section('page-scripts')
    @parent
    <script>
        $('#routesTable').DataTable({
            dom: "<'row'<'col-sm-6 pull-left'f><'col-sm-6 text-right'p>>rt<'row' <'col-sm-3 text-left'l><'col-sm-3 text-right'i><'col-sm-6 text-right'p>>",
//            columnDefs: [
//                // Columns 2-5 are not visible; other visible
//                {targets: [2, 3, 4, 5, 6], visible: false},
//                {targets: '_all', visible: true}
//            ],
            paging: true,
            lengthChange: true,
            searching: true,
            ordering: true,
            order: [[2, "desc"]],
            info: true,
            autoWidth: true,
            initComplete: function (settings, json) {
                $('.overlay').hide();
            }
        });
    </script>
@endsection
