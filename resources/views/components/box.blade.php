<div class="{{'box '.$type}}">
  <!----------------------------------------------=>>>>>
  = Box Heading =
  ----------------------------------------------=>>>>-->
  <div class="box-header resource-detail">
    <div class="row">
      <div class="col-sm-6">
        <h3 class="box-title">{{$title}}</h3>
      </div>
      <div class="col-sm-6 header-buttons text-right">
        @forelse($buttons as $name => $url)

          @if ($name != 'delete')
            {{--Edit contest button--}}
            <div id="editContestBtn" class="tooltip-btn" tooltip="Επεξεργασία">
              <a id="editContestBtn" href="{{$url}}">
                <i class="text-blue hover-text-yellow fa fa-edit fa-2x grow"></i>
              </a>
            </div>
          @else
            {{--Delete contest button--}}

            <form action="{{$url}}" method="POST">
              @csrf
              @method('DELETE')
              <div tooltip="Διαγραφή" class="tooltip-btn">
                <button
                  type="submit"
                  id="deleteContestBtn"
                  class="delete-btn text-blue hover-text-red grow"
                >
                  <i class="fa fa-times fa-2x"></i>
                </button>
              </div>
            </form>
          @endif
        @empty

        @endforelse
      </div>
    </div>
  </div>
  <!----------------------------------------------=>>>>>
  = Box Body =
  ----------------------------------------------=>>>>-->
  <div class="box-body">
    {{ $slot }}
  </div>
  <!----------------------------------------------=>>>>>
  = Box Footer =
  ----------------------------------------------=>>>>-->
  @if(isset($footer))
    <div class="box-footer">
      {{ $footer }}
    </div>
  @endif

</div>
