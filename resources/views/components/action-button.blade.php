@props([
'method' => 'POST',
'action' => '',
'data' => []
])

<form method="{{ $method === 'GET' ? 'GET' : 'POST' }}" action="{{ $action }}" class="inline">
    @if($method !== 'GET')
        @csrf
    @endif
    @if (! in_array($method, ['GET', 'POST']))
        @method($method)
    @endif
    @if (in_array($method, ['POST', 'PUT']))
        @foreach($data as $key => $value)
            <input type="hidden" name="{{ $key }}" value="{{ $value }}"></input>
        @endforeach
    @endif
    <button class="{{ $method === 'DELETE' ? 'action-button delete-btn': 'action-button'  }}" type="submit">
        {{ $slot  }}
    </button>
</form>
