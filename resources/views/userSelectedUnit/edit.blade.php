@extends('layouts.app')

@section('htmlheader_title')
  Επεξεργασία Χρήστη
@endsection

@section('contentheader_title')
  Επεξεργασία στοιχείων {{ $user->name }} ({{ $user->username }})
@endsection

@section('main-content')
  @if ($user->primary_unit_id === null)
    <div class="row">
      <div class="col-sm-6 col-sm-offset-3">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">Επιλογή Υπηρεσίας</h3>
          </div>
          <div class="box-body">
            <form action="{{ route('userSelectedUnit.update', $user) }}" method="POST">
              @csrf
              @method('PUT')
              <div class="row">
                <div class="col-sm-12">
                  <select
                    name="primary_unit_id"
                    class="form-control select2"
                  >
                    <option value="">Επιλέξτε κύρια Υπηρεσία</option>
                    @foreach($units as $id => $name)
                      <option value="{{ $id }}">{{ $name }}</option>
                    @endforeach
                  </select>
                </div>
              </div>
              <div class="row" style="margin-top: 1rem">
                <div class="col-sm-12">
                  <button type="submit" class="btn btn-primary btn-block">Καταχώρηση</button>
                </div>
              </div>

            </form>
          </div>
        </div>
      </div>
    </div>
  @elseif($user->primary_unit_id !== null && $user->units()->count() > 1)
    <div class="row">
      <div class="col-sm-6 col-sm-offset-3">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">Επιλογή Ενεργής Υπηρεσίας</h3>
          </div>
          <div class="box-body">
            <form action="{{ route('userSelectedUnit.update', ['user' => $user]) }}" method="POST">
              @csrf
              @method('PUT')
              <div class="row">
                <div class="col-sm-12">
                  <select
                    name="unit_id"
                    id="unit_id"
                    class="form-control select2">
                    <option value="">Επιλέξτε κύρια Υπηρεσία</option>
                    @foreach($user->units()->pluck('name', 'id') as $id => $name)
                      <option value="{{ $id }}">
                        {{ $name }}
                      </option>
                    @endforeach
                  </select>
                </div>
              </div>
              <div class="row" style="margin-top: 1rem;">
                <div class="col-sm-3">
                  <a class="btn btn-default btn-block" href="{{ route('home') }}">
                    <i class="fa fa-arrow-circle-left"></i> Επιστροφή
                  </a>
                </div>
                <div class="col-sm-9">
                  <button class="btn btn-primary btn-block" type="submit">
                    <i class="fa fa-check-circle"></i> Ενημέρωση
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  @else
    <div class="alert alert-info col-sm-6 col-sm-offset-3">
      Δεν υπάρχουν διαθέσιμες ενέργειες
    </div>
  @endif
@endsection
