<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">
              <i
                class="fa fa-search"
                aria-hidden="true"
              />
              Αναζήτηση Αιτήσεων
            </h3>
            <button
              v-if="$can('registry.update')"
              class="btn btn-default"
            >
              Test button
            </button>
          </div>
          <div class="box-body">
            <resource-filters
              v-bind="{url, orderables, filterables}"
              @data-received="applications = $event"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">
              <i
                class="fa fa-filter"
                aria-hidden="true"
              />
              Αποτελέσματα Αναζήτησης
            </h3>
          </div>
          <div class="box-body">
            <ResourceListing
              :items="applications"
              :listables="listables"
            >
              <template #default="{ tableData }">
                <span v-if="tableData.column.field === 'actions'">
                  <a
                    :href="`/contractuals/contests/${tableData.row.contest_id}/applications/${tableData.row.id}`"
                    class="btn btn-info"
                  ><i class="fa fa-info-circle" />
                  </a>
                </span>
                <span v-else>
                  {{ tableData.row[tableData.column.field] }}
                </span>
              </template>
            </ResourceListing>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ResourceFilters from '../../shared/components/ui/Data/ResourceFilters.vue';
import ResourceListing from '../../shared/components/ui/Data/ResourceListing.vue';
import Gate from '../../shared/mixins/Gate';

export default {
  name: 'ApplicationListing',

  components: {
    ResourceFilters,
    ResourceListing,
  },

  mixins: [Gate],

  props: {
    initialApplications: Array,
    relatedModels: Object, // used to populate select options in ResourceFilters component
  },

  data() {
    return {
      // $authorizableModels : [
      //     {className: 'Contractuals\\Contest', id: null},
      //     {className: 'Contractuals\\Application', id: 1},
      // ],
      applications: this.initialApplications,
      url: '/contractuals/applications',
      orderables: [
        { label: 'Αρ. πρωτ.', field: 'protocol_number' },
        { label: 'ΑΦΜ', field: 'afm' },
      ],
      filterables: [
        { label: 'Αρ. πρωτ.', field: 'protocol_number', type: 'number' },
        { label: 'Ημ/νία. πρωτ.', field: 'protocol_date', type: 'date' },
        { label: 'ΑΦΜ', field: 'afm', type: 'string' },
        {
          label: 'Θέσεις - ειδικότητα',
          field: 'positions.specialization_id',
          type: 'select',
          selectOptions: this.relatedModels.positions,
        },
      ],
      listables: [
        { label: 'Αρ. πρωτ.', field: 'protocol_number', type: 'string' },
        {
          label: 'Ημ/νία. πρωτ.',
          field: 'protocol_date',
          type: 'date',
          dateInputFormat: 'YYYY-MM-DD',
          dateOutputFormat: 'DD-MM-YYYY',
        },
        { label: 'ΑΦΜ', field: 'afm', type: 'string' },
        { label: 'ΑΜΚΑ', field: 'amka', type: 'string' },
      ],
    };
  },
};
</script>
