<template>
  <div class="box box-primary">
    <div class="box-header">
      <h2 class="box-title">
        Αίτηση υποψηφίου <strong>{{ application.name }} {{ application.surname }}</strong>
        με αρ. πρωτ <strong>{{ applicationProtocol }}</strong>
      </h2>
    </div>
    <div class="box-body">
      <!-- ΓΕΝΙΚΑ ΠΡΟΣΟΝΤΑ ΠΡΟΣΛΗΨΗΣ -->

      <div class="row">
        <div class="col-sm-12">
          <div class="box box-primary box-solid">
            <div class="box-header">
              <h2 class="box-title">
                Γενικά προσόντα πρόσληψης
              </h2>
            </div>
            <div class="box-body">
              <table class="table">
                <tbody>
<!--                  <tr>-->
<!--                    <td style="width: 30%">-->
<!--                      Πολίτης GR ή πολίτης EU-->
<!--                    </td>-->
<!--                    <td style="width: 70%" />-->
<!--                    <td-->
<!--                      style="width: 50px"-->
<!--                      class="centered"-->
<!--                    >-->
<!--                      <i :class="['fa fa-2x', isRequirementSatisfied('citizenship') ? 'fa-check-circle text-green' : 'fa-exclamation-circle text-red']" />-->
<!--                    </td>-->
<!--                  </tr>-->
                  <tr>
                    <td>Ηλικία 18-65</td>
                    <td />
                    <td class="centered">
                      <i :class="['fa fa-2x', isRequirementSatisfied('age') ? 'fa-check-circle text-green' : 'fa-exclamation-circle text-red']" />
                    </td>
                  </tr>
                  <tr>
                    <td>Γενική καταλληλότητα</td>
                    <td />
                    <td class="centered">
                      <i :class="['fa fa-2x', isRequirementSatisfied('meets_general_requirements') ? 'fa-check-circle text-green' : 'fa-exclamation-circle text-red']" />
                    </td>
                  </tr>
                  <tr>
                    <td>Έγκυρη Αίτηση</td>
                    <td>
                      {{ !isRequirementSatisfied('invalidated') ? application.invalidation_description: '' }}
                    </td>
                    <td class="centered">
                      <i :class="['fa fa-2x', isRequirementSatisfied('invalidated') ? 'fa-check-circle text-green' : 'fa-exclamation-circle text-red']" />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- ΒΑΘΜΟΛΟΓΗΣΗ ΘΕΣΕΩΝ -->

      <template v-if="globalRequirementsSatisfied">
        <div
          v-for="position in positions"
          :key="position.id"
        >
          <div
            :key="position.id"
            class="row"
          >
            <div class="col-sm-12">
              <div class="box box-primary box-solid">
                <div class="box-header with-border">
                  <h2 class="box-title">
                    Βαθμολόγηση θέσης:
                    {{ position.specialization.name }}
                    ({{ position.location }}) -
                    {{ position.unit.abbrv }}
                  </h2>
                </div>

                <div class="box-body">
                  <div class="row">
                    <div class="col-sm-6" align="left">
                      <!-- Επιλογή πίνακα (κύριος/επικουρικός)-->

                      <section class="auxiliary_level">
                        <h5 class="capitalized">
                          ΕΠΙΛΟΓΗ ΕΠΙΚΟΥΡΙΑΣ
                        </h5>
                        <el-radio-group
                          v-model="position.selectedAuxiliaryLevel"
                          @change="resetSelectedEvaluationsForPosition(position)"
                        >
                          <el-radio-button label="0">
                            Κύριος
                          </el-radio-button>
                          <el-radio-button label="1" v-if="application.applicant_category === 3">
                            Α' Επικουρικός
                          </el-radio-button>
                          <el-radio-button label="2" v-if="application.applicant_category === 3">
                            Β' Επικουρικός
                          </el-radio-button>
                        </el-radio-group>
                      </section>
                    </div>
                    <div class="col-sm-6" align="left" style="padding-left: 5rem">
                      <!-- Εντοπιότητα-->
                      <section class="auxiliary_level" v-show="position.hasLocality">
                        <h5 class="capitalized">
                          ΕΝΤΟΠΙΟΤΗΤΑ
                          <el-checkbox
                              v-model="position.selectedLocality"
                              @change="updateLocality(position)"
                              style="padding-left:1rem"
                          />
                        </h5>
                      </section>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-sm-6">
                      <!-- Απαιτούμενα προσόντα -->

                      <section class="requirements">
                        <h5 class="capitalized">
                          ΑΠΑΙΤΟΥΜΕΝΑ ΠΡΟΣΟΝΤΑ
                        </h5>
                        <div
                          v-for="(requirement, index) in getFilteredRequirements(position)"
                          :key="index"
                          class="row"
                        >
                          <div class="col-sm-12">
                            <div class="form-group">
                              <label>{{ requirement.name }}</label>
                              <el-select
                                v-model="requirement.selectedEvaluations"
                                placeholder="Κενό"
                                :multiple="requirement.requirement_type.qualifiable_type === 'experiences'"
                                clearable
                                @change="updateEvaluations(requirement.id, position.id, position.selectedAuxiliaryLevel, $event)"
                              >
                                <el-option
                                  v-for="evaluation in getFilteredEvaluations(position, requirement)"
                                  :key="evaluation.id"
                                  :label="evaluation.qualification.qualifiable.descriptive_name"
                                  :value="evaluation.id"
                                />
                              </el-select>
                            </div>
                          </div>
                        </div>
                      </section>
                    </div>
                    <div
                      class="col-sm-6"
                      style="padding-left: 5rem"
                    >
                      <!-- Υποβληθέντα δικαιολογητικά -->

                      <section class="qualifications">
                        <h5 class="capitalized">
                          ΥΠΟΒΛΗΘΕΝΤΑ ΔΙΚΑΙΟΛΟΓΗΤΙΚΑ
                        </h5>
                        <table class="table table-hover">
                          <tbody>
                            <tr>
                              <th style="width: 25%">
                                Τύπος
                              </th>
                              <th>Προσόν</th>
                              <th style="width: 40px">
                                Σχετικό
                              </th>
                            </tr>
                            <tr
                              v-for="evaluation in getFilteredUnassociatedEvaluations(position)"
                              :key="evaluation.id"
                            >
                              <td>
                                {{ getQualificationTypeGr(evaluation.qualification) }}
                              </td>
                              <td>{{ evaluation.qualification.qualifiable.descriptive_name }}</td>
                              <td class="centered">
                                <el-checkbox v-model="evaluation.relevant" />
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </section>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div class="box-footer">
      <!--ΚΑΤΑΧΩΡΗΣΗ ΦΟΡΜΑΣ-->
      <div class="row">
        <div class="col-sm-2">
          <button
            class="btn btn-block btn-default"
            @click="goToApplicationContest"
          >
            <i class="fa fa-arrow-left" /> Επιστροφή
          </button>
        </div>
        <div class="col-sm-2">
          <button
            class="btn btn-block btn-outline btn-danger"
            @click="destroy"
          >
            <i class="fa fa-times" /> Διαγραφή
          </button>
        </div>
        <div class="col-sm-5">
          <button
            class="btn btn-block btn-primary"
            @click="submit"
          >
            <i class="fa fa-paper-plane" /> Καταχώρηση
          </button>
        </div>
        <div class="col-sm-3">
          <button
            class="btn btn-block btn-outline btn-primary"
            @click="submitAndRate"
          >
            <i class="fa fa-flag-checkered" /> Βαθμολόγηση
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import __ from '../../shared/Helpers';

export default {
  name: 'EvaluationForm',

  props: {
    application: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      evaluations: [],
      positions: [],
      foo: [],
    };
  },

  computed: {
    applicationEditURL() { // FIXME change to showURL
      return `/contractuals/contests/${this.application.contest_id}/applications/${this.application.id}/edit`;
    },
    applicantEditURL() {
      return `/contractuals/applicants/${this.application.applicant_id}/edit`;
    },
    applicationProtocol() {
      return `${this.application.protocol_number}/${this.application.protocol_date}`;
    },

    applicantAge() {
      const contestEndDate = this.application.contest.end_date; // DD-MM-YYYY
      const applicantBirthdate = this.application.birthdate; // YYYY-MM-DD HH:mm:ss

      return moment(contestEndDate, 'DD-MM-YYYY')
        .diff(moment(applicantBirthdate, 'YYYY-MM-DD HH:mm:ss'), 'years');
    },

    globalRequirementsSatisfied() {
      return this.isRequirementSatisfied('citizenship')
                    && this.isRequirementSatisfied('age')
                    && this.isRequirementSatisfied('meets_general_requirements')
                    && this.isRequirementSatisfied('invalidated');
    },
  },

  created() {
    this.evaluations = this.initializeEvaluations(this.application);
    this.positions = this.initializePositions(this.application);
    this.positions.forEach(position => {this.updateLocality(position)});
  },

  methods: {
    submit() {
      this.$http.patch(`/contractuals/applications/${this.application.id}/evaluations`, this.evaluations)
        .then((res) => {
          window.swal({
            type: 'success',
            title: 'Επιτυχία',
            text: res.data.message,
            showConfirmButton: false,
            timer: 3000,
          }, () => {
            window.location.reload(); // FIXME: Accept the evaluations in appropriate form from the server and update them
          });
        })
        .catch((err) => {
          window.swal({
            type: 'error',
            title: `Σφάλμα ${err.response.status}`,
            text: err.response.data.message,
            showConfirmButton: false,
            timer: 4000,
          });
        });
    },

    destroy() {
      __.askToConfirm({
        title: 'Διαγραφή βαθμολόγησης',
        body: 'Επιθυμείτε να συνεχίσετε;',
      }).then(() => {
        const self = this;
        this.$http.delete(`/api/contractuals/applications/${this.application.id}/evaluations`)
          .then((res) => {
            setTimeout(() => {
              window.swal({
                type: 'success',
                title: 'Επιτυχία',
                text: res.data.message,
                showConfirmButton: false,
                timer: 3000,
              }, () => {
                window.location.href = `/contractuals/contests/${self.application.contest_id}`;
              });
            }, 100);
          })
          .catch((err) => {
            window.swal({
              type: 'error',
              title: `Σφάλμα ${err.response.status}`,
              text: err.response.data.message,
              showConfirmButton: false,
              timer: 4000,
            });
          });
      });
    },

    submitAndRate() {
      this.$http.patch(`/contractuals/applications/${this.application.id}/evaluations`, this.evaluations)
        .then((res) => {
          this.$http.post('/api/contractuals/rated-applications', {
            application_id: this.application.id,
            contest_type: this.application.contest.type_id,
          })
            .then((res) => {
              window.swal({
                type: 'success',
                title: 'Επιτυχία!',
                text: 'H βαθμολόγηση οριστικοποιήθηκε',
                showConfirmButton: false,
                timer: 3000,
              }, () => {
                window.location.href = `/contractuals/contests/${this.application.contest_id}`;
              });
            })
            .catch((err) => {
              window.swal({
                type: 'error',
                title: `Σφάλμα ${err.response.status}`,
                text: err.response.data.message,
                showConfirmButton: false,
                timer: 4000,
              });
            });
        })
        .catch((err) => {
          window.swal({
            type: 'error',
            title: `Σφάλμα ${err.response.status}`,
            text: err.response.data.message,
            showConfirmButton: false,
            timer: 4000,
          });
        });
    },

    updateLocality(position) {
      const newSelectedLocality = position.selectedLocality;

      this.evaluations.forEach((evaluation) => {
        if (evaluation.position_id === position.id) {
          evaluation.locality = newSelectedLocality;
        }
      });
    },

    initializeEvaluations(application) {
      return application.evaluations.map((evaluation) => ({
        id: evaluation.id,
        qualification_id: evaluation.qualification_id,
        position_id: evaluation.position_id,
        requirement_id: evaluation.requirement_id, // TODO v-model
        relevant: !!evaluation.relevant,
        qualification: evaluation.qualification,
        auxiliary_level: evaluation.auxiliary_level,
      }));
    },

    initializePositions(application) {
      return application.positions.map((position) => ({
        id: position.id,
        specialization: position.specialization,
        location: position.location,
        unit: position.unit,
        hasLocality: position.has_locality,
        requirements: this.getRequirementsForPosition(position),
        selectedAuxiliaryLevel: this.getSelectedAuxiliaryLevel(position),
        selectedLocality: this.getSelectedLocality(position),
      }));
    },

    getSelectedAuxiliaryLevel(position) {
      return position.pivot.auxiliary_level || 0;
    },

    getSelectedLocality(position) {
      return position.pivot.locality === 1;
    },

    getRequirementsForPosition(position) {
      return position.requirements.filter((requirement) => !(requirement.requirement_type.qualifiable_type === 'greek_languages' && this.application.greek_nationality === 1)).map((requirement) => ({
        id: requirement.id,
        name: requirement.name,
        requirement_type: requirement.requirement_type,
        selectedEvaluations: this.getSelectedEvaluationsForRequirementAndPosition(requirement, position),
        auxiliaryLevel: requirement.pivot.auxiliary_level,
      }));
    },

    getSelectedEvaluationsForRequirementAndPosition(requirement, position) {
      const evaluations = this.evaluations.filter((evaluation) => (evaluation.requirement_id === requirement.id) && (position.id === evaluation.position_id));

      if (requirement.requirement_type.qualifiable_type !== 'experiences') {
        return evaluations.length === 0 ? '' : evaluations[0].id;
      }
      return evaluations.length === 0 ? [] : evaluations.map((evaluation) => evaluation.id);
    },

    getFilteredRequirements(position) {
      return position.requirements
        .filter((requirement) => requirement.auxiliaryLevel === Number(position.selectedAuxiliaryLevel))
        .filter((requirement) => requirement.type !== 'greek_languages' || this.application.eu_citizen === 1);
    },

    getFilteredEvaluations(position, requirement) {
      return this.evaluations
        .filter((evaluation) => evaluation.position_id === position.id)
        .filter((evaluation) => evaluation.qualification.qualifiable_type_slug === requirement.requirement_type.qualifiable_type);
    },

    getFilteredUnassociatedEvaluations(position) {
      return this.evaluations
        .filter((evaluation) => evaluation.position_id === position.id)
        .filter((evaluation) => evaluation.qualification.qualifiable_type_slug !== 'greek_languages' || this.application.eu_citizen === 1)
        .filter((evaluation) => evaluation.requirement_id === null || evaluation.requirement_id === '')
        .filter((evaluation) => this.evaluationHasValue(evaluation));
        // .filter((evaluation) => !this.isSocialQualification(evaluation.qualification));
    },

    evaluationHasValue(evaluation) {
      let q = evaluation.qualification.qualifiable;
      return (
          !(Object.hasOwn(q, 'months') && q.months === 0) &&
          !(Object.hasOwn(q, 'amount') && q.amount === 0) &&
          !(Object.hasOwn(q, 'is_eligible') && q.is_eligible == false)
      );
    },

    updateEvaluations(requirementId, positionId, selectedAuxiliaryLevel, evaluationId) {
      // If evaluation is not multiple (i.e. not experiences)
      if (!Array.isArray(evaluationId)) {
        this.evaluations.forEach((evaluation) => {
          // associate
          if (
            evaluation.id === evaluationId
          ) {
            evaluation.requirement_id = requirementId;
            evaluation.auxiliary_level = Number(selectedAuxiliaryLevel);
            // disassociate
          } else if (
            !evaluationId
                            && evaluation.requirement_id === requirementId
                            && evaluation.position_id === positionId
                            && evaluation.auxiliary_level === Number(selectedAuxiliaryLevel)
          ) {
            evaluation.requirement_id = null;
            evaluation.auxiliary_level = 0;
          }
        });
      } else {
        // If evaluation is multiple (i.e. experiences)
        this.evaluations.forEach((evaluation) => {
          // associate
          if (
            evaluationId.includes(evaluation.id)
          // && evaluationId.length > 0
          ) {
            evaluation.requirement_id = requirementId;
            evaluation.auxiliary_level = Number(selectedAuxiliaryLevel);
          }
          // disassociate
          else if (
            !evaluationId.includes(evaluation.id)
                            && evaluation.position_id === positionId
                            && evaluation.requirement_id === requirementId
                            && evaluation.auxiliary_level === Number(selectedAuxiliaryLevel)
          ) {
            evaluation.requirement_id = null;
            evaluation.auxiliary_level = 0;
          }
        });
      }
    },

    resetSelectedEvaluationsForPosition(position) {
      position.requirements.forEach((requirement) => {
        if (requirement.requirement_type.qualifiable_type !== 'experiences') {
          requirement.selectedEvaluations = '';
        } else {
          requirement.selectedEvaluations = [];
        }
      });

      const newSelectedAuxiliaryLevel = position.selectedAuxiliaryLevel;

      this.evaluations.forEach((evaluation) => {
        if (evaluation.position_id === position.id) {
          evaluation.requirement_id = null;
          evaluation.auxiliary_level = newSelectedAuxiliaryLevel;
          evaluation.relevant = false;
        }
      });
    },

    getQualificationTypeGr(qualification) {
      switch (qualification.qualifiable_type_slug) {
        case 'degrees':
          return 'Τίτλος σπουδών';
        case 'postgraduates':
          return 'Μεταπτυχιακό';
        case 'doctorates':
          return 'Διδιακτορικό';
        case 'greek_languages':
          return 'Ελληνομάθεια';
        case 'language_skills':
          return 'Ξένη γλώσσα';
        case 'computer_skills':
          return 'Γνώση Η/Υ';
        case 'experiences':
          return 'Εμπειρία';
        case 'unemployments':
          return 'Ανεργία';
        case 'multi_child_families':
          return 'Πολύτεκνος';
        case 'three_child_families':
          return 'Τρίτεκνος';
        case 'single_parent_families':
          return 'Μονογονεική οικ.';
        case 'minors':
          return 'Ανήλικα τέκνα';
        case 'disabilities':
          return 'Αναπηρία';
        case 'family_disabilities':
          return 'Αναπηρία οικ.';
      }
    },

    isRequirementSatisfied(requirement_type) {
      switch (requirement_type) {
        case 'citizenship':
          return this.application.eu_citizen || this.application.greek_nationality;
        case 'age':
          return this.applicantAge >= 18 && this.applicantAge <= 65;
        case 'meets_general_requirements':
          return !!this.application.meets_general_requirements;
        case 'invalidated':
          return !this.application.invalidated;
      }
    },

    isSocialQualification(qualification) {
      return ([
        'unemployments',
        'multi_child_families',
        'three_child_families',
        'single_parent_families',
        'minors',
        'disabilities',
        'family_disabilities',
      ].includes(qualification.qualifiable_type_slug));
    },

    goToApplicationContest() {
      window.location.href = `/contractuals/contests/${this.application.contest_id}`;
    },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
    td.centered {
        text-align: center; /* center checkbox horizontally */
        vertical-align: middle; /* center checkbox vertically */
    }

    section.auxiliary_level {
        margin-bottom: 2rem;
    }

    section.requirements {
        margin-right: 3rem;
    }

    .section.qualifiactions {
        margin-left: 3rem;
    }

    h5.capitalized {
        color: hsl(220, 3%, 38.8%);
        margin-bottom: 1rem;
        font-weight: 600;
    }
</style>
