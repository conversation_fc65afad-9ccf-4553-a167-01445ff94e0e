<script setup lang="ts">

import useHttp from '@/shared/composables/useHttp';
import useNavigation from '@/shared/composables/useNavigation';
import useSwal from '@/shared/composables/useSwal';

const { redirectTo, scrollTop } = useNavigation();
const { confirm, error: errorAlert, success: successAlert } = useSwal();
const { delete: destroy } = useHttp();

const props = defineProps<{
    contestId: number;
}>();

function deleteContest() {
  confirm('Ο διαγωνισμός μαζί με όλες τις θέσεις (αν έχουν καταχωρηθεί) θα διαγραφεί οριστικά. Προσοχή η ενέργεια αυτή δεν είναι αναστρέψιμη')
    .then(() => {
      destroy(`/api/contractuals/contests/${props.contestId}`)
        .then((res) => {
          successAlert({ message: res.message }, () => {
            redirectTo('/contractuals/contests');
          });
        })
        .catch((err) => {
          errorAlert({ message: err.response.data.message });
        });
    });
}
</script>

<template>
  <button
    class="btn btn-danger btn-block btn-outline"
    @click="deleteContest"
  >
    Διαγραφή διαγωνισμού
  </button>
</template>
