<template>
  <div>
    <Box title="Στοιχεία διαγωνισμού">
      <div class="row">
        <!-- Contest details -->
        <div class="col-sm-10">
          <ContestDetails :contest="contest" />
        </div>
        <!--
        Contest secondary actions
        -->
        <div class="col-sm-2 text-right">
          <ToggleRestrictedStatusButton
            v-if="contest.can.admin"
            :contest="contest"
            @toggle-restricted-status="updateRestrictedStatus"
          />
          <!-- Show/edit/import contest positions -->
          <a
            v-if="!contestIsLocked"
            :href="`/contractuals/contests/${contest.id}/positions`"
            :class="['btn', 'btn-control', 'btn-block']"
          >
            <i
              class="fa fa-search"
              aria-hidden="true"
            />
            Προβολή θέσεων
          </a>
          <!-- FIXME: Should appear when contest is ranked AND calculation has finished -->
          <a
            v-if="latestCalculation && !latestCalculation.is_distribution"
            :href="`/contractuals/contests/${contest.id}/ranked-positions`"
            :class="['btn', 'btn-control', 'btn-block']"
          >
            <i
              class="fa fa-table"
              aria-hidden="true"
            />
            Προβολή πινάκων
          </a>
          <a
            v-if="contest.can.admin"
            :href="`/contractuals/statistics/front-office?contest_id=${contest.id}`"
            class="btn btn-control btn-block"
          >
            <i
              class="fa fa-line-chart"
              aria-hidden="true"
            />
            Στατιστικά Front Office
          </a>
          <a
            v-if="hasApplications && contest.can.admin"
            :href="`/contractuals/statistics/back-office?contest_id=${contest.id}`"
            class="btn btn-control btn-block"
          >
            <i
              class="fa fa-line-chart"
              aria-hidden="true"
            />
            Στατιστικά Back Office
          </a>
        </div>
      </div>
      <!--
      Contest primary actions
      -->
      <div
        v-if="contest.can.admin"
        class="row"
      >
        <div
          v-if="!contestIsLocked"
          class="col-sm-3"
        >
          <a
            :href="`/contractuals/contests/${contest.id}/edit`"
            class="btn btn-primary btn-block btn-outline"
          >
            Επεξεργασία διαγωνισμού
          </a>
        </div>
        <!-- Toggle contest lock status -->
        <div
          v-if="!contestIsRanked && !hasApplications"
          class="col-sm-3"
        >
          <ToggleLockedStatusButton
            :contest="contest"
            @toggle-locked-status="updateLockedStatus"
          />
        </div>
        <!-- Distribute applications -->
        <div
          v-if="!contest.has_single_unit && contestIsLocked && hasApplications && !contestIsRated && !contestIsRanked && !contestIsRanking"
          class="col-sm-3"
        >
          <DistributeContestButton
            :contest="contest"
            @success="notifyUser"
          />
        </div>
        <!-- Toggle rated status -->
        <div
          v-if="contestIsLocked && hasApplications && !contestIsRanked && !contestIsRanking"
          class="col-sm-3"
        >
          <ToggleRatedStatusButton
            :contest="contest"
            @toggle-rated-status="updateRatedStatus"
          />
        </div>
        <!-- rank -->
        <div
          v-if="contestIsRated && !contestIsRanked && !contestIsRanking"
          class="col-sm-3"
        >
          <RankContestButton
            :contest="contest"
            @calculation-job:started="notifyUser"
          />
        </div>
        <!-- is ranking -->
        <div
          v-if="contestIsRanking"
          class="col-sm-6 col-sm-offset-3"
        >
          <InfoAlert>
            <p>
              <i
                class="fa fa-spinner fa-spin"
                aria-hidden="true"
              /> Πραγματοποιείται υπολογισμός πινάκων.
            </p>
            <p>
              Όταν ο υπολογισμός οκληρωθεί, θα λάβετε σχετική ειδοποίηση με email στην διεύθυνση {{ contest.notifications_email }}.
              Εάν οι πίνακες δεν εμφανίζονται μετά την επιτυχή ολοκλήρωση του υπολογισμού, παρακαλούμε
              κάνετε <strong>refresh</strong> την σελίδα.
            </p>
          </InfoAlert>
        </div>
        <!-- unrank -->
        <div
          v-if="contestIsRated && contestIsRanked"
          class="col-sm-3"
        >
          <UnrankContestButton
            :contest="contest"
            @unrank-contest="unrankContest"
          />
        </div>
      </div>
    </Box>
  </div>
</template>

<script>
import DistributeContestButton from '@/contractuals/components/calculation/DistributeContestButton.vue';
import RankContestButton from '@/contractuals/components/calculation/RankContestButton.vue';
import ContestDetails from '@/contractuals/components/contest/ContestDetails.vue';
import ToggleLockedStatusButton from '@/contractuals/components/contest/ToggleLockedStatusButton.vue';
import ToggleRatedStatusButton from '@/contractuals/components/contest/ToggleRatedStatusButton.vue';
import ToggleRestrictedStatusButton from '@/contractuals/components/contest/ToggleRestrictedStatusButton.vue';
import UnrankContestButton from '@/contractuals/components/contest/UnrankContestButton.vue';
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';

export default {
  name: 'ContestActionPanel',

  components: {
    ContestDetails,
    DistributeContestButton,
    UnrankContestButton,
    ToggleRatedStatusButton,
    ToggleLockedStatusButton,
    Box,
    ToggleRestrictedStatusButton,
    RankContestButton,
    InfoAlert,
  },

  props: {
    contestData: {
      type: Object,
      required: true,
    },
    hasApplications: {
      type: Boolean,
      required: true,
    },
    isCalculating: {
      type: Boolean,
      required: true,
    },
    latestCalculation: {
      type: Object,
      default() { return null; },
    },
  },
  data() {
    return {
      contest: this.contestData,
      rankedPositionCount: 0,
      rankedPositionCode: null,
      contestIsRanking: this.isCalculating,
    };
  },
  computed: {
    // contestHasPositions() {
    //   return this.contest.positions.length > 0;
    // },
    contestIsLocked() {
      return !!this.contest.locked_at;
    },
    contestIsRestricted() {
      return !!this.contest.restricted_at;
    },
    contestIsRated() {
      return !!this.contest.rated_at;
    },
    contestIsRanked() {
      return !!this.contest.ranked_at;
    },
  },
  methods: {
    updateLockedStatus(contest) {
      this.contest.locked_at = contest.locked_at;
      this.$emit('update-locked-status', contest);
    },
    updateRestrictedStatus(contest) {
      this.contest.restricted_at = contest.restricted_at;
    },
    updateRatedStatus(contest) {
      this.contest.rated_at = contest.rated_at;
    },
    notifyUser(message) {
      this.$swal.success(message);
      this.contestIsRanking = true;
    },
    unrankContest(contest) {
      this.contest.ranked_at = contest.ranked_at;
    },
  },
};
</script>
