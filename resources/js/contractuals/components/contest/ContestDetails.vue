<template>
  <div class="contest">
    <div class="contest__title">
      <h4 class="text-bold">
        {{ contest.name }}
      </h4>
      <p><i class="fa fa-calendar" /> {{ contest.start_date }} - {{ contest.end_date }}</p>
      <p>ΑΔΑ: {{ contest.ada }}</p>
      <p>Αρ.Πρωτ: {{ contest.protocol }}</p>
    </div>
    <div class="contest__description">
      <dl class="max-w-prose">
        <dt>Περιγραφή</dt>
        <dd>{{ contest.description }}</dd>
      </dl>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ContestDetails',
  props: {
    contest: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style scoped>
.contest {
  display: flex;
}

.contest__description {
  padding-left: 40px;
}
</style>
