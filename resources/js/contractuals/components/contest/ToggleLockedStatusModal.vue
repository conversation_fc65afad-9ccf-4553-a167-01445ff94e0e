<template>
  <Modal
    v-if="open"
    @close="cancel"
  >
    <ConfirmationModal
      :open="open"
      :is-loading="isLoading"
      @confirm="toggleLockedStatus"
      @cancel="cancel"
    >
      <div
        v-if="errorMessage"
        class="callout callout-danger"
      >
        {{ errorMessage }}
      </div>
      {{ confirmationText }}
    </ConfirmationModal>
  </Modal>
</template>

<script>
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';
import Modal from '@/shared/components/ui/Modal/Modal.vue';

export default {
  name: 'ToggleLockedStatusModal',
  components: {
    ConfirmationModal,
    Modal,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
    contest: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
      errorMessage: '',
    };
  },
  computed: {
    isLocked() {
      return this.contest.locked_at !== null;
    },
    confirmationText() {
      return this.isLocked
        ? 'Θέλετε να ξελειδώσετε τον διαγωνισμό;'
        : 'Θέλετε να κλειδώσετε τον διαγωνισμό;';
    },
  },
  methods: {
    toggleLockedStatus() {
      this.errorMessage = '';
      this.isLoading = true;
      this.$http.put(`/api/contractuals/contests/${this.contest.id}/locked-status`)
        .then((res) => {
          this.$emit('success', res.data);
        })
        .catch((error) => {
          this.errorMessage = `Σφάλμα ${error.response.status}: ${error.response.data.message}`;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    cancel() {
      this.errorMessage = '';
      this.$emit('cancel');
    },
  },
};
</script>
