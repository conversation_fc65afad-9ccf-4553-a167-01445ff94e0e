<template>
  <div>
    <button
      type="button"
      class="btn btn-control btn-block"
      style="margin-bottom: 5px;"
      @click="openModal = true"
    >
      <i :class="['fa', contestIsRestricted ? 'fa-ban': 'fa-globe']" />
      {{ restrictedStatusButtonText }}
    </button>
    <ToggleRestrictedStatusModal
      :open="openModal"
      :contest="contest"
      @success="toggleRestrictedStatus"
      @cancel="openModal = false"
    />
  </div>
</template>

<script>
import ToggleRestrictedStatusModal from '@/contractuals/components/contest/ToggleRestrictedStatusModal.vue';

export default {
  name: 'ToggleRestrictedStatusButton',
  components: { ToggleRestrictedStatusModal },
  props: {
    contest: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      openModal: false,
    };
  },
  computed: {
    contestIsRestricted() {
      return this.contest.restricted_at !== null;
    },
    restrictedStatusButtonText() {
      return this.contestIsRestricted ? 'Άρση αποκλεισμού' : 'Αποκλεισμός περιφερειών';
    },
  },
  methods: {
    toggleRestrictedStatus(contest) {
      this.openModal = false;
      this.$emit('toggle-restricted-status', contest);
    },
  },
};
</script>
