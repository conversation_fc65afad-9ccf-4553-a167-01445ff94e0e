<template>
  <ConfirmationModal
    :open="open"
    :is-loading="isLoading"
    @confirm="toggleRatedStatus"
    @cancel="cancel"
  >
    <DangerAlert
      v-if="errorMessage"
      :message="errorMessage"
    />
    {{ confirmationText }}
  </ConfirmationModal>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';

export default {
  name: 'ToggleRatedStatusModal',
  components: {
    DangerAlert,
    ConfirmationModal,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
    contest: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
      errorMessage: '',
    };
  },
  computed: {
    contestIsRated() {
      return this.contest.rated_at !== null;
    },
    confirmationText() {
      return this.contestIsRated
        ? 'Επιβεβαιώστε ότι θέλετε να επαναβαθμολογήσετε τις αιτήσεις.'
        : 'Επιβεβαιώστε ότι θέλετε να οριστικοποιήσετε την βαθμολόγηση των αιτήσεων';
    },
  },
  methods: {
    toggleRatedStatus() {
      this.errorMessage = '';
      this.isLoading = true;
      this.$http.put(`/api/contractuals/contests/${this.contest.id}/rated-status`)
        .then((res) => {
          this.$emit('success', res.data);
        })
        .catch((error) => {
          this.errorMessage = error.response.data.message;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    cancel() {
      this.errorMessage = '';
      this.$emit('cancel');
    },
  },
};
</script>
