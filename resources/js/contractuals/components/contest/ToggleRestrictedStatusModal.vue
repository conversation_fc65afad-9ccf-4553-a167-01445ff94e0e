<template>
  <ConfirmationModal
    :open="open"
    :is-loading="isLoading"
    @confirm="toggleRestrictedStatus"
    @cancel="cancel"
  >
    <div
      v-if="errorMessage"
      class="callout callout-danger"
    >
      {{ errorMessage }}
    </div>
    <div>
      <p>{{ confirmationText }}</p>
    </div>
  </ConfirmationModal>
</template>

<script>

import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';

export default {
  name: 'ToggleRestrictedStatusModal',
  components: {
    ConfirmationModal,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
    contest: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
      errorMessage: '',
    };
  },
  computed: {
    isRestricted() {
      return this.contest.restricted_at !== null;
    },
    confirmationText() {
      return this.isRestricted
        ? 'Θέλετε να επιτρέψεται στις Περιφερειακές Υπηρεσίες να επεξεργάζονται τις αιτήσεις'
        : 'Θέλετε να απαγορέψετε στις Περιφερειακές Υπηρεσίες να επεξεργάζονται τις αιτήσεις';
    },
  },
  methods: {
    toggleRestrictedStatus() {
      this.errorMessage = '';
      this.isLoading = true;
      this.$http.put(`/api/contractuals/contests/${this.contest.id}/restricted-status`)
        .then((res) => {
          this.$emit('success', res.data);
        })
        .catch((error) => {
          this.errorMessage = `Σφάλμα ${error.response.status}: ${error.response.data.message}`;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    cancel() {
      this.errorMessage = '';
      this.$emit('cancel');
    },
  },
};
</script>
