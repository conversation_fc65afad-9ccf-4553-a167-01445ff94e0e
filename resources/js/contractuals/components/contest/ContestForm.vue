<template>
  <Box title="Καταχώρηση νέου Διαγωνισμού">
    <form
      id="contestForm"
      @input="form.errors.clear($event.target.name)"
    >
      <div class="row">
        <div class="col-sm-6">
          <SelectField
            v-model="form.data.type_id"
            title="Είδος Διαγωνισμού *"
            name="type_id"
            :error="form.errors.get('type_id')"
            :options="menuOptions['contestTypes']"
            @input="form.errors.clear('type_id')"
          />
        </div>
        <div class="col-sm-6">
          <TextField
            v-model="form.data.ada"
            title="ΑΔΑ *"
            name="ada"
            :error="form.errors.get('ada')"
          />
        </div>
      </div>
      <div class="row">
        <div class="col-sm-6">
          <TextField
            v-model="form.data.protocol_number"
            title="Αριθμός πρωτοκόλλου προκήρυξης *"
            name="protocol_number"
            :error="form.errors.get('protocol_number')"
          />
        </div>
        <div class="col-sm-6">
          <DateField
            v-model="form.data.protocol_date"
            title="Ημερομηνία πρωτοκόλλου προκήρυξης *"
            name="protocol_date"
            :error="form.errors.get('protocol_date')"
            @input="form.errors.clear('protocol_date')"
          />
        </div>
      </div>
      <div class="row">
        <div class="col-sm-12">
          <TextareaField
            v-model="form.data.name"
            title="Ονομασία Διαγωνισμού * (πχ. ΣΟΧ 1/2021)"
            name="name"
            :error="form.errors.get('name')"
          />
        </div>
      </div>
      <div class="row">
        <div class="col-sm-12">
          <TextareaField
            v-model="form.data.description"
            title="Περιγραφή Διαγωνισμού *"
            name="description"
            :error="form.errors.get('name')"
          />
        </div>
      </div>
      <div class="row">
        <div class="col-sm-6">
          <DateTimePicker
            v-model="form.data.start_date"
            title="Ημερομηνία έναρξης Διαγωνισμού *"
            name="start_date"
            :error="form.errors.get('start_date')"
            @input="form.errors.clear('start_date')"
          />
        </div>
        <div class="col-sm-6">
          <DateTimePicker
            v-model="form.data.end_date"
            title="Ημερομηνία λήξης Διαγωνισμού *"
            name="end_date"
            :error="form.errors.get('end_date')"
            @input="form.errors.clear('end_date')"
          />
        </div>
      </div>
      <div class="row">
        <div class="col-sm-12">
          <SelectmultipleField
            v-model="form.data.units"
            title="καταχώρηση Αιτήσεων από τις Υπηρεσίες"
            name="units"
            :error="form.errors.get('units')"
            :options="menuOptions['units']"
            @valid="form.errors.clear('units')"
          />
        </div>
      </div>
      <section class="row">
        <h4 class="col-sm-12">
          Ρυθμίσεις export
        </h4>
        <div class="col-sm-6">
          <TextField
            v-model="form.data.organization"
            title="Όνομα φορέα (πχ. ΥΠΠΟ) *"
            name="organization"
            :error="form.errors.get('organization')"
          />
        </div>
        <div class="col-sm-6">
          <TextField
            v-model="form.data.contract_duration"
            title="Διάρκεια σύμβασης (πχ. 7 μήνες) *"
            name="contract_duration"
            :error="form.errors.get('contract_duration')"
          />
        </div>
        <div class="col-sm-6">
          <TextField
            v-model="form.data.undersigned_name"
            title="Ονοματεπώνυμο υπογράφοντα/ούσας *"
            name="undersigned_name"
            :error="form.errors.get('undersigned_name')"
          />
        </div>
        <div class="col-sm-6">
          <TextField
            v-model="form.data.undersigned_title_first_row"
            title="Βαθμός υπογράφοντα/οuσας (1η γραμμή) *"
            name="undersigned_title_first_row"
            placeholder="πχ: Η Γενική Διευθύντρια"
            :error="form.errors.get('undersigned_title_first_row')"
          />
        </div>
        <div class="col-sm-6 col-sm-offset-6">
          <TextField
            v-model="form.data.undersigned_title_second_row"
            title="Βαθμός υπογράφοντα/οuσας (2η γραμμή)"
            name="undersigned_title_second_row"
            placeholder="πχ: Αρχαιοτήτων και Πολιτιστικής Κληρονομιάς"
            :error="form.errors.get('undersigned_title_second_row')"
          />
        </div>
      </section>
      <section class="row">
        <h4 class="col-sm-12">
          Ρυθμίσεις επικοινωνίας
        </h4>
        <div class="col-sm-6">
          <TextField
            v-model="form.data.notifications_email"
            title="Email επικοινωνίας (για την αποστολή ενημερώσεων απο το σύστημα) *"
            name="notifications_email"
            :error="form.errors.get('notifications_email')"
          />
        </div>
      </section>
    </form>
    <template #footer>
      <div class="row">
        <div class="col-sm-3">
          <button
            class="btn btn-block btn-default"
            onclick="window.history.back();"
          >
            <i
              class="fa fa-arrow-left"
              aria-hidden="true"
            /> Επιστροφή
          </button>
        </div>
        <div class="col-sm-3">
          <button
            class="btn btn-block btn-default"
            onclick="window.location.reload();"
          >
            <i
              class="fa fa-refresh"
              aria-hidden="true"
            /> Επαναφορά
          </button>
        </div>
        <div
          v-if="isEditForm"
          class="col-sm-3"
        >
          <ContestDeleteButton :contest-id="contest.id" />
        </div>
        <div :class="[isEditForm ? 'col-sm-3' : 'col-sm-6']">
          <button
            class="btn btn-block btn-primary"
            @click="submitForm"
          >
            <i
              class="fa fa-paper-plane"
              aria-hidden="true"
            /> Καταχώρηση
          </button>
        </div>
      </div>
    </template>
  </Box>
</template>

<script>
import ContestDeleteButton from '@/contractuals/components/contest/ContestDeleteButton.vue';

import Box from '../../../shared/components/ui/Boxes/Box.vue';
import DateField from '../../../shared/components/ui/FormFields/DateField.vue';
import DateTimePicker from '../../../shared/components/ui/FormFields/DateTimePicker.vue';
import SelectField from '../../../shared/components/ui/FormFields/SelectField.vue';
import SelectmultipleField from '../../../shared/components/ui/FormFields/SelectmultipleField.vue';
import TextareaField from '../../../shared/components/ui/FormFields/TextareaField.vue';
import TextField from '../../../shared/components/ui/FormFields/TextField.vue';
import Form from '../../../shared/Form';

export default {
  name: 'ContestForm',

  components: {
    ContestDeleteButton,
    DateTimePicker,
    SelectmultipleField,
    DateField,
    TextField,
    SelectField,
    TextareaField,
    Box,
  },

  props: {
    contest: Object,
    menuOptions: Object,
  },

  data() {
    return {
      form: new Form({
        type_id: '',
        protocol_number: '',
        protocol_date: '',
        name: '',
        description: '',
        ada: '',
        start_date: '',
        end_date: '',
        units: [],
        organization: '',
        contract_duration: '',
        undersigned_title_first_row: '',
        undersigned_title_second_row: '',
        undersigned_name: '',
        notifications_email: '',
      }),
    };
  },

  computed: {
    isEditForm() {
      return this.contest !== undefined;
    },
  },

  mounted() {
    if (this.isEditForm) {
      this.form.populate(this.contest);
    }
  },

  methods: {
    submitForm() {
      let action;
      let
        url;

      if (this.isEditForm) {
        action = 'put';
        url = `/contractuals/contests/${this.contest.id}`;
      } else {
        action = 'post';
        url = '/contractuals/contests';
      }

      this.form[action](url)
        .then((response) => {
          window.swal({
            type: 'success',
            title: 'Επιτυχία!',
            text: response.data.message,
            timer: 3000,
            showConfirmButton: false,
          }, () => {
            window.location.href = '/contractuals/contests';
          });
        })
        .catch((error) => {
          window.swal({
            type: 'error',
            title: `${error.response.status} Σφάλμα Καταχώρησης`,
            text: 'Παρακαλώ ελέγξτε τα στοιχεία της φόρμας',
            showConfirmButton: false,
            timer: 3000,
          });
        });
    },
  },
};
</script>

<style scoped>

</style>
