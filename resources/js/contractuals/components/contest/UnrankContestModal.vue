<template>
  <ConfirmationModal
    :open="open"
    :is-loading="isLoading"
    @confirm="unrankContest"
    @cancel="cancel"
  >
    <div
      v-if="errorMessage"
      class="callout callout-danger"
    >
      {{ errorMessage }}
    </div>
    Θέλετε να επανυπολογίσετε τους οριστικούς πίνακες κατάταξης;
  </ConfirmationModal>
</template>

<script>
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';

export default {
  name: 'UnrankContestModal',
  components: {
    ConfirmationModal,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
    contest: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
      errorMessage: '',
    };
  },
  methods: {
    unrankContest() {
      this.clearErrorMessage();
      this.isLoading = true;
      this.$http.delete(`/api/contractuals/contests/${this.contest.id}/ranked-status`)
        .then((res) => {
          this.$emit('success', res.data);
        })
        .catch((error) => {
          this.errorMessage = `Σφάλμα ${error.response.status}: ${error.response.data.message}`;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    cancel() {
      this.clearErrorMessage();
      this.$emit('cancel');
    },
    clearErrorMessage() {
      this.errorMessage = '';
    },
  },
};
</script>
