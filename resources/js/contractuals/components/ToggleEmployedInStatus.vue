<template>
  <div>
    <button
      v-show="rankingData.accepted !== 0"
      type="button"
      :class="['btn btn-sm', rankingData.accepted ? 'btn-primary' : 'btn-default']"
      @click="toggleAcceptPosition"
      v-text="'ΝΑΙ'"
    />
    <button
        v-show="rankingData.accepted !== 1"
      type="button"
      :class="['btn btn-sm', rankingData.accepted === 0 ? 'btn-danger' : 'btn-default']"
      @click="toggleRejectPosition"
      v-text="'ΟΧΙ'"
    />
  </div>
</template>
<script>
export default {
  name: 'ToggleEmployedInStatus',
  props: {
    rankingData: {
      type: Object,
      required: true,
    },
  },
  methods: {
    toggleAcceptPosition() {
      this.$http.put(`/api/contractuals/contests/${this.rankingData.contest_id}/applications/${this.rankingData.application_id}/employed-in-status`, {
        position_accepted: true,
        position_id: this.rankingData.accepted !== null ? null : this.rankingData.position_id,
      })
        .then((res) => {
          this.$emit("toggle-employed-in-status", {id: this.rankingData.id, accepted: res.data.accepted});
        })
        .catch((err) => {
          window.swal({
            type: 'error',
            title: `Σφάλμα ${err.response.status}`,
            text: err.response.data.message,
            showConfirmButton: false,
            timer: 4000,
          });
        });
    },
    toggleRejectPosition() {
      this.$http.put(`/api/contractuals/contests/${this.rankingData.contest_id}/applications/${this.rankingData.application_id}/employed-in-status`, {
        position_accepted: false,
        position_id: this.rankingData.accepted === 0 ? null : 0,
      })
        .then((res) => {
          this.$emit("toggle-employed-in-status", {id: this.rankingData.id, accepted: res.data.accepted});
        })
        .catch((err) => {
          window.swal({
            type: 'error',
            title: `Σφάλμα ${err.response.status}`,
            text: err.response.data.message,
            showConfirmButton: false,
            timer: 4000,
          });
        });
    },
  },
};
</script>
