<template>
  <ConfirmationModal
    :open="open"
    :is-loading="isLoading"
    @confirm="rankContest"
    @cancel="cancel"
  >
    <DangerAlert
      v-if="errorMessage"
      :message="errorMessage"
    />
    <div class="row">
      <div class="col-sm-12">
        <WarningAlert>
          <p>Πρόκειται να προχωρήσετε στον <em>υπολογισμό των τελικών πινάκων</em>.</p>
          <p>
            Αν θέλετε να συνεχίσετε, παρακαλούμε εισάγετε μια περιγραφή
            για τον υπολογισμό και μετά πατήστε επιβεβαίωση.
          </p>
        </WarningAlert>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <form>
          <TextareaField
            v-model="form.data.description"
            title="Περιγραφή"
            name="description"
            :error="form.errors.get('description')"
          />
          <label>
            <input
              v-model="form.data.hire_runners_up"
              type="checkbox"
            >
            Υπολογισμός επιλαχόντων
          </label>
          <InfoAlert v-if="form.data.hire_runners_up">
            <strong>Προσοχή!</strong> Ο υπολογισμός θα γίνει λαμβάνοντας
            υπόψη το αν κάποιοι υποψήφιοι έχουν ήδη διοριστεί.
          </InfoAlert>
        </form>
      </div>
    </div>
  </ConfirmationModal>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';
import WarningAlert from '@/shared/components/ui/Alerts/WarningAlert.vue';
import TextareaField from '@/shared/components/ui/FormFields/TextareaField.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';
import Form from '@/shared/Form';

export default {
  name: 'RankContestModal',
  components: {
    InfoAlert,
    DangerAlert,
    ConfirmationModal,
    TextareaField,
    WarningAlert,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
    contest: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
      errorMessage: null,
      form: new Form({
        description: '',
        hire_runners_up: false,
        contest_id: this.contest.id,
      }),
    };
  },
  methods: {
    rankContest() {
      this.errorMessage = null;
      this.isLoading = true;
      this.form.post('/api/contractuals/ranked-contests')
        .then((res) => {
          this.form.reset();
          this.$emit('success', res.data.message);
        })
        .catch((error) => {
          this.errorMessage = error.response.data.message;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    cancel() {
      this.errorMessage = null;
      this.form.data.description = '';
      this.form.data.hire_runners_up = false;
      this.$emit('cancel');
    },
  },
};
</script>
