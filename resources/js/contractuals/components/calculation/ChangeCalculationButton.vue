<template>
  <div>
    <button
      type="button"
      class="btn btn-control btn-block"
      style="margin-bottom: 5px;"
      @click="openModal = true"
    >
      <i class="fa fa-history" />
      Αλλαγή υπολογισμού
    </button>
    <BasicModal
      :open="openModal"
      @close="openModal = false"
    >
      <template #header>
        Αλλαγή υπολογισμού
      </template>
      <SelectCalculationForPositions
        :contest-id="contestId"
        :calculation-id="calculationId"
      />
    </BasicModal>
  </div>
</template>

<script>
import SelectCalculationForPositions from '@/contractuals/components/calculation/SelectCalculationForPositions.vue';
import BasicModal from '@/shared/components/ui/Modal/BasicModal.vue';

export default {
  name: 'ChangeCalculationButton',
  components: { SelectCalculationForPositions, BasicModal },
  props: {
    contestId: {
      type: Number,
      required: true,
    },
    calculationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      openModal: false,
    };
  },
};
</script>

<style scoped>

</style>
