<template>
  <div>
    <button
      type="button"
      class="btn btn-primary btn-block btn-outline"
      @click="openModal = true"
    >
      <i class="fa fa-exchange" /> Διαμοιρασμός αιτήσεων
    </button>
    <DistributeContestModal
      :open="openModal"
      :contest="contest"
      @success="notifyUser"
      @cancel="openModal = false"
    />
    <BasicModal
      :open="openNotification"
      @close="openNotification = false"
    >
      <template #header>
        <h4>O διαμοιρασμός αιτήσεων έχει ξεκινήσει</h4>
      </template>
      <template #default>
        <p>Θα ειδοποιηθείτε μόλις αυτός τελειώσει</p>
      </template>
      <template #footer>
        <button
          type="button"
          class="btn btn-primary btn-block"
          @click="openNotification = false"
        >
          Εντάξει, θα περιμένω!
        </button>
      </template>
    </BasicModal>
  </div>
</template>

<script>
import DistributeContestModal from '@/contractuals/components/calculation/DistributeContestModal.vue';
import BasicModal from '@/shared/components/ui/Modal/BasicModal.vue';

export default {
  name: 'DistributeContestButton',
  components: { BasicModal, DistributeContestModal },
  props: {
    contest: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      openModal: false,
      openNotification: false,
    };
  },
  methods: {
    notifyUser(message) {
      this.openModal = false;
      this.$emit('success', message);
    },
    close() {
      this.openModal = false;
    },
  },
};
</script>
