<template>
  <div>
    <button
      type="button"
      class="btn btn-primary btn-block"
      @click="openModal = true"
    >
      <i class="fa fa-cogs" /> Υπολογισμός πινάκων
    </button>
    <RankContestModal
      :open="openModal"
      :contest="contest"
      @success="onSuccess"
      @cancel="closeModal"
    />
  </div>
</template>

<script>
import RankContestModal from '@/contractuals/components/calculation/RankContestModal.vue';

export default {
  name: 'RankContestButton',
  components: { RankContestModal },
  props: {
    contest: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      openModal: false,
    };
  },
  methods: {
    onSuccess(message) {
      this.$emit('calculation-job:started', message);
      this.closeModal();
    },
    closeModal() {
      this.openModal = false;
    },
  },
};
</script>
