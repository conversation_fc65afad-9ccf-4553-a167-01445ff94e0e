<template>
  <ConfirmationModal
    :open="open"
    :is-loading="isLoading"
    @confirm="rankContest"
    @cancel="cancel"
  >
    <DangerAlert
      v-if="errorMessage"
      :message="errorMessage"
    />
    <div class="row">
      <div class="col-sm-12">
        <WarningAlert>
          <p>Πρόκειται να προχωρήσετε στον <em>διαμοιρασμό των αιτήσεων</em> στις αρμόδιες Υπηρεσίες.</p>
          Αν θέλετε να συνεχίσετε παρακαλούμε πατήστε επιβεβαίωση
        </WarningAlert>
      </div>
    </div>
    <div class="row">
      <form>
        <div class="col-sm-12">
          <SelectField
              v-model="form.data.n"
              title="Πολλαπλασιαστής Ν"
              name="n"
              :error="form.errors.get('n')"
              :options="nOptions"
          />
        </div>
        <div class="col-sm-12">
          <TextField
              v-model="form.data.limit"
              title="Πλήθος αιτήσεων για Ομάδα Εργασίας"
              name="limit"
              :error="form.errors.get('limit')"
          />
        </div>
      </form>
    </div>
  </ConfirmationModal>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import WarningAlert from '@/shared/components/ui/Alerts/WarningAlert.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';
import Form from "@/shared/Form";
import TextField from "@/shared/components/ui/FormFields/TextField";
import SelectField from "@/shared/components/ui/FormFields/SelectField";

export default {
  name: 'DistributeContestModal',
  components: {
    WarningAlert,
    DangerAlert,
    ConfirmationModal,
    TextField,
    SelectField,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
    contest: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
      errorMessage: null,
      form: new Form({
        n: 1,
        limit: 500,
        contest_id: this.contest.id,
      }),
      nOptions: {
        1: '1',
        2: '2',
        3: '3',
        4: '4',
        5: '5',
        6: '6',
        7: '7',
        8: '8',
        9: '9',
      }
    };
  },
  methods: {
    rankContest() {
      this.errorMessage = null;
      this.isLoading = true;
      this.form.post('/api/contractuals/distributed-contests')
      // this.$http.post('/api/contractuals/distributed-contests', {
      //   contest_id: this.contest.id,
      // })
        .then((res) => {
          this.$emit('success', res.data.message);
        })
        .catch((error) => {
          this.errorMessage = error.response.data.message;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    cancel() {
      this.errorMessage = null;
      this.$emit('cancel');
    },
  },
};
</script>
