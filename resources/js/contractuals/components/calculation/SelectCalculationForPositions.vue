<template>
  <div class="calculation">
    <SelectField
      v-model="selectedCalculationId"
      class="calculation__search"
      title="Επιλέξτε calculation"
      name="calculation_id"
      :options="availableCalculations"
    />
    <a
      :href="redirectUrl"
      class="btn btn-primary"
      style="margin-top: 25px"
    >
      επιλογή
    </a>
  </div>
</template>

<script>
import SelectField from '@/shared/components/ui/FormFields/SelectField.vue';

export default {
  name: 'SelectCalculationForPositions',
  components: { SelectField },
  props: {
    contestId: {
      type: Number,
      required: true,
    },
    calculationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      selectedCalculationId: this.calculationId,
      availableCalculations: [],
    };
  },
  computed: {
    redirectUrl() {
      return `/contractuals/contests/${this.contestId}/ranked-positions?calculation=${this.selectedCalculationId}`;
    },
  },
  mounted() {
    this.$http.get(`/api/contractuals/contests/${this.contestId}/calculations`)
      .then((res) => {
        this.availableCalculations = res.data;
      })
      .catch((err) => {
        this.$swal.error(err);
      });
  },
};
</script>

<style scoped>

</style>
