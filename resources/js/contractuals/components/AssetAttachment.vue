<template>
    <div>
        <!--=============================================>>>>>
        = Upload new files =
        ===============================================>>>>-->

        <upload-field name="files"
                      :url="storeUrl"
                      @success="updateAttachments"
        >
        </upload-field>

        <!--=============================================>>>>>
        = List uploaded files =
        ===============================================>>>>-->

        <div class="row">
            <div class="col-sm-12">
                <h4>Συνημμένα αρχεία</h4>
                <ul class="list-group">
                    <li class="list-group-item"
                        v-for="attachment in attachments"
                        :key="attachment.id"
                    >
                        <i :class="getAttachmentIconClass(attachment)" aria-hidden="true"></i>
                        <a :href="attachment.url">{{ attachment.name }}</a>
                        <button type="button"
                                class="btn-grow text-blue hover-text-red"
                                @click="removeAttachment(attachment)"
                        >
                            <i class="fa fa-times"></i>
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
    import UploadField from '../../shared/components/ui/FormFields/UploadLegacyField.vue'

    export default {
        name: 'AssetAttachment',

        components: {
            UploadField,
        },

        props: {
            asset: Object,
            assetType: String,
        },

        data() {
            return {
                attachments: [],
                storeUrl: `/api/contractuals/assets/${this.assetType}/${this.asset.id}/attachments`,
                indexUrl: `/api/contractuals/assets/${this.assetType}/${this.asset.id}/attachments`,
                deleteUrl: `/api/contractuals/assets/${this.assetType}/${this.asset.id}/attachments`,
                active: false
            }
        },

        created() {
            this.$http.get(this.indexUrl)
                .then(response => {
                    this.attachments = response.data;
                })
                .catch(error => console.log(error));
        },

        methods: {
            updateAttachments(updatedAttachments) {
                this.attachments = updatedAttachments;
                this.$emit('attachments-updated', this.attachments);
            },

            removeAttachment(attachment) {
                this.$http.delete(this.deleteUrl, {params: {'name': attachment.name}})
                    .then(res => {
                        this.attachments = res.data
                        this.$emit('attachments-updated', this.attachments);
                    })
                    .catch(err => console.log(err));
            },

            getAttachmentIconClass(attachment) {
                let fileExt = attachment.downloadName.split('.').pop();
                switch (fileExt) {
                    case 'doc':
                    case 'docx':
                    case 'odt':
                        return 'fa fa-file-word-o text-blue';
                    case 'zip':
                    case 'rar':
                        return 'fa fa-file-archive-o text-yellow';
                    case 'pdf':
                        return 'fa fa-file-pdf-o text-red';
                    case 'xls':
                    case 'xlsx':
                    case 'ods':
                    case 'csv':
                        return 'fa fa-file-excel-o text-green';
                    default:
                        return 'fa fa-file-o';
                }
            },
        }
    };
</script>

<style lang="scss" rel="stylesheet/scss">
    .btn-grow {
        padding: 5px 10px;
        background-color: inherit;
        border: none;
        transition: all .2s ease-in-out;

        &:focus {
            outline: none; // stackoverflow.com/questions/19053181/how-to-remove-focus-around-buttons-on-click
        }

        &:hover {
            transform: scale(1.2);
        }
    }
</style>