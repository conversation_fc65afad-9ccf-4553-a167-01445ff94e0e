<template>
  <div>
    <form
      id="applicantForm"
      method="POST"
      action="./"
      @submit.prevent="submitForm"
      @keydown="form.errors.clear($event.target.name)"
    >
      <!--TABS-->
      <tabs>
        <!--=============================================>>>>>
                = Προσωπικά στοιχεία =
                ===============================================>>>>-->

        <tab
          name="Προσωπικά στοιχεία"
          :has-errors="tabHasErrors('name', 'surname')"
          :selected="true"
        >
          <div class="row">
            <div class="col-sm-6">
              <!--'Ονομα-->
              <text-field
                v-model="form.data.name"
                title="ΟΝΟΜΑ *"
                name="name"
                :error="form.errors.get('name')"
              />
            </div>
            <div class="col-sm-6">
              <!--Επώνυμο-->
              <text-field
                v-model="form.data.surname"
                title="ΕΠΩΝΥΜΟ *"
                name="surname"
                :error="form.errors.get('surname')"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <!--Ονοματεπώνυμο Πατέρα-->
              <text-field
                v-model="form.data.fathername"
                title="ΟΝ/ΝΥΜΟ ΠΑΤΕΡΑ *"
                name="fathername"
                :error="form.errors.get('fathername')"
              />
            </div>
            <div class="col-sm-6">
              <!--Ονοματεπώνυμο Μητέρας-->
              <text-field
                v-model="form.data.mothername"
                title="ΟΝ/ΝΥΜΟ ΜΗΤΕΡΑΣ *"
                name="mothername"
                :error="form.errors.get('mothername')"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <!--'Ημερομηνία Γέννησης-->
              <date-field
                v-model="form.data.birthdate"
                title="ΗΜ/ΝΙΑ ΓΕΝΝΗΣΗΣ *"
                name="birthdate"
                :error="form.errors.get('birthdate')"
              />
            </div>
            <div class="col-sm-3">
              <!--Ελληνική ιθαγένεια-->
              <check-field
                v-model="form.data.greek_nationality"
                title="ΕΛΛΗΝΙΚΗ ΙΘΑΓΕΝΕΙΑ"
                name="greek_nationality"
                :error="form.errors.get('greek_nationality')"
              />
            </div>
            <div class="col-sm-3">
              <!--Πολίτης Ε.Ε.-->
              <check-field
                v-model="form.data.eu_citizen"
                title="ΠΟΛΙΤΗΣ Ε.Ε."
                name="eu_citizen"
                :error="form.errors.get('eu_citizen')"
              />
            </div>
          </div>
          <transition name="fade">
            <div
              v-if="form.data.eu_citizen"
              class="row"
            >
              <div class="col-sm-3">
                <!--Τιτλος πιστοποιητικού ελληνομάθειας-->
                <text-field
                  v-model="form.data.greek_languages[0].name"
                  title="ΕΛΛΗΝΟΜΑΘΕΙΑ"
                  :name="`greek_languages.name`"
                  :error="form.errors.get(`greek_languages.name`)"
                />
              </div>
              <div class="col-sm-3">
                <!--Αρχή έκδοσης πιστοποιητικού ελληνομάθειας-->
                <text-field
                  v-model="form.data.greek_languages[0].issuer"
                  title="ΑΡΧΗ ΕΚΔΟΣΗΣ"
                  :name="`greek_languages.issuer`"
                  :error="form.errors.get(`greek_languages.issuer`)"
                />
              </div>
              <div class="col-sm-3">
                <!--Επίπεδο πιστοποιητικού ελληνομάθειας-->
                <text-field
                  v-model="form.data.greek_languages[0].level"
                  title="ΕΠΙΠΕΔΟ"
                  :name="`greek_languages.level`"
                  :error="form.errors.get(`greek_languages.level`)"
                />
              </div>
              <div class="col-sm-1">
                <br>
                <!--Attach greek language certificate-->
                <a
                  v-if="form.data.greek_languages[0].id"
                  href="#"
                  :class="['flex-item', 'hover-text-light-blue', form.data.greek_languages[0].has_attachments? 'text-blue-light': 'text-gray']"
                  @click.prevent="openAttachmentsModal(form.data.greek_languages[0], 'greek_language')"
                >
                  <i
                    class="fa fa-paperclip fa-2x"
                    aria-hidden="true"
                  />
                </a>
              </div>
              <div class="col-sm-1">
                <br>
                <!--Verify greek language-->
                <a
                  v-if="form.data.greek_languages[0].id"
                  href="#"
                  :class="['flex-item', 'hover-text-light-blue', form.data.greek_languages[0].verified ? 'text-blue-light': 'text-gray']"
                  @click.prevent="openVerificationsModal(form.data.greek_languages[0], 'greek_language')"
                >
                  <i
                    class="fa fa-check fa-2x"
                    aria-hidden="true"
                  />
                </a>
              </div>
            </div>
          </transition>
          <div class="row">
            <div class="col-sm-3">
              <!--ΑΔΤ-->
              <text-field
                v-model="form.data.policeid_number"
                title="Α.Δ.Τ."
                name="policeid_number"
                :error="form.errors.get('policeid_number')"
              />
            </div>
            <div class="col-sm-3">
              <!--'Ημερομηνία Έκδοσης-->
              <date-field
                v-model="form.data.policeid_date"
                title="ΗΜ/ΝΙΑ ΕΚΔΟΣΗΣ"
                name="policeid_date"
                :error="form.errors.get('policeid_date')"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-3">
              <!--ΑΦΜ-->
              <text-field
                v-model="form.data.afm"
                title="Α.Φ.Μ."
                name="afm"
                :error="form.errors.get('afm')"
              />
            </div>
            <div class="col-sm-3">
              <!--ΔΟΥ-->
              <text-field
                v-model="form.data.doy"
                title="Δ.Ο.Υ."
                name="doy"
                :error="form.errors.get('doy')"
              />
            </div>
            <div class="col-sm-3">
              <!--ΑΜΚΑ-->
              <text-field
                v-model="form.data.amka"
                title="Α.Μ.Κ.Α."
                name="amka"
                :error="form.errors.get('amka')"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-3" />
          </div>
        </tab>

        <!--=============================================>>>>>
                = Στοιχεία επικοινωνίας =
                ===============================================>>>>-->

        <tab
          name="Στοιχεία Επικοινωνιας"
          :has-errors="tabHasErrors('email')"
        >
          <div class="row">
            <div class="col-sm-4">
              <!--Διεύθυνση κατοικιάς (οδός)-->
              <text-field
                v-model="form.data.street"
                title="ΔΙΕΥΘΥΝΣΗ"
                name="street"
                :error="form.errors.get('street')"
              />
            </div>
            <div class="col-sm-2">
              <!--Διεύθυνσης κατοικίας (αριθμός)-->
              <text-field
                v-model="form.data.street_number"
                title="ΑΡ."
                name="street_number"
                :error="form.errors.get('street_number')"
              />
            </div>
            <div class="col-sm-2">
              <!--Τ.Κ.-->
              <text-field
                v-model="form.data.postcode"
                title="Τ.Κ."
                name="postcode"
                :error="form.errors.get('postcode')"
              />
            </div>
            <div class="col-sm-4">
              <!--Πόλη-->
              <text-field
                v-model="form.data.city"
                title="ΠΟΛΗ"
                name="city"
                :error="form.errors.get('city')"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-3">
              <!--Τηλέφωνο 1-->
              <text-field
                v-model="form.data.phonenumber1"
                title="Τηλέφωνο 1"
                name="phonenumber1"
                :error="form.errors.get('phonenumber1')"
              />
            </div>
            <div class="col-sm-3">
              <!--Τηλέφωνο 2-->
              <text-field
                v-model="form.data.phonenumber2"
                title="Τηλέφωνο 2"
                name="phonenumber2"
                :error="form.errors.get('phonenumber2')"
              />
            </div>
            <div class="col-sm-6">
              <!--Email-->
              <text-field
                v-model="form.data.email"
                title="Email"
                name="email"
                :error="form.errors.get('email')"
              />
            </div>
          </div>
        </tab>

        <!--=============================================>>>>>
                    = Τίτλοι σπουδών =
                    ===============================================>>>>-->

        <tab
          name="Τίτλοι Σπουδών"
          :has-errors="tabHasErrors('degrees.*.mark')"
        >
          <div class="row">
            <div class="col-sm-10">
              <!--If no asset has been added yet show this message-->
              <h5
                v-show="form.data.degrees.length == 0"
                class="text-right"
              >
                Δεν έχουν καταχωρηθεί τίτλοι σπουδών μέχρι στιγμής
              </h5>
            </div>
            <div class="col-sm-2">
              <!--Add asset-->
              <button
                class="btn bg-light-blue"
                type="button"
                @click="addAsset('degrees')"
              >
                <i
                  class="fa fa-plus"
                  aria-hidden="true"
                />
                Προσθήκη
              </button>
            </div>
          </div>
          <br>
          <div class="row">
            <div class="col-sm-12">
              <table
                v-show="form.data.degrees.length > 0"
                class="table table-condensed asset-table"
              >
                <tbody>
                  <tr class="bg-primary">
                    <th>A/A</th>
                    <th>Τίτλος</th>
                    <th>Ίδρυμα</th>
                    <th>Βαθμός</th>
                    <th>Έτος</th>
                    <th><i class="fa fa-cog" /></th>
                  </tr>
                  <tr
                    v-for="(degree, index) in form.data.degrees"
                    :key="degree.id"
                  >
                    <!--No.-->
                    <td>
                      {{ index + 1 }}
                    </td>
                    <td width="40%">
                      <!--Degree name-->
                      <text-field
                        v-model="degree.name"
                        :name="`degrees.${ index }.name`"
                        :error="form.errors.get(`degrees.${ index }.name`)"
                      />
                    </td>
                    <td width="40%">
                      <!--Degree issuer-->
                      <text-field
                        v-model="degree.issuer"
                        :name="`degrees.${ index }.issuer`"
                        :error="form.errors.get(`degrees.${ index }.issuer`)"
                      />
                    </td>
                    <td width="10%">
                      <!--Degree mark-->
                      <text-field
                        v-model="degree.mark"
                        :name="`degrees.${ index }.mark`"
                        :error="form.errors.get(`degrees.${ index }.mark`)"
                      />
                    </td>
                    <td width="10%">
                      <!--Degree year-->
                      <text-field
                        v-model="degree.year"
                        :name="`degrees.${ index }.year`"
                        :error="form.errors.get(`degrees.${ index }.year`)"
                      />
                    </td>
                    <td class="action-btn-container">
                      <!--Attach degree-->
                      <a
                        v-if="degree.id"
                        href="#"
                        :class="['flex-item', degree.has_attachments? 'text-blue-light': 'text-gray']"
                        @click.prevent="openAttachmentsModal(degree, 'degree')"
                      >
                        <i
                          class="fa fa-paperclip fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Verify degree-->
                      <a
                        v-if="degree.id"
                        href="#"
                        :class="['flex-item', degree.verified ? 'text-blue-light': 'text-gray']"
                        @click.prevent="openVerificationsModal(degree, 'degree')"
                      >
                        <i
                          class="fa fa-check fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Delete degree-->
                      <a
                        href="#"
                        class="flex-item text-gray hover-text-red"
                        @click.prevent="removeAsset('degrees', index)"
                      >
                        <i
                          class="fa fa-remove fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </tab>

        <!--=============================================>>>>>
                = Μεταπτυχιακοί τίτλοι =
                ===============================================>>>>-->

        <tab
          name="Μεταπτυχιακοί Τίτλοι"
          :has-errors="tabHasErrors('postgraduates.*.mark')"
        >
          <div class="row">
            <div class="col-sm-10">
              <h5
                v-show="form.data.postgraduates.length == 0"
                class="text-right"
              >
                Δεν έχουν
                καταχωρηθεί
                μεταπτυχιακοί τίτλοι σπουδών μέχρι
                στιγμής
              </h5>
            </div>
            <div class="col-sm-2">
              <!--Add Postgrad-->
              <button
                class="btn bg-light-blue"
                type="button"
                @click="addAsset('postgraduates')"
              >
                <i
                  class="fa fa-plus"
                  aria-hidden="true"
                />
                Προσθήκη
              </button>
            </div>
          </div>
          <br>
          <div class="row">
            <div class="col-sm-12">
              <table
                v-show="form.data.postgraduates.length > 0"
                class="table table-condensed asset-table"
              >
                <tbody>
                  <tr class="bg-primary">
                    <th>A/A</th>
                    <th>Τίτλος</th>
                    <th>Ίδρυμα</th>
                    <th>Έτος</th>
                    <th><i class="fa fa-cog" /></th>
                  </tr>
                  <tr
                    v-for="(postgraduate, index) in form.data.postgraduates"
                    :key="postgraduate.id"
                  >
                    <td>
                      <!--No.-->
                      {{ index + 1 }}
                    </td>
                    <td width="45%">
                      <!--Postgrad name-->
                      <text-field
                        v-model="postgraduate.name"
                        :name="`postgraduates.${ index }.name`"
                        :error="form.errors.get(`postgraduates.${ index }.name`)"
                      />
                    </td>
                    <td width="45%">
                      <!--Postgrad issuer-->
                      <text-field
                        v-model="postgraduate.issuer"
                        :name="`postgraduates.${ index }.issuer`"
                        :error="form.errors.get(`postgraduates.${ index }.issuer`)"
                      />
                    </td>
                    <td width="10%">
                      <!--Postgrad year-->
                      <text-field
                        v-model="postgraduate.year"
                        :name="`postgraduates.${ index }.year`"
                        :error="form.errors.get(`postgraduates.${ index }.year`)"
                      />
                    </td>
                    <td class="action-btn-container">
                      <!--Attach postgraduate-->
                      <a
                        v-if="postgraduate.id"
                        href="#"
                        :class="['flex-item', postgraduate.has_attachments? 'text-blue-light': 'text-gray']"
                        @click.prevent="openAttachmentsModal(postgraduate, 'postgraduate')"
                      >
                        <i
                          class="fa fa-paperclip fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Verify postgraduate-->
                      <a
                        v-if="postgraduate.id"
                        href="#"
                        :class="['flex-item', postgraduate.verified ? 'text-blue-light': 'text-gray']"
                        @click.prevent="openVerificationsModal(postgraduate, 'postgraduate')"
                      >
                        <i
                          class="fa fa-check fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Delete postgraduate-->
                      <a
                        href="#"
                        class="flex-item text-gray hover-text-red"
                        @click.prevent="removeAsset('postgraduates', index)"
                      >
                        <i
                          class="fa fa-remove fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </tab>

        <!--=============================================>>>>>
                = Διδακτορικοί τίτλοι =
                ===============================================>>>>-->

        <tab
          name="Διδακτορικοί Τίτλοι"
          :has-errors="tabHasErrors('doctorates.*.mark')"
        >
          <div class="row">
            <div class="col-sm-10">
              <h5
                v-show="form.data.doctorates.length == 0"
                class="text-right"
              >
                Δεν έχουν
                καταχωρηθεί
                διδακτορικά μέχρι στιγμής
              </h5>
            </div>
            <div class="col-sm-2">
              <!--Add doctorate-->
              <button
                class="btn bg-light-blue"
                type="button"
                @click="addAsset('doctorates')"
              >
                <i
                  class="fa fa-plus"
                  aria-hidden="true"
                />
                Προσθήκη
              </button>
            </div>
          </div>
          <br>
          <div class="row">
            <div class="col-sm-12">
              <table
                v-show="form.data.doctorates.length > 0"
                class="table table-condensed asset-table"
              >
                <tbody>
                  <tr class="bg-primary">
                    <th>A/A</th>
                    <th>Τίτλος</th>
                    <th>Ίδρυμα</th>
                    <th>Έτος</th>
                    <th><i class="fa fa-cog" /></th>
                  </tr>
                  <tr
                    v-for="(doctorate, index) in form.data.doctorates"
                    :key="doctorate.id"
                  >
                    <td>
                      <!--No.-->
                      {{ index + 1 }}
                    </td>
                    <td width="45%">
                      <!--Doctorate name-->
                      <text-field
                        v-model="doctorate.name"
                        :name="`doctorates.${ index }.name`"
                        :error="form.errors.get(`doctorates.${ index }.name`)"
                      />
                    </td>
                    <td width="45%">
                      <!--Doctorate issuer-->
                      <text-field
                        v-model="doctorate.issuer"
                        :name="`doctorates.${ index }.issuer`"
                        :error="form.errors.get(`doctorates.${ index }.issuer`)"
                      />
                    </td>
                    <td width="10%">
                      <!--Doctorate year-->
                      <text-field
                        v-model="doctorate.year"
                        :name="`doctorates.${ index }.year`"
                        :error="form.errors.get(`doctorates.${ index }.year`)"
                      />
                    </td>
                    <td class="action-btn-container">
                      <!--Attach doctorate-->
                      <a
                        v-if="doctorate.id"
                        href="#"
                        :class="['flex-item', doctorate.has_attachments? 'text-blue-light': 'text-gray']"
                        @click.prevent="openAttachmentsModal(doctorate, 'doctorate')"
                      >
                        <i
                          class="fa fa-paperclip fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Verify doctorate-->
                      <a
                        v-if="doctorate.id"
                        href="#"
                        :class="['flex-item', doctorate.verified ? 'text-blue-light': 'text-gray']"
                        @click.prevent="openVerificationsModal(doctorate, 'doctorate')"
                      >
                        <i
                          class="fa fa-check fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Delete doctorate-->
                      <a
                        href="#"
                        class="flex-item text-gray hover-text-red"
                        @click.prevent="removeAsset('doctorates', index)"
                      >
                        <i
                          class="fa fa-remove fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </tab>

        <!--=============================================>>>>>
                = Εμπειρία =
                ===============================================>>>>-->

        <!--TODO: consider to use ...rest to pass tabErrors in a concise format, like (experiences.*.*) -->
        <tab
          name="Εμπειρία"
          :has-errors="tabHasErrors('experiences.*.issuer', 'experiences.*.months')"
        >
          <div class="row">
            <div class="col-sm-10">
              <h5
                v-show="form.data.experiences.length == 0"
                class="text-right"
              >
                Δεν έχει καταχωρηθεί εργασιακή εμπειρία μέχρι στιγμής
              </h5>
            </div>
            <div class="col-sm-2">
              <!--Add experience-->
              <button
                class="btn bg-light-blue"
                type="button"
                @click="addAsset('experiences')"
              >
                <i
                  class="fa fa-plus"
                  aria-hidden="true"
                />
                Προσθήκη
              </button>
            </div>
          </div>
          <br>
          <div class="row">
            <div class="col-sm-12">
              <table
                v-show="form.data.experiences.length > 0"
                class="table table-condensed asset-table"
              >
                <tbody>
                  <tr class="bg-primary">
                    <th>A/A</th>
                    <th>Aπό</th>
                    <th>Έως</th>
                    <th>Μήνες</th>
                    <th>Φορέας</th>
                    <th>Ειδικότητα</th>
                    <th><i class="fa fa-cog" /></th>
                  </tr>
                  <tr
                    v-for="(experience, index) in form.data.experiences"
                    :key="experience.id"
                  >
                    <td>
                      <!--No.-->
                      {{ index + 1 }}
                    </td>
                    <td>
                      <!--Experience start date-->
                      <date-field
                        v-model="experience.started_at"
                        :name="`experiences.${ index }.started_at`"
                        :error="form.errors.get(`experiences.${ index }.started_at`)"
                      />
                    </td>
                    <td>
                      <!--Experience end date-->
                      <date-field
                        v-model="experience.ended_at"
                        :name="`experiences.${ index }.ended_at`"
                        :error="form.errors.get(`experiences.${ index }.ended_at`)"
                      />
                    </td>
                    <td width="5%">
                      <!--Experience overall duration in months-->
                      <text-field
                        v-model="experience.months"
                        :name="`experiences.${ index }.months`"
                        :error="form.errors.get(`experiences.${ index }.months`)"
                      />
                    </td>
                    <td width="30%">
                      <!--Experience issuer-->
                      <text-field
                        v-model="experience.issuer"
                        :name="`experiences.${ index }.issuer`"
                        :error="form.errors.get(`experiences.${ index }.issuer`)"
                      />
                    </td>
                    <td width="30%">
                      <!--Experience description (name)-->
                      <text-field
                        v-model="experience.name"
                        :name="`experiences.${ index }.name`"
                        :error="form.errors.get(`experiences.${ index }.name`)"
                      />
                    </td>
                    <td class="action-btn-container">
                      <!--Attach experience-->
                      <a
                        v-if="experience.id"
                        href="#"
                        :class="['flex-item', experience.has_attachments? 'text-blue-light': 'text-gray']"
                        @click.prevent="openAttachmentsModal(experience, 'experience')"
                      >
                        <i
                          class="fa fa-paperclip fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Verify experience-->
                      <a
                        v-if="experience.id"
                        href="#"
                        :class="['flex-item', experience.verified ? 'text-blue-light': 'text-gray']"
                        @click.prevent="openVerificationsModal(experience, 'experience')"
                      >
                        <i
                          class="fa fa-check fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Delete experience-->
                      <a
                        href="#"
                        class="flex-item text-gray hover-text-red"
                        @click.prevent="removeAsset('experiences', index)"
                      >
                        <i
                          class="fa fa-remove fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </tab>

        <!--=============================================>>>>>
                = Γλωσσομάθεια =
                ===============================================>>>>-->

        <tab
          name="Γλωσσομάθεια"
          :has-errors="tabHasErrors('language_skills.*.name')"
        >
          <div class="row">
            <div class="col-sm-10">
              <h5
                v-show="form.data.language_skills.length === 0"
                class="text-right"
              >
                Δεν έχει καταχωρηθεί πιστοποιητικό γλωσσομάθειας μέχρι στιγμής
              </h5>
            </div>
            <div class="col-sm-2">
              <!--Add Language Skill-->
              <button
                class="btn bg-light-blue"
                type="button"
                @click="addAsset('language_skills')"
              >
                <i
                  class="fa fa-plus"
                  aria-hidden="true"
                />
                Προσθήκη
              </button>
            </div>
          </div>
          <br>
          <div class="row">
            <div class="col-sm-12">
              <table
                v-show="form.data.language_skills.length > 0"
                class="table table-condensed asset-table"
              >
                <tbody>
                  <tr class="bg-primary">
                    <th>A/A</th>
                    <th>Γλώσσα</th>
                    <th>Επίπεδο</th>
                    <th>Τίτλος Πιστοποιητικού</th>
                    <th>Φορέας</th>
                    <th><i class="fa fa-cog" /></th>
                  </tr>
                  <tr
                    v-for="(language_skill, index) in form.data.language_skills"
                    :key="language_skill.id"
                  >
                    <td>
                      <!--No.-->
                      {{ index + 1 }}
                    </td>
                    <td width="20%">
                      <!--Language-->
                      <select-field
                        v-model="language_skill.language_id"
                        :name="`language_skills.${ index }.language_id`"
                        :error="form.errors.get(`language_skills.${ index }.language_id`)"
                        :options="formRelatedModels['languages']"
                      />
                    </td>
                    <td width="20%">
                      <!--Language Level-->
                      <select-field
                        v-model="language_skill.language_level_id"
                        :name="`language_skills.${ index }.language_level_id`"
                        :error="form.errors.get(`language_skills.${ index }.language_level_id`)"
                        :options="formRelatedModels['languageLevels']"
                      />
                    </td>
                    <td width="30%">
                      <!--Language certification name-->
                      <text-field
                        v-model="language_skill.name"
                        :name="`language_skills.${ index }.name`"
                        :error="form.errors.get(`language_skills.${ index }.name`)"
                      />
                    </td>
                    <td width="30%">
                      <!--Language certification issuer-->
                      <text-field
                        v-model="language_skill.issuer"
                        :name="`language_skills.${ index }.issuer`"
                        :error="form.errors.get(`language_skills.${ index }.issuer`)"
                      />
                    </td>
                    <td class="action-btn-container">
                      <!--Attach language skill-->
                      <a
                        v-if="language_skill.id"
                        href="#"
                        :class="['flex-item', 'hover-text-light-blue', language_skill.has_attachments ? 'text-blue-light': 'text-gray']"
                        @click.prevent="openAttachmentsModal(language_skill, 'language_skill')"
                      >
                        <i
                          class="fa fa-paperclip fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Verify language_skill-->
                      <a
                        v-if="language_skill.id"
                        href="#"
                        :class="['flex-item', language_skill.verified ? 'text-blue-light': 'text-gray']"
                        @click.prevent="openVerificationsModal(language_skill, 'language_skill')"
                      >
                        <i
                          class="fa fa-check fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Delete language_skill-->
                      <a
                        href="#"
                        class="flex-item text-gray hover-text-red"
                        @click.prevent="removeAsset('language_skills', index)"
                      >
                        <i
                          class="fa fa-remove fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </tab>

        <!--=============================================>>>>>
                = Γνώση χρήσης Η/Υ =
                ===============================================>>>>-->

        <tab
          name="Γνώση Χρήσης Η/Υ"
          :has-errors="tabHasErrors('computer_skills.*.name')"
        >
          <div class="row">
            <div class="col-sm-10">
              <h5
                v-show="form.data.computer_skills.length == 0"
                class="text-right"
              >
                Δεν έχει καταχωρηθεί αποδεικτικό γνώσης χρήσης Η/Υ μέχρι στιγμής
              </h5>
            </div>
            <div class="col-sm-2">
              <!--Add Computer Skill-->
              <button
                class="btn bg-light-blue"
                type="button"
                @click="addAsset('computer_skills')"
              >
                <i
                  class="fa fa-plus"
                  aria-hidden="true"
                />
                Προσθήκη
              </button>
            </div>
          </div>
          <br>
          <div class="row">
            <div class="col-sm-12">
              <table
                v-show="form.data.computer_skills.length > 0"
                class="table table-condensed asset-table"
              >
                <tbody>
                  <tr class="bg-primary">
                    <th>A/A</th>
                    <th>Τίτλος αποδεικτικού</th>
                    <th><i class="fa fa-cog" /></th>
                  </tr>
                  <tr
                    v-for="(computer_skill, index) in form.data.computer_skills"
                    :key="computer_skill"
                  >
                    <!--No.-->
                    <td>
                      {{ index + 1 }}
                    </td>
                    <td>
                      <!--Computer Skills certification name-->
                      <text-field
                        v-model="computer_skill.name"
                        :name="`computer_skills.${ index }.name`"
                        :error="form.errors.get(`computer_skills.${ index }.name`)"
                      />
                    </td>
                    <td class="action-btn-container">
                      <!--Attach computer skill-->
                      <a
                        v-if="computer_skill.id"
                        href="#"
                        :class="['flex-item', computer_skill.has_attachments? 'text-blue-light': 'text-gray']"
                        @click.prevent="openAttachmentsModal(computer_skill, 'computer_skill')"
                      >
                        <i
                          class="fa fa-paperclip fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Verify computer_skill-->
                      <a
                        v-if="computer_skill.id"
                        href="#"
                        :class="['flex-item', computer_skill.verified ? 'text-blue-light': 'text-gray']"
                        @click.prevent="openVerificationsModal(computer_skill, 'computer_skill')"
                      >
                        <i
                          class="fa fa-check fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                      <!--Delete computer_skill-->
                      <a
                        href="#"
                        class="flex-item text-gray hover-text-red"
                        @click.prevent="removeAsset('computer_skills', index)"
                      >
                        <i
                          class="fa fa-remove fa-2x"
                          aria-hidden="true"
                        />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </tab>

        <!--=============================================>>>>>
                = Κοινωνικά κριτήρια =
                ===============================================>>>>-->

        <tab name="Κοινωνικά Κριτήρια">
          <div class="row">
            <div class="col-sm-6">
              <div class="row">
                <div class="col-sm-6">
                  <!-- Multi-child families (children) -->
                  <number-field
                    v-model="form.data.multi_child_families[0].children"
                    title="Πολύτεκνος"
                    :name="`multi_child_families.0.children`"
                    :error="form.errors.get(`multi_child_families.0.children`)"
                  />
                </div>
                <div class="col-sm-6">
                  <!-- Multi-child families (siblings) -->
                  <number-field
                    v-model="form.data.multi_child_families[0].siblings"
                    title="Τέκνο πολύτεκνης οικ."
                    :name="`multi_child_families.0.siblings`"
                    :error="form.errors.get(`multi_child_families.0.siblings`)"
                  />
                </div>
              </div>
              <div class="row">
                <div class="col-sm-6">
                  <!-- Three-child families (children) -->
                  <switch-field
                    v-model="form.data.three_child_families[0].children"
                    title="Τρίτεκνος"
                    :name="`three_child_families.0.children`"
                    :error="form.errors.get(`three_child_families.0.children`)"
                  />
                </div>
                <div class="col-sm-6">
                  <!-- Three-child families (siblings) -->
                  <switch-field
                    v-model="form.data.three_child_families[0].siblings"
                    title="Τέκνο τρίτεκνης οικ."
                    :name="`three_child_families.0.siblings`"
                    :error="form.errors.get(`three_child_families.0.siblings`)"
                  />
                </div>
              </div>
              <div class="row">
                <div class="col-sm-6">
                  <!-- Single-parent families (children) -->
                  <number-field
                    v-model="form.data.single_parent_families[0].children"
                    title="Γονέας μονογονεικής"
                    :name="`single_parent_families.0.children`"
                    :error="form.errors.get(`single_parent_families.0.children`)"
                  />
                </div>
                <div class="col-sm-6">
                  <!-- Single-parent families (siblings) -->
                  <number-field
                    v-model="form.data.single_parent_families[0].siblings"
                    title="Τέκνο μονογονεικής"
                    :name="`single_parent_families.0.siblings`"
                    :error="form.errors.get(`single_parent_families.0.siblings`)"
                  />
                </div>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="row">
                <div class="col-sm-6 col-sm-offset-6">
                  <number-field
                    v-model="form.data.unemployments[0].months"
                    title="Μήνες Ανεργίας"
                    :name="`unemployments.0.months`"
                    :error="form.errors.get(`unemployments.0.months`)"
                  />
                </div>
              </div>
              <div class="row">
                <div class="col-sm-6 col-sm-offset-6">
                  <!-- Minors -->
                  <number-field
                    v-model="form.data.minors[0].amount"
                    title="Αρ. ανήλικων τέκνων"
                    :name="`minors.0.amount`"
                    :error="form.errors.get(`minors.0.amount`)"
                    @valid="form.errors.clear(`minors.0.amount`)"
                  />
                </div>
              </div>
              <div class="row">
                <div class="col-sm-6 col-sm-offset-6">
                  <!-- Disabilities -->
                  <number-field
                    v-model="form.data.disabilities[0].percentage"
                    title="Ατομική αναπηρία"
                    :name="`disabilities.0.percentage`"
                    :error="form.errors.get(`disabilities.0.percentage`)"
                  />
                </div>
              </div>
              <div class="row">
                <div class="col-sm-6 col-sm-offset-6">
                  <!-- Disability family -->
                  <number-field
                    v-model="form.data.family_disabilities[0].percentage"
                    title="Αναπηρία μέλους οικ."
                    :name="`family_disabilities.0.percentage`"
                    :error="form.errors.get(`family_disabilities.0.percentage`)"
                  />
                </div>
              </div>
            </div>
          </div>
        </tab>
      </tabs>
      <hr>

      <!--=============================================>>>>>
            = FORM BUTTONS =
            ===============================================>>>>-->

      <div class="footer">
        <!--Return button-->
        <div class="footer-left">
          <button
            class="btn btn-default col-sm-12"
            type="button"
            @click="form.reset()"
          >
            <i
              class="fa fa-arrow-left"
              aria-hidden="true"
            /> Επιστροφή
          </button>
        </div>
        <!--Submit button-->
        <div class="footer-right">
          <button
            class="btn btn-primary col-sm-12"
            type="submit"
            :disabled="form.errors.has()"
          >
            <i
              class="fa fa-paper-plane"
              aria-hidden="true"
            /> Καταχώρηση
          </button>
        </div>
      </div>
    </form>

    <!--=============================================>>>>>
        = Asset Verification modal =
        ===============================================>>>>-->

    <modal
      v-if="verificationDialogVisible"
      @close="verificationDialogVisible = false"
    >
      <asset-verification
        :asset="selectedAsset"
        :asset-type="selectedAssetType"
        @close="updateVerifiedStatus"
      />
    </modal>

    <!--=============================================>>>>>
        = Asset Attachment modal =
        ===============================================>>>>-->

    <modal
      v-if="attachmentModalVisible"
      @close="attachmentModalVisible = false"
    >
      <asset-attachment
        :asset="selectedAsset"
        :asset-type="selectedAssetType"
        @close="updateHasAttachmentsStatus"
      />
    </modal>
  </div>
</template>

<script>
import CheckField from '../../shared/components/ui/FormFields/CheckField.vue';
import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import NumberField from '../../shared/components/ui/FormFields/NumberField.vue';
import SelectField from '../../shared/components/ui/FormFields/SelectField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import Modal from '../../shared/components/ui/Modal/Modal.vue';
import Tab from '../../shared/components/ui/Tabs/TabItem.vue';
import Tabs from '../../shared/components/ui/Tabs/VerticalTabs.vue';
import Form from '../../shared/Form';
import __ from '../../shared/Helpers';
import AssetAttachment from './AssetAttachment.vue';
import AssetVerification from './AssetVerification.vue';

export default {
  name: 'ApplicantForm',

  components: {
    Tabs,
    Tab,
    AssetVerification,
    AssetAttachment,
    TextField,
    DateField,
    SelectField,
    NumberField,
    SwitchField,
    CheckField,
    Modal,
  },

  props: {
    applicant: {
      type: Object,
    },
    formRelatedModels: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      verificationDialogVisible: false,
      attachmentModalVisible: false,
      selectedAsset: {},
      selectedAssetType: '',
      form: new Form({
        name: '',
        surname: '',
        fathername: '',
        mothername: '',
        birthdate: '',
        policeid_number: '',
        policeid_date: '',
        afm: '',
        doy: '',
        amka: '',
        street: '',
        street_number: '',
        postcode: '',
        city: '',
        phonenumber1: '',
        phonenumber2: '',
        email: '',
        eu_citizen: false,
        greek_nationality: true,
        degrees: [],
        postgraduates: [],
        doctorates: [],
        experiences: [],
        language_skills: [],
        computer_skills: [],
        greek_languages: [{
          id: '', name: '', issuer: '', level: '', verified: false,
        }],
        unemployments: [{ id: '', months: '', verified: false }],
        multi_child_families: [{
          id: '', children: 0, siblings: 0, verified: false,
        }],
        three_child_families: [{
          id: '', children: false, siblings: false, verified: false,
        }],
        single_parent_families: [{
          id: '', children: 0, siblings: 0, verified: false,
        }],
        disabilities: [{ id: '', percentage: '', verified: false }],
        family_disabilities: [{ id: '', percentage: '', verified: false }],
        minors: [{ id: '', amount: '', verified: false }],
      }),
    };
  },

  watch: {
    /**
             * Use it to toggle GR/EU citizen checkboxes
             */
    'form.data.eu_citizen': function () {
      this.form.data.greek_nationality = !this.form.data.eu_citizen;
    },

    'form.data.greek_nationality': function () {
      this.form.data.eu_citizen = !this.form.data.greek_nationality;
    },
  },

  created() {
    if (this.applicant) {
      this.form.populate(this.applicant);
      // TODO this.form = new Form(this.applicant)
    }
  },

  methods: {
    submitForm() {
      const isEditForm = !!this.applicant;

      if (isEditForm) {
        const putURL = `/contractuals/applicants/${this.applicant.id}`;
        this.form.put(putURL)
          .then((response) => {
            swal({
              type: 'success',
              title: 'Επιτυχία!',
              text: 'Ο φάκελος του αιτούντα ενημερώθηκε',
              showConfirmButton: false,
              timer: 3000,
            });
          })
          .catch((error) => swal({
            type: 'error',
            title: `Σφάλμα ${error.response.status}`,
            text: error.response.data.message,
            showConfirmButton: false,
            timer: 3000,
          }));
      } else {
        const postURL = '/contractuals/applicants';
        this.form.post(postURL)
          .then((response) => {
            swal({
              type: 'success',
              title: 'Επιτυχία!',
              text: response.data.message,
              showConfirmButton: false,
              timer: 3000,
            });

            // TODO: Redirect user to applicant show
          })
          .catch((error) => {
            swal({
              type: 'error',
              title: `Σφάλμα ${error.response.status}`,
              text: error.response.data.message,
              showConfirmButton: false,
              timer: 3000,
            });
          });
      }
    },

    tabHasErrors(...errors) {
      for (const error of errors) {
        if (this.form.errors.has(error)) return true;
      }

      return false;
    },

    addAsset(assetField) {
      switch (assetField) {
        case 'degrees':
          this.form.data[assetField].push({
            id: '',
            name: '',
            issuer: '',
            mark: '',
            year: '',
            verified: '',
          });
          break;
        case 'postgraduates':
          this.form.data[assetField].push({
            id: '',
            name: '',
            issuer: '',
            year: '',
            verified: '',
          });
          break;
        case 'doctorates':
          this.form.data[assetField].push({
            id: '',
            name: '',
            issuer: '',
            year: '',
            verified: '',
          });
          break;
        case 'experiences':
          this.form.data[assetField].push({
            id: '',
            name: '',
            issuer: '',
            started_at: '',
            ended_at: '',
            months: '',
            verified: '',
          });
          break;
        case 'language_skills':
          this.form.data[assetField].push({
            id: '',
            language_id: '',
            language_level_id: '',
            name: '',
            issuer: '',
            verified: '',
          });
          break;
        case 'computer_skills':
          this.form.data[assetField].push({
            id: '',
            name: '',
            verified: '',
          });
          break;
          // FIXME: catch errors
        default:
          alert('This asset field does not exist');
      }
    },

    removeAsset(assetField, index) {
      const { id } = this.form.data[assetField][index];
      const assetExistsOnServer = id !== '';

      if (assetExistsOnServer) {
        __.askToConfirm({
          title: 'Είστε σίγουροι?',
          body: 'Θα γίνει διαγραφή της εγγραφής!',
        }).then(() => {
          const url = `/api/contractuals/assets/${assetField}/${id}`;
          this.$http.delete(url)
            .then((response) => {
              this.form.data[assetField].splice(index, 1);
              swal({
                type: 'success',
                title: 'Επιτυχία!',
                text: response.data.message,
                showConfirmButton: false,
                timer: 3000,
              });
            })
            .catch((error) => {
              swal({
                type: 'error',
                title: `Σφάλμα ${error.status}`,
                text: error.statusText,
                showConfirmButton: false,
                timer: 3000,
              });
            });
        });
      } else {
        this.form.data[assetField].splice(index, 1);
        swal({
          type: 'success',
          title: 'Επιτυχία!',
          text: 'Not persisted asset was deleted',
          showConfirmButton: false,
          timer: 3000,
        });
      }
    },

    openVerificationsModal(asset, type) {
      this.selectedAsset = asset;
      this.selectedAssetType = type;
      this.verificationDialogVisible = true;
    },

    updateVerifiedStatus(status) {
      this.selectedAsset.verified = status;
      this.selectedAsset = {};
      this.verificationDialogVisible = false;
    },

    openAttachmentsModal(asset, type) {
      this.selectedAsset = asset;
      this.selectedAssetType = type;
      this.attachmentModalVisible = true;
    },

    updateHasAttachmentsStatus(newAttachments) {
      this.selectedAsset.has_attachments = !!newAttachments.length;
      this.selectedAsset = {};
      this.attachmentModalVisible = false;
    },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

    /*Variables*/
    $light-blue: #0C90AD;

    .is-v-aligned {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
    }

    .action-btn-container {
        height: 100%;
        padding: 0;
        margin: 0;
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        align-items: center; // align to cross-axis
        justify-content: space-around !important; //align to main-axis
    }

    pre {
        font-size: 0.8rem !important;
    }

    .help-block {
        color: #F36877 !important;
    }

    /*
    Tables
    */
    table.asset-table {
        th {
            text-align: center;
            vertical-align: bottom;

            &:first-child {
                width: 32px !important;
            }

            &:last-child {
                width: 100px;
            }
        }

        td {
            vertical-align: middle;

            &:first-child {
                width: 32px !important;
            }

            &:last-child {
                width: 100px;
            }

            .form-group {
                margin-bottom: 0;
            }
        }
    }

    /*
    Footer
    */
    .footer {
        display: flex;

        .footer-left {
            flex: 0 0 20%;
            padding: 0 5px;
        }

        .footer-right {
            flex: 1 1 auto;
            padding: 0 5px;
        }
    }

    /*Transitions*/
    .fade-enter-active, .fade-leave-active {
        transition: opacity .8s;
    }

    .fade-enter, .fade-leave-to {
        opacity: 0
    }
</style>
