<template>
    <div>
        <!--=============================================>>>>>
        = Καταγραφή νέας ενέργειας πιστοποίησης =
        ===============================================>>>>-->

        <div class="row">
            <div class="col-sm-9">
                <h5 v-show="form.data.verifications.length == 0" class="text-right">
                    Δεν έχουν πραγματοποιηθεί ενέργειες πιστοποίησης
                </h5>
            </div>
            <div class="col-sm-3">
                <!-- Add verification log entry -->
                <button class="btn bg-light-blue pull-right"
                        type="button"
                        @click="addVerification"
                >
                    <i class="fa fa-plus" aria-hidden="true"></i>
                    Προσθήκη νέας καταγραφής
                </button>
            </div>
        </div>
        <br>

        <!--=============================================>>>>>
        = Πίνακας πραγματοποιηθήσων ενεργειών πιστοποίησης =
        ===============================================>>>>-->

        <div class="row">
            <div class="col-sm-12">
                <form id="applicantForm"
                      method="POST"
                      action="./"
                      @submit.prevent="onSubmit"
                      @keydown="form.errors.clear($event.target.name)"
                      v-show="form.data.verifications.length > 0"
                >
                    <table class="table table-condensed">
                        <tbody>
                        <tr class="bg-primary">
                            <th>A/A</th>
                            <th>Αρ. Πρωτ.</th>
                            <th>Ημ/νία</th>
                            <th>Περιγραφή</th>
                            <th>Χρήστης</th>
                            <th><!-- Delete verification --></th>
                        </tr>
                        <tr v-for="(verification, index) in form.data.verifications" :key="verification.id">
                            <td width="5%">
                                <!-- No.-->
                                {{ index + 1 }}
                            </td>
                            <td width="20%">
                                <!--Protocol number-->
                                <text-field v-model="verification.protocol_number"
                                            :name="`verifications.${ index }.protocol_number`"
                                            :error="form.errors.get(`verifications.${ index }.protocol_number`)"
                                >

                                </text-field>
                            </td>
                            <td width="20%">
                                <!--Verification action date-->
                                <date-field v-model="verification.protocol_date"
                                            :name="`verifications.${ index }.protocol_date`"
                                            :error="form.errors.get(`verifications.${ index }.protocol_date`)"
                                >

                                </date-field>
                            </td>
                            <td width="35%">
                                <!--Verification action description-->
                                <text-field v-model="verification.description"
                                            :name="`verifications.${ index }.description`"
                                            :error="form.errors.get(`verifications.${ index }.description`)"
                                >

                                </text-field>
                            </td>
                            <td width="10%">
                                <!--User-->
                                <p v-if="verification.user_id">{{ verification.user_id }}</p>
                            </td>
                            <td width="10%">
                                <!--Delete verification-->
                                <a href="#" @click.prevent="deleteVerification(index)">
                                    <i class="fa fa-remove fa-2x text-red" aria-hidden="true"></i>
                                </a>
                            </td>
                        </tr>
                        </tbody>
                    </table>

                    <!--=============================================>>>>>
                    = Οριστικοποίηση πιστοποίησης =
                    ===============================================>>>>-->

                    <!--Informative dialog-->
                    <div class="row">
                        <div class="col-sm-12">
                            <div :class="[form.data.isVerified? 'note-panel-success': 'note-panel-danger', 'note-panel']">
                                <span v-if="!form.data.isVerified">
                                    <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                                    Η επαληθευση του προσόντος δεν έχει οριστικοποιηθεί
                                </span>
                                <span v-if="form.data.isVerified">
                                    <i class="fa fa-check-circle" aria-hidden="true"></i>
                                    Η επαληθευση του προσόντος έχει οριστικοποιηθεί
                                </span>
                            </div>
                        </div>
                    </div>

                    <!--Button-->
                    <div class="row">
                        <div class="col-sm-12">
                            <switch-field title="Οριστικοποίηση"
                                          v-model="form.data.isVerified"
                                          :name="`isVerified`"
                                          :error="null"
                            >

                            </switch-field>
                        </div>
                    </div>

                    <br>

                    <!--=============================================>>>>>
                    = FORM BUTTONS =
                    ===============================================>>>>-->

                    <div class="row">
                        <div class="col-sm-4">
                            <button class="btn btn-default col-sm-12"
                                    type="button"
                                    @click="form.reset()"
                            >
                                <i class="fa fa-arrow-left" aria-hidden="true"></i> Επιστροφή
                            </button>
                        </div>
                        <div class="col-sm-8">
                            <button class="btn btn-primary col-sm-12"
                                    type="submit"
                                    :disabled="form.errors.has()"
                            >
                                <i class="fa fa-paper-plane" aria-hidden="true"></i> Καταχώρηση
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
    import Form from '../../shared/Form';
    import TextField from '../../shared/components/ui/FormFields/TextField.vue';
    import DateField from '../../shared/components/ui/FormFields/DateField.vue';
    import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';
    import __ from '../../shared/Helpers';

    export default {
        name: 'AssetVerification',

        components: {
            TextField,
            DateField,
            SwitchField,
        },

        props: {
            asset: {
                type: Object,
            },
            assetType: {
                type: String,
            }
        },

        data() {
            return {
                form: new Form({
                    verifications: [],
                    isVerified: this.asset.verified,
                }),
            }
        },

        created() {
            /*
             * Fetch the required verification and update form fields
             * TODO: perform this on modal open if *edit* !!!
             */
            this.$http.get(`/api/contractuals/assets/${this.assetType}/${this.asset.id}/verifications`)
                .then(response => {
                    this.form.populate(response.data);
                })
                .catch(error => console.log(error));
        },

        methods: {

            addVerification() {
                this.form.data.verifications.push({
                    id: '',
                    protocol_date: '',
                    protocol_number: '',
                    description: '',
                    applicant_id: this.asset.applicant_id,
                });
            },

            deleteVerification(index) {

                // Check if the asset already exist on the server
                let verification = this.form.data.verifications[index];
                let existsOnServer = verification.id !== "";

                if (existsOnServer) {
                    // Ask user for confirmation
                    __.askToConfirm({
                        title: 'Είστε σίγουροι;',
                        body: 'Η ενέργεια πιστοποίησης θα διαγραφεί!'
                    }).then(() => {
                        // Make an ajax call to delete the asset on the server
                        let deleteURL = `/api/contractuals/assets/${this.assetType}/${this.asset.id}/verifications/${verification.id}`;
                        this.$http.delete(deleteURL)
                            .then(response => {
                                this.form.data.verifications.splice(index, 1);
                                swal({
                                    type: 'success',
                                    title: `Επιτυχία!`,
                                    text: response.data.message,
                                    showConfirmButton: false,
                                    timer: 3000,
                                });
                            })
                            .catch(error => {
                                swal({
                                    type: 'error',
                                    title: `Σφάλμα ${error.status}`,
                                    text: error.statusText,
                                    showConfirmButton: false,
                                    timer: 3000,
                                });
                            });
                    });

                } else {
                    // Remove the asset from the DOM without performing ajax call
                    this.form.data.verifications.splice(index, 1);
                    console.log('Delete not-persisted asset');
                }

            },

            onSubmit() {
                // TODO: distinguish between edit and create form...

                let postURL = `/api/contractuals/assets/${this.assetType}/${this.asset.id}/verifications`;

                // we must initialize this here because after form successfully submits,
                // the form is cleared and the verification status resets to the initial value
                let verifiedStatus = this.form.data.isVerified;

                this.form.post(postURL)
                    .then(response => {
                        swal({
                            type: 'success',
                            title: `Επιτυχία!`,
                            text: response.data.message,
                            showConfirmButton: false,
                            timer: 3000,
                        });

                        this.$emit('close', verifiedStatus);
                    })
                    .catch(error => swal({
                        type: 'error',
                        title: `Σφάλμα ${error.response.status}`,
                        text: error.response.statusText,
                        showConfirmButton: false,
                        timer: 3000,
                    }));

                // TODO close the asset's verification modal
//                this.$emit('close', some message);
            }
        }
    }
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
    /*
    -----------------------------------------------------------------------------------
    notes-panel
    -----------------------------------------------------------------------------------
    */

    .note-panel {
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 3px;

        &.note-panel-success {
            color: #3c763d;
            background-color: #dff0d8;
            border-color: #d6e9c6;
        }

        &.note-panel-danger {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
    }

    /*
    Tables
    */
    table {
        .form-group {
            margin-bottom: 0;
        }

        td:first-child {
            vertical-align: middle !important;
            text-align: center;
        }
    }
</style>