<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <div>
    <box
      title="Επεξεργασία Αίτησης"
      type="box-solid"
      :is-loading="isUpdating"
    >
      <form
        id="applicationForm"
        @input="form.errors.clear($event.target.name)"
      >
        <tabs>
          <!--=============================================>>>>>
          = Στοιχεία διαγωνισμού =
          ===============================================>>>>-->

          <tab
            name="Στοιχεία Διαγωνισμού"
            :selected="true"
            :has-errors="tabHasErrors('protocol_number', 'applicant_category', 'invalidation_description_id')"
          >
            <div class="row">
              <div class="col-sm-6">
                <dl>
                  <dt>ΤΙΤΛΟΣ</dt>
                  <dd>{{ contest.name }}</dd>
                </dl>
              </div>
              <div class="col-sm-6">
                <dl>
                  <dt>ΠΡΟΚHΡYΞΗ</dt>
                  <dd>{{ contest.protocol_number }}/{{ contest.protocol_date }}</dd>
                </dl>
              </div>
            </div>
            <div class="row">
              <!--Αρ.Πρωτ. αίτησης-->
              <div class="col-sm-12">
                <dl>
                  <dt>ΑΡΙΘΜΟΣ ΠΡΩΤΟΚΟΛΛΟΥ ΑΙΤΗΣΗΣ</dt>
                  <dd>{{ form.data.protocol_number }}/{{ form.data.protocol_date }}</dd>
                </dl>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-4">
                <template v-if="contestHasExternalData">
                  <div class="form-group">
                    <label class="control-label">ΚΑΤΗΓΟΡΙΑ</label>
                    <p>{{ applicantCategories[form.data.applicant_category-1]['label'] }}</p>
                  </div>
                </template>
                <template v-else>
                  <select-field
                    v-model="form.data.applicant_category"
                    title="ΚΑΤΗΓΟΡΙΑ *"
                    :name="`applicant_category`"
                    :error="form.errors.get(`applicant_category`)"
                    :options="applicantCategories"
                  />
                </template>
              </div>
              <div class="col-sm-8">
                <switch-field
                  v-model="form.data.impediment_eight_months"
                  title="ΚΩΛΥΜΑ 8 ΜΗΝΟΥ"
                  :name="`impediment_eight_months`"
                  :error="form.errors.get(`impediment_eight_months`)"
                />
                <span><strong>Εργασία κατά το τελευταίο 8μηνο: {{ form.data.has_eight_months_employment ? 'NAI' : 'OXI' }}</strong></span>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-4">
                <!--Ακύρωση αίτησης (Άλλοι λόγοι)-->
                <switch-field
                  v-model="form.data.invalidated"
                  title="ΑΚΥΡΗ ΑΙΤΗΣΗ"
                  :name="`invalidated`"
                  @input="form.errors.clear('invalidation_description_id')"
                />
              </div>
              <!--Αιτιολογία ακύρωσης αίτησης-->
              <div class="col-sm-8">
                <select-field
                  v-if="form.data.invalidated"
                  v-model="form.data.invalidation_description_id"
                  :options="formRelatedModels['invalidationDescriptions']"
                  title="Αιτιολογία Απόρριψης"
                  name="invalidation_description_id"
                  :error="form.errors.get('invalidation_description_id')"
                  @input="form.errors.clear('invalidation_description_id')"
                />
                <span v-if="form.data.invalidated && (form.data.invalidation_description !== null && form.data.invalidation_description_id === null)">
                  Παλαιά Αιτιολογία: {{ form.data.invalidation_description }}
                </span>
              </div>
            </div>
          </tab>

          <!--=============================================>>>>>
          = Επιλογή θέσεων =
          ===============================================>>>>-->

          <tab
            name="Επιλογή θέσεων"
            :has-errors="tabHasErrors('positions', 'positions.*.id', 'positions.*.pivot.auxiliary_level')"
          >
            <!--Προσθήκη νέας επιλεγμένης θέσης-->
            <div class="row">
              <div class="col-sm-10">
                <h5
                  v-show="(typeof form.data.positions !== 'undefined' && form.data.positions.length === 0)"
                  class="text-right"
                >
                  Δεν έχουν καταχωρηθεί οι θέσεις προτίμησης!
                </h5>
              </div>
              <div class="col-sm-2">
                <button
                  v-if="! contestHasExternalData"
                  class="btn bg-light-blue"
                  type="button"
                  @click="addPosition()"
                >
                  <i
                    class="fa fa-plus"
                    aria-hidden="true"
                  /> Προσθήκη
                </button>
              </div>
            </div>
            <!--Επιλεγμένες θέσεις-->
            <div
              v-if="form.data.applicant_category === 3"
              class="row"
            >
              <div class="col-sm-6">
                <p>Αλλαγή Επικουρίας σε όλες τις θέσεις:</p>
              </div>
              <div class="col-sm-6">
                <selectfilter-field
                  v-model="applicationAuxiliaryLevel"
                  :options="auxiliaryLevels"
                  @input="updatePositionsAuxiliaryLevel"
                />
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <table
                  v-show="(typeof form.data.positions !== 'undefined' && form.data.positions.length > 0)"
                  id="preferredPositions"
                  class="table table-condensed"
                >
                  <tbody>
                    <tr class="bg-primary">
                      <th>Σειρά</th>
                      <th>Επιλογή Θέσης</th>
                      <th v-if="form.data.applicant_category === 3">
                        Επικουρία
                      </th>
                      <th>Εντοπιότητα</th>
                      <th v-if="! contestHasExternalData">
                        <i class="fa fa-cog" />
                      </th>
                    </tr>
                    <tr
                      v-for="(position, index) in form.data.positions"
                      :key="position.id"
                    >
                      <td>
                        {{ index + 1 }}
                      </td>
                      <td>
                        <template v-if="contestHasExternalData">
                          {{ positions.find(p => p.id === position.id).title }}
                        </template>
                        <template v-else>
                          <selectfilter-field
                            v-model="position.id"
                            name="foobar"
                            :error="form.errors.get(`positions.${ index }.id`)"
                            :options="positions"
                            @input="form.errors.clear(`positions.${ index }.id`)"
                          />
                        </template>
                      </td>
                      <td
                        v-if="form.data.applicant_category === 3"
                        class="text-center"
                      >
                        {{ auxiliaryLevels[position.pivot.auxiliary_level].label }}
                      </td>
                      <td class="text-center">
                        <!-- Locality -->
                        <check-field
                          v-if="position.id !== '' && contest.positions.filter(p => p.id === position.id)[0].has_locality"
                          v-model="position.pivot.locality"
                          name="`positions.${ index }.locality`"
                        />
                      </td>
                      <td v-if="! contestHasExternalData">
                        <a
                          href="#"
                          class="flex-item text-gray hover-text-red"
                          @click.prevent="removePosition(index)"
                        >
                          <i
                            class="fa fa-remove fa-2x"
                            aria-hidden="true"
                          />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </tab>

          <!--=============================================>>>>>
          = Προσωπικά στοιχεία =
          ===============================================>>>>-->

          <tab
            name="Προσωπικά στοιχεία"
            :has-errors="tabHasErrors('name', 'surname', 'fathername', 'birthdate',
                                      'policeid_number', 'afm', 'birthdate')"
          >
            <div class="row">
              <div class="col-sm-6">
                <!--Επώνυμο-->
                <text-field
                  v-model="form.data.surname"
                  title="ΕΠΩΝΥΜΟ *"
                  name="surname"
                  :error="form.errors.get('surname')"
                  :disabled="contestHasExternalData"
                />
              </div>
              <div class="col-sm-6">
                <!--'Ονομα-->
                <text-field
                  v-model="form.data.name"
                  title="ΟΝΟΜΑ *"
                  name="name"
                  :error="form.errors.get('name')"
                  :disabled="contestHasExternalData"
                />
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <!--Ονοματεπώνυμο Πατέρα-->
                <text-field
                  v-model="form.data.fathername"
                  title="ΟΝ/ΝΥΜΟ ΠΑΤΕΡΑ *"
                  name="fathername"
                  :error="form.errors.get('fathername')"
                  :disabled="contestHasExternalData"
                />
              </div>
              <div class="col-sm-6">
                <!--Ονοματεπώνυμο Μητέρας-->
                <text-field
                  v-model="form.data.mothername"
                  title="ΟΝ/ΝΥΜΟ ΜΗΤΕΡΑΣ"
                  name="mothername"
                  :error="form.errors.get('mothername')"
                  :disabled="contestHasExternalData"
                />
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <!--Ημερομηνία Γέννησης-->
                <date-field
                  v-model="form.data.birthdate"
                  title="ΗΜ/ΝΙΑ ΓΕΝΝΗΣΗΣ *"
                  name="birthdate"
                  :error="form.errors.get('birthdate')"
                  @input="form.errors.clear('birthdate')"
                />
              </div>
              <div class="col-sm-3">
                <!--Ελληνική ιθαγένεια-->
                <check-field
                  v-model="form.data.greek_nationality"
                  title="ΕΛΛΗΝΙΚΗ ΙΘΑΓΕΝΕΙΑ"
                  name="greek_nationality"
                  :error="form.errors.get('greek_nationality')"
                />
              </div>
            </div>
            <div class="row">
              <div class="col-sm-3">
                <!--ΑΔΤ-->
                <text-field
                  v-model="form.data.policeid_number"
                  title="Α.Δ.Τ. *"
                  name="policeid_number"
                  :error="form.errors.get('policeid_number')"
                />
              </div>
              <div class="col-sm-3">
                <!--'Ημερομηνία Έκδοσης-->
                <date-field
                  v-model="form.data.policeid_date"
                  title="ΗΜ/ΝΙΑ ΕΚΔΟΣΗΣ"
                  name="policeid_date"
                  :error="form.errors.get('policeid_date')"
                  @input="form.errors.clear('policeid_date')"
                />
              </div>
            </div>
            <div class="row">
              <div class="col-sm-3">
                <!--ΑΦΜ-->
                <text-field
                  v-model="form.data.afm"
                  title="Α.Φ.Μ."
                  name="afm"
                  :error="form.errors.get('afm')"
                  :disabled="contestHasExternalData"
                />
              </div>
              <div class="col-sm-3">
                <!--ΔΟΥ-->
                <text-field
                  v-model="form.data.doy"
                  title="Δ.Ο.Υ."
                  name="doy"
                  :error="form.errors.get('doy')"
                />
              </div>
              <div class="col-sm-3">
                <!--ΑΜΚΑ-->
                <text-field
                  v-model="form.data.amka"
                  title="Α.Μ.Κ.Α."
                  name="amka"
                  :error="form.errors.get('amka')"
                />
              </div>
            </div>
            <div class="row">
              <div class="col-sm-3" />
            </div>
          </tab>

          <!--=============================================>>>>>
          = Στοιχεία επικοινωνίας =
          ===============================================>>>>-->

          <tab name="Στοιχεία Επικοινωνιας">
            <div class="row">
              <div class="col-sm-4">
                <!--Διεύθυνση κατοικιάς (οδός)-->
                <text-field
                  v-model="form.data.street"
                  title="ΔΙΕΥΘΥΝΣΗ"
                  name="street"
                  :error="form.errors.get('street')"
                />
              </div>
              <div class="col-sm-2">
                <!--Διεύθυνσης κατοικίας (αριθμός)-->
                <text-field
                  v-model="form.data.street_number"
                  title="ΑΡ."
                  name="street_number"
                  :error="form.errors.get('street_number')"
                />
              </div>
              <div class="col-sm-2">
                <!--Τ.Κ.-->
                <text-field
                  v-model="form.data.postcode"
                  title="Τ.Κ."
                  name="postcode"
                  :error="form.errors.get('postcode')"
                />
              </div>
              <div class="col-sm-4">
                <!--Πόλη-->
                <text-field
                  v-model="form.data.city"
                  title="ΠΟΛΗ"
                  name="city"
                  :error="form.errors.get('city')"
                />
              </div>
            </div>
            <div class="row">
              <div class="col-sm-3">
                <!--Τηλέφωνο 1-->
                <text-field
                  v-model="form.data.phonenumber1"
                  title="Τηλέφωνο 1"
                  name="phonenumber1"
                  :error="form.errors.get('phonenumber1')"
                />
              </div>
              <div class="col-sm-3">
                <!--Τηλέφωνο 2-->
                <text-field
                  v-model="form.data.phonenumber2"
                  title="Τηλέφωνο 2"
                  name="phonenumber2"
                  :error="form.errors.get('phonenumber2')"
                />
              </div>
              <div class="col-sm-6">
                <!--Email-->
                <text-field
                  v-model="form.data.email"
                  title="Email"
                  name="email"
                  :error="form.errors.get('email')"
                />
              </div>
            </div>
          </tab>

          <!--=============================================>>>>>
          = Τίτλοι σπουδών =
          ===============================================>>>>-->

          <tab
            name="Τίτλοι Σπουδών"
            :has-errors="tabHasErrors('degrees', 'degrees.*.name', 'degrees.*.mark')"
          >
            <div class="row">
              <div class="col-sm-10">
                <!--If no asset has been added yet show this message-->
                <h5
                  v-show="form.data.degrees.length === 0"
                  class="text-right"
                >
                  Δεν έχουν καταχωρηθεί τίτλοι σπουδών μέχρι στιγμής
                </h5>
              </div>
              <div class="col-sm-2">
                <!--Add asset-->
                <button
                  class="btn bg-light-blue"
                  type="button"
                  @click="addQualification('degrees')"
                >
                  <i
                    class="fa fa-plus"
                    aria-hidden="true"
                  />
                  Προσθήκη
                </button>
              </div>
            </div>
            <br>
            <div class="row">
              <div class="col-sm-12">
                <table
                  v-show="form.data.degrees.length > 0"
                  class="table table-condensed asset-table"
                >
                  <tbody>
                    <tr class="bg-primary">
                      <th>A/A</th>
                      <th>Τίτλος *</th>
                      <th>Βασικός</th>
                      <th>Βαθμός *</th>
                      <th>Έτος</th>
                      <th><i class="fa fa-cog" /></th>
                    </tr>
                    <tr
                      v-for="(degree, index) in form.data.degrees"
                      :key="degree.id"
                    >
                      <!--No.-->
                      <td>
                        {{ index + 1 }}
                      </td>
                      <td width="70%">
                        <!--Degree name-->
                        <text-field
                          v-model="degree.name"
                          :name="`degrees.${ index }.name`"
                          :error="form.errors.get(`degrees.${ index }.name`)"
                        />
                      </td>
                      <td
                        width="10%"
                        class="text-center"
                      >
                        <!--Primary degree-->
                        <check-field
                          v-model="degree.is_primary"
                          name="`degrees.${ index }.is_primary`"
                          @input="form.errors.clear('degrees')"
                        />
                      </td>
                      <!--                      <td width="40%">-->
                      <!--                        &lt;!&ndash;Degree issuer&ndash;&gt;-->
                      <!--                        <text-field-->
                      <!--                          v-model="degree.issuer"-->
                      <!--                          :name="`degrees.${ index }.issuer`"-->
                      <!--                          :error="form.errors.get(`degrees.${ index }.issuer`)"-->
                      <!--                        />-->
                      <!--                      </td>-->
                      <td width="10%">
                        <!--Degree mark-->
                        <text-field
                          v-model="degree.mark"
                          :name="`degrees.${ index }.mark`"
                          :error="form.errors.get(`degrees.${ index }.mark`)"
                        />
                      </td>
                      <td width="10%">
                        <!--Degree year-->
                        <text-field
                          v-model="degree.year"
                          :name="`degrees.${ index }.year`"
                          :error="form.errors.get(`degrees.${ index }.year`)"
                        />
                      </td>
                      <td class="action-btn-container">
                        <!--Attach degree-->
                        <!--                        <a-->
                        <!--                          v-if="degree.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', degree.has_attachments? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openAttachmentsModal(degree, 'degree')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-paperclip fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Verify degree-->
                        <!--                        <a-->
                        <!--                          v-if="degree.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', degree.verified ? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openVerificationsModal(degree, 'degree')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-check fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Delete degree-->
                        <a
                          href="#"
                          class="flex-item text-gray hover-text-red"
                          @click.prevent="removeQualification('degrees', index)"
                        >
                          <i
                            class="fa fa-remove fa-2x"
                            aria-hidden="true"
                          />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <p class="text-danger">
                  {{ form.errors.get('degrees') }}
                </p>
              </div>
            </div>
          </tab>

          <!--=============================================>>>>>
          = Μεταπτυχιακοί τίτλοι =
          ===============================================>>>>-->

          <tab
            name="Μεταπτυχιακοί Τίτλοι"
            :has-errors="tabHasErrors('postgraduates.*.name', 'postgraduates.*.issuer', 'postgraduates.*.mark')"
          >
            <div class="row">
              <div class="col-sm-10">
                <h5
                  v-show="form.data.postgraduates.length === 0"
                  class="text-right"
                >
                  Δεν έχουν
                  καταχωρηθεί
                  μεταπτυχιακοί τίτλοι σπουδών μέχρι
                  στιγμής
                </h5>
              </div>
              <div class="col-sm-2">
                <!--Add Postgrad-->
                <button
                  class="btn bg-light-blue"
                  type="button"
                  @click="addQualification('postgraduates')"
                >
                  <i
                    class="fa fa-plus"
                    aria-hidden="true"
                  />
                  Προσθήκη
                </button>
              </div>
            </div>
            <br>
            <div class="row">
              <div class="col-sm-12">
                <table
                  v-show="form.data.postgraduates.length > 0"
                  class="table table-condensed asset-table"
                >
                  <tbody>
                    <tr class="bg-primary">
                      <th>A/A</th>
                      <th>Τίτλος</th>
                      <th>Εννιαίο</th>
                      <th><i class="fa fa-cog" /></th>
                    </tr>
                    <tr
                      v-for="(postgraduate, index) in form.data.postgraduates"
                      :key="postgraduate.id"
                    >
                      <td>
                        <!--No.-->
                        {{ index + 1 }}
                      </td>
                      <td width="90%">
                        <!--Postgrad name-->
                        <text-field
                          v-model="postgraduate.name"
                          :name="`postgraduates.${ index }.name`"
                          :error="form.errors.get(`postgraduates.${ index }.name`)"
                        />
                      </td>
                      <td width="10%">
                        <!--Integrated Postgraduate-->
                        <check-field
                          v-model="postgraduate.is_integrated"
                          name="`postgraduates.${ index }.is_integrated`"
                          :error="form.errors.get(`postgraduates.${ index }.is_integrated`)"
                        />
                      </td>
                      <!--                      <td width="10%">-->
                      <!--                        &lt;!&ndash;Postgrad year&ndash;&gt;-->
                      <!--                        <text-field-->
                      <!--                          v-model="postgraduate.year"-->
                      <!--                          :name="`postgraduates.${ index }.year`"-->
                      <!--                          :error="form.errors.get(`postgraduates.${ index }.year`)"-->
                      <!--                        />-->
                      <!--                      </td>-->
                      <td class="action-btn-container">
                        <!--Attach postgraduate-->
                        <!--                        <a-->
                        <!--                          v-if="postgraduate.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', postgraduate.has_attachments? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openAttachmentsModal(postgraduate, 'postgraduate')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-paperclip fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Verify postgraduate-->
                        <!--                        <a-->
                        <!--                          v-if="postgraduate.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', postgraduate.verified ? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openVerificationsModal(postgraduate, 'postgraduate')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-check fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Delete postgraduate-->
                        <a
                          href="#"
                          class="flex-item text-gray hover-text-red"
                          @click.prevent="removeQualification('postgraduates', index)"
                        >
                          <i
                            class="fa fa-remove fa-2x"
                            aria-hidden="true"
                          />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </tab>

          <!--=============================================>>>>>
          = Διδακτορικοί τίτλοι =
          ===============================================>>>>-->

          <tab
            name="Διδακτορικοί Τίτλοι"
            :has-errors="tabHasErrors('doctorates.*.name', 'doctorates.*.issuer', 'doctorates.*.mark')"
          >
            <div class="row">
              <div class="col-sm-10">
                <h5
                  v-show="form.data.doctorates.length === 0"
                  class="text-right"
                >
                  Δεν έχουν
                  καταχωρηθεί
                  διδακτορικά μέχρι στιγμής
                </h5>
              </div>
              <div class="col-sm-2">
                <!--Add doctorate-->
                <button
                  class="btn bg-light-blue"
                  type="button"
                  @click="addQualification('doctorates')"
                >
                  <i
                    class="fa fa-plus"
                    aria-hidden="true"
                  />
                  Προσθήκη
                </button>
              </div>
            </div>
            <br>
            <div class="row">
              <div class="col-sm-12">
                <table
                  v-show="form.data.doctorates.length > 0"
                  class="table table-condensed asset-table"
                >
                  <tbody>
                    <tr class="bg-primary">
                      <th>A/A</th>
                      <th>Τίτλος</th>
                      <!--                      <th>Ίδρυμα</th>-->
                      <!--                      <th>Έτος</th>-->
                      <th><i class="fa fa-cog" /></th>
                    </tr>
                    <tr
                      v-for="(doctorate, index) in form.data.doctorates"
                      :key="doctorate.id"
                    >
                      <td>
                        <!--No.-->
                        {{ index + 1 }}
                      </td>
                      <td width="100%">
                        <!--Doctorate name-->
                        <text-field
                          v-model="doctorate.name"
                          :name="`doctorates.${ index }.name`"
                          :error="form.errors.get(`doctorates.${ index }.name`)"
                        />
                      </td>
                      <!--                      <td width="45%">-->
                      <!--                        &lt;!&ndash;Doctorate issuer&ndash;&gt;-->
                      <!--                        <text-field-->
                      <!--                          v-model="doctorate.issuer"-->
                      <!--                          :name="`doctorates.${ index }.issuer`"-->
                      <!--                          :error="form.errors.get(`doctorates.${ index }.issuer`)"-->
                      <!--                        />-->
                      <!--                      </td>-->
                      <!--                      <td width="10%">-->
                      <!--                        &lt;!&ndash;Doctorate year&ndash;&gt;-->
                      <!--                        <text-field-->
                      <!--                          v-model="doctorate.year"-->
                      <!--                          :name="`doctorates.${ index }.year`"-->
                      <!--                          :error="form.errors.get(`doctorates.${ index }.year`)"-->
                      <!--                        />-->
                      <!--                      </td>-->
                      <td class="action-btn-container">
                        <!--Attach doctorate-->
                        <!--                        <a-->
                        <!--                          v-if="doctorate.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', doctorate.has_attachments? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openAttachmentsModal(doctorate, 'doctorate')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-paperclip fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Verify doctorate-->
                        <!--                        <a-->
                        <!--                          v-if="doctorate.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', doctorate.verified ? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openVerificationsModal(doctorate, 'doctorate')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-check fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Delete doctorate-->
                        <a
                          href="#"
                          class="flex-item text-gray hover-text-red"
                          @click.prevent="removeQualification('doctorates', index)"
                        >
                          <i
                            class="fa fa-remove fa-2x"
                            aria-hidden="true"
                          />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </tab>

          <!--=============================================>>>>>
          = Εμπειρία =
          ===============================================>>>>-->

          <tab
            name="Εμπειρία"
            :has-errors="tabHasErrors('experiences.*.name', 'experiences.*.months')"
          >
            <div class="row">
              <div class="col-sm-10">
                <h5
                  v-show="form.data.experiences.length === 0"
                  class="text-right"
                >
                  Δεν έχει καταχωρηθεί εργασιακή εμπειρία μέχρι στιγμής
                </h5>
              </div>
              <div class="col-sm-2">
                <!--Add experience-->
                <button
                  v-show="form.data.experiences.length == 0"
                  class="btn bg-light-blue"
                  type="button"
                  @click="addQualification('experiences')"
                >
                  <i
                    class="fa fa-plus"
                    aria-hidden="true"
                  />
                  Προσθήκη
                </button>
              </div>
            </div>
            <br>
            <div class="row">
              <div class="col-sm-12">
                <table
                  v-show="form.data.experiences.length > 0"
                  class="table table-condensed asset-table"
                >
                  <tbody>
                    <tr class="bg-primary">
                      <th>A/A</th>
                      <!--                      <th>Aπό</th>-->
                      <!--                      <th>Έως</th>-->
                      <th>Μήνες *</th>
                      <!--                      <th>Φορέας</th>-->
                      <!--                      <th>Ειδικότητα *</th>-->
                      <th><i class="fa fa-cog" /></th>
                    </tr>
                    <tr
                      v-for="(experience, index) in form.data.experiences"
                      :key="experience.id"
                    >
                      <td>
                        <!--No.-->
                        {{ index + 1 }}
                      </td>
                      <!--                      <td>-->
                      <!--                        &lt;!&ndash;Experience start date&ndash;&gt;-->
                      <!--                        <date-field-->
                      <!--                          v-model="experience.started_at"-->
                      <!--                          :name="`experiences.${ index }.started_at`"-->
                      <!--                          :error="form.errors.get(`experiences.${ index }.started_at`)"-->
                      <!--                          @input="form.errors.clear(`experiences.${ index }.started_at`)"-->
                      <!--                        />-->
                      <!--                      </td>-->
                      <!--                      <td>-->
                      <!--                        &lt;!&ndash;Experience end date&ndash;&gt;-->
                      <!--                        <date-field-->
                      <!--                          v-model="experience.ended_at"-->
                      <!--                          :name="`experiences.${ index }.ended_at`"-->
                      <!--                          :error="form.errors.get(`experiences.${ index }.ended_at`)"-->
                      <!--                          @input="form.errors.clear(`experiences.${ index }.ended_at`)"-->
                      <!--                        />-->
                      <!--                      </td>-->
                      <td width="100%">
                        <!--Experience overall duration in months-->
                        <text-field
                          v-model="experience.months"
                          :name="`experiences.${ index }.months`"
                          :error="form.errors.get(`experiences.${ index }.months`)"
                        />
                      </td>
                      <!--                      <td width="30%">-->
                      <!--                        &lt;!&ndash;Experience issuer&ndash;&gt;-->
                      <!--                        <text-field-->
                      <!--                          v-model="experience.issuer"-->
                      <!--                          :name="`experiences.${ index }.issuer`"-->
                      <!--                          :error="form.errors.get(`experiences.${ index }.issuer`)"-->
                      <!--                        />-->
                      <!--                      </td>-->
                      <!--                      <td width="30%">-->
                      <!--                        &lt;!&ndash;Experience description (name)&ndash;&gt;-->
                      <!--                        <text-field-->
                      <!--                          v-model="experience.name"-->
                      <!--                          :name="`experiences.${ index }.name`"-->
                      <!--                          :error="form.errors.get(`experiences.${ index }.name`)"-->
                      <!--                        />-->
                      <!--                      </td>-->
                      <td class="action-btn-container">
                        <!--Attach experience-->
                        <!--                        <a-->
                        <!--                          v-if="experience.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', experience.has_attachments? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openAttachmentsModal(experience, 'experience')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-paperclip fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Verify experience-->
                        <!--                        <a-->
                        <!--                          v-if="experience.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', experience.verified ? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openVerificationsModal(experience, 'experience')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-check fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Delete experience-->
                        <a
                          href="#"
                          class="flex-item text-gray hover-text-red"
                          @click.prevent="removeQualification('experiences', index)"
                        >
                          <i
                            class="fa fa-remove fa-2x"
                            aria-hidden="true"
                          />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </tab>

          <!--=============================================>>>>>
          = Γλωσσομάθεια =
          ===============================================>>>>-->

          <tab
            name="Γλωσσομάθεια"
            :has-errors="tabHasErrors('language_skills.*.language_id', 'languages_skill.*.language_level_id')"
          >
            <div class="row">
              <div class="col-sm-10">
                <h5
                  v-show="form.data.language_skills.length === 0"
                  class="text-right"
                >
                  Δεν έχει καταχωρηθεί πιστοποιητικό γλωσσομάθειας μέχρι στιγμής
                </h5>
              </div>
              <div class="col-sm-2">
                <!--Add Language Skill-->
                <button
                  class="btn bg-light-blue"
                  type="button"
                  @click="addQualification('language_skills')"
                >
                  <i
                    class="fa fa-plus"
                    aria-hidden="true"
                  />
                  Προσθήκη
                </button>
              </div>
            </div>
            <br>
            <div class="row">
              <div class="col-sm-12">
                <table
                  v-show="form.data.language_skills.length > 0"
                  class="table table-condensed asset-table"
                >
                  <tbody>
                    <tr class="bg-primary">
                      <th>A/A</th>
                      <th>Γλώσσα *</th>
                      <th>Επίπεδο *</th>
                      <th>Τίτλος Πιστοποιητικού</th>
                      <!--                      <th>Φορέας</th>-->
                      <th><i class="fa fa-cog" /></th>
                    </tr>
                    <tr
                      v-for="(language_skill, index) in form.data.language_skills"
                      :key="language_skill.id"
                    >
                      <td>
                        <!--No.-->
                        {{ index + 1 }}
                      </td>
                      <td width="20%">
                        <!--Language-->
                        <select-field
                          v-model="language_skill.language_id"
                          :name="`language_skills.${ index }.language_id`"
                          :error="form.errors.get(`language_skills.${ index }.language_id`)"
                          :options="formRelatedModels['languages']"
                        />
                      </td>
                      <td width="20%">
                        <!--Language Level-->
                        <select-field
                          v-model="language_skill.language_level_id"
                          :name="`language_skills.${ index }.language_level_id`"
                          :error="form.errors.get(`language_skills.${ index }.language_level_id`)"
                          :options="formRelatedModels['languageLevels']"
                        />
                      </td>
                      <td width="60%">
                        <!--Language certification name-->
                        <text-field
                          v-model="language_skill.name"
                          :name="`language_skills.${ index }.name`"
                          :error="form.errors.get(`language_skills.${ index }.name`)"
                        />
                      </td>
                      <!--                      <td width="30%">-->
                      <!--                        &lt;!&ndash;Language certification issuer&ndash;&gt;-->
                      <!--                        <text-field-->
                      <!--                          v-model="language_skill.issuer"-->
                      <!--                          :name="`language_skills.${ index }.issuer`"-->
                      <!--                          :error="form.errors.get(`language_skills.${ index }.issuer`)"-->
                      <!--                        />-->
                      <!--                      </td>-->
                      <td class="action-btn-container">
                        <!--Attach language skill-->
                        <!--                        <a-->
                        <!--                          v-if="language_skill.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', 'hover-text-light-blue', language_skill.has_attachments ? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openAttachmentsModal(language_skill, 'language_skill')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-paperclip fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Verify language_skill-->
                        <!--                        <a-->
                        <!--                          v-if="language_skill.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', language_skill.verified ? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openVerificationsModal(language_skill, 'language_skill')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-check fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Delete language_skill-->
                        <a
                          href="#"
                          class="flex-item text-gray hover-text-red"
                          @click.prevent="removeQualification('language_skills', index)"
                        >
                          <i
                            class="fa fa-remove fa-2x"
                            aria-hidden="true"
                          />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </tab>

          <!--=============================================>>>>>
          = Γνώση χρήσης Η/Υ =
          ===============================================>>>>-->

          <tab
            name="Γνώση Χρήσης Η/Υ"
            :has-errors="tabHasErrors('computer_skills.*.name')"
          >
            <div class="row">
              <div class="col-sm-10">
                <h5
                  v-show="form.data.computer_skills.length === 0"
                  class="text-right"
                >
                  Δεν έχει καταχωρηθεί αποδεικτικό γνώσης χρήσης Η/Υ μέχρι στιγμής
                </h5>
              </div>
              <div class="col-sm-2">
                <!--Add Computer Skill-->
                <button
                  class="btn bg-light-blue"
                  type="button"
                  @click="addQualification('computer_skills')"
                >
                  <i
                    class="fa fa-plus"
                    aria-hidden="true"
                  />
                  Προσθήκη
                </button>
              </div>
            </div>
            <br>
            <div class="row">
              <div class="col-sm-12">
                <table
                  v-show="form.data.computer_skills.length > 0"
                  class="table table-condensed asset-table"
                >
                  <tbody>
                    <tr class="bg-primary">
                      <th>A/A</th>
                      <th>Τίτλος αποδεικτικού</th>
                      <th><i class="fa fa-cog" /></th>
                    </tr>
                    <tr
                      v-for="(computer_skill, index) in form.data.computer_skills"
                      :key="computer_skill.id"
                    >
                      <!--No.-->
                      <td>
                        {{ index + 1 }}
                      </td>
                      <td>
                        <!--Computer Skills certification name-->
                        <text-field
                          v-model="computer_skill.name"
                          :name="`computer_skills.${ index }.name`"
                          :error="form.errors.get(`computer_skills.${ index }.name`)"
                        />
                      </td>
                      <td class="action-btn-container">
                        <!--Attach computer skill-->
                        <!--                        <a-->
                        <!--                          v-if="computer_skill.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', computer_skill.has_attachments? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openAttachmentsModal(computer_skill, 'computer_skill')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-paperclip fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Verify computer_skill-->
                        <!--                        <a-->
                        <!--                          v-if="computer_skill.id"-->
                        <!--                          href="#"-->
                        <!--                          :class="['flex-item', computer_skill.verified ? 'text-blue-light': 'text-gray']"-->
                        <!--                          @click.prevent="openVerificationsModal(computer_skill, 'computer_skill')"-->
                        <!--                        >-->
                        <!--                          <i-->
                        <!--                            class="fa fa-check fa-2x"-->
                        <!--                            aria-hidden="true"-->
                        <!--                          />-->
                        <!--                        </a>-->
                        <!--Delete computer_skill-->
                        <a
                          href="#"
                          class="flex-item text-gray hover-text-red"
                          @click.prevent="removeQualification('computer_skills', index)"
                        >
                          <i
                            class="fa fa-remove fa-2x"
                            aria-hidden="true"
                          />
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </tab>

          <!--=============================================>>>>>
          = Κοινωνικά κριτήρια =
          ===============================================>>>>-->

          <tab name="Κοινωνικά Κριτήρια">
            <div class="row">
              <div class="col-sm-6">
                <!-- Unemployments -->
                <div class="row">
                  <div class="col-sm-6">
                    <number-field
                      v-model="form.data.unemployments[0].months"
                      title="Μήνες Ανεργίας"
                      :name="`unemployments.0.months`"
                      :error="form.errors.get(`unemployments.0.months`)"
                    />
                  </div>
                  <div class="col-sm-6">
                    <switch-field
                      v-model="form.data.unemployments[0].is_continued"
                      title="Συνεχόμενη ανεργία"
                      :name="`unemployments.0.is_continued`"
                      :error="form.errors.get(`unemployments.0.is_continued`)"
                    />
                  </div>
                </div>
                <div class="row">
                  <div class="col-sm-6">
                    <!-- Minors -->
                    <number-field
                      v-model="form.data.minors[0].amount"
                      title="Αρ. ανήλικων τέκνων"
                      :name="`minors.0.amount`"
                      :error="form.errors.get(`minors.0.amount`)"
                      @valid="form.errors.clear(`minors.0.amount`)"
                    />
                  </div>
                </div>
              </div>
              <div class="col-sm-6">
                <div class="row">
                  <div class="col-sm-12">
                    <!-- Multi-child families (children) -->
                    <switch-field
                      v-model="form.data.multi_child_families[0].is_eligible"
                      title="Πολύτεκνος ή τέκνο πολύτεκνης οικ."
                      :name="`multi_child_families.0.is_eligible`"
                      :error="form.errors.get(`multi_child_families.0.is_eligible`)"
                    />
                  </div>
                </div>
                <div class="row">
                  <div class="col-sm-12">
                    <!-- Three-child families (children) -->
                    <switch-field
                      v-model="form.data.three_child_families[0].is_eligible"
                      title="Τρίτεκνος ή τέκνο τρίτεκνης οικ."
                      :name="`three_child_families.0.is_eligible`"
                      :error="form.errors.get(`three_child_families.0.is_eligible`)"
                    />
                  </div>
                </div>
                <div class="row">
                  <div class="col-sm-12">
                    <!-- Single-parent families (children) -->
                    <switch-field
                      v-model="form.data.single_parent_families[0].is_eligible"
                      title="Γονέας ή τέκνο μονογονεικής οικ."
                      :name="`single_parent_families.0.is_eligible`"
                      :error="form.errors.get(`single_parent_families.0.is_eligible`)"
                    />
                  </div>
                </div>
                <div class="row">
                  <div class="col-sm-12">
                    <!-- Disabilities -->
                    <switch-field
                      v-model="form.data.disabilities[0].is_eligible"
                      title="Ατομική αναπηρία"
                      :name="`disabilities.0.is_eligible`"
                      :error="form.errors.get(`disabilities.0.is_eligible`)"
                    />
                  </div>
                </div>
                <div class="row">
                  <div class="col-sm-12">
                    <!-- Disability family -->
                    <switch-field
                      v-model="form.data.family_disabilities[0].is_eligible"
                      title="Αναπηρία μέλους οικ."
                      :name="`family_disabilities.0.is_eligible`"
                      :error="form.errors.get(`family_disabilities.0.is_eligible`)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </tab>
        </tabs>
      </form>
      <template #footer>
        <div class="row">
          <div class="col-sm-3">
            <a
              v-if="! isUpdating"
              class="btn btn-block btn-default"
              :href="`/contractuals/contests/${contest.id}`"
            >
              <i
                class="fa fa-arrow-left"
                aria-hidden="true"
              /> Επιστροφή
            </a>
            <a
              v-else
              class="btn btn-block btn-default"
              href=""
              disabled
            ><i
              class="fa fa-hourglass"
              aria-hidden="true"
            /></a>
          </div>
          <div class="col-sm-3">
            <button
              class="btn btn-block btn-warning btn-outline"
              type="button"
              :disabled="form.errors.has() || isUpdating"
              @click="importApplication"
            >
              <i
                class="fa fa-refresh"
                aria-hidden="true"
              /> Επαναφορά
            </button>
          </div>
          <div class="col-sm-3">
            <button
              class="btn btn-block btn-primary btn-outline"
              type="button"
              :disabled="form.errors.has() || isUpdating"
              @click="submit"
            >
              <i
                class="fa fa-check-square"
                aria-hidden="true"
              /> Προσωρινή Αποθήκευση
            </button>
          </div>
          <div class="col-sm-3">
            <button
              class="btn btn-block btn-primary"
              type="button"
              :disabled="form.errors.has() || isUpdating"
              @click="submitAndLock"
            >
              <i
                class="fa fa-gavel"
                aria-hidden="true"
              /> Αποθήκευση & Βαθμολόγηση
            </button>
          </div>
        </div>
      </template>
    </box>

    <!--=============================================>>>>>
    = Asset Verification modal =
    ===============================================>>>>-->

    <modal
      v-if="verificationDialogVisible"
      @close="verificationDialogVisible = false"
    >
      <asset-verification
        :asset="selectedAsset"
        :asset-type="selectedAssetType"
        @close="updateVerifiedStatus"
      />
    </modal>

    <!--=============================================>>>>>
    = Asset Attachment modal =
    ===============================================>>>>-->

    <modal
      v-if="attachmentModalVisible"
      @close="attachmentModalVisible = false"
    >
      <asset-attachment
        :asset="selectedAsset"
        :asset-type="selectedAssetType"
        @attachments-updated="updateHasAttachmentsStatus"
      />
    </modal>
  </div>
</template>

<script>
import merge from 'lodash/merge';

import Box from '../../shared/components/ui/Boxes/Box.vue';
import CheckField from '../../shared/components/ui/FormFields/CheckField.vue';
import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import NumberField from '../../shared/components/ui/FormFields/NumberField.vue';
import SelectField from '../../shared/components/ui/FormFields/SelectField.vue';
import SelectfilterField from '../../shared/components/ui/FormFields/SelectfilterField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import Modal from '../../shared/components/ui/Modal/Modal.vue';
import Tab from '../../shared/components/ui/Tabs/TabItem.vue';
import Tabs from '../../shared/components/ui/Tabs/VerticalTabs.vue';
import Form from '../../shared/Form';
import __ from '../../shared/Helpers';
import AssetAttachment from './AssetAttachment.vue';
import AssetVerification from './AssetVerification.vue';

export default {
  name: 'ApplicationForm',

  components: {
    Box,
    Tabs,
    Tab,
    Modal,
    TextField,
    DateField,
    SelectfilterField,
    SelectField,
    SwitchField,
    NumberField,
    CheckField,
    AssetVerification,
    AssetAttachment,
  },

  props: {
    contest: { type: Object, required: true },
    applicant: { type: Object, default: null },
    application: { type: Object, default: null },
    formRelatedModels: { type: Object },
  },

  data() {
    return {
      verificationDialogVisible: false,
      attachmentModalVisible: false,
      selectedAsset: {},
      selectedAssetType: '',
      applicantCategories: [{ value: 1, label: 'ΠΕ' }, { value: 2, label: 'ΤΕ' }, { value: 3, label: 'ΔΕ' }, { value: 4, label: 'ΥΕ' }],
      contestHasExternalData: true,
      isUpdating: false,
      applicationAuxiliaryLevel: null,
      auxiliaryLevels: [{ value: 0, label: 'Κύρια' }, { value: 1, label: 'Α' }, { value: 2, label: 'Β' }],
      form: new Form({
        id: '',
        applicant_id: '',
        contest_id: this.contest.id,
        protocol_number: '',
        protocol_date: '',
        positions: [],
        applicant_category: '',
        meets_general_requirements: true,
        invalidated: false,
        impediment_eight_months: false,
        invalidation_description: '',
        invalidation_description_id: '',
        score: '',
        name: '',
        surname: '',
        fathername: '',
        mothername: '',
        birthdate: '',
        policeid_number: '',
        policeid_date: '',
        afm: this.formRelatedModels.afm || '',
        doy: '',
        amka: '',
        street: '',
        street_number: '',
        postcode: '',
        city: '',
        phonenumber1: '',
        phonenumber2: '',
        email: '',
        eu_citizen: false,
        greek_nationality: true,
        degrees: [],
        postgraduates: [],
        doctorates: [],
        experiences: [],
        language_skills: [],
        computer_skills: [],
        unemployments: [{ id: '', months: 0, verified: false }],
        multi_child_families: [{
          id: '', children: 0, siblings: 0, verified: false,
        }],
        three_child_families: [{
          id: '', children: 0, siblings: 0, verified: false,
        }],
        single_parent_families: [{
          id: '', children: 0, siblings: 0, verified: false,
        }],
        disabilities: [{ id: '', percentage: 0, verified: false }],
        family_disabilities: [{ id: '', percentage: 0, verified: false }],
        minors: [{ id: '', amount: 0, verified: false }],
      }),
    };
  },

  computed: {
    positions() {
      return this.extractContestPositions();
    },
  },

  watch: {
    /**
       * Use it to toggle GR/EU citizen checkboxes
       */
    // 'form.data.eu_citizen': function () {
    //   this.form.data.greek_nationality = !this.form.data.eu_citizen;
    // },
    //
    // 'form.data.greek_nationality': function () {
    //   this.form.data.eu_citizen = !this.form.data.greek_nationality;
    // },

    'form.data.invalidated': function () {
      if (this.form.data.invalidated === false) {
        this.form.data.invalidation_description = '';
        this.form.data.invalidation_description_id = '';
      }
    },
  },

  created() {
    // In case the applicant's profile pre-exists
    if (!this.application && this.applicant) {
      //= =====================================================================================================
      // NASTY HACK:
      //= =====================================================================================================
      // Since we are trying to update the application form with applicant data (that is a subset of
      // the application data, e.g. no "healthy" or "unrestricted" properties exist on applicant),
      // we MUST explicitly include those extra properties to the data passed to form.populate().
      // In addition, we have to make sure that the applicant's 'id' property does not
      // overwrites the application's id property, but the "applicant_id" property...
      // Therefore...
      //= =====================================================================================================

      // ...we find any additional properties that exist on the this.form.data object
      const extraProps = __.pickAdditionalProperties(this.form.data, this.applicant);

      // we take care of the applicant_id
      extraProps.applicant_id = this.applicant.id;

      // ...we update the applicant data to include those extra properties, and
      const res = merge(this.applicant, extraProps);

      // ...finally we se the id property to null since this is a new application and
      // we update the application form data with all required information
      res.id = ''; // this is the application's id, but it has taken the value of applicant id !!!

      this.form.populate(res);
    }

    // In case this is an edit form
    if (this.application) {
      this.form.populate(this.application);
    }

    if (this.contest.type_id === 1) {
      this.contestHasExternalData = false;
    }

    // Add the watcher here, to make it available after inintializing the form data.
    this.$watch('form.data.applicant_category', function (newVal, oldVal) {
      if (newVal !== oldVal) {
        this.form.data.positions = [];
      }
    });
  },

  methods: {
    submit() {
      const isEditForm = !!this.application;

      this.isUpdating = true;

      let submitMethod = 'post';
      let submitUrl = `/contractuals/contests/${this.contest.id}/applications`;

      if (isEditForm) {
        submitMethod = 'put';
        submitUrl = `${submitUrl}/${this.application.id}?t=${this.application.updated_at}`;
      }

      this.form[submitMethod](submitUrl)
        .then((response) => {
          window.swal({
            type: 'success',
            title: 'Επιτυχία!',
            text: response.data.message,
            showConfirmButton: false,
            timer: 3000,
          });
          window.location.href = `/contractuals/contests/${this.contest.id}/applications/${response.data.application.id}/edit`;
        })
        .catch((error) => {
          if (error.response.status === 409 || error.response.status === 403) {
            window.swal({
              type: 'error',
              title: 'Σφάλμα!',
              text: error.response.data.message,
              showConfirmButton: true,
            }, () => {
              window.location.replace(`/contractuals/contests/${this.contest.id}`);
            });
          } else {
            window.swal({
              type: 'error',
              title: 'Σφάλμα Καταχώρησης',
              text: 'Παρακαλώ ελέγξτε τα στοιχεία της αίτησης.', // error.response.data.message,
              showConfirmButton: false,
              timer: 3000,
            });
            this.isUpdating = false;
          }
        });
    },

    submitAndLock() {
      const isEditForm = !!this.application;
      this.isUpdating = true;

      let submitMethod = 'post';
      let submitUrl = `/contractuals/contests/${this.contest.id}/applications`;

      if (isEditForm) {
        submitMethod = 'put';
        submitUrl = `${submitUrl}/${this.application.id}?t=${this.application.updated_at}`;
      }

      this.form[submitMethod](submitUrl)
        .then((res) => {
          this.$http.put(`/api/contractuals/applications/${res.data.application.id}/locked-status`)
            .then(() => {
              window.swal({
                type: 'success',
                title: 'Επιτυχία!',
                text: 'Η αίτηση κλειδώθηκε',
                showConfirmButton: false,
                timer: 3000,
              }, () => {
                window.location.replace(`/contractuals/contests/${this.contest.id}`);
              });
            })
            .catch((err) => {
              window.swal({
                type: 'error',
                title: 'Σφάλμα Καταχώρησης',
                text: err.response.data.message, // error.response.data.message,
                showConfirmButton: false,
                timer: 3000,
              });
              this.isUpdating = false;
            });
        })
        .catch((error) => {
          if (error.response.status === 409) {
            window.swal({
              type: 'error',
              title: 'Σφάλμα!',
              text: error.response.data.message,
              showConfirmButton: true,
            }, () => {
              window.location.replace(`/contractuals/contests/${this.contest.id}`);
            });
          } else {
            window.swal({
              type: 'error',
              title: 'Σφάλμα Καταχώρησης',
              text: 'Παρακαλώ ελέγξτε τα στοιχεία της αίτησης.', // error.response.data.message,
              showConfirmButton: false,
              timer: 3000,
            });
            this.isUpdating = false;
          }
          this.isUpdating = false;
        });
    },

    importApplication() {
      __.askToConfirm({
        title: 'Επαναφορά Αίτησης',
        body: 'Επιθυμείτε να συνεχίσετε; Θα επαναφέρετε την αίτηση στην αρχική της μορφή όπως υποβλήθηκε από τον υποψήφιο, με αυτόματη βαθμολόγηση. Η αίτηση θα πρέπει να ελεγχθεί εξ αρχής καθώς τυχόν διορθώσεις θα αναιρεθούν.',
      }).then(() => {
        this.isUpdating = true;
        this.$http.put(`/api/contractuals/applications/${this.application.id}/import`)
          .then(() => {
            window.swal({
              type: 'success',
              title: 'Επιτυχία!',
                text: 'Η αίτηση επανήλθε στην αρχική μορφή σε κατάσταση "Προς Έλεγχο". Η αίτηση θα πρέπει να ελεγχθεί εξ αρχής!',
              showConfirmButton: true,
              timer: 3000,
            }, () => {
              window.location.replace(`/contractuals/contests/${this.contest.id}`);
            });
          })
          .catch((err) => {
            console.log(err.response.data.message);
            setTimeout(() => {
              window.swal({
                type: 'error',
                title: `Σφάλμα ${err.response.status}`,
                text: err.response.data.message,
                showConfirmButton: false,
                timer: 4000,
              });
            }, 100);
          })
          .finally(() => {
            this.isUpdating = false;
          });
      });
    },

    tabHasErrors(...errors) {
      for (const error of errors) {
        if (this.form.errors.has(error)) return true;
      }

      return false;
    },

    addQualification(assetType) {
      switch (assetType) {
        case 'degrees':
          this.form.data[assetType].push({
            id: '',
            name: '',
            issuer: '',
            mark: '',
            year: '',
            verified: '',
          });
          this.form.errors.clear('degrees');
          break;
        case 'postgraduates':
          this.form.data[assetType].push({
            id: '',
            name: '',
            issuer: '',
            year: '',
            verified: '',
          });
          break;
        case 'doctorates':
          this.form.data[assetType].push({
            id: '',
            name: '',
            issuer: '',
            year: '',
            verified: '',
          });
          break;
        case 'experiences':
          this.form.data[assetType].push({
            id: '',
            name: '',
            issuer: '',
            started_at: '',
            ended_at: '',
            months: '',
            verified: '',
          });
          break;
        case 'language_skills':
          this.form.data[assetType].push({
            id: '',
            language_id: '',
            language_level_id: '',
            name: '',
            issuer: '',
            verified: '',
          });
          break;
        case 'computer_skills':
          this.form.data[assetType].push({
            id: '',
            name: '',
            verified: '',
          });
          break;
        default:
          alert('This asset field does not exist');
      }
    },

    removeQualification(assetType, index) {
      const { id } = this.form.data[assetType][index];
      const assetExistsOnServer = id !== '';
      const isEditForm = !!this.application;
      this.form.errors.clear(`experiences.${index}.months`);

      // Don't do ajax delete request when removing qualifications.
      // Just remove them from frontend and update db on submit.
      //
      // if (assetExistsOnServer) {
      //   let url = `/api/contractuals/applications/${this.application.id}/qualifications/${assetType}/${id}`;
      //   if (isEditForm) {
      //     url += `?t=${this.application.updated_at}`;
      //   }

      //   __.askToConfirm({
      //     title: 'Είστε σίγουροι?',
      //     body: 'Θα γίνει διαγραφή της εγγραφής!',
      //   }).then(() => {
      //     this.$http.delete(url)
      //       .then((response) => {
      //         this.form.data[assetType].splice(index, 1);
      //         window.swal({
      //           type: 'success',
      //           title: 'Επιτυχία!',
      //           text: response.data.message,
      //           showConfirmButton: false,
      //           timer: 3000,
      //         });
      //       })
      //       .catch((error) => {
      //         window.swal({
      //           type: 'error',
      //           title: `Σφάλμα ${error.status}`,
      //           text: error.statusText,
      //           showConfirmButton: false,
      //           timer: 3000,
      //         });
      //       });
      //   });
      // } else {
      // TODO clear validation errors when deleting an asset
      // this.form.errors.clear(`${ assetType }.${ index }.${ fieldName }`);
      this.form.data[assetType].splice(index, 1);
      window.swal({
        type: 'success',
        title: 'Επιτυχία!',
        text: 'Η εγγραφή διαγράφτηκε.',
        showConfirmButton: false,
        timer: 3000,
      });
    },

    openVerificationsModal(asset, type) {
      this.selectedAsset = asset;
      this.selectedAssetType = type;
      this.verificationDialogVisible = true;
    },

    updateVerifiedStatus(status) {
      this.selectedAsset.verified = status;
      this.selectedAsset = {};
      this.verificationDialogVisible = false;
    },

    openAttachmentsModal(asset, type) {
      this.selectedAsset = asset;
      this.selectedAssetType = type;
      this.attachmentModalVisible = true;
    },

    updateHasAttachmentsStatus(newAttachments) {
      this.selectedAsset.has_attachments = !!newAttachments.length;
      this.selectedAsset = {};
      this.attachmentModalVisible = false;
    },

    addPosition() {
      this.form.data.positions.push({ id: '', pivot: { auxiliary_level: '', locality: '' } }); // TODO no need to pass the name ???
    },

    removePosition(index) {
      // TODO check if asset exist on the server or no
      // TODO __.askToConfirm
      this.form.data.positions.splice(index, 1);
    },

    extractContestPositions() {
      if (this.form.data.applicant_category !== '') {
        return this.contest.positions.filter((p) => p.specialization.specialization_type_id === this.form.data.applicant_category).map((position) => ({
          id: position.id,
          title: `${position.code} - ${position.specialization.shortname} (${position.unit.abbrv})`,
        }));
      }
      return this.contest.positions.map((position) => ({
        id: position.id,
        title: `${position.code} - ${position.specialization.shortname} (${position.unit.abbrv})`,
      }));
    },

    resetPositions() {
      this.extractContestPositions();
    },

    updatePositionsAuxiliaryLevel(value) {
      this.form.data.positions.forEach((p) => {
        p.pivot.auxiliary_level = value;
      });
    },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

  form#applicationForm {
    /******************************
            Transitions
    ******************************/
    .fade-enter-active, .fade-leave-active {
      transition: opacity .8s;
    }

    .fade-enter, .fade-leave-to {
      opacity: 0
    }

  }

  /******************************
             Tables
  ******************************/
  table.asset-table {
    th {
      text-align: center;
      vertical-align: bottom;

      &:first-child {
        width: 32px !important;
      }

      &:last-child {
        width: 100px;
      }
    }

    td {
      vertical-align: middle;

      &:first-child {
        width: 32px !important;
      }

      &:last-child {
        width: 100px;
      }

      .form-group {
        margin-bottom: 0;
      }

      &.action-btn-container {
        height: 100%;
        padding: 0;
        margin: 0;
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        align-items: center; // align to cross-axis
        justify-content: space-around !important; //align to main-axis
      }
    }
  }

  table#preferredPositions {
    margin-top: 20px;

    th {
      text-align: center;
      vertical-align: bottom;

      &:first-child {
        width: 32px !important;
      }

      &:last-child {
        width: 100px;
      }
    }

    td {
      vertical-align: middle;

      &:first-child {
        width: 32px !important;
      }

      &:last-child {
        width: 100px;
      }

      .form-group {
        margin-bottom: 0;
      }
    }
  }

  /*************************
          Various
   ************************/
  pre {
    font-size: 0.8rem !important;
  }

  .help-block {
    color: #F36877 !important;
  }

</style>
