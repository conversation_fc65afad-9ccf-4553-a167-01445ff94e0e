<template>
  <Box title="XLS συγκεντρωτικών πινάκων">
    <div class="row">
      <div class="col-sm-12">
        <InfoAlert v-if="!calculation.is_latest && exportedFiles.length > 0">
          <p>
            Προβολή πινάκων με βάση τον υπολογισμό {{ calculation.description }}
          </p>
        </InfoAlert>
        <WarningAlert v-if="contest.ranked_at === null && exportedFiles.length > 0">
          Οι παρακάτω πίνακες XLS προέκυψαν με βάση τον τελευταίο υπολογισμό <strong>{{ calculation.description }}</strong>.
          Ο διαγωνισμός βρίσκεται σε στάδιο επαναβαθμολόγησης και τα αποτελέσματα <strong>ενδέχεται να αλλάξουν</strong>.
        </WarningAlert>
        <InfoAlert v-if="isCreatingExports && exportedFiles.length < 4">
          <p>
            Τα xls των συγκεντρωτικών πινάκων δημιουργούνται.
            Θα ειδοποιηθείτε για την ολοκλήρωση τους με email στην ηλεκτρονική
            διεύθυνση <strong>{{ contest.notifications_email }}</strong>.
            Παρακαλώ περιμένετε...
          </p>
          <p>
            Μέχρι στιγμης έχουν δημιουργηθεί {{ exportedFiles.length }} από τα 4 xls.
            Κάντε <strong>refresh</strong> την σελίδα για να δείτε τα νέα αρχεία.
          </p>
        </InfoAlert>
        <WarningAlert v-if="!isCreatingExports && exportedFiles.length === 0">
          Δεν έχουν δημιουργηθεί τα τελικά xls
        </WarningAlert>
        <ul class="list-unstyled excel-list">
          <li
            v-for="(exportedFile, index) in exportedFiles"
            :key="index"
          >
            <a
              class="excel-item"
              :href="exportedFile.url"
            >
              <i class="fa fa-3x fa-file-excel-o text-green" />
              <span class="excel-item__filename">{{ exportedFile.name }}</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-3 col-sm-offset-9">
        <button
          v-if="contest.ranked_at !== null && calculation.is_latest"
          class="btn btn-primary btn-outline btn-block"
          :disabled="isCreatingExports"
          @click="askCreateExports"
        >
          <i
            v-if="isCreatingExports"
            class="fa fa-spinner fa-spin"
          />
          <i
            v-else
            class="fa fa-send"
          />
          Δημιουργία xls συγκεντρωτικών πινάκων
        </button>
      </div>
    </div>
  </Box>
</template>

<script>
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';
import WarningAlert from '@/shared/components/ui/Alerts/WarningAlert.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';

export default {
  name: 'RankedPositionExports',
  components: {
    WarningAlert,
    InfoAlert,
    Box,
  },
  props: {
    contest: {
      type: Object,
      required: true,
    },
    calculation: {
      type: Object,
      required: true,
    },
    exportedFiles: {
      type: Array,
      required: true,
    },
    isCreatingExports: {
      type: Boolean,
      required: true,
    },
    hasExportedFiles: {
      type: Boolean,
      required: true,
    },
  },
  methods: {
    askCreateExports() {
      if (this.hasExportedFiles) {
        window.swal({
          type: 'warning',
          title: 'Είστε σίγουροι;',
          text: 'Θα δημιουργηθούν νέα αρχεία excel και τα ήδη υπάρχοντα θα διαγραφούν.',
          confirmButtonText: 'NAI',
          cancelButtonText: 'OXI',
          showConfirmButton: true,
          showCancelButton: true,
          closeOnConfirm: true,
        }, () => {
          this.createExports();
        });
      } else {
        this.createExports();
      }
    },
    createExports() {
      this.exportRunning = true;
      this.$http.post(`/api/contractuals/contests/${this.contest.id}/ranked-position-exports`)
        .then((res) => {
          this.$emit('exports-calculating');
          window.swal({
            type: 'success',
            title: 'Επιτυχία',
            text: res.data.message,
            showConfirmButton: true,
          });
        })
        .catch((err) => {
          this.exportRunning = false;
          window.swal({
            type: 'error',
            title: `Σφάλμα ${err.response.status}`,
            text: err.response.data.message,
            showConfirmButton: true,
          });
        });
    },
  },
};
</script>

<style scoped>
.excel-item {
  display: flex;
  width: 200px;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #333;
  padding: 10px;
}

.excel-item:hover {
  background: var(--blue-100);
  border-radius: 5px;
}

.excel-item__filename {
  font-size: 12px;
  text-align: center;
  margin-top: 5px;
}

.excel-list {
  display: grid;
  grid-template-columns: repeat(2, 300px);
}
</style>
