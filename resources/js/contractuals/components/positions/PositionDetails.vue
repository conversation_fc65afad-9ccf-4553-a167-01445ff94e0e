<template>
  <div class="position">
    <div class="position__title">
      <h4 class="text-bold">
        ΚΩΔ. {{ position.code }}
      </h4>
      <p>{{ position.specialization_name }} ({{ position.unit_abbrv }})</p>
      <p>Αριθμός θέσεων {{ position.amount }}</p>
    </div>
    <div class="position__description">
      <dl class="max-w-prose">
        <dt>Τόπος απασχόλησης</dt>
        <dd>{{ position.location }}</dd>
      </dl>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PositionDetails',
  props: {
    position: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style scoped>
.position {
  display: flex;
}

.position__title {
}

.position__description {
  padding-left: 40px;
}
</style>
