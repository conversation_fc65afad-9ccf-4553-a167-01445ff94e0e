<template>
  <Box title="Πίνακας Επιτυχόντων">
    <WarningAlert v-if="contest.ranked_at === null && calculation.is_latest">
      Ο παρακάτω πίνακας προέκυψε με βάση τον τελευταίο υπολογισμό <strong>{{ calculation.description }}</strong>.
      Ο διαγωνισμός βρίσκεται σε στάδιο επαναβαθμολόγησης και τα αποτελέσματα <strong>ενδέχεται να αλλάξουν</strong>.
    </WarningAlert>
    <InfoAlert v-if="!calculation.is_latest">
      <p>
        Προβολή πινάκων με βάση τον υπολογισμό {{ calculation.description }}
      </p>
    </InfoAlert>
    <DataViewer
      :listables="columns"
      :items="succeededRankings"
    >
      <template #default="{ tableData }">
        <p v-if="tableData.column.field === 'protocol_number'">
          <a
            :href="`/contractuals/contests/${tableData.row.contest_id}/applications/${tableData.row.application_id}`"
            class="action-button action-button-show color-info"
            v-text="tableData.row[tableData.column.field]"
          />
        </p>
        <p v-else-if="tableData.column.field === 'employable_description'"
          style="display: flex; justify-content: space-between"
        >
          <span v-if="tableData.row.employable_description.startsWith('ΘΕΣΗ')">
            <a
              :href="`/contractuals/contests/${tableData.row.contest_id}/ranked-positions/${tableData.row.employable_in}`"
              class="action-button action-button-show color-info"
              v-text="tableData.row.employable_description + (tableData.row.was_runner_up ? ' (Ε)' : '')"
            />
          </span>
          <span
            v-else
            v-text="tableData.row.employable_description + (tableData.row.was_runner_up ? ' (Ε)' : '')"
          />
          <i class="fa fa-thumbs-o-up text-primary" v-if="tableData.row.accepted===1" />
          <i class="fa fa-thumbs-o-down text-red" v-else-if="tableData.row.accepted===0" />
<!--          <span class="badge btn-primary text-sm" v-if="tableData.row.accepted===1">-->
<!--            ΑΠΕΔΕΧΘΗ-->
<!--          </span>-->
<!--          <span class="badge bg-red text-sm" v-else-if="tableData.row.accepted===0">-->
<!--            ΔΕΝ ΑΠΕΔΕΧΘΗ-->
<!--          </span>-->
        </p>
        <div
          v-else-if="tableData.column.field === 'accepted'"
          class="text-center"
        >
          <ToggleEmployedInStatus
            v-if="tableData.row.employable && isLastCalculation && contest.can.admin"
            :ranking-data="tableData.row"
            @toggle-employed-in-status="updateEmployedInStatus"
          />
        </div>
        <span
          v-else
          key="other-fields"
          v-text="tableData.row[tableData.column.field]"
        />
      </template>
    </DataViewer>
  </Box>
</template>
<script>
import ToggleEmployedInStatus from '@/contractuals/components/ToggleEmployedInStatus.vue';
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';
import WarningAlert from '@/shared/components/ui/Alerts/WarningAlert.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';
import DataViewer from '@/shared/components/ui/Data/DataViewer.vue';

export default {
  name: 'PositionSucceededRankings',
  components: {
    WarningAlert,
    InfoAlert,
    ToggleEmployedInStatus,
    Box,
    DataViewer,
  },
  props: {
    succeededRankingsData: {
      type: Array,
      required: true,
    },
    calculation: {
      type: Object,
      required: true,
    },
    contest: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      succeededRankings: this.succeededRankingsData,
      columns: [
        { label: 'Αρ.Πρωτ.', field: 'protocol_number', type: 'number' },
        { label: 'Επώνυμο', field: 'surname' },
        { label: 'Όνομα', field: 'name' },
        { label: 'Πατρώνυμο', field: 'fathername' },
        { label: 'Αρ.Ταυτ.', field: 'policeid_number' },
        { label: 'Κώλυμα 8Μ', field: 'impediment_eight_months' },
        { label: 'Επικουρία', field: 'auxiliary_level' },
        { label: 'Μονάδες', field: 'score', type: 'number' },
        { label: 'Κατάταξη', field: 'rank', type: 'number' },
        { label: 'Επιλογή', field: 'position_order', type: 'number' },
        { label: 'Προσληπτέος', field: 'employable_description', type: 'string' },
        ...this.contest.can.admin && this.calculation.is_latest ? [{ label: 'Προσελήφθη', field: 'accepted', type: 'string' }] : [],
      ],
    };
  },
  computed: {
    // FIXME
    isLastCalculation() {
      return true;
    },
  },
  methods: {
    updateEmployedInStatus(val) {
      let index = this.succeededRankings.findIndex(ranking => ranking.id === val.id);
      let updatedRanking = {
        ...this.succeededRankings[index],
        accepted: val.accepted
      };
      this.$set(this.succeededRankings, index, updatedRanking);
    }
  }
};
</script>
