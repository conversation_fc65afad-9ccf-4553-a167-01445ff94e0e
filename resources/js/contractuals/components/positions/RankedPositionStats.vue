<template>
  <div class="stats-boxes">
    <StatsBox
      text="Συνολικές θέσεις"
      :number="stats.totalPositions"
      icon="fa-users"
      color="#0c90ad"
    />
    <StatsBox
      text="Προσληπτέοι"
      :number="stats.totalEmployables"
      icon="fa-check"
      color="#07b6d5"
    />
    <StatsBox
      text="Απορριπτέοι"
      :number="stats.totalRejected"
      icon="fa-times"
      color="#f56565"
    />
    <StatsBox
      text="Αποδέχθηκαν"
      :number="stats.totalAccepted"
      icon="fa-user-plus"
      color="#48bb78"
    />
    <StatsBox
      text="Δεν αποδέχθηκαν"
      :number="stats.totalDeclined"
      icon="fa-user-times"
      color="#ecc94b"
    />
  </div>
</template>

<script>
import StatsBox from '@/shared/components/ui/Widgets/StatsBox.vue';

export default {
  name: 'RankedPositionStats',
  components: {
    StatsBox,
  },
  props: {
    stats: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style scoped>
.stats-boxes {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  margin-top: 1rem;
  margin-bottom: 3rem;
}
</style>
