<template>
  <Box
    title="Προβολή συγκεντρωτικών πινάκων"
    :is-loading="isFetchingRankedPositions"
  >
    <div class="row">
      <div class="col-sm-12">
        <WarningAlert v-if="contest.ranked_at === null && calculation.is_latest">
          Οι παρακάτω πίνακες προέκυψαν με βάση τον τελευταίο υπολογισμό <strong>{{ calculation.description }}</strong>.
          Ο διαγωνισμός βρίσκεται σε στάδιο επαναβαθμολόγησης και τα αποτελέσματα <strong>ενδέχεται να αλλάξουν</strong>.
        </WarningAlert>
        <InfoAlert v-if="!calculation.is_latest">
          <p>
            Προβολή πινάκων με βάση τον υπολογισμό {{ calculation.description }}
          </p>
        </InfoAlert>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <ResourceViewer
          v-if="! fetchRankedPositionsError"
          :paginated-data="rankedPositions"
          :filterables="filterables"
          :sortables="sortables"
          :listables="listables"
          :filter-cookie="filterCookie"
          @fetch-data="fetchRankedPositions"
        >
          <template #default="{ tableData }">
            <p v-if="tableData.column.field === 'has_locality'">
              <span v-if="tableData.row.has_locality">
                <i
                  class="fa fa-check"
                  aria-hidden="true"
                /> Ναι</span>
            </p>
            <p
              v-else-if="tableData.column.field === 'actions'"
              class="action-buttons"
            >
              <a
                :href="`/contractuals/contests/${contest.id}/ranked-positions/${tableData.row.id}?calculation=${calculation.id}`"
                class="action-button"
                data-toggle="tooltip"
                title="Προβολή πινάκων"
              >
                <i class="fa fa-2x fa-table" />
              </a>
            </p>
            <p v-else>
              {{ tableData.row[tableData.column.field] }}
            </p>
          </template>
        </ResourceViewer>
        <DangerAlert
          v-else
          :message="fetchRankedPositionsError"
        />
      </div>
    </div>
  </Box>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';
import WarningAlert from '@/shared/components/ui/Alerts/WarningAlert.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';
import ResourceViewer from '@/shared/components/ui/Data/ResourceViewer.vue';

export default {
  name: 'RankedPositions',
  components: {
    WarningAlert,
    InfoAlert,
    DangerAlert,
    ResourceViewer,
    Box,
  },
  props: {
    contest: {
      type: Object,
      required: true,
    },
    calculation: {
      type: Object,
      required: true,
    },
    filters: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      listables: [
        { label: 'Κωδ.', field: 'code' },
        { label: 'Ειδικότητα.', field: 'specialization_name' },
        { label: 'Περιοχή', field: 'location' },
        { label: 'Υπηρεσία.', field: 'unit_abbrv' },
        { label: 'Εντοπιότητα', field: 'has_locality' },
        { label: 'Αρ. θέσεων', field: 'amount' },
        { label: 'Προσληπτέοι', field: 'employable_count' },
        { label: 'Απεδέχθησαν', field: 'accepted_count' },
        { label: 'Δεν απεδέχθησαν', field: 'declined_count' },
        { label: 'Ενέργειες', field: 'actions', type: 'number' },
      ],
      filterables: [
        { label: 'Κωδ.', field: 'code', type: 'string' },
        {
          label: 'Ειδικότητα', field: 'specialization_id', type: 'select', selectOptions: this.filters.specializations,
        },
        { label: 'Περιοχή', field: 'location', type: 'string' },
        {
          label: 'Υπηρεσία', field: 'unit_id', type: 'select', selectOptions: this.filters.units,
        },
      ],
      sortables: [
        { label: 'Κωδ.', field: 'code' },
        { label: 'Ειδικότητα', field: 'specialization_id' },
        { label: 'Περιοχή', field: 'location' },
        { label: 'Υπηρεσία', field: 'unit_id' },
      ],
      filterCookie: 'contractuals_ranked_positions_filters',
      rankedPositions: { data: [], meta: {}, links: {} },
      fetchRankedPositionsError: '',
      isFetchingRankedPositions: false,
    };
  },
  created() {
    this.fetchRankedPositions(this.$cookies.get(this.filterCookie));
  },
  methods: {
    fetchRankedPositions(filters) {
      this.isFetchingRankedPositions = true;

      this.$http.get(`/api/contractuals/contests/${this.contest.id}/ranked-positions`, { params: filters })
        .then((res) => {
          this.rankedPositions = res.data.data;
        })
        .catch((err) => {
          this.fetchRankedPositionsError = err.response.data.message;
        })
        .finally(() => {
          this.isFetchingRankedPositions = false;
        });
    },
  },
};
</script>
