<template>
  <Box title="Προβολή θέσεων">
    <div class="row">
      <div class="col-sm-12">
        <DataViewer
          :items="positions"
          :listables="columns"
        >
          <template #default="{ tableData }">
            <p v-if="tableData.column.field === 'has_locality'">
              <span v-if="tableData.row.has_locality">
                <i
                  class="fa fa-check"
                  aria-hidden="true"
                /> Ναι</span>
            </p>
            <p
              v-else-if="tableData.column.field === 'actions'"
              class="action-buttons"
            >
              <a
                :href="`positions/${tableData.row.id}`"
                class="action-button"
              >
                <i class="fa fa-2x fa-info-circle" />
              </a>
              <a
                :href="`positions/${tableData.row.id}/edit`"
                class="action-button"
              >
                <i class="fa fa-2x fa-edit" />
              </a>
            </p>
            <p v-else>
              {{ tableData.row[tableData.column.field] }}
            </p>
          </template>
        </DataViewer>
      </div>
    </div>
  </Box>
</template>

<script>
import Box from '@/shared/components/ui/Boxes/Box.vue';
import DataViewer from '@/shared/components/ui/Data/DataViewer.vue';

export default {
  name: 'Positions',
  components: {
    DataViewer,
    Box,
  },
  props: {
    positions: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      columns: [
        { label: 'Κωδ.', field: 'code' },
        { label: 'Ειδικότητα.', field: 'specialization_name' },
        { label: 'Αρ. θέσεων.', field: 'amount' },
        { label: 'Περιοχή', field: 'location' },
        { label: 'Υπηρεσία.', field: 'unit_abbrv' },
        { label: 'Εντοπιότητα', field: 'has_locality' },
        { label: 'Ενέργειες', field: 'actions', type: 'number' },
      ],
    };
  },
};
</script>

<style scoped>

</style>
