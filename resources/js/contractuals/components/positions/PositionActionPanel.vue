<template>
  <Box title="Στοιχεία διαγωνισμού">
    <div class="row">
      <div class="col-sm-10">
        <ContestDetails :contest="contest" />
      </div>
      <div class="col-sm-2">
<!--        <a-->
<!--          :href="`/contractuals/contests/${contest.id}/positions/create`"-->
<!--          class="btn btn-control btn-block"-->
<!--        >-->
<!--          <i class="fa fa-plus" />-->
<!--          Προσθήκη Θέσης-->
<!--        </a>-->
        <a
          :href="`/contractuals/contests/${contest.id}/import-positions`"
          class="btn btn-control btn-block"
        >
          <i class="fa fa-upload" />
          Προσθήκη θέσεων
        </a>
      </div>
    </div>
  </Box>
</template>
<script>
import ContestDetails from '@/contractuals/components/contest/ContestDetails.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';

export default {
  name: 'PositionActionPanel',
  components: {
    Box,
    ContestDetails,
  },
  props: {
    contest: {
      type: Object,
      required: true,
    },
  },
};
</script>
