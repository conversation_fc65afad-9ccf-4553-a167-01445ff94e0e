<template>
  <Box title="Στοιχεία διαγωνισμού">
    <div class="row">
      <div class="col-sm-10">
        <ContestDetails :contest="contest" />
      </div>
      <div class="col-sm-2">
        <ChangeCalculationButton
          :contest-id="contest.id"
          :calculation-id="calculation.id"
        />
        <a
          v-if="calculation.is_latest && contest.can.admin && contest.ranked_at !== null"
          :href="`/contractuals/contest/${contest.id}/calculation/${calculation.id}/employable-diff`"
          class="btn btn-control btn-block"
          style="margin-bottom: 5px;"
        >
          <i class="fa fa-exchange" />
          Παρακολούθηση μεταβολών
        </a>
        <a
          v-if="calculation.is_latest && contest.can.admin && contest.ranked_at !== null"
          :href="`/contractuals/contest/${contest.id}/calculation/${calculation.id}/vacant-positions`"
          class="btn btn-control btn-block"
          style="margin-bottom: 5px;"
        >
          <i class="fa fa-list-alt" />
          Παρακολούθηση κενών θέσεων
        </a>
      </div>
    </div>
  </Box>
</template>
<script>
import ChangeCalculationButton from '@/contractuals/components/calculation/ChangeCalculationButton.vue';
import ContestDetails from '@/contractuals/components/contest/ContestDetails.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';

export default {
  name: 'RankedPositionActionPanel',
  components: {
    ChangeCalculationButton,
    Box,
    ContestDetails,
  },
  props: {
    contest: {
      type: Object,
      required: true,
    },
    calculation: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      exportRunning: this.isCreatingExports,
    };
  },
};
</script>
