<template>
  <div>
    <button
      class="btn btn-block btn-primary"
      @click="openModal = true"
    >
      <i class="fa fa-gavel" /> Οριστική βαθμολόγηση
    </button>
    <RateApplicationModal
      :open="openModal"
      :application="application"
      :form="form"
      @success="onSuccess"
      @cancel="closeModal"
    >
      Θέλετε να προχωρήσετε στην οριστική βαθμολόγηση της αίτησης;
    </RateApplicationModal>
  </div>
</template>

<script>

import RateApplicationModal from '@/contractuals/components/application/RateApplicationModal.vue';

export default {
  name: 'RateApplicationButton',
  components: {
    RateApplicationModal,
  },
  props: {
    application: {
      type: Object,
      required: true,
    },
    form: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      openModal: false,
    };
  },
  methods: {
    onSuccess() {
      this.closeModal();
      window.location.replace(`/contractuals/contests/${this.application.contest_id}/applications/${this.application.id}`);
    },
    closeModal() {
      this.openModal = false;
    },
  },
};
</script>
