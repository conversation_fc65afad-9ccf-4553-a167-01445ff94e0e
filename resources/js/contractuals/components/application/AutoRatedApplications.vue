<template>
  <ResourceViewer
    :paginated-data="autoRatedApplications"
    :filterables="filterables"
    :sortables="sortables"
    :listables="listables"
    filter-cookie="contractuals_auto_rated_applications_filters"
    @fetch-data="fetchApplications"
  >
    <template #default="{ tableData }">
      <p
        v-if="tableData.column.field === 'actions'"
        class="action-buttons"
      >
        <a
          :href="`/contractuals/contests/${contest.id}/applications/${tableData.row.id}`"
          class="action-button"
        >
          <i class="fa fa-2x fa-info-circle" />
        </a>
      </p>
      <p v-else>
        {{ tableData.row[tableData.column.field] }}
      </p>
    </template>
  </ResourceViewer>
</template>

<script>
import ResourceViewer from '@/shared/components/ui/Data/ResourceViewer.vue';

export default {
  name: 'AutoRatedApplications',
  components: {
    ResourceViewer,
  },
  props: {
    contest: {
      type: Object,
      required: true,
    },
    autoRatedApplications: {
      type: Object,
      required: true,
    },
    applicantCategoryOptions: {
      type: Array,
      default() {
        return [
          { id: 2, name: 'ΤΕ' },
          { id: 3, name: 'ΔΕ' },
          { id: 4, name: 'ΥΕ' },
        ];
      },
    },
    unitOptions: {
      type: Array,
      required: true,
    },
    auxiliaryLevelOptions: {
      type: Array,
      default() {
        return [
          { id: 0, name: 'Κύρια' },
          { id: 1, name: 'Α' },
          { id: 2, name: 'Β' }
        ]
      }
    },
    impedimentEightMonthsOptions: {
      type: Array,
      default() {
        return [
          { id: 0, name: 'OXI' },
          { id: 1, name: 'NAI' }
        ]
      }
    },
  },
  data() {
    return {
      listables: [
        { label: 'Επίθετο', field: 'surname' },
        { label: 'Όνομα', field: 'name' },
        { label: 'Βαθμίδα', field: 'applicant_category' },
        { label: 'Αρ. Πρωτ.', field: 'protocol_number' },
        { label: 'Υποβολή', field: 'submitted_at' },
        { label: 'Έλεγχος από', field: 'validator' },
        { label: 'Μονάδες', field: 'score', type: 'number' },
        { label: 'Ενέργειες', field: 'actions', type: 'number' },
      ],
      filterables: [
        { label: 'Επίθετο', field: 'surname', type: 'string' },
        { label: 'Όνομα', field: 'name', type: 'string' },
        { label: 'Αρ.Πρωτ.', field: 'protocol_number', type: 'string' },
        {
          label: 'Βαθμίδα', field: 'applicant_category', type: 'select', selectOptions: this.applicantCategoryOptions,
        },
        {
          label: 'Βαθμός επικουρίας', field: 'auxiliary_level', type: 'select', selectOptions: this.auxiliaryLevelOptions,
        },
        {
          label: 'Κώλυμα οκταμήνου', field: 'impediment_eight_months', type: 'select', selectOptions: this.impedimentEightMonthsOptions,
        },
        {
          label: 'Υπηρεσία ελέγχου', field: 'validating_unit_id', type: 'select', selectOptions: this.unitOptions,
        },
      ],
      sortables: [
        { label: 'Επίθετο', field: 'surname' },
        { label: 'Βαθμίδα', field: 'applicant_category' },
        { label: 'Υπηρεσία ελέγχου', field: 'validating_unit_id' },
      ],
    };
  },
  methods: {
    fetchApplications(filters) {
      this.$emit('fetch-applications', filters);
    },
  },
};
</script>
