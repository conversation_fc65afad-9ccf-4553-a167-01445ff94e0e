<template>
  <ResourceViewer
    :paginated-data="ratedApplications"
    :filterables="filterables"
    :sortables="sortables"
    :listables="listables"
    filter-cookie="contractuals_evaluated_applications_filters"
    @fetch-data="fetchApplications"
  >
    <template #default="{ tableData }">
      <p v-if="tableData.column.field === 'score'">
        {{ printApplicationScore(tableData.row) }}
      </p>
      <p v-else-if="tableData.column.field === 'rater_details'">
        {{ tableData.row['rater_unit'] }} ({{ tableData.row['rater'] }})
        <br>
        <small>
          <i
            class="fa fa-calendar"
            aria-hidden="true"
          />
          {{ tableData.row['rated_at'] }}
        </small>
      </p>
      <p
        v-else-if="tableData.column.field === 'actions'"
        class="action-buttons"
      >
        <a
          :href="`${contest.id}/applications/${tableData.row.id}`"
          class="action-button"
        >
          <i class="fa fa-2x fa-info-circle" />
        </a>
      </p>
      <p v-else>
        {{ tableData.row[tableData.column.field] }}
      </p>
    </template>
  </ResourceViewer>
</template>

<script>
import ResourceViewer from '@/shared/components/ui/Data/ResourceViewer.vue';
import Gate from '@/shared/mixins/Gate';

export default {
  name: 'RatedApplications',
  components: {
    ResourceViewer,
  },
  mixins: [
    Gate, // FIXME use a store to share user permissions
  ],
  props: {
    contest: {
      type: Object,
      required: true,
    },
    ratedApplications: {
      type: Object,
      required: true,
    },
    applicantCategoryOptions: {
      type: Array,
      default() {
        return [
          { id: 2, name: 'ΤΕ' },
          { id: 3, name: 'ΔΕ' },
          { id: 4, name: 'ΥΕ' },
        ];
      },
    },
    unitOptions: {
      type: Array,
      required: true,
    },
    auxiliaryLevelOptions: {
      type: Array,
      default() {
        return [
          { id: 0, name: 'Κύρια' },
          { id: 1, name: 'Α' },
          { id: 2, name: 'Β' }
        ]
      }
    },
    impedimentEightMonthsOptions: {
      type: Array,
      default() {
        return [
          { id: 0, name: 'OXI' },
          { id: 1, name: 'NAI' }
        ]
      }
    },
  },
  data() {
    return {
      listables: [
        { label: 'Επίθετο', field: 'surname' },
        { label: 'Όνομα', field: 'name' },
        { label: 'Βαθμίδα', field: 'applicant_category' },
        { label: 'Αρ.Πρωτ.', field: 'protocol_number' },
        { label: 'Υποβολή', field: 'submitted_at' },
        { label: 'Ελέγχθηκαν από', field: 'rater_details' },
        { label: 'Μονάδες', field: 'score', type: 'number' },
        { label: 'Ενέργειες', field: 'actions', type: 'number' },
      ],
      filterables: [
        { label: 'Επίθετο', field: 'surname', type: 'string' },
        { label: 'Όνομα', field: 'name', type: 'string' },
        { label: 'Αρ.Πρωτ.', field: 'protocol_number', type: 'string' },
        {
          label: 'Βαθμίδα', field: 'applicant_category', type: 'select', selectOptions: this.applicantCategoryOptions,
        },
        {
          label: 'Βαθμός επικουρίας', field: 'auxiliary_level', type: 'select', selectOptions: this.auxiliaryLevelOptions,
        },
        {
          label: 'Κώλυμα οκταμήνου', field: 'impediment_eight_months', type: 'select', selectOptions: this.impedimentEightMonthsOptions,
        },
        {
          label: 'Υπηρεσία ελέγχου.', field: 'rated_by_unit_id', type: 'select', selectOptions: this.unitOptions,
        },
        {
          label: 'Απορριπτέοι',
          field: 'rejected',
          type: 'select',
          selectOptions: [
            { id: 1, name: 'Ναι' },
            { id: 0, name: 'Όχι' },
          ],
        },
      ],
      sortables: [
        { label: 'Επίθετο', field: 'surname' },
        { label: 'Βαθμίδα', field: 'applicant_category' },
        { label: 'Υπηρεσία βαθμ.', field: 'rated_by_unit_id' },
        { label: 'Μονάδες', field: 'score' },
      ],
      isLoading: false,
    };
  },
  computed: {
    contestIsRated() {
      return this.contest.rated_at !== null;
    },
  },
  methods: {
    printApplicationScore(application) {
      return application.rejected === true ? `❌ ${application.rejection_description}` : application.score;
    },
    unrateApplication(application) {
      this.$emit('application-unrated', application);
    },
    fetchApplications(filters) {
      this.$emit('fetch-applications', filters);
    },
  },
};
</script>
