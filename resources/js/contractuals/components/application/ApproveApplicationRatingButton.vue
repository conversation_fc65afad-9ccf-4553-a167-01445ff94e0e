<template>
  <div>
    <button
      class="btn btn-primary btn-block"
      @click="openModal = true"
    >
      <i class="fa fa-thumbs-up" /> Έγκριση αυτόματης βαθμολόγησης
    </button>
    <ApproveApplicationRatingModal
      :application="application"
      :open="openModal"
      @success="onSuccess"
      @cancel="openModal = false"
    />
  </div>
</template>

<script>
import ApproveApplicationRatingModal from '@/contractuals/components/application/ApproveApplicationRatingModal.vue';

export default {
  name: 'ApproveApplicationRatingButton',
  components: { ApproveApplicationRatingModal },
  props: {
    application: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      openModal: false,
    };
  },
  methods: {
    onSuccess(data) {
      this.$emit('approve-auto-rating', data);
      this.closeModal();
      // window.swal({
      //   type: 'success',
      //   title: 'Eπιτυχία!',
      //   text: 'H αίτηση έχει βαθμολογηθεί οριστικά!',
      //   showConfirmButton: true,
      // });
    },
    closeModal() {
      this.openModal = false;
    },
  },
};
</script>
