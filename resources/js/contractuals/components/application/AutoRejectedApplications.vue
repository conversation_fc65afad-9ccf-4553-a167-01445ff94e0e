<template>
  <Box title="Αιτήσεις με αυτόματη απόρριψη">
    <DataViewer
      :listables="autoRejectedApplicationsColumns"
      :items="autoRejectedApplications"
      local-storage-uuid="auto-rejected"
    >
      <template #default="{ tableData }">
        <p
          v-if="tableData.column.field === 'actions'"
          class="action-buttons"
        >
          <a
            :href="`${contest.id}/applications/${tableData.row.id}`"
            class="action-button"
          >
            <i class="fa fa-2x fa-info-circle" />
          </a>
        </p>
        <p v-else>
          {{ tableData.row[tableData.column.field] }}
        </p>
      </template>
    </DataViewer>
  </Box>
</template>

<script>
import Box from '@/shared/components/ui/Boxes/Box.vue';
import DataViewer from '@/shared/components/ui/Data/DataViewer.vue';

export default {
  name: 'AutoRejectedApplications',
  components: {
    Box,
    DataViewer,
  },
  props: {
    autoRejectedApplications: {
      type: Array,
      required: true,
    },
    contest: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      autoRejectedApplicationsColumns: [
        { label: 'Επίθετο', field: 'surname' },
        { label: 'Όνομα', field: 'name' },
        { label: 'Βαθμίδα', field: 'applicant_category' },
        { label: 'Αρ. Πρωτ.', field: 'protocol_number' },
        { label: 'Υποβολή', field: 'submitted_at' },
        { label: 'Λόγος Απόρριψης', field: 'rejection_description' },
        { label: 'Ενέργειες', field: 'actions', type: 'number' },
      ],
    };
  },
};
</script>
