<template>
  <ConfirmationModal
    :open="open"
    :is-loading="isLoading"
    @confirm="saveAndRate"
    @cancel="cancel"
  >
    Αποδέχεστε την αυτόματη βαθμολόγηση της αίτησης, όπως αυτή προκύπτει από την εφαρμογή;
  </ConfirmationModal>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';

export default {
  name: 'RateApplicationModal',
  components: {
    ConfirmationModal,
    DangerAlert,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
    application: {
      type: Object,
      required: true,
    },
    form: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
    };
  },
  methods: {
    saveAndRate() {
      this.clearErrorMessage();
      this.isLoading = true;
      this.form.put(`/api/contractuals/contests/${this.application.contest_id}/applications/${this.application.id}`)
        .then(() => this.$http.post(`/api/contractuals/contests/${this.application.contest_id}/rated-applications`, {
          application_id: this.application.id,
        }))
        .then((res) => {
          window.swal({
            type: 'success',
            title: 'Επιτυχία!',
            text: res.data.message,
            showConfirmButton: true,
          }, () => {
            this.$emit('success');
          });
        })
        .catch((err) => {
          this.$swal.error(err);
          this.cancel();
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    cancel() {
      this.clearErrorMessage();
      this.$emit('cancel');
    },
    clearErrorMessage() {
      this.errorMessage = '';
    },
  },
};
</script>
