<template>
  <Box title="Στοιχεία Πρόσληψης">
    <div class="row">
      <div
        v-if="positionData"
        class="col-sm-12"
      >
        <h4 class="text-success">
          Προσληπτέος
        </h4>
        <table class="table">
          <tbody>
            <tr>
              <th>Υπηρεσία</th>
              <td>{{ positionData.unit.name }}</td>
            </tr>
            <tr>
              <th>Κωδικός θέσης</th>
              <td>
                {{ positionData.code }}
                ({{ positionData.location }})
              </td>
            </tr>
            <tr>
              <th>Ειδικότητα</th>
              <td>{{ positionData.specialization.name }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        v-else
        class="col-sm-12"
      >
        <h4 class="text-danger">
          Δεν προσλαμβάνεται
        </h4>
      </div>
    </div>
  </Box>
</template>

<script>
import Box from '@/shared/components/ui/Boxes/Box.vue';

export default {
  name: 'EmployablePosition',
  components: { Box },
  props: {
    positionData: {
      type: Object,
      default: null,
    },
  },
};
</script>
