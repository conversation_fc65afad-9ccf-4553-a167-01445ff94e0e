<template>
  <div>
    <a
      href="#"
      v-bind="$attrs"
      @click.prevent="openModal"
    >
      <i class="fa fa-pencil-square-o fa-2x" />
    </a>
    <BasicModal
      :open="isOpen"
      @close="closeModal"
    >
      <template #header>
        <h4>Επεξεργασία θέσης κωδ. {{ position.code }}</h4>
      </template>
      <PositionForm
        :position-data="position"
        :application-id="applicationId"
        :contest-id="contestId"
        @update:position="onSuccess"
        @cancel="closeModal"
      />
    </BasicModal>
  </div>
</template>

<script>
import PositionForm from '@/contractuals/components/application/positions/PositionForm.vue';
import BasicModal from '@/shared/components/ui/Modal/BasicModal.vue';

export default {
  name: 'PositionEditButton',
  components: {
    PositionForm,
    BasicModal,
  },
  props: {
    position: {
      type: Object,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      isOpen: false,
    };
  },
  methods: {
    onSuccess(updatedQualifiable) {
      this.$emit('update', updatedQualifiable);
      this.closeModal();
    },
    openModal() {
      this.isOpen = true;
    },
    closeModal() {
      this.isOpen = false;
    },
  },
};
</script>

<style scoped>
a {
  color: var(--gray-600);
  text-decoration: none;
}

a:hover {
  color: var(--yellow-600);
}
</style>
