<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <PositionList
          :positions="positions"
          :contest-id="contestId"
          :application-id="applicationId"
          @position:update="updatePosition"
        />
      </div>
    </div>
  </div>
</template>

<script>
import PositionChangeAuxiliaryLevelsButton
  from '@/contractuals/components/application/positions/PositionChangeAuxiliaryLevelsButton.vue';
import PositionList from '@/contractuals/components/application/positions/PositionList.vue';

export default {
  name: 'Positions',
  components: {
    PositionList,
    PositionChangeAuxiliaryLevelsButton,
  },
  props: {
    positionsData: {
      type: Array,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      positions: this.positionsData,
    };
  },
  methods: {
    updatePosition(position) {
      const positionIndex = this.positions.findIndex((_position) => _position.id === position.id);
      if (positionIndex !== -1) {
        this.positions.splice(positionIndex, 1, position);
      }
    },
  },
};
</script>
