<template>
  <div>
    <a
      href="#"
      v-bind="$attrs"
      @click.prevent="openModal"
    >
      <i class="fa fa-pencil-square-o fa-2x" /> Αλλαγή επικουρίας
    </a>
    <BasicModal
      :open="isOpen"
      @close="closeModal"
    >
      <form @submit.prevent="update">
        <div class="row">
          <div class="col-sm-12">
            <SelectField
              v-model="applicationAuxiliaryLevel"
              title="Αλλαγή επικουρίας σε όλες τις θέσεις"
              :options="[]"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-sm-6">
            <button
              type="button"
              class="btn btn-default btn-block"
              @click="closeModal"
            >
              Cancel
            </button>
          </div>
          <div class="col-sm-6">
            <button
              type="submit"
              class="btn btn-primary btn-block"
            >
              Submit
            </button>
          </div>
        </div>
      </form>
    </BasicModal>
  </div>
</template>

<script>
import SelectField from '@/shared/components/ui/FormFields/SelectField.vue';
import BasicModal from '@/shared/components/ui/Modal/BasicModal.vue';

export default {
  name: 'PositionChangeAuxiliaryLevelsButton',
  components: {
    SelectField,
    BasicModal,
  },
  data() {
    return {
      isOpen: false,
      applicationAuxiliaryLevel: '',
    };
  },
  methods: {
    update() {
      // TODO async
      this.$emit('positions:update', this.applicationAuxiliaryLevel); // TODO emit res.data
      this.closeModal();
    },
    openModal() {
      this.isOpen = true;
    },
    closeModal() {
      this.isOpen = false;
    },
  },
};
</script>

<style scoped>
a {
  color: var(--gray-600);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  margin-bottom: 1rem;
}

a i {
  margin-right: 0.5rem;
}

a:hover {
  color: var(--yellow-600);
}
</style>
