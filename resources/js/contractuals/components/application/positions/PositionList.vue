<template>
  <table class="table assets-table">
    <thead>
      <tr>
        <th>Σειρά</th>
        <th>Κωδικός</th>
        <th>Υπηρεσία</th>
        <th>Χώρος</th>
        <th>Ειδικότητα</th>
        <th>Εντοπιότητα</th>
        <th><i class="fa fa-cog" /></th>
      </tr>
    </thead>
    <tbody>
      <tr
        v-for="(position, index) in positions"
        :key="index"
      >
        <td>{{ position.order }}</td>
        <td>{{ position.code }}</td>
        <td>{{ position.unit_abbrv }}</td>
        <td>{{ position.location }}</td>
        <td>{{ position.specialization_abbrv }}</td>
        <td>{{ position.locality ? '✓ Ναι' : '' }}</td>
        <td>
          <PositionEditButton
            v-if="position.has_locality"
            :position="position"
            :application-id="applicationId"
            :contest-id="contestId"
            @update="$emit('position:update', $event)"
          />
          <p v-else>
            --
          </p>
        </td>
      </tr>
    </tbody>
  </table>
</template>

<script>
import PositionEditButton from '@/contractuals/components/application/positions/PositionEditButton.vue';

export default {
  name: 'PositionList',
  components: {
    PositionEditButton,
  },
  props: {
    positions: {
      type: Array,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
  },
};
</script>

<style scoped>
table.assets-table thead,
table.positions-table thead {
  background-color: var(--gray-400)
}

table.assets-table tbody tr:hover {
  background-color: var(--gray-200)
}

table.assets-table tbody tr td {
  border: unset;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  vertical-align: middle;
}

table.assets-table tbody tr td .form-group {
  margin: 0;
}

table.assets-table thead tr th:first-child,
table.assets-table tbody tr td:first-child {
  width: 32px;
  text-align: center;
}
table.assets-table thead tr th:last-child,
table.assets-table tbody tr td:last-child {
  width: 100px;
  text-align: center;
}
table.assets-table tbody tr td:last-child div {
  display: flex;
  justify-content: space-around;
}
</style>
