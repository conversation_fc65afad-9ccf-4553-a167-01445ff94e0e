<template>
  <div>
    <div class="row">
      <div class="col-sm-12 text-right">
        <AddQualifiableButton
          qualifiable-type="Degree"
          :application-id="applicationId"
          :contest-id="contestId"
          @create:qualifiable="addDegree"
        />
      </div>
      <div class="col-sm-12">
        <DegreeList
          :degrees="degrees"
          :contest-id="contestId"
          :application-id="applicationId"
          @update:degree="updateDegree"
          @remove:degree="removeDegree"
        />
      </div>
    </div>
  </div>
</template>

<script>

import AddQualifiableButton from '@/contractuals/components/application/qualifiables/AddQualifiableButton.vue';
import DegreeList from '@/contractuals/components/application/qualifiables/degree/DegreeList.vue';
import DegreeForm from '@/contractuals/components/application/qualifiables/degree/DegreeForm.vue';

export default {
  name: 'Degrees',
  components: {
    DegreeForm,
    AddQualifiableButton,
    DegreeList,
  },
  props: {
    degreesData: {
      type: Array,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      degrees: this.degreesData,
    };
  },
  methods: {
    addDegree(degree) {
      this.degrees.push(degree);
    },
    updateDegree(degree) {
      const index = this.degrees.findIndex((d) => d.id === degree.id);
      this.degrees.splice(index, 1, degree);
    },
    removeDegree(degree) {
      this.degrees = this.degrees.filter((d) => d.id !== degree.id);
    },
  },
};
</script>
