<template>
  <table
    v-if="degrees.length > 0"
    class="table assets-table"
  >
    <thead>
      <tr>
        <th>A/A</th>
        <th>Τίτλος *</th>
        <th style="width: 10%;">
          Βασικός
        </th>
        <th style="width: 10%;">
          <PERSON><PERSON><PERSON><PERSON><PERSON>ς *
        </th>
        <th style="width: 10%;">
          Έτος
        </th>
        <th><i class="fa fa-cog" /></th>
      </tr>
    </thead>
    <tbody>
      <tr
        v-for="(degree, index) in degrees"
        :key="index"
      >
        <td>
          {{ index + 1 }}
        </td>
        <td>{{ degree.name }}</td>
        <td>{{ degree.is_primary ? 'ΝΑΙ' : '' }}</td>
        <td>{{ degree.mark }}</td>
        <td>{{ degree.year }}</td>
        <td>
          <div>
            <EditQualifiableButton
              qualifiable-type="Degree"
              :qualifiable-data="degree"
              :application-id="applicationId"
              :contest-id="contestId"
              @update:qualifiable="$emit('update:degree', $event)"
            />
            <RemoveQualifiableButton
              qualifiable-type="Degree"
              :qualifiable-data="degree"
              :application-id="applicationId"
              :contest-id="contestId"
              @remove:qualifiable="$emit('remove:degree', $event)"
            />
          </div>
        </td>
      </tr>
    </tbody>
  </table>
  <InfoAlert v-else>
    <i class="fa fa-exclamation-circle" /> Δεν έχουν καταχωρισθεί τίτλοι σπουδών
  </InfoAlert>
</template>

<script>
import EditQualifiableButton from '@/contractuals/components/application/qualifiables/EditQualifiableButton.vue';
import RemoveQualifiableButton from '@/contractuals/components/application/qualifiables/RemoveQualifiableButton.vue';
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';

export default {
  name: 'DegreeList',
  components: {
    RemoveQualifiableButton,
    EditQualifiableButton,
    InfoAlert,
  },
  props: {
    degrees: {
      type: Array,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
};
</script>

<style scoped>
table.assets-table thead,
table.positions-table thead {
  background-color: var(--gray-400)
}

table.assets-table tbody tr td {
  border: unset;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  vertical-align: middle;
}

table.assets-table thead tr th:first-child,
table.assets-table tbody tr td:first-child {
  width: 32px;
  text-align: center;
}

table.assets-table thead tr th:last-child,
table.assets-table tbody tr td:last-child {
  width: 100px;
  text-align: center;
}

table.assets-table tbody tr td:last-child div {
  display: flex;
  justify-content: space-around;
}
</style>
