<template>
  <form @submit.prevent="upsert">
    <div class="row">
      <div class="col-sm-12">
        <DangerAlert v-if="errorMessage">
          <strong>Σφάλμα!</strong> {{ errorMessage }}
        </DangerAlert>
        <TextField
          v-model="form.data.name"
          title="Όνομα"
          name="name"
          :error="form.errors.get('name')"
        />
      </div>
      <div class="col-sm-12">
        <CheckField
          v-model="form.data.is_integrated"
          title="Ενιαίο"
          name="is_integrated"
          :error="form.errors.get('is_integrated')"
          @input="form.errors.clear('is_integrated')"
        />
      </div>
    </div>
    <hr>
    <div class="row">
      <div class="col-sm-6">
        <CancelButton @cancel="$emit('cancel')">
          <i class="fa fa-arrow-left" /> Επιστροφή
        </CancelButton>
      </div>
      <div class="col-sm-6">
        <SubmitButton :busy="form.busy">
          <i class="fa fa-floppy-o" /> Αποθήκευση
        </SubmitButton>
      </div>
    </div>
  </form>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import CancelButton from '@/shared/components/ui/Buttons/CancelButton.vue';
import SubmitButton from '@/shared/components/ui/Buttons/SubmitButton.vue';
import CheckField from '@/shared/components/ui/FormFields/CheckField.vue';
import TextField from '@/shared/components/ui/FormFields/TextField.vue';
import Form from '@/shared/Form';

export default {
  name: 'PostgraduateForm',
  components: {
    CheckField,
    TextField,
    SubmitButton,
    CancelButton,
    DangerAlert,
  },
  props: {
    postgraduate: {
      type: Object,
      default: () => null,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      form: new Form({
        name: this.postgraduate ? this.postgraduate.name : '',
        is_integrated: this.postgraduate ? this.postgraduate.is_integrated : false,
      }),
      errorMessage: null,
    };
  },
  computed: {
    isEditing() {
      return this.postgraduate !== null;
    },
  },
  methods: {
    async upsert() {
      const url = this.isEditing
        ? `/api/contractuals/contests/${this.contestId}/applications/${this.applicationId}/postgraduates/${this.postgraduate.id}`
        : `/api/contractuals/contests/${this.contestId}/applications/${this.applicationId}/postgraduates`;

      const method = this.isEditing ? 'put' : 'post';

      const event = this.isEditing ? 'update' : 'create';

      this.errorMessage = null;

      try {
        const res = await this.form[method](url);
        this.$emit(event, res.data.data);
      } catch (e) {
        this.errorMessage = e.response.data.message;
      }
    },
  },
};
</script>
