<template>
  <div>
    <div class="row">
      <div class="col-sm-12 text-right">
        <AddQualifiableButton
          qualifiable-type="Postgraduate"
          :application-id="applicationId"
          :contest-id="contestId"
          @create:qualifiable="addPostgraduate"
        />
      </div>
      <div class="col-sm-12">
        <PostgraduateList
          :postgraduates="postgraduates"
          :contest-id="contestId"
          :application-id="applicationId"
          @update:postgraduate="updatePostgraduate"
          @remove:postgraduate="removePostgraduate"
        />
      </div>
    </div>
  </div>
</template>

<script>

import AddQualifiableButton from '@/contractuals/components/application/qualifiables/AddQualifiableButton.vue';
import PostgraduateList from '@/contractuals/components/application/qualifiables/postgraduate/PostgraduateList.vue';
import PostgraduateForm from "@/contractuals/components/application/qualifiables/postgraduate/PostgraduateForm";

export default {
  name: 'Postgraduates',
  components: {
    PostgraduateForm,
    PostgraduateList,
    AddQualifiableButton,
  },
  props: {
    postgraduatesData: {
      type: Array,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      postgraduates: this.postgraduatesData,
    };
  },
  methods: {
    addPostgraduate(postgraduate) {
      this.postgraduates.push(postgraduate);
    },
    updatePostgraduate(postgraduate) {
      const index = this.postgraduates.findIndex((d) => d.id === postgraduate.id);
      this.postgraduates.splice(index, 1, postgraduate);
    },
    removePostgraduate(postgraduate) {
      this.postgraduates = this.postgraduates.filter((d) => d.id !== postgraduate.id);
    },
  },
};
</script>
