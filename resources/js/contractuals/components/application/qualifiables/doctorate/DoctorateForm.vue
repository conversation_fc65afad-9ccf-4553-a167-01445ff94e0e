<template>
  <form @submit.prevent="upsert">
    <div class="row">
      <div class="col-sm-12">
        <TextField
          v-model="form.data.name"
          title="Όνομα"
          name="name"
          :error="form.errors.get('name')"
        />
      </div>
    </div>
    <hr>
    <div class="row">
      <div class="col-sm-6">
        <CancelButton @cancel="$emit('cancel')">
          <i class="fa fa-arrow-left" /> Επιστροφή
        </CancelButton>
      </div>
      <div class="col-sm-6">
        <SubmitButton :busy="form.busy">
          <i class="fa fa-floppy-o" /> Αποθήκευση
        </SubmitButton>
      </div>
    </div>
  </form>
</template>

<script>
import CancelButton from '@/shared/components/ui/Buttons/CancelButton.vue';
import SubmitButton from '@/shared/components/ui/Buttons/SubmitButton.vue';
import TextField from '@/shared/components/ui/FormFields/TextField.vue';
import Form from '@/shared/Form';

export default {
  name: 'DoctorateForm',
  components: {
    TextField,
    CancelButton,
    SubmitButton,
  },
  props: {
    doctorate: {
      type: Object,
      default: () => null,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      form: new Form({
        name: this.doctorate ? this.doctorate.name : '',
      }),
      errorMessage: null,
    };
  },
  computed: {
    isEditing() {
      return this.doctorate !== null;
    },
  },
  methods: {
    async upsert() {
      const url = this.isEditing
        ? `/api/contractuals/contests/${this.contestId}/applications/${this.applicationId}/doctorates/${this.doctorate.id}`
        : `/api/contractuals/contests/${this.contestId}/applications/${this.applicationId}/doctorates`;

      const method = this.isEditing ? 'put' : 'post';

      const event = this.isEditing ? 'update' : 'create';

      this.errorMessage = null;

      try {
        const res = await this.form[method](url);
        this.$emit(event, res.data.data);
      } catch (e) {
        this.errorMessage = e.response.data.message;
      }
    },
  },
};
</script>
