<template>
  <div>
    <div class="row">
      <div class="col-sm-12 text-right">
        <AddQualifiableButton
          qualifiable-type="LanguageSkill"
          :application-id="applicationId"
          :contest-id="contestId"
          :languages="languages"
          :language-levels="languageLevels"
          @create:qualifiable="addLanguageSkill"
        />
      </div>
      <div class="col-sm-12">
        <LanguageSkillList
          :language-skills="languageSkills"
          :application-id="applicationId"
          :contest-id="contestId"
          :languages="languages"
          :language-levels="languageLevels"
          @update:language-skill="updateLanguageSkill"
          @remove:language-skill="removeLanguageSkill"
        />
      </div>
    </div>
  </div>
</template>

<script>
import AddQualifiableButton from '@/contractuals/components/application/qualifiables/AddQualifiableButton.vue';
import LanguageSkillList from '@/contractuals/components/application/qualifiables/language-skill/LanguageSkillList.vue';
import LanguageSkillForm from "@/contractuals/components/application/qualifiables/language-skill/LanguageSkillForm";

export default {
  name: 'LanguageSkills',
  components: {
    LanguageSkillForm,
    AddQualifiableButton,
    LanguageSkillList,
  },
  props: {
    languageSkillsData: {
      type: Array,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
    languages: {
      type: Array,
      required: true,
    },
    languageLevels: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      languageSkills: this.languageSkillsData,
    };
  },
  methods: {
    addLanguageSkill(languageSkill) {
      this.languageSkills.push(languageSkill);
    },
    updateLanguageSkill(languageSkill) {
      const index = this.languageSkills.findIndex((d) => d.id === languageSkill.id);
      this.languageSkills.splice(index, 1, languageSkill);
    },
    removeLanguageSkill(languageSkill) {
      this.languageSkills = this.languageSkills.filter((d) => d.id !== languageSkill.id);
    },
  },
};
</script>
