<template>
  <table
    v-if="languageSkills.length > 0"
    class="table assets-table"
  >
    <thead>
      <tr>
        <th>A/A</th>
        <th style="width: 20%;">
          Γλώσσα
        </th>
        <th style="width: 20%;">
          Επίπεδο
        </th>
        <th>Τίτλος αποδεικτικού</th>
        <th>Ενέργειες</th>
      </tr>
    </thead>
    <tbody>
      <tr
        v-for="(languageSkill, index) in languageSkills"
        :key="index"
      >
        <td>
          {{ index + 1 }}
        </td>
        <td>{{ getLanguageName(languageSkill.language_id) }}</td>
        <td>{{ getLanguageLevelName(languageSkill.language_level_id) }}</td>
        <td>{{ languageSkill.name }}</td>
        <td>
          <div>
            <EditQualifiableButton
              qualifiable-type="LanguageSkill"
              :qualifiable-data="languageSkill"
              :application-id="applicationId"
              :contest-id="contestId"
              :languages="languages"
              :language-levels="languageLevels"
              @update:qualifiable="$emit('update:language-skill', $event)"
            />
            <RemoveQualifiableButton
              qualifiable-type="LanguageSkill"
              :qualifiable-data="languageSkill"
              :application-id="applicationId"
              :contest-id="contestId"
              @remove:qualifiable="$emit('remove:language-skill', $event)"
            />
          </div>
        </td>
      </tr>
    </tbody>
  </table>
  <InfoAlert v-else>
    <i class="fa fa-exclamation-circle" /> Δεν έχουν καταχωρισθεί τίτλοι σπουδών
  </InfoAlert>
</template>

<script>
import EditQualifiableButton from '@/contractuals/components/application/qualifiables/EditQualifiableButton.vue';
import RemoveQualifiableButton from '@/contractuals/components/application/qualifiables/RemoveQualifiableButton.vue';
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';

export default {
  name: 'LanguageSkillList',
  components: {
    EditQualifiableButton,
    RemoveQualifiableButton,
    InfoAlert,
  },
  props: {
    languageSkills: {
      type: Array,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
    languages: {
      type: Array,
      required: true,
    },
    languageLevels: {
      type: Array,
      required: true,
    },
  },
  methods: {
    getLanguageName(languageId) {
      const language = this.languages.find((language) => language.id === languageId);
      return language ? language.name : '';
    },
    getLanguageLevelName(languageLevelId) {
      const languageLevel = this.languageLevels.find((languageLevel) => languageLevel.id === languageLevelId);
      return languageLevel ? languageLevel.name : '';
    },
  },
};
</script>

<style scoped>
table.assets-table thead,
table.positions-table thead {
  background-color: var(--gray-400)
}

table.assets-table tbody tr td {
  border: unset;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  vertical-align: middle;
}

table.assets-table thead tr th:first-child,
table.assets-table tbody tr td:first-child {
  width: 32px;
  text-align: center;
}

table.assets-table thead tr th:last-child,
table.assets-table tbody tr td:last-child {
  width: 100px;
  text-align: center;
}

table.assets-table tbody tr td:last-child div {
  display: flex;
  justify-content: space-around;
}
</style>
