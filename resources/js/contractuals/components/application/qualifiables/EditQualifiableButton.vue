<template>
  <div>
    <a
      href="#"
      @click.prevent="openModal"
    >
      <i class="fa fa-pencil-square-o fa-2x" />
    </a>
    <BasicModal
      :open="isOpen"
      @close="closeModal"
    >
      <template #header>
        <h4>Επεξεργασία</h4>
      </template>
      <Component
        :is="formComponent"
        v-bind="{...formComponentProps, ...$attrs}"
        :contest-id="contestId"
        :application-id="applicationId"
        @update="onUpdate"
        @cancel="closeModal"
      />
    </BasicModal>
  </div>
</template>

<script>
import BasicModal from '@/shared/components/ui/Modal/BasicModal.vue';

export default {
  name: 'EditQualifiableButton',
  components: {
    BasicModal,
    DegreeForm: () => import('@/contractuals/components/application/qualifiables/degree/DegreeForm.vue'),
    PostgraduateForm: () => import('@/contractuals/components/application/qualifiables/postgraduate/PostgraduateForm.vue'),
    DoctorateForm: () => import('@/contractuals/components/application/qualifiables/doctorate/DoctorateForm.vue'),
    LanguageSkillForm: () => import('@/contractuals/components/application/qualifiables/language-skill/LanguageSkillForm.vue'),
    ComputerSkillForm: () => import('@/contractuals/components/application/qualifiables/computer-skill/ComputerSkillForm.vue'),
  },
  props: {
    qualifiableType: {
      type: String,
      required: true,
      validator(value) {
        return ['Degree', 'Postgraduate', 'Doctorate', 'LanguageSkill', 'ComputerSkill'].includes(value);
      },
    },
    qualifiableData: {
      type: Object,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      isOpen: false,
    };
  },
  computed: {
    formComponent() {
      return `${this.qualifiableType}Form`;
    },
    formComponentProps() {
      switch (this.qualifiableType) {
        case 'Degree':
          return { degree: this.qualifiableData };
        case 'Postgraduate':
          return { postgraduate: this.qualifiableData };
        case 'Doctorate':
          return { doctorate: this.qualifiableData };
        case 'LanguageSkill':
          return { languageSkill: this.qualifiableData };
        case 'ComputerSkill':
          return { 'computer-skill': this.qualifiableData };
        default:
          return {};
      }
    },
    // update() {
    //   if (this.type === 'Degree') {
    //     return { 'update:degree': this.onSuccess };
    //   }
    // },
  },
  methods: {
    onUpdate(updatedQualifiable) {
      this.$emit('update:qualifiable', updatedQualifiable);
      this.closeModal();
    },
    openModal() {
      this.isOpen = true;
    },
    closeModal() {
      this.isOpen = false;
    },
  },
};
</script>

<style scoped>
a {
  color: var(--gray-600);
  text-decoration: none;
}

a:hover {
  color: var(--yellow-600);
}
</style>
