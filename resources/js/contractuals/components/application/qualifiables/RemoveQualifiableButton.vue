<template>
  <div>
    <a
      href="#"
      @click.prevent="openModal"
    >
      <i class="fa fa-trash-o fa-2x" />
    </a>
    <ConfirmationModal
      :open="isOpen"
      :is-loading="isLoading"
      @confirm="removeQualifiable"
      @cancel="closeModal"
    >
      <DangerAlert v-if="errorMessage">
        <strong>Σφάλμα!</strong> {{ errorMessage }}
      </DangerAlert>
      Είστε βέβαιος/η ότι θέλετε να προχωρήσετε στην διαγραφή;
    </ConfirmationModal>
  </div>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';

export default {
  name: 'RemoveQualifiableButton',
  components: {
    ConfirmationModal,
    DangerAlert,
  },
  props: {
    qualifiableType: {
      type: String,
      required: true,
      validator(value) {
        return ['Degree', 'Postgraduate', 'Doctorate', 'LanguageSkill', 'ComputerSkill'].includes(value);
      },
    },
    qualifiableData: {
      type: Object,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      isOpen: false,
      isLoading: false,
      errorMessage: null,
    };
  },
  computed: {
    url() {
      switch (this.qualifiableType) {
        case 'Degree':
          return `/api/contractuals/contests/${this.contestId}/applications/${this.applicationId}/degrees/${this.qualifiableData.id}`;
        case 'Postgraduate':
          return `/api/contractuals/contests/${this.contestId}/applications/${this.applicationId}/postgraduates/${this.qualifiableData.id}`;
        case 'Doctorate':
          return `/api/contractuals/contests/${this.contestId}/applications/${this.applicationId}/doctorates/${this.qualifiableData.id}`;
        case 'LanguageSkill':
          return `/api/contractuals/contests/${this.contestId}/applications/${this.applicationId}/language-skills/${this.qualifiableData.id}`;
        case 'ComputerSkill':
          return `/api/contractuals/contests/${this.contestId}/applications/${this.applicationId}/computer-skills/${this.qualifiableData.id}`;
        default:
          return null;
      }
    },
  },
  methods: {
    removeQualifiable() {
      this.isLoading = true;
      this.errorMessage = null;
      this.$http.delete(this.url)
        .then(() => {
          this.$emit('remove:qualifiable', this.qualifiableData);
          this.closeModal();
        })
        .catch((error) => {
          this.errorMessage = error.response.data.message;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    openModal() {
      this.isOpen = true;
    },
    closeModal() {
      this.isOpen = false;
    },
  },
};
</script>

<style scoped>
a {
  color: var(--gray-600);
  text-decoration: none;
}

a:hover {
  color: var(--red-600);
}
</style>
