<template>
  <div>
    <div class="row">
      <div class="col-sm-12 text-right">
        <AddQualifiableButton
          qualifiable-type="ComputerSkill"
          :application-id="applicationId"
          :contest-id="contestId"
          @create:qualifiable="addComputerSkill"
        />
      </div>
      <div class="col-sm-12">
        <ComputerSkillList
          :computer-skills="computerSkills"
          :application-id="applicationId"
          :contest-id="contestId"
          @update:computer-skill="updateComputerSkill"
          @remove:computer-skill="removeComputerSkill"
        />
      </div>
    </div>
  </div>
</template>

<script>

import AddQualifiableButton from '@/contractuals/components/application/qualifiables/AddQualifiableButton.vue';
import ComputerSkillList from '@/contractuals/components/application/qualifiables/computer-skill/ComputerSkillList.vue';
import ComputerSkillForm from "@/contractuals/components/application/qualifiables/computer-skill/ComputerSkillForm";

export default {
  name: 'ComputerSkills',
  components: {
    ComputerSkillForm,
    AddQualifiableButton,
    ComputerSkillList,
  },
  props: {
    computerSkillsData: {
      type: Array,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      computerSkills: this.computerSkillsData,
    };
  },
  methods: {
    addComputerSkill(computerSkill) {
      this.computerSkills.push(computerSkill);
    },
    updateComputerSkill(computerSkill) {
      const index = this.computerSkills.findIndex((d) => d.id === computerSkill.id);
      this.computerSkills.splice(index, 1, computerSkill);
    },
    removeComputerSkill(computerSkill) {
      this.computerSkills = this.computerSkills.filter((d) => d.id !== computerSkill.id);
    },
  },
};
</script>
