<template>
  <table
    v-if="computerSkills.length > 0"
    class="table assets-table"
  >
    <thead>
      <tr>
        <th>A/A</th>
        <th>Τίτλος</th>
        <th>Ενέργειες</th>
      </tr>
    </thead>
    <tbody>
      <tr
        v-for="(computerSkill, index) in computerSkills"
        :key="index"
      >
        <td>
          {{ index + 1 }}
        </td>
        <td>{{ computerSkill.name }}</td>
        <td>
          <div>
            <EditQualifiableButton
              qualifiable-type="ComputerSkill"
              :qualifiable-data="computerSkill"
              :application-id="applicationId"
              :contest-id="contestId"
              @update:qualifiable="$emit('update:computer-skill', $event)"
            />
            <RemoveQualifiableButton
              qualifiable-type="ComputerSkill"
              :qualifiable-data="computerSkill"
              :application-id="applicationId"
              :contest-id="contestId"
              @remove:qualifiable="$emit('remove:computer-skill', $event)"
            />
          </div>
        </td>
      </tr>
    </tbody>
  </table>
  <InfoAlert v-else>
    <i class="fa fa-exclamation-circle" /> Δεν έχουν καταχωρισθεί αποδεικτικά γνώσης Η/Υ
  </InfoAlert>
</template>

<script>
import EditQualifiableButton from '@/contractuals/components/application/qualifiables/EditQualifiableButton.vue';
import RemoveQualifiableButton from '@/contractuals/components/application/qualifiables/RemoveQualifiableButton.vue';
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';

export default {
  name: 'ComputerSkillList',
  components: {
    InfoAlert,
    EditQualifiableButton,
    RemoveQualifiableButton,
  },
  props: {
    computerSkills: {
      type: Array,
      required: true,
    },
    contestId: {
      type: Number,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
};
</script>

<style scoped>
table.assets-table thead,
table.positions-table thead {
  background-color: var(--gray-400)
}

table.assets-table tbody tr td {
  border: unset;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  vertical-align: middle;
}

table.assets-table thead tr th:first-child,
table.assets-table tbody tr td:first-child {
  width: 32px;
  text-align: center;
}

table.assets-table thead tr th:last-child,
table.assets-table tbody tr td:last-child {
  width: 100px;
  text-align: center;
}

table.assets-table tbody tr td:last-child div {
  display: flex;
  justify-content: space-around;
}
</style>
