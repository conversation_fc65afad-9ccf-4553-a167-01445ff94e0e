<template>
  <div class="row">
    <div class="col-sm-12">
      <Box title="Τρέχουσα Εικόνα Αίτησης">
        <div class="row">
          <div class="col-sm-12">
            <h4>
              <a
                v-if="! attachmentError"
                :href="'/contractuals/front-office/applications/' + application.public_application_id"
                class="action-button"
              >
                Αρ. πρωτ. <strong>{{ application.protocol_number }}/{{ application.submitted_at }}</strong>
                <i class="fa fa-file-pdf-o file-download-icon" />
              </a>
              <span v-else>
                Αρ. πρωτ. <strong>{{ application.protocol_number }}/{{ application.submitted_at }}</strong>
              </span>
            </h4>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-6">
            <table class="table table">
              <tbody>
                <tr>
                  <th>Ονοματεπώνυμο</th>
                  <td>{{ application.surname }} {{ application.name }}</td>
                </tr>
                <tr>
                  <th>Όνομα πατρός</th>
                  <td>{{ application.fathername }}</td>
                </tr>
                <tr>
                  <th>ΑΔΤ</th>
                  <td>{{ application.policeid_number }}</td>
                </tr>
                <tr>
                  <th>Κατηγορία</th>
                  <td>{{ application.applicant_category }}</td>
                </tr>
                <tr>
                  <th>Διεύθυνση</th>
                  <td>{{ application.address }}</td>
                </tr>
                <tr>
                  <th>Τηλέφωνο</th>
                  <td>{{ application.telephones }}</td>
                </tr>
                <tr>
                  <th>Email</th>
                  <td>{{ application.email }}</td>
                </tr>
                <tr>
                  <th>Εργασία κατά το τελευταίο 8μηνο</th>
                  <td>{{ application.has_eight_months_employment ? 'NAI' : 'OXI' }}</td>
                </tr>
                <tr>
                  <th>Κώλυμα 8μήνου</th>
                  <td>{{ application.impediment_eight_months ? 'NAI' : 'OXI' }}</td>
                </tr>
                <tr v-if="application.rejected && application.is_auto_rated">
                  <th>Άκυρη Αίτηση (από αυτόματη βαθμολόγηση)</th>
                  <td>{{ application.rejection_description }}</td>
                </tr>
                <tr v-else-if="application.rejected && ! application.is_auto_rated">
                  <th>Άκυρη Αίτηση (από έλεγχο)</th>
                  <td>{{ application.rejection_description }}</td>
                </tr>
                <!-- TODO: add auxiliary level -->
              </tbody>
            </table>
          </div>
          <div class="col-sm-6">
            <table class="table">
              <thead>
                <tr>
                  <th>Προσόν</th>
                  <th>Περιγραφή</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="qualification in qualifications"
                  :key="qualification.id"
                >
                  <td>{{ qualification.type }}</td>
                  <td>{{ qualification.summary }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </Box>
    </div>
  </div>
</template>

<script>
import Box from '@/shared/components/ui/Boxes/Box.vue';

export default {
  name: 'ApplicationSummary',
  components: {
    Box,
  },
  props: {
    application: {
      type: Object,
      required: true,
    },
    qualifications: {
      type: Array,
      required: true,
    },
    attachmentError: {
      type: String,
      default: '',
    },
  },
  // computed: {
  //   totalPoints() {
  //     return this.application.qualifications.reduce((acc, qualification) => acc + qualification.points, 0);
  //   },
  // },
};
</script>
