<template>
  <ConfirmationModal
    :open="open"
    :is-loading="isLoading"
    @confirm="approve"
    @cancel="cancel"
  >
    <DangerAlert
      v-if="errorMessage"
      message="errorMessage"
    />
    Αποδέχεστε την αυτόματη βαθμολόγηση της αίτησης, όπως αυτή προκύπτει από την εφαρμογή;
  </ConfirmationModal>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';

export default {
  name: 'ApproveApplicationRatingModal',
  components: {
    ConfirmationModal,
    DangerAlert,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
    application: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
      errorMessage: '',
    };
  },
  methods: {
    approve() {
      this.clearErrorMessage();
      this.isLoading = true;
      this.$http.post(`/api/contractuals/contests/${this.application.contest_id}/rated-applications`, {
        application_id: this.application.id,
      })
        .then((res) => {
          this.$emit('success', res.data);
        })
        .catch((error) => {
          this.errorMessage = error.response.data.message;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    cancel() {
      this.clearErrorMessage();
      this.$emit('cancel');
    },
    clearErrorMessage() {
      this.errorMessage = '';
    },
  },
};
</script>
