<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <Box
          title="Αιτήσεις προς αξιολόγηση"
          :is-loading="isFetchingAutoRatedApplications"
        >
          <AutoRatedApplications
            v-if="! fetchingAutoRatedApplicationsError"
            :contest="contestData"
            :auto-rated-applications="autoRatedApplications"
            :unit-options="unitOptions"
            @fetch-applications="fetchAutoRatedApplications"
          />
          <DangerAlert
            v-else
            :message="fetchingAutoRatedApplicationsError"
          />
        </Box>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <Box
          title="Αιτήσεις υπο αξιολόγηση"
          :is-loading="isFetchingUnderRatingApplications"
        >
          <UnderRatingApplications
            v-if="! fetchingUnderRatingApplicationsError"
            :contest="contestData"
            :under-rating-applications="underRatingApplications"
            :unit-options="unitOptions"
            @fetch-applications="fetchUnderRatingApplications"
          />
          <DangerAlert
            v-else
            :message="fetchingUnderRatingApplicationsError"
          />
        </Box>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <Box
          title="Αιτήσεις αξιολογημένες"
          :is-loading="isFetchingRatedApplications"
        >
          <RatedApplications
            v-if="! fetchingRatedApplicationsError"
            :contest="contestData"
            :rated-applications="ratedApplications"
            :unit-options="unitOptions"
            @fetch-applications="fetchRatedApplications"
            @application-unrated="unrateApplication"
          />
          <DangerAlert
            v-else
            :message="fetchingRatedApplicationsError"
          />
        </Box>
      </div>
    </div>
  </div>
</template>

<script>
import AutoRatedApplications from '@/contractuals/components/application/AutoRatedApplications.vue';
import RatedApplications from '@/contractuals/components/application/RatedApplications.vue';
import UnderRatingApplications from '@/contractuals/components/application/UnderRatingApplications.vue';
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';

export default {
  name: 'ContestApplications',
  components: {
    DangerAlert,
    AutoRatedApplications,
    UnderRatingApplications,
    RatedApplications,
    Box,
  },
  props: {
    contestData: {
      type: Object,
      required: true,
    },
    unitOptions: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      // TODO: Consider storing filter information into the session and pass xxxAplicationsData as props
      autoRatedApplications: { data: [], meta: {}, links: {} },
      underRatingApplications: { data: [], meta: {}, links: {} },
      ratedApplications: { data: [], meta: {}, links: {} },
      isFetchingAutoRatedApplications: false,
      isFetchingUnderRatingApplications: false,
      isFetchingRatedApplications: false,
      fetchingAutoRatedApplicationsError: null,
      fetchingUnderRatingApplicationsError: null,
      fetchingRatedApplicationsError: null,
    };
  },
  computed: {
    contestIsRanked() {
      return !!this.contestData.ranked_at;
    },
  },
  created() {
    // TODO: Consider storing filter information into the session and pass xxxAplicationsData as props
    this.fetchAutoRatedApplications(this.$cookies.get('contractuals_auto_rated_applications_filters'));
    this.fetchUnderRatingApplications(this.$cookies.get('contractuals_under_evaluation_applications_filters'));
    this.fetchRatedApplications(this.$cookies.get('contractuals_evaluated_applications_filters'));
  },
  methods: {
    fetchAutoRatedApplications(filters) {
      this.isFetchingAutoRatedApplications = true;
      this.fetchingAutoRatedApplicationsError = null;

      const url = `/api/contractuals/contests/${this.contestData.id}/auto-rated-applications`;

      this.$http.get(url, { params: filters })
        .then((res) => {
          this.autoRatedApplications = res.data.data; // res.data: { ... data: { data: [...], meta: {...} }, ... }
        })
        .catch((err) => {
          this.fetchingAutoRatedApplicationsError = err.response.data.message;
        })
        .finally(() => {
          this.isFetchingAutoRatedApplications = false;
        });
    },
    fetchUnderRatingApplications(filters) {
      this.isFetchingUnderRatingApplications = true;
      this.fetchingUnderRatingApplicationsError = null;

      const url = `/api/contractuals/contests/${this.contestData.id}/under-evaluation-applications`;

      this.$http.get(url, { params: filters })
        .then((res) => {
          this.underRatingApplications = res.data.data; // res.data: { ... data: { data: [...], meta: {...} }, ... }
        })
        .catch((err) => {
          this.fetchingUnderRatingApplicationsError = err.response.data.message;
        })
        .finally(() => {
          this.isFetchingUnderRatingApplications = false;
        });
    },
    fetchRatedApplications(filters) {
      this.isFetchingRatedApplications = true;
      this.fetchingRatedApplicationsError = null;

      const url = `/api/contractuals/contests/${this.contestData.id}/rated-applications`;

      this.$http.get(url, { params: filters })
        .then((res) => {
          this.ratedApplications = res.data.data; // res.data: { ... data: { data: [...], meta: {...} }, ... }
        })
        .catch((err) => {
          this.fetchingRatedApplicationsError = err.response.data.message;
        })
        .finally(() => {
          this.isFetchingRatedApplications = false;
        });
    },
    unrateApplication(application) {
      this.fetchUnderRatingApplications(this.$cookies.get('contractuals_under_evaluation_applications_filters'));
      this.fetchRatedApplications(this.$cookies.get('contractuals_evaluated_applications_filters'));
    },
    applicationIsRejected(application) {
      return application.rejected === true;
    },
    applicationIsLocked(application) {
      return application.locked_at !== null;
    },
    applicationIsRated(application) {
      return application.rated_at !== null;
    },
    applicationIsAutoRated(application) {
      return application.is_auto_rated === true;
    },
    applicationIsDistributed(application) {
      return application.is_distributed === true;
    },
  },
};
</script>
