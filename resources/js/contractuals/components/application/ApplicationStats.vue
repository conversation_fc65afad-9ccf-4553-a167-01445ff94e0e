<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <div class="stats-boxes">
          <StatsBox
            text="Συνολικές"
            :number="totalApplications"
            icon="fa-files-o"
          />
          <StatsBox
            text="Αξιολογημένες"
            :number="totalRatedApplications"
            icon="fa-gavel"
            :color="chartColors.rated"
          />
          <StatsBox
            text="Υπό αξιολόγηση"
            :number="totalUnderRatingApplications"
            icon="fa-balance-scale"
            :color="chartColors.underRating"
          />
          <StatsBox
            text="Διαμοιρασμένες"
            :number="totalDistributedApplications"
            icon="fa-share"
            :color="chartColors.distributed"
          />
          <StatsBox
            text="Μη διαμοιρασμένες"
            :number="totalNonDistributedApplications"
            icon="fa-arrow-circle-o-down"
            :color="chartColors.nonDistributed"
          />
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <div class="col-sm-12">
          <Bar
            :chart-options="chartOptions"
            :chart-data="chartData"
            :chart-id="chartId"
            :height="100"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  BarElement, CategoryScale, Chart as ChartJS, LinearScale,
} from 'chart.js';
import { Bar } from 'vue-chartjs/legacy';

import StatsBox from '@/shared/components/ui/Widgets/StatsBox.vue';

ChartJS.register(LinearScale, CategoryScale, BarElement); // Title, Tooltip, Legend,

export default {
  name: 'ApplicationStats',
  components: {
    StatsBox,
    Bar,
  },
  props: {
    totalApplications: {
      type: Number,
      required: true,
    },
    totalRatedApplications: {
      type: Number,
      required: true,
    },
    totalUnderRatingApplications: {
      type: Number,
      required: true,
    },
    totalDistributedApplications: {
      type: Number,
      required: true,
    },
    totalNonDistributedApplications: {
      type: Number,
      required: true,
    },
    totalAutoRejectedApplications: {
      type: Number,
      required: true,
    },
    chartId: {
      type: String,
      default: 'bar-chart',
    },
  },
  data() {
    return {
      chartOptions: {
        maintainAspectRatio: false,
        barThickness: 12,
        indexAxis: 'y',
        plugins: {
          // title: {
          //   display: false,
          //   text: 'Κατανομή Αιτήσεων',
          // },
          // legend: {
          //   display: true,
          // },
        },
        animation: false,
        responsive: true,
        scales: {
          x: {
            beginAtZero: true,
            stacked: true,
            ticks: {
              display: false,
            },
            grid: {
              display: false,
              drawBorder: false,
            },
            max: this.totalApplications,
          },
          y: {
            stacked: true,
            ticks: {
              display: false,
            },
            grid: {
              drawBorder: false,
              display: false,
            },
          },
        },
      },
      chartColors: {
        rated: '#48bb78',
        underRating: '#ecc94b',
        distributed: '#07b6d5',
        nonDistributed: '#a0aec0',
        autoRejected: '#f56565',
      },
    };
  },
  computed: {
    chartData() {
      return {
        labels: [''],
        datasets: [
          {
            label: 'Αξιολογημένες',
            data: [this.totalRatedApplications],
            backgroundColor: this.chartColors.rated,
          },
          {
            label: 'Υπό αξιολόγηση',
            data: [this.totalUnderRatingApplications],
            backgroundColor: this.chartColors.underRating,
          },
          {
            label: 'Διαμοιρασμένες',
            data: [this.totalDistributedApplications],
            backgroundColor: this.chartColors.distributed,
          },
          {
            label: 'Μη διαμοιρασμένες',
            data: [this.totalNonDistributedApplications],
            backgroundColor: this.chartColors.nonDistributed,
          },
          {
            label: 'Αυτ Απόρριψη',
            data: [this.totalAutoRejectedApplications],
            backgroundColor: this.chartColors.autoRejected,
          },
        ],
      };
    },
  },
};
</script>

<style scoped>
.stats-boxes {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  margin-top: 2rem;
}
</style>
