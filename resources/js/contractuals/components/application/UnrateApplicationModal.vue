<template>
  <ConfirmationModal
    :open="open"
    :is-loading="isLoading"
    @confirm="unrateApplication"
    @cancel="cancel"
  >
    <DangerAlert
      v-if="errorMessage"
      :message="errorMessage"
    />
    Θέλετε να αναιρέσετε την βαθμολόγηση της αίτησης;
  </ConfirmationModal>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';

export default {
  name: 'UnrateApplicationModal',
  components: {
    DangerAlert,
    ConfirmationModal,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
    application: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
      errorMessage: '',
    };
  },
  methods: {
    unrateApplication() {
      this.clearErrorMessage();
      this.isLoading = true;
      this.$http.delete(`/api/contractuals/contests/${this.application.contest_id}/rated-applications/${this.application.id}`)
        .then((res) => {
          this.$emit('success', res.data);
        })
        .catch((error) => {
          this.errorMessage = error.response.data.message;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    cancel() {
      this.clearErrorMessage();
      this.$emit('cancel');
    },
    clearErrorMessage() {
      this.errorMessage = '';
    },
  },
};
</script>
