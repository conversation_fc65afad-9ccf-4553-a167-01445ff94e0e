<template>
  <div>
    <button
      class="btn btn-primary btn-outline btn-block"
      @click="openModal = true"
    >
      <i class="fa fa-refresh" /> Επαναφορά αίτησης
    </button>
    <ImportApplicationModal
      :application="application"
      :open="openModal"
      @success="onSuccess"
      @cancel="closeModal"
    />
  </div>
</template>

<script>
import ImportApplicationModal from '@/contractuals/components/application/ImportApplicationModal.vue';

export default {
  name: 'ImportApplicationButton',
  components: {
    ImportApplicationModal,
  },
  props: {
    application: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      openModal: false,
    };
  },
  methods: {
    onSuccess() {
      this.closeModal();
      window.swal({
        type: 'success',
        title: 'Eπιτυχία!',
        text: 'Η αίτηση επανήλθε στην αρχική μορφή σε κατάσταση "Προς Έλεγχο" και θα πρέπει να ελεγχθεί εξ αρχής!',
        showConfirmButton: true,
      }, () => {
        window.location.replace(`/contractuals/contests/${this.application.contest_id}/applications/${this.application.id}`);
      });
    },
    closeModal() {
      this.openModal = false;
    },
  },
};
</script>
