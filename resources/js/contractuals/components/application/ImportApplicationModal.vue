<template>
  <ConfirmationModal
    :open="open"
    :is-loading="isLoading"
    @confirm="importApplication"
    @cancel="cancel"
  >
    <DangerAlert
      v-if="errorMessage"
      :message="errorMessage"
    />
    <p>Θέλετε να επαναφέρετε την αίτηση από το Front Office;</p>
    <WarningAlert>
      <p>
        Με την επιβαβαίωση της επαναφοράς της αίτησης, όλες οι μεταβολλές που έχουν πραγμτοποιηθεί μέχρι
        αυτή την στιγμή θα αναιρεθούν. Η αίτηση θα εμφανίζεται στην αρχική της μορφή, όπως υποβλήθηκε
        από τον υποψήφιο, με αυτόματη βαθμολόγηση και θα πρέπει να ελεγχθεί εξ' αρχής.
      </p>
    </WarningAlert>
  </ConfirmationModal>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import WarningAlert from '@/shared/components/ui/Alerts/WarningAlert.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';

export default {
  name: 'ImportApplicationModal',
  components: {
    WarningAlert,
    ConfirmationModal,
    DangerAlert,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
    application: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isLoading: false,
      errorMessage: '',
    };
  },
  methods: {
    importApplication() {
      this.clearErrorMessage();
      this.isLoading = true;
      this.$http.post(`/api/contractuals/contests/${this.application.contest_id}/imported-applications/`, {
        public_application_id: this.application.public_application_id,
      })
        .then(() => {
          this.$emit('success');
        })
        .catch((error) => {
          this.errorMessage = error.response.data.message;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    cancel() {
      this.clearErrorMessage();
      this.$emit('cancel');
    },
    clearErrorMessage() {
      this.errorMessage = '';
    },
  },
};
</script>
