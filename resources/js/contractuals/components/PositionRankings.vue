<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <PositionSucceededRankings
          :succeeded-rankings-data="succeededRankings"
          :calculation="calculation"
          :contest="contest"
        />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <PositionRejectedRankings
          :rejected-rankings="rejectedRankings"
          :calculation="calculation"
          :contest="contest"
        />
      </div>
    </div>
  </div>
</template>

<script>
import PositionRejectedRankings from '@/contractuals/components/positions/PositionRejectedRankings.vue';
import PositionSucceededRankings from '@/contractuals/components/positions/PositionSucceededRankings.vue';

export default {
  name: 'PositionRankings',

  components: {
    PositionRejectedRankings,
    PositionSucceededRankings,
  },

  props: {
    succeededRankings: {
      type: Array,
      required: true,
    },
    rejectedRankings: {
      type: Array,
      required: true,
    },
    calculation: {
      type: Object,
      required: true,
    },
    contest: {
      type: Object,
      required: true,
    },
  },
};
</script>
