<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <PositionActionPanel :contest="contest" />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <Positions :positions="positions" />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-4">
        <a
          :href="`/contractuals/contests/${contest.id}`"
          class="btn btn-default btn-block"
        >
          <i class="fa fa-arrow-left" /> Επιστροφή στην προβολή διαγωνισμού
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import PositionActionPanel from '@/contractuals/components/positions/PositionActionPanel.vue';
import Positions from '@/contractuals/components/positions/Positions.vue';

export default {
  name: 'PositionIndex',
  components: {
    PositionActionPanel,
    Positions,
  },
  props: {
    contest: {
      type: Object,
      required: true,
    },
    positions: {
      type: Array,
      required: true,
    },
    calculation: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {

    };
  },
  computed: {
    contestIsRanked() {
      return this.contest.ranked_at !== null;
    },
  },
};
</script>
