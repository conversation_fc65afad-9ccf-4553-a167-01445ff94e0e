<template>
  <div>
    <ul class="timeline">
      <template v-for="applicationRating in applicationRatings">
        <li
          :key="`${applicationRating.id}_label`"
          class="time-label"
        >
          <span class="bg-blue">
            <i class="fa fa-hashtag" /> {{ applicationRating.evaluation_number}}
          </span>
          <span class="bg-blue">
            <i class="fa fa-clock-o" /> {{ applicationRating.evaluated_at }}
          </span>
        </li>
        <li :key="`${applicationRating.id}_item`">
          <i
            v-if="isAutoRated(applicationRating)"
            class="fa fa-cogs bg-gray"
          />
          <i
            v-else
            class="fa fa-gavel bg-blue"
          />
          <div class="timeline-item">
            <ApplicationRating :application-rating="applicationRating" />
          </div>
        </li>
      </template>
    </ul>
    <div class="row">
      <div class="col-sm-3">
        <a
          class="btn btn-default btn-block"
          :href="`/contractuals/contests/${contest.id}/applications/${application.id}`"
        >
          <i class="fa fa-arrow-left" /> Επιστροφή στην προβολή της αίτησης
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import ApplicationRating from '@/contractuals/components/application/ApplicationRating.vue';

export default {
  name: 'ApplicationRatingIndex',
  components: { ApplicationRating },
  props: {
    contest: {
      type: Object,
      required: true,
    },
    application: {
      type: Object,
      required: true,
    },
    applicationRatings: {
      type: Array,
      required: true,
    },
  },
  mounted() {
    const numberOfTr = document.querySelector('table tbody').rows.length;
    for (let i = 1; i <= numberOfTr; i += 1) {
      const valueTags = document.querySelectorAll(`table tbody tr:nth-child(${i}) td:nth-child(2)`);
      for (let j = 0; j < (valueTags.length - 1); j += 1) {
        if (valueTags[j].innerText !== valueTags[j + 1].innerText) {
          valueTags[j].parentElement.style.backgroundColor = '#fefcbf';
        }
      }
      const auxiliatyLevelTags = document.querySelectorAll('dl dd.auxiliary-level');
      const eightMonthsImpedimentTags = document.querySelectorAll('dl dd.impediment-eight-months');
      const localityTags = document.querySelectorAll('dl dd.locality');
      for (let k = 0; k < (auxiliatyLevelTags.length - 1); k += 1) {
        if (auxiliatyLevelTags[k].innerText !== auxiliatyLevelTags[k + 1].innerText) {
          auxiliatyLevelTags[k].style.backgroundColor = '#fefcbf';
        }
        if (eightMonthsImpedimentTags[k].innerText !== eightMonthsImpedimentTags[k + 1].innerText) {
          eightMonthsImpedimentTags[k].style.backgroundColor = '#fefcbf';
        }
        if (localityTags[k].innerText !== localityTags[k + 1].innerText) {
          localityTags[k].style.backgroundColor = '#fefcbf';
        }
      }
    }
  },
  methods: {
    isAutoRated(applicationRating) {
      return applicationRating.evaluation_type === 'import' || applicationRating.evaluation_type === 'reset';
    },
  },
};
</script>
