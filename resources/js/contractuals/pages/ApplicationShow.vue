<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <ApplicationSummary
          :application="application"
          :qualifications="qualifications"
          :attachment-error="attachmentError"
        />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <ApplicationAttachments
          v-if="! attachmentError"
          :attachment-data="attachmentData"
          :application-id="application.public_application_id"
        />
        <DangerAlert
          v-else
          :message="attachmentError"
        />
      </div>
    </div>
    <div class="row">
      <div
        class="col-sm-12 text-right"
        style="margin-bottom: 5px;"
      >
        <a :href="`/contractuals/contests/${application.contest_id}/applications/${application.id}/application-ratings`">
          <i
            class="fa fa-history"
            aria-hidden="true"
          />
          Δείτε το ιστορικό των βαθμολογίων...
        </a>
      </div>
      <div class="col-sm-12">
        <ApplicationRating
          v-if="applicationIsRated || applicationIsAutoRated"
          :application-rating="applicationRating"
        />
        <WarningAlert v-else>
          <h4><i class="icon fa fa-pencil-square-o" /> Αίτηση υπο επεξεργασία!</h4>
          <p>Η αίτηση βρίσκεται υπό επεξεργασία και δεν έχει μοριοδoτηθεί</p>
        </WarningAlert>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <PositionRatings
          :position-ratings="positionRatings"
          :application-is-rated="applicationIsRated || applicationIsAutoRated"
        />
      </div>
    </div>
    <div
      v-if="contest.is_ranked"
      class="row"
    >
      <div class="col-sm-12">
        <EmployablePosition :position-data="employablePosition" />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <ApplicationActions
          :application="application"
          :contest="contest"
          @auto-rating-approved="approveAutoRating"
          @auto-rating-disapproved="updateApplicationState"
          @application-unrated="updateApplicationState"
        />
      </div>
    </div>
  </div>
</template>

<script>
import ApplicationActions from '@/contractuals/components/application/ApplicationActions.vue';
import ApplicationAttachments from '@/contractuals/components/application/ApplicationAttachments.vue';
import ApplicationRating from '@/contractuals/components/application/ApplicationRating.vue';
import ApplicationSummary from '@/contractuals/components/application/ApplicationSummary.vue';
import EmployablePosition from '@/contractuals/components/application/EmployablePosition.vue';
import PositionRatings from '@/contractuals/components/application/PositionRatings.vue';
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import WarningAlert from '@/shared/components/ui/Alerts/WarningAlert.vue';

export default {
  name: 'ApplicationShow',
  components: {
    EmployablePosition,
    DangerAlert,
    WarningAlert,
    ApplicationRating,
    ApplicationAttachments,
    ApplicationSummary,
    ApplicationActions,
    PositionRatings,
  },
  props: {
    applicationData: {
      type: Object,
      required: true,
    },
    contest: {
      type: Object,
      required: true,
    },
    qualifications: {
      type: Array,
      required: true,
    },
    applicationRatingData: {
      type: Object,
      required: true,
    },
    positionRatings: {
      type: Array,
      required: true,
    },
    attachmentData: {
      type: Object,
      default: null,
    },
    attachmentError: {
      type: String,
      default: null,
    },
    employablePosition: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      application: this.applicationData,
      applicationRating: this.applicationRatingData,
    };
  },
  computed: {
    applicationIsAutoRated() {
      return this.application.is_auto_rated === true;
    },
    applicationIsRated() {
      return this.application.rated_at !== null && this.application.is_auto_rated === false;
    },
    applicationIsUnderRating() {
      return this.application.rated_at === null && this.application.is_auto_rated === false;
    },
  },
  methods: {
    updateApplicationState(application) {
      this.application.rated_at = application.rated_at;
      this.application.is_auto_rated = application.is_auto_rated;
      this.application.rated_by = application.rated_by;
      this.application.rated_by_unit_id = application.rated_by_unit_id;
      this.application.validating_unit_id = application.validating_unit_id;
    },
    approveAutoRating(data) {
      window.swal({
        type: 'success',
        title: 'Επιτυχία!',
        text: data.message,
        showConfirmButton: true,
      }, () => {
        window.location.reload();
      });
    },
  },
};
</script>
