<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <ContestActionPanel
          :contest-data="contest"
          :has-applications="hasApplications"
          :is-calculating="isCalculating"
          :latest-calculation="latestCalculation"
          @update-locked-status="updateLockedStatus"
        />
      </div>
    </div>
    <div
      v-if="hasApplications && contest.can.admin"
      class="row"
    >
      <div class="col-sm-12">
        <ApplicationStats
          :total-applications="applicationStats.totalApplications"
          :total-rated-applications="applicationStats.totalRatedApplications"
          :total-under-rating-applications="applicationStats.totalUnderRatingApplications"
          :total-auto-rated-applications="applicationStats.totalAutoRatedApplications"
          :total-distributed-applications="applicationStats.totalDistributedApplications"
          :total-non-distributed-applications="applicationStats.totalNonDistributedApplications"
          :total-auto-rejected-applications="applicationStats.totalAutoRejectedApplications"
        />
      </div>
    </div>
    <div
      v-if="isLocked"
      class="row"
    >
      <div class="col-sm-12">
        <ContestApplications
          :contest-data="contest"
          :unit-options="unitOptions"
        />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-3">
        <a
          href="/contractuals/contests"
          class="btn btn-default btn-block"
        >
          <i class="fa fa-arrow-left" /> Επιστροφή στο αρχείο διαγωνισμών
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import ApplicationStats from '@/contractuals/components/application/ApplicationStats.vue';
import ContestApplications from '@/contractuals/components/application/ContestApplications.vue';
import ContestActionPanel from '@/contractuals/components/contest/ContestActionPanel.vue';
import Gate from '@/shared/mixins/Gate';

export default {
  name: 'ContestShow',
  components: {
    ApplicationStats,
    ContestActionPanel,
    ContestApplications,
  },
  mixins: [
    Gate,
  ],
  props: {
    contest: {
      type: Object,
      required: true,
    },
    hasApplications: {
      type: Boolean,
      required: true,
    },
    isCalculating: {
      type: Boolean,
      required: true,
    },
    applicationStats: {
      type: Object,
      required: true,
    },
    unitId: { // TODO: Remove this prop and use Gate mixin instead
      type: Number,
      required: true,
    },
    unitOptions: {
      type: Array,
      required: true,
    },
    latestCalculation: {
      type: Object,
      default() { return null; },
    },
  },
  data() {
    return {
      isLocked: !!this.contest.locked_at,
    };
  },
  methods: {
    updateLockedStatus(contest) {
      this.isLocked = !!contest.locked_at;
    },
  },
};
</script>
