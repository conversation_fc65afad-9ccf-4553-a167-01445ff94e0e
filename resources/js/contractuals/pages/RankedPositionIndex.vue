<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <RankedPositionActionPanel
          :contest="contest"
          :calculation="calculation"
        />
      </div>
    </div>
    <div
      v-if="contest.can.admin"
      class="row"
    >
      <div class="col-sm-12">
        <RankedPositionStats :stats="stats" />
      </div>
    </div>
    <div
      v-if="contest.can.admin"
      class="row"
    >
      <div class="col-sm-12">
        <RankedPositionExports
          :contest="contest"
          :calculation="calculation"
          :exported-files="exportedFiles"
          :is-creating-exports="isCreatingExportedFiles"
          :has-exported-files="hasExportedFiles"
          @exports-calculating="jobHasStarted"
        />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <RankedPositions
          :contest="contest"
          :calculation="calculation"
          :filters="filters"
        />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-4">
        <a
          :href="`/contractuals/contests/${contest.id}`"
          class="btn btn-default btn-block"
        >
          <i class="fa fa-arrow-left" /> Επιστροφή στην προβολή διαγωνισμού
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import RankedPositionActionPanel from '@/contractuals/components/positions/RankedPositionActionPanel.vue';
import RankedPositionExports from '@/contractuals/components/positions/RankedPositionExports.vue';
import RankedPositions from '@/contractuals/components/positions/RankedPositions.vue';
import RankedPositionStats from '@/contractuals/components/positions/RankedPositionStats.vue';

export default {
  name: 'RankedPositionIndex',
  components: {
    RankedPositionExports,
    RankedPositionStats,
    RankedPositionActionPanel,
    RankedPositions,

  },
  props: {
    contest: {
      type: Object,
      required: true,
    },
    calculation: {
      type: Object,
      default: null,
    },
    filters: {
      type: Object,
      required: true,
    },
    stats: {
      type: Object,
      required: true,
    },
    exports: {
      type: Array,
      required: true,
    },
    isCreatingExports: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      exportedFiles: this.exports,
      isCreatingExportedFiles: this.isCreatingExports,
    };
  },
  computed: {
    contestIsRanked() {
      return this.contest.ranked_at !== null;
    },
    hasExportedFiles() {
      return this.exportedFiles.length > 0;
    },
  },
  methods: {
    jobHasStarted() {
      this.exportedFiles = [];
      this.isCreatingExportedFiles = true;
    },
  },
};
</script>
