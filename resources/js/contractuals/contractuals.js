import '../bootstrap';

import ApplicantForm from '@/contractuals/components/ApplicantForm.vue';
import ContestApplications from '@/contractuals/components/application/ContestApplications.vue';
import ApplicationForm from '@/contractuals/components/ApplicationForm.vue';
import ApplicationListing from '@/contractuals/components/ApplicationListing.vue';
import BackofficeStats from '@/contractuals/components/BackofficeStats.vue';
import ContestForm from '@/contractuals/components/contest/ContestForm.vue';
import EvaluationForm from '@/contractuals/components/EvaluationForm.vue';
import PositionRankings from '@/contractuals/components/PositionRankings.vue';
import ApplicationEdit from '@/contractuals/pages/ApplicationEdit.vue';
import ApplicationRatingIndex from '@/contractuals/pages/ApplicationRatingIndex.vue';
import ApplicationShow from '@/contractuals/pages/ApplicationShow.vue';
import ContestShow from '@/contractuals/pages/ContestShow.vue';
import PositionIndex from '@/contractuals/pages/PositionIndex.vue';
import RankedPositionIndex from '@/contractuals/pages/RankedPositionIndex.vue';
import RankedPositionShow from '@/contractuals/pages/RankedPositionShow.vue';

Vue.component('ContestShow', ContestShow);
Vue.component('ContestForm', ContestForm);
Vue.component('ApplicationShow', ApplicationShow);
Vue.component('ApplicationEdit', ApplicationEdit);
Vue.component('ContestApplications', ContestApplications);
Vue.component('ApplicationListing', ApplicationListing);
Vue.component('ApplicationForm', ApplicationForm);
Vue.component('EvaluationForm', EvaluationForm);
Vue.component('PositionRankings', PositionRankings);
Vue.component('BackofficeStats', BackofficeStats);
Vue.component('PositionIndex', PositionIndex);
Vue.component('RankedPositionIndex', RankedPositionIndex);
Vue.component('RankedPositionShow', RankedPositionShow);
Vue.component('ApplicationRatingIndex', ApplicationRatingIndex);

/**
 * Instantiate the route Vue instance
 */
new Vue({
  el: '#app',
});
