<script setup lang="ts">
import { computed, useSlots } from 'vue';

import type { AssetDetail, AssetList } from '@/assets/types';
import BaseButton from '@/shared/components/ui/Buttons/BaseButton.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';
import useHttp, { isAxiosError } from '@/shared/composables/useHttp';
import useState from '@/shared/composables/useState';
import useSwal from '@/shared/composables/useSwal';

const props = withDefaults(defineProps<{
  asset: AssetDetail | AssetList;
  buttonVariation?: 'primary' | 'secondary' | 'outline' | 'plain' | 'destructive';
}>(), {
  buttonVariation: 'secondary',
});

// Get slots and check if default slot has content
const slots = useSlots();
const hasDefaultSlot = computed(() => !!slots.default);

// Only apply custom variation when there's content in the slot
const buttonVariationToUse = computed(() => (hasDefaultSlot.value ? props.buttonVariation : 'secondary'));

const emit = defineEmits<{
  (e: 'deleted', assetId: number): void;
}>();

const { delete: destroy } = useHttp();
const { success, error } = useSwal();

const [isOpen, setIsOpen] = useState(false);
const [isProcessing, setIsProcessing] = useState(false);

const openDeleteModal = () => {
  setIsOpen(true);
};

const cancelDelete = () => {
  setIsOpen(false);
};

const confirmDelete = async () => {
  const assetId = props.asset?.id;
  if (!assetId) {
    error({ message: 'Δεν βρέθηκε το αναγνωριστικό του περιουσιακού στοιχείου' });
    return;
  }

  setIsProcessing(true);
  try {
    const response = await destroy(`/api/assets/asset/${assetId}`);
    setIsOpen(false);
    success({
      message: response.message || 'Το περιουσιακό στοιχείο διαγράφηκε επιτυχία',
    }, () => {
      emit('deleted', assetId);
    });
  } catch (err) {
    if (isAxiosError(err)) {
      setIsOpen(false);
      error({ message: err.response?.data.message || 'Παρουσιάστηκε σφάλμα κατά τη διαγραφή' });
    }
  } finally {
    setIsProcessing(false);
  }
};
</script>

<script lang="ts">
export default {
  name: 'AssetDeleteConfirmationButton',
  components: { BaseButton },
};
</script>

<template>
  <div>
    <BaseButton
      icon="tabler:trash"
      :icon-only="!hasDefaultSlot"
      :variation="buttonVariationToUse"
      title="Delete Asset"
      @click="openDeleteModal"
    >
      <slot />
    </BaseButton>

    <!-- Confirmation Modal -->
    <ConfirmationModal
      cancel-btn-text="Ακύρωση"
      confirm-btn-text="Διαγραφή περιουσιακού στοιχείου"
      :open="isOpen"
      :processing="isProcessing"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    >
      <div class="max-w-prose mx-auto">
        <p>
          Πρόκειται να διαγράψετε οριστικά αυτό το περιουσιακό στοιχείο.
          Μετά την διαγραφή του δεν θα μπορεί να ανακτηθεί.
          Θέλετε να συνεχίσετε;
        </p>
      </div>
    </ConfirmationModal>
  </div>
</template>

<style scoped>
.action-button-delete {
  background: none;
  color: inherit;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}

.action-button {
  color: var(--gray-600);
}

.action-button:hover {
  color: var(--brand-color);
}
</style>
