<script setup lang="ts">
import { inject, onMounted, ref } from 'vue';
import type { VueCookies } from 'vue-cookies';

import SubmittedAssetList from '@/assets/components/SubmittedAssetList.vue';
import UnsubmittedAssetList from '@/assets/components/UnsubmittedAssetList.vue';
import type { AssetList, ContractDetail } from '@/assets/types';
import type { Paginator, ResourceFilters } from '@/global';
import Box from '@/shared/components/ui/Boxes/Box.vue';
import useHttp, { isAxiosError } from '@/shared/composables/useHttp';
import useSwal from '@/shared/composables/useSwal';

const props = defineProps<{
  contract: ContractDetail;
}>();

// Define filterables without contract (since it's locked by contract ID)
const filterables = [
  {
    label: 'Σειριακός αριθμός',
    field: 'serial_number',
    type: 'string',
  },
  {
    label: 'Εγκατάσταση',
    field: 'location',
    type: 'string',
  },
  {
    label: 'Ημερομηνία Κεφαλαιοποίησης',
    field: 'date_of_receipt',
    type: 'date',
  },
];

// Define sortables
const sortables = [
  { label: 'Σειριακός αριθμός', field: 'serial_number' },
  { label: 'Εγκατάσταση', field: 'location' },
  { label: 'Ημερομηνία Κεφαλαιοποίησης', field: 'date_of_receipt' },
];

// Define listables without contract (since all assets will be for the same contract)
const listables = [
  { label: 'Σειριακός αριθμός', field: 'serial_number' },
  { label: 'Κατηγορία', field: 'asset_category' },
  { label: 'Ποσότητα', field: 'quantity' },
  { label: 'Εγκατάσταση', field: 'location' },
  { label: 'Ημερομηνία Κεφαλαιοποίησης', field: 'date_of_receipt' },
  { label: 'Κόστος Απόκτησης', field: 'acquisition_cost' },
  { label: 'Υπηρεσία', field: 'unit_abbrv' },
  { label: '⚙ Ενέργειες', field: 'actions' },
];

const unsubmittedAssets = ref<Paginator<AssetList> | {
  data: [],
  meta: Record<string, never>,
  links: Record<string, never>,
}>({
  data: [],
  meta: {},
  links: {},
});

const submittedAssets = ref<Paginator<AssetList> | {
  data: [],
  meta: Record<string, never>,
  links: Record<string, never>,
}>({
  data: [],
  meta: {},
  links: {},
});

const { get } = useHttp();
const { error } = useSwal();
const $cookies = inject<VueCookies>('$cookies');

const unsubmittedAssetsCookie = `contract_${props.contract.id}_unsubmitted_assets`;
const submittedAssetsCookie = `contract_${props.contract.id}_submitted_assets`;

const fetchUnsubmittedAssets = async (filters: ResourceFilters) => {
  try {
    const url = `/api/assets/contracts/${props.contract.id}/unsubmitted-assets`;
    const response = await get<Paginator<AssetList>>(url, filters);
    if (response.data) {
      unsubmittedAssets.value = response.data;
    }
  } catch (err) {
    if (isAxiosError(err)) {
      error({ message: err.response?.data.message || 'An unknown error occurred' });
    }
  }
};

const fetchSubmittedAssets = async (filters: ResourceFilters) => {
  try {
    const url = `/api/assets/contracts/${props.contract.id}/submitted-assets`;
    const response = await get<Paginator<AssetList>>(url, filters);
    if (response.data) {
      submittedAssets.value = response.data;
    }
  } catch (err) {
    if (isAxiosError(err)) {
      error({ message: err.response?.data.message || 'An unknown error occurred' });
    }
  }
};

const handleAssetDeleted = () => {
  // Get active filters from cookie
  const activeFilters = $cookies?.get(unsubmittedAssetsCookie) as ResourceFilters ?? { page: 1, limit: 10 };
  fetchUnsubmittedAssets(activeFilters);
};

const handleAssetSubmitted = () => {
  // Refresh both listings when an asset is submitted
  const unsubmittedFilters = $cookies?.get(unsubmittedAssetsCookie) as ResourceFilters ?? { page: 1, limit: 10 };
  const submittedFilters = $cookies?.get(submittedAssetsCookie) as ResourceFilters ?? { page: 1, limit: 10 };

  fetchUnsubmittedAssets(unsubmittedFilters);
  fetchSubmittedAssets(submittedFilters);
};

const handleAssetWithdrawn = () => {
  // Refresh both listings when an asset is withdrawn
  const unsubmittedFilters = $cookies?.get(unsubmittedAssetsCookie) as ResourceFilters ?? { page: 1, limit: 10 };
  const submittedFilters = $cookies?.get(submittedAssetsCookie) as ResourceFilters ?? { page: 1, limit: 10 };

  fetchUnsubmittedAssets(unsubmittedFilters);
  fetchSubmittedAssets(submittedFilters);
};

onMounted(() => {
  // Initialize with default filters
  fetchUnsubmittedAssets({ page: 1, limit: 10 });
  fetchSubmittedAssets({ page: 1, limit: 10 });
});
</script>

<script lang="ts">
export default {
  name: 'ContractAssetsList',
};
</script>

<template>
  <div>
    <Box title="Μη Υποβεβλημένα Περιουσιακά Στοιχεία">
      <UnsubmittedAssetList
        :assets="unsubmittedAssets"
        :filterables="filterables"
        :sortables="sortables"
        :listables="listables"
        :active-filters-cookie-key="unsubmittedAssetsCookie"
        @fetch-data="fetchUnsubmittedAssets"
        @asset-submitted="handleAssetSubmitted"
        @asset-deleted="handleAssetDeleted"
      />
    </Box>

    <Box
      title="Υποβεβλημένα Περιουσιακά Στοιχεία"
      class="tw:mt-4"
    >
      <SubmittedAssetList
        :assets="submittedAssets"
        :filterables="filterables"
        :sortables="sortables"
        :listables="listables"
        :active-filters-cookie-key="submittedAssetsCookie"
        @fetch-data="fetchSubmittedAssets"
        @asset-withdrawn="handleAssetWithdrawn"
      />
    </Box>
  </div>
</template>
