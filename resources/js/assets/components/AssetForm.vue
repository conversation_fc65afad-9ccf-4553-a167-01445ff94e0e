<script setup lang="ts">
import { computed } from 'vue';

import type { AssetForm, FormOptions, User } from '@/assets/types';
import CancelButton from '@/shared/components/ui/Buttons/CancelButton.vue';
import SubmitButton from '@/shared/components/ui/Buttons/SubmitButton.vue';
import CurrencyField from '@/shared/components/ui/FormFields/CurrencyField.vue';
import DateField from '@/shared/components/ui/FormFields/DateField.vue';
import FormSection from '@/shared/components/ui/FormFields/FormSection.vue';
import NumberField from '@/shared/components/ui/FormFields/NumberField.vue';
import SelectFilterField from '@/shared/components/ui/FormFields/SelectfilterField.vue';
import TextField from '@/shared/components/ui/FormFields/TextField.vue';
import useForm from '@/shared/composables/useForm';
import { isAxiosError } from '@/shared/composables/useHttp';

const emit = defineEmits<{
  (e: 'success', data: {
    response: Record<string, unknown>,
    isEdit: boolean,
    context: { isContractContext: boolean, contractId?: number }
  }): void;
  (e: 'error', data: { message: string, error: unknown }): void;
  (e: 'cancel', data: { isContractContext: boolean, contractId?: number }): void;
}>();

const props = defineProps<{
  asset?: AssetForm;
  formOptions: FormOptions;
  contract?: {
    id: number;
    contract_number: string;
  };
  user: User;
}>();

// Initialize form with asset data, contract context, or default values
const form = useForm<AssetForm>({
  asset_category_id: props.asset?.asset_category_id ?? '',
  contract_id: props.asset?.contract_id ?? props.contract?.id ?? '',
  serial_number: props.asset?.serial_number ?? '',
  quantity: props.asset?.quantity ?? null,
  location: props.asset?.location ?? '',
  date_of_receipt: props.asset?.date_of_receipt ?? '',
  acquisition_cost: props.asset?.acquisition_cost ?? null,
  unit_id: props.asset?.unit_id ?? '',
});

const isEdit = computed(() => !!props.asset?.id);
const isContractContext = computed(() => !!props.contract);

// Determine the appropriate form URL based on context
const formUrl = computed(() => {
  if (isEdit.value) {
    return `/api/assets/asset/${props.asset?.id}`;
  }

  if (isContractContext.value) {
    return `/api/assets/contracts/${props.contract?.id}/asset`;
  }

  return '/api/assets/asset';
});

const formMethod = computed(() => (isEdit.value ? 'put' : 'post'));

const handleSubmit = async () => {
  try {
    const response = await form[formMethod.value](formUrl.value);
    emit('success', {
      response,
      isEdit: isEdit.value,
      context: {
        isContractContext: isContractContext.value,
        contractId: props.contract?.id,
      },
    });
  } catch (err) {
    let errorMessage = 'Παρουσιάστηκε σφάλμα κατά την αποθήκευση. Παρακαλώ προσπαθήστε ξανά.';
    if (isAxiosError(err)) {
      errorMessage = err.response?.data?.message || errorMessage;
    }
    emit('error', {
      message: errorMessage,
      error: err,
    });
  }
};

const handleCancel = () => {
  emit('cancel', {
    isContractContext: isContractContext.value,
    contractId: props.contract?.id,
  });
};
</script>

<script lang="ts">
export default {
  name: 'AssetForm',
};
</script>

<template>
  <form @submit.prevent="handleSubmit">
    <FormSection
      title="Βασικές Πληροφορίες"
      icon="fa-info-circle"
    >
      <div class="row">
        <div class="col-sm-6">
          <SelectFilterField
            v-model="form.asset_category_id"
            title="Περιγραφή Παγίου"
            name="asset_category_id"
            :options="formOptions.assetCategories"
            :error="form.errors.asset_category_id"
            required
            help-text="Η περιγραφή του παγίου (έως 50 χαρακτήρες) χρησιμοποιείται για την καταχώρηση σημαντικών στοιχείων που επιτρέπουν την διαφοροποίηση παγίων των ιδίων κατηγοριών (είδος παγίου π.χ. άρμα μάχης ή περιπολικό κλπ)."
          />
        </div>
        <div class="col-sm-6">
          <SelectFilterField
            v-model="form.contract_id"
            title="Αριθμός σύμβασης"
            name="contract_id"
            :options="formOptions.contracts"
            :error="form.errors.contract_id"
            :disabled="!!props.contract"
            required
          />
        </div>
      </div>
      <div class="row">
        <div class="col-sm-6">
          <TextField
            v-model="form.serial_number"
            title="Σειριακός Αριθμός"
            name="serial_number"
            :error="form.errors.serial_number"
            help-text="Ο μοναδικός αριθμός του Παγίου (έως 18 χαρακτήρες) αν υπάρχει (για τις κατηγορίες παγίων που είναι διαθέσιμος π.χ. σειριακός αριθμός Η/Υ, αριθμός πλαισίου οχήματος, κωδικός ΜΑΠ κλπ)."
            required
          />
        </div>
        <div class="col-sm-6">
          <NumberField
            v-model="form.quantity"
            title="Ποσότητα"
            name="quantity"
            :error="form.errors.quantity"
            required
            help-text="Ανάλογα με τον τρόπο αναγνώρισης του παγίου θα παρέχεται πληροφόρηση σχετικά με την τηρούμενη ποσότητα. Αν π.χ καταχωρίζονται Η/Υ βάσει συνολικού ποσού σύμβασης θα καταχωρίζεται ποσότητα μεγαλύτερη της μονάδας."
          />
        </div>
      </div>
    </FormSection>

    <FormSection
      title="Πληροφορίες Τοποθεσίας και Κόστους"
      icon="fa-map-marker"
    >
      <div class="row">
        <div class="col-sm-6">
          <TextField
            v-model="form.location"
            title="Εγκατάσταση"
            name="location"
            :error="form.errors.location"
            help-text="Η ακριβής τοποθεσία του παγίου. Η συγκεκριμένη οργανωτική δομή θα έρχεται και από το υποσύστημα των Προμηθειών (SAP MM), όταν αυτή τεθεί σε εφαρμογή."
            required
          />
        </div>
        <div class="col-sm-6">
          <DateField
            v-model="form.date_of_receipt"
            title="Ημερομηνία Κεφαλαιοποίησης Παγίου"
            name="date_of_receipt"
            :error="form.errors.date_of_receipt"
            required
            help-text="Η ημερομηνία κατά την οποία το πάγιο έρχεται στη κυριότητα της οντότητας αναφοράς βάσει όρων συναλλαγών της σχετικής σύμβασης (π.χ. ημερομηνία φυσικής παραλαβής) εφόσον είναι ανιχνέυσιμη, αλλιώς καταχωρίζεται ως Ημερομηνία Κεφαλαιοποίησης Παγίου η 01.01.2026."
          />
        </div>
      </div>
      <div class="row">
        <div class="col-sm-6">
          <CurrencyField
            v-model="form.acquisition_cost"
            title="Κόστος Απόκτησης ή Παραγωγής Παγίου"
            name="acquisition_cost"
            :error="form.errors.acquisition_cost"
            required
            help-text="Η αξία που προκύπτει από την επιμέτρηση."
          />
        </div>
        <div
          v-if="props.user.can.admin && formOptions.units"
          class="col-sm-6"
        >
          <SelectFilterField
            v-model="form.unit_id"
            title="Υπηρεσία"
            name="unit_id"
            :options="formOptions.units"
            :error="form.errors.unit_id"
            required
          />
        </div>
        <div
          v-else
          class="col-sm-6"
        >
          <dl>
            <dt class="tw:mb-2">
              Υπηρεσία
            </dt>
            <dd>{{ props.user.unit.name }}</dd>
          </dl>
        </div>
      </div>
    </FormSection>

    <hr>
    <div class="row mt-4">
      <div class="col-sm-6">
        <CancelButton @cancel="handleCancel">
          <i class="fa fa-arrow-left" /> Επιστροφή
        </CancelButton>
      </div>
      <div class="col-sm-6 text-right">
        <SubmitButton :busy="form.processing">
          <i class="fa fa-floppy-o" /> {{ isEdit ? 'Ενημέρωση' : 'Αποθήκευση' }}
        </SubmitButton>
      </div>
    </div>
  </form>
</template>
