<script setup lang="ts">
import { computed, useSlots } from 'vue';

import type { AssetDetail, AssetList } from '@/assets/types';
import BaseButton from '@/shared/components/ui/Buttons/BaseButton.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';
import useHttp, { isAxiosError } from '@/shared/composables/useHttp';
import useState from '@/shared/composables/useState';
import useSwal from '@/shared/composables/useSwal';

const props = withDefaults(defineProps<{
  asset: AssetDetail | AssetList;
  buttonVariation?: 'primary' | 'secondary' | 'outline' | 'plain' | 'destructive';
}>(), {
  buttonVariation: 'primary',
});

// Get slots and check if default slot has content
const slots = useSlots();
const hasDefaultSlot = computed(() => !!slots.default);

// Only apply custom variation when there's content in the slot
const buttonVariationToUse = computed(() => (hasDefaultSlot.value ? props.buttonVariation : 'secondary'));

const emit = defineEmits<{
  (e: 'submitted', assetId: number): void;
}>();

const { post } = useHttp();
const { success, error } = useSwal();

const [isOpen, setIsOpen] = useState(false);
const [isProcessing, setIsProcessing] = useState(false);

const openSubmitModal = () => {
  setIsOpen(true);
};

const cancelSubmit = () => {
  setIsOpen(false);
};

const confirmSubmit = async () => {
  const assetId = props.asset?.id;
  if (!assetId) {
    error({ message: 'Δεν βρέθηκε το αναγνωριστικό του περιουσιακού στοιχείου' });
    return;
  }

  setIsProcessing(true);
  try {
    const response = await post('/api/assets/submitted-assets', {
      asset_id: assetId,
    });
    setIsOpen(false);
    success({
      message: response.message || 'Το περιουσιακό στοιχείο υποβλήθηκε επιτυχώς!',
    }, () => {
      emit('submitted', assetId);
    });
  } catch (err) {
    if (isAxiosError(err)) {
      setIsOpen(false);
      error({ message: err.response?.data.message || 'Παρουσιάστηκε σφάλμα κατά την υποβολή' });
    }
  } finally {
    setIsProcessing(false);
  }
};
</script>

<script lang="ts">
export default {
  name: 'AssetSubmitConfirmationButton',
};
</script>

<template>
  <div>
    <BaseButton
      icon="tabler:arrow-down-to-arc"
      :icon-only="!hasDefaultSlot"
      :variation="buttonVariationToUse"
      title="Submit Asset"
      @click="openSubmitModal"
    >
      <slot />
    </BaseButton>

    <!-- Confirmation Modal -->
    <ConfirmationModal
      cancel-btn-text="Ακύρωση"
      confirm-btn-text="Υποβολή"
      :open="isOpen"
      :processing="isProcessing"
      @confirm="confirmSubmit"
      @cancel="cancelSubmit"
    >
      <div class="max-w-prose mx-auto">
        <p>
          Πρόκειται να υποβάλετε οριστικά αυτό το περιουσιακό στοιχείο.
          Μετά την υποβολή του δεν θα μπορείτε να το επεξεργαστείτε ή να το διαγράψετε.
          Θέλετε να συνεχίσετε;
        </p>
      </div>
    </ConfirmationModal>
  </div>
</template>

<style scoped>
.action-button-submit {
  background: none;
  color: inherit;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}

.action-button {
  color: var(--gray-600);
}

.action-button:hover {
  color: var(--brand-color);
}
</style>
