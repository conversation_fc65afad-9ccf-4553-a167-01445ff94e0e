/* eslint-disable @typescript-eslint/ban-ts-comment, no-new */
// @ts-nocheck

/**
 * Assets application main TypeScript file
 */

// Import dependencies
import '../bootstrap';

// Import components
import AssetListPage from '@/assets/Pages/AssetListPage.vue';
import AssetCreatePage from '@/assets/Pages/AssetCreatePage.vue';
import AssetEditPage from '@/assets/Pages/AssetEditPage.vue';
import AssetDetailPage from '@/assets/Pages/AssetDetailPage.vue';
import ContractListPage from '@/assets/Pages/ContractListPage.vue';
import ContractCreatePage from '@/assets/Pages/ContractCreatePage.vue';
import ContractEditPage from '@/assets/Pages/ContractEditPage.vue';
import ContractDetailPage from '@/assets/Pages/ContractDetailPage.vue';

import { Icon } from '@iconify/vue2';

// Register components globally
Vue.component('AssetListPage', AssetListPage);
Vue.component('AssetCreatePage', AssetCreatePage);
Vue.component('AssetEditPage', AssetEditPage);
Vue.component('AssetDetailPage', AssetDetailPage);
Vue.component('ContractListPage', ContractListPage);
Vue.component('ContractCreatePage', ContractCreatePage);
Vue.component('ContractEditPage', ContractEditPage);
Vue.component('ContractDetailPage', ContractDetailPage);

Vue.component('VIcon', Icon);

// Initialize Vue instance
const cookies = Vue.prototype.$cookies;

new Vue({
  el: '#app',
  provide: {
    $cookies: cookies,
  },
});
