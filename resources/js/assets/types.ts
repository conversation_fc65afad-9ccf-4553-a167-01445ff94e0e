/* eslint-disable camelcase */
import type { Auth, Ui } from '@/global';

export type Unit = {
  id: number;
  name: string;
}

export type UserAbilities = {
    [ability in Auth.Ability]?: boolean;
}

export type User = {
    id: number;
    name: string;
    unit: Unit;
    can: UserAbilities;
}

export type AssetList = {
    id: number;
    contract_id: number;
    serial_number: string;
    quantity: number;
    date_of_receipt: string;
    location: string;
    acquisition_cost: number;
    asset_category: string;
    contract: string;
    unit_abbrv: string;
    can?: UserAbilities;
}

export type AssetForm = {
    id?: number;
    contract_id: number | string;
    asset_category_id: number | string;
    serial_number: string;
    quantity: number | null;
    date_of_receipt: string;
    location: string;
    acquisition_cost: number | null;
    unit_id?: number | string;
}

export type AssetDetail = {
    id: number;
    serial_number: string;
    quantity: number;
    date_of_receipt: string;
    location: string;
    acquisition_cost: string;
    created_at: string;
    updated_at: string;
    submitted_at: string | null;
    status: string;
    contract: {
        id: number | null;
        contract_number: string;
    };
    asset_category: {
        id: number | null;
        code: string;
        description: string;
        measure_unit: string;
        duration_years: string | number;
    };
    user: User;
    unit: Unit;
    can: {
        update: boolean;
        delete: boolean;
        submit: boolean;
        withdraw: boolean;
    };
}

export type FormOptions = {
    assetCategories: Ui.SelectOption[];
    contracts: Ui.SelectOption[];
    units: Ui.SelectOption[];
}

export type ContractList = {
    id: number;
    contract_number: string;
    assets_count: number;
    can?: UserAbilities;
}

export type ContractForm = {
    id?: number;
    contract_number: string;
    can?: {
        admin?: boolean;
    };
}

export type ContractDetail = {
    id: number;
    contract_number: string;
    assets_count: number;
    assets?: AssetList[];
    can?: {
        update: boolean;
        delete: boolean;
    };
}
