<script setup lang="ts">
import { defineProps } from 'vue';

import AssetDeleteConfirmationButton from '@/assets/components/AssetDeleteConfirmationButton.vue';
import AssetSubmitConfirmationButton from '@/assets/components/AssetSubmitConfirmationButton.vue';
import AssetWithdrawConfirmationButton from '@/assets/components/AssetWithdrawConfirmationButton.vue';
import type { AssetDetail } from '@/assets/types';
import BaseButton from '@/shared/components/ui/Buttons/BaseButton.vue';
import useNavigation from '@/shared/composables/useNavigation';

// Define props for the component
defineProps<{
  asset: AssetDetail;
}>();

const { goBack } = useNavigation();

const handleAssetSubmitted = () => {
  // Redirect to the asset index page after submission
  window.location.href = '/assets/asset';
};

const handleAssetDeleted = () => {
  // Redirect to the asset index page after deletion
  window.location.href = '/assets/asset';
};

const handleAssetWithdrawn = () => {
  // Reload the page to reflect the updated status
  window.location.reload();
};
</script>

<script lang="ts">
export default {
  name: 'AssetDetailPage',
  components: { BaseButton },
};
</script>

<template>
  <div class="tw:bg-white tw:shadow-md tw:rounded-lg tw:overflow-hidden">
    <div class="tw:p-6">
      <div class="tw:mb-6">
        <h1 class="tw:text-2xl tw:font-bold tw:text-gray-800">
          Περιουσιακό Στοιχείο #{{ asset.id }}
        </h1>
      </div>

      <!-- 1. Στοιχεία σύμβασης -->
      <div class="tw:mt-8 tw:bg-gray-50 tw:p-6 tw:rounded-lg">
        <h2 class="tw:text-xl tw:font-semibold tw:text-gray-700 tw:mb-4">
          Στοιχεία σύμβασης
        </h2>
        <div class="tw:space-y-4">
          <div>
            <p class="tw:text-sm tw:text-gray-500">
              Αριθμός σύμβασης
            </p>
            <p class="tw:font-medium">
              {{ asset.contract.contract_number }}
            </p>
          </div>
        </div>
      </div>

      <!-- 2. Πληροφοριές παγίου -->
      <div class="tw:mt-8 tw:bg-gray-50 tw:p-6 tw:rounded-lg">
        <h2 class="tw:text-xl tw:font-semibold tw:text-gray-700 tw:mb-4">
          Πληροφοριές παγίου
        </h2>
        <div class="tw:grid tw:grid-cols-1 tw:md:grid-cols-2 tw:gap-4">
          <div>
            <p class="tw:text-sm tw:text-gray-500">
              Περιγραφή Παγίου
            </p>
            <p class="tw:font-medium">
              {{ asset.asset_category.description }} ({{ asset.asset_category.code }})
            </p>
          </div>
          <div>
            <p class="tw:text-sm tw:text-gray-500">
              Σειριακός αριθμός
            </p>
            <p class="tw:font-medium">
              {{ asset.serial_number }}
            </p>
          </div>
          <div>
            <p class="tw:text-sm tw:text-gray-500">
              Ποσότητα
            </p>
            <p class="tw:font-medium">
              {{ asset.quantity }} {{ asset.asset_category.measure_unit }}
            </p>
          </div>
          <div>
            <p class="tw:text-sm tw:text-gray-500">
              Ημερομηνία Κεφαλαιοποίησης Παγίου
            </p>
            <p class="tw:font-medium">
              {{ asset.date_of_receipt }}
            </p>
          </div>
          <div>
            <p class="tw:text-sm tw:text-gray-500">
              Εγκατάσταση
            </p>
            <p class="tw:font-medium">
              {{ asset.location }}
            </p>
          </div>
          <div>
            <p class="tw:text-sm tw:text-gray-500">
              Κόστος Απόκτησης ή Παραγωγής Παγίου
            </p>
            <p class="tw:font-medium">
              {{ asset.acquisition_cost }}
            </p>
          </div>
        </div>
      </div>

      <!-- 3. Στοιχεία καταχώρησης -->
      <div class="tw:mt-8 tw:bg-gray-50 tw:p-6 tw:rounded-lg">
        <h2 class="tw:text-xl tw:font-semibold tw:text-gray-700 tw:mb-4">
          Στοιχεία καταχώρησης
        </h2>
        <!-- First row: User and Unit -->
        <div class="tw:grid tw:grid-cols-1 tw:md:grid-cols-2 tw:gap-4 tw:mb-4">
          <div>
            <p class="tw:text-sm tw:text-gray-500">
              Καταχωρήθηκε από
            </p>
            <p class="tw:font-medium">
              {{ asset.user.name }}
            </p>
          </div>
          <div>
            <p class="tw:text-sm tw:text-gray-500">
              Υπηρεσία
            </p>
            <p class="tw:font-medium">
              {{ asset.unit.name }}
            </p>
          </div>
        </div>

        <!-- Second row: Status and Date -->
        <div class="tw:grid tw:grid-cols-1 tw:md:grid-cols-2 tw:gap-4">
          <div>
            <p class="tw:text-sm tw:text-gray-500">
              Κατάσταση
            </p>
            <p class="tw:font-medium">
              <span
                class="tw:inline-block tw:px-3 tw:py-1 tw:text-sm tw:font-semibold tw:rounded-full"
                :class="{
                  'tw:bg-green-100 tw:text-green-800': asset.submitted_at,
                  'tw:bg-yellow-100 tw:text-yellow-800': !asset.submitted_at
                }"
              >
                {{ asset.submitted_at ? 'Υποβλήθηκε' : 'Πρόχειρο' }}
              </span>
            </p>
          </div>
          <div>
            <p class="tw:text-sm tw:text-gray-500">
              {{ asset.submitted_at ? 'Ημερομηνία Υποβολής' : 'Τελευταία ενημέρωση' }}
            </p>
            <p class="tw:font-medium">
              {{ asset.submitted_at || asset.updated_at }}
            </p>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="tw:mt-8 tw:flex tw:flex-wrap tw:gap-4">
        <!-- Go Back Button (always visible) -->
        <BaseButton
          variation="secondary"
          icon="tabler:arrow-left"
          @click="goBack"
        >
          Επιστροφή
        </BaseButton>

        <!-- Buttons for unsubmitted assets -->
        <template v-if="!asset.submitted_at">
          <!-- Edit Button -->
          <BaseButton
            v-if="asset.can.update"
            as="a"
            icon="tabler:edit"
            :href="`/assets/asset/${asset.id}/edit`"
          >
            Επεξεργασία
          </BaseButton>

          <!-- Delete Button -->
          <AssetDeleteConfirmationButton
            v-if="asset.can.delete"
            :asset="asset"
            @deleted="handleAssetDeleted"
          >
            Επιβεβαίωση Διαγραφής
          </AssetDeleteConfirmationButton>

          <!-- Submit Button -->
          <AssetSubmitConfirmationButton
            v-if="asset.can.submit"
            :asset="asset"
            @submitted="handleAssetSubmitted"
          >
            Επιβεβαίωση Υποβολής
          </AssetSubmitConfirmationButton>
        </template>

        <!-- Buttons for submitted assets -->
        <template v-else>
          <!-- Withdraw Button -->
          <AssetWithdrawConfirmationButton
            v-if="asset.can.withdraw"
            :asset="asset"
            @withdrawn="handleAssetWithdrawn"
          >
            Επιβεβαίωση Απόσυρσης
          </AssetWithdrawConfirmationButton>
        </template>
      </div>
    </div>
  </div>
</template>
