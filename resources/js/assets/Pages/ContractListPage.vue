<script setup lang="ts">
import { inject, onMounted, ref } from 'vue';
import type { VueCookies } from 'vue-cookies';

import ContractDeleteConfirmationButton from '@/assets/components/ContractDeleteConfirmationButton.vue';
import type { ContractList } from '@/assets/types';
import type { Paginator, ResourceFilters } from '@/global';
import Box from '@/shared/components/ui/Boxes/Box.vue';
import BaseButton from '@/shared/components/ui/Buttons/BaseButton.vue';
import ResourceViewer from '@/shared/components/ui/Data/ResourceViewer.vue';
import useHttp, { isAxiosError } from '@/shared/composables/useHttp';
import useNavigation from '@/shared/composables/useNavigation';
import useSwal from '@/shared/composables/useSwal';

const filterables = [
  {
    label: 'Αριθμός Σύμβασης',
    field: 'contract_number',
    type: 'string',
  },
];

const sortables = [
  { label: 'Αριθμός Σύμβασης', field: 'contract_number' },
  { label: 'Πάγια', field: 'assets_count' },
];

const listables = [
  { label: 'Αριθμός Σύμβασης', field: 'contract_number' },
  { label: 'Πάγια', field: 'assets_count' },
  { label: '⚙ Ενέργειες', field: 'actions' },
];

const contracts = ref<Paginator<ContractList> | {
  data: [],
  meta: Record<string, never>,
  links: Record<string, never>,
}>({
  data: [],
  meta: {},
  links: {},
});

const { get } = useHttp();
const { redirectTo } = useNavigation();
const { error } = useSwal();

const $cookies = inject<VueCookies>('$cookies');
const contractsCookieKey = 'assets_contracts';

const getActiveFilters = () => $cookies?.get(contractsCookieKey) as ResourceFilters ?? null;

const fetchContracts = async (filters: ResourceFilters) => {
  try {
    const response = await get<Paginator<ContractList>>('/api/assets/contract', filters);
    if (response.data) {
      contracts.value = response.data;
    }
  } catch (err) {
    if (isAxiosError(err)) {
      error({ message: err.response?.data.message || 'An unknown error occurred' });
    }
  }
};

const handleContractDeleted = () => {
  // Refresh the contracts list
  const activeFilters = getActiveFilters();
  fetchContracts(activeFilters ?? {
    page: 1,
    limit: 10,
  });
};

onMounted(() => {
  const activeFilters = getActiveFilters();
  fetchContracts(activeFilters ?? {
    page: 1,
    limit: 10,
  });
});
</script>

<script lang="ts">
export default {
  name: 'ContractListPage',
};
</script>

<template>
  <div>
    <Box title="Συμβάσεις">
      <ResourceViewer
        :paginated-data="contracts"
        :filterables="filterables"
        :sortables="sortables"
        :listables="listables"
        :filter-cookie="contractsCookieKey"
        @fetch-data="fetchContracts"
      >
        <template #default="{ tableData }">
          <div
            v-if="tableData.column.field === 'actions'"
            class="action-buttons pull-right"
          >
            <BaseButton
              as="a"
              icon="tabler:info-circle"
              icon-only
              title="View Details"
              :href="`/assets/contract/${tableData.row.id}`"
            />
            <BaseButton
              v-if="tableData.row.can?.update"
              as="a"
              icon="tabler:edit"
              icon-only
              title="Edit Contract"
              :href="`/assets/contract/${tableData.row.id}/edit`"
            />
            <ContractDeleteConfirmationButton
              v-if="tableData.row.assets_count === 0 && tableData.row.can?.delete"
              :contract="tableData.row"
              icon-only
              @deleted="handleContractDeleted"
            />
          </div>
        </template>
      </ResourceViewer>
    </Box>
  </div>
</template>

<style scoped>
.action-buttons {
  display: flex;
  justify-content: space-around;
  gap: 10px;
}

.action-button {
  color: var(--gray-600);
}

.action-button:hover {
  color: var(--brand-color);
}
</style>
