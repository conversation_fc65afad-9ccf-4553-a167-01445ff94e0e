<script setup lang="ts">
import { defineProps } from 'vue';

import ContractAssetsList from '@/assets/components/ContractAssetsList.vue';
import ContractDeleteConfirmationButton from '@/assets/components/ContractDeleteConfirmationButton.vue';
import type { ContractDetail } from '@/assets/types';
import BaseButton from '@/shared/components/ui/Buttons/BaseButton.vue';
import useNavigation from '@/shared/composables/useNavigation';

// Define props for the component
const props = defineProps<{
  contract: ContractDetail;
}>();

const { goBack } = useNavigation();

const handleContractDeleted = () => {
  window.location.href = '/assets/contract';
};
</script>

<script lang="ts">
export default {
  name: 'ContractDetailPage',
  components: { BaseButton, ContractDeleteConfirmationButton, ContractAssetsList },
};
</script>

<template>
  <div>
    <div class="tw:bg-white tw:shadow-md tw:rounded-lg tw:overflow-hidden">
      <div class="tw:p-6">
        <div class="tw:mb-6">
          <h1 class="tw:text-2xl tw:font-bold tw:text-gray-800">
            Σύμβαση #{{ contract.id }}
          </h1>
        </div>

        <!-- 1. Στοιχεία σύμβασης -->
        <div class="tw:mt-8 tw:bg-gray-50 tw:p-6 tw:rounded-lg">
          <h2 class="tw:text-xl tw:font-semibold tw:text-gray-700 tw:mb-4">
            Στοιχεία σύμβασης
          </h2>
          <div class="tw:grid tw:grid-cols-1 tw:md:grid-cols-2 tw:gap-4">
            <div>
              <p class="tw:text-sm tw:text-gray-500">
                Αριθμός σύμβασης
              </p>
              <p class="tw:font-medium">
                {{ contract.contract_number }}
              </p>
            </div>
            <div>
              <p class="tw:text-sm tw:text-gray-500">
                Πλήθος περιουσιακών στοιχείων
              </p>
              <p class="tw:font-medium">
                {{ contract.assets_count }}
              </p>
            </div>
          </div>
        </div>

        <!-- 2. Στοιχεία καταχώρησης -->
        <div class="tw:mt-8 tw:bg-gray-50 tw:p-6 tw:rounded-lg">
          <h2 class="tw:text-xl tw:font-semibold tw:text-gray-700 tw:mb-4">
            Στοιχεία καταχώρησης
          </h2>
          <div class="tw:grid tw:grid-cols-1 tw:md:grid-cols-2 tw:gap-4">
            <div>
              <p class="tw:text-sm tw:text-gray-500">
                Ημερομηνία δημιουργίας
              </p>
              <p class="tw:font-medium">
                {{ contract.created_at }}
              </p>
            </div>
            <div>
              <p class="tw:text-sm tw:text-gray-500">
                Τελευταία ενημέρωση
              </p>
              <p class="tw:font-medium">
                {{ contract.updated_at }}
              </p>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="tw:mt-8 tw:flex tw:flex-wrap tw:gap-4">
          <!-- Go Back Button (always visible) -->
          <BaseButton
            variation="secondary"
            icon="tabler:arrow-left"
            class="tw:mr-auto"
            @click="goBack"
          >
            Επιστροφή
          </BaseButton>

          <!-- Edit Button -->
          <BaseButton
            v-if="contract.can?.update"
            as="a"
            icon="tabler:edit"
            :href="`/assets/contract/${contract.id}/edit`"
          >
            Επεξεργασία
          </BaseButton>

          <!-- Create Asset Button -->
          <BaseButton
            as="a"
            icon="tabler:plus"
            variation="primary"
            :href="`/assets/contract/${contract.id}/asset/create`"
          >
            Δημιουργία Περιουσιακού Στοιχείου
          </BaseButton>

          <!-- Delete Button -->
          <ContractDeleteConfirmationButton
            v-if="contract.can?.delete"
            :contract="contract"
            @deleted="handleContractDeleted"
          >
            Διαγραφή
          </ContractDeleteConfirmationButton>
        </div>
      </div>
    </div>
    <!-- 3. Συνδεδεμένα περιουσιακά στοιχεία -->
    <div class="tw:mt-8">
      <ContractAssetsList :contract="contract" />
    </div>
  </div>
</template>
