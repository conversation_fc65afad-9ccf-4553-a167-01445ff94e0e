<script setup lang="ts">
import AssetForm from '@/assets/components/AssetForm.vue';
import type { AssetForm as AssetFormType, FormOptions, User } from '@/assets/types';
import Box from '@/shared/components/ui/Boxes/Box.vue';
import useNavigation from '@/shared/composables/useNavigation';
import useSwal from '@/shared/composables/useSwal';

defineProps<{
  formOptions: FormOptions;
  asset: AssetFormType;
  user: User;
}>();

const { success, error } = useSwal();
const { redirectTo } = useNavigation();

const handleSuccess = (data: {
  response: Record<string, unknown>,
  isEdit: boolean,
  context: { isContractContext: boolean, contractId?: number }
}) => {
  success({
    message: data.response.message as string,
    confirmButtonText: 'Επιστροφή στη λίστα',
  }, () => {
    if (data.context.isContractContext) {
      redirectTo(`/assets/contract/${data.context.contractId}`);
    } else {
      redirectTo('/assets/asset');
    }
  });
};

const handleError = (data: { message: string, error: unknown }) => {
  error({
    message: data.message,
  });
};

const handleCancel = (data: { isContractContext: boolean, contractId?: number }) => {
  if (data.isContractContext) {
    redirectTo(`/assets/contract/${data.contractId}`);
  } else {
    redirectTo('/assets/asset');
  }
};
</script>

<script lang="ts">
export default {
  name: 'AssetEditPage',
};
</script>

<template>
  <div>
    <Box title="Επεξεργασία Περιουσιακού Στοιχείου">
      <AssetForm
        :asset="asset"
        :form-options="formOptions"
        :user="user"
        @success="handleSuccess"
        @error="handleError"
        @cancel="handleCancel"
      />
    </Box>
  </div>
</template>
