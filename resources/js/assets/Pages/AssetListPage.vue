<script setup lang="ts">
import { inject, onMounted, ref } from 'vue';
import type { VueCookies } from 'vue-cookies';

import SubmittedAssetList from '@/assets/components/SubmittedAssetList.vue';
import UnsubmittedAssetList from '@/assets/components/UnsubmittedAssetList.vue';
import type { AssetList } from '@/assets/types';
import type {
  Paginator, ResourceFilters,
} from '@/global';
import Box from '@/shared/components/ui/Boxes/Box.vue';
import useHttp, { isAxiosError } from '@/shared/composables/useHttp';
import useSwal from '@/shared/composables/useSwal';

defineProps<{
  filterOptions: {
    [key: string]: {id: number, name: string}[];
  };
}>();

const filterables = [
  {
    label: 'Σειριακός Αριθμός',
    field: 'serial_number',
    type: 'string',
  },
  {
    label: 'Εγκατάσταση',
    field: 'location',
    type: 'string',
  },
  {
    label: 'Ημερομηνία Κεφαλαιοποίησης',
    field: 'date_of_receipt',
    type: 'date',
  },
];

const sortables = [
  { label: 'Σειριακός Αριθμός', field: 'serial_number' },
  { label: 'Εγκατάσταση', field: 'location' },
  { label: 'Ημερομηνία Κεφαλαιοποίησης', field: 'date_of_receipt' },
];

const listables = [
  { label: 'Σειριακός Αριθμός', field: 'serial_number' },
  { label: 'Κατηγορία', field: 'asset_category' },
  { label: 'Σύμβαση', field: 'contract' },
  { label: 'Ποσότητα', field: 'quantity' },
  { label: 'Εγκατάσταση', field: 'location' },
  { label: 'Ημερομηνία Κεφαλαιοποίησης', field: 'date_of_receipt' },
  { label: 'Κόστος Απόκτησης', field: 'acquisition_cost' },
  { label: 'Υπηρεσία', field: 'unit_abbrv' },
  { label: '⚙ Ενέργειες', field: 'actions' },
];

const unsubmittedAssets = ref<Paginator<AssetList> | {
  data: [],
  meta: Record<string, never>,
  links: Record<string, never>,
}>({
  data: [],
  meta: {},
  links: {},
});

const submittedAssets = ref<Paginator<AssetList> | {
  data: [],
  meta: Record<string, never>,
  links: Record<string, never>,
}>({
  data: [],
  meta: {},
  links: {},
});

const { get } = useHttp();

const { error } = useSwal();

const $cookies = inject<VueCookies>('$cookies');

const unsubmittedAssetsCookie = 'assets_unsubmitted_assets';
const submittedAssetsCookie = 'assets_submitted_assets';

const getUnsubmittedAssetsActiveFilters = () => $cookies?.get(unsubmittedAssetsCookie) as ResourceFilters ?? null;
const getSubmittedAssetsActiveFilters = () => $cookies?.get(submittedAssetsCookie) as ResourceFilters ?? null;

const fetchUnsubmittedAssets = async (filters: ResourceFilters) => {
  try {
    const response = await get<Paginator<AssetList>>('/api/assets/unsubmitted-assets', filters);
    if (response.data) {
      unsubmittedAssets.value = response.data;
    }
  } catch (err) {
    if (isAxiosError(err)) {
      error({ message: err.response?.data.message || 'An unknown error occurred' });
    }
  }
};

const fetchSubmittedAssets = async (filters: ResourceFilters) => {
  try {
    const response = await get<Paginator<AssetList>>('/api/assets/submitted-assets', filters);
    if (response.data) {
      submittedAssets.value = response.data;
    }
  } catch (err) {
    if (isAxiosError(err)) {
      error({ message: err.response?.data.message || 'An unknown error occurred' });
    }
  }
};

const handleAssetDeleted = () => {
  const activeFilters: ResourceFilters = getUnsubmittedAssetsActiveFilters();

  fetchUnsubmittedAssets(activeFilters ?? {
    page: 1,
    limit: 10,
  });
};

const handleAssetSubmitted = () => {
  // Refresh both listings when an asset is submitted
  const unsubmittedFilters: ResourceFilters = getUnsubmittedAssetsActiveFilters();
  const submittedFilters: ResourceFilters = getSubmittedAssetsActiveFilters();

  fetchUnsubmittedAssets(unsubmittedFilters ?? {
    page: 1,
    limit: 10,
  });

  fetchSubmittedAssets(submittedFilters ?? {
    page: 1,
    limit: 10,
  });
};

const handleAssetWithdrawn = () => {
  // Refresh both listings when an asset is withdrawn
  const unsubmittedFilters: ResourceFilters = getUnsubmittedAssetsActiveFilters();
  const submittedFilters: ResourceFilters = getSubmittedAssetsActiveFilters();

  fetchUnsubmittedAssets(unsubmittedFilters ?? {
    page: 1,
    limit: 10,
  });

  fetchSubmittedAssets(submittedFilters ?? {
    page: 1,
    limit: 10,
  });
};

onMounted(() => {
  const unsubmittedFilters = getUnsubmittedAssetsActiveFilters();
  const submittedFilters = getSubmittedAssetsActiveFilters();

  fetchUnsubmittedAssets(unsubmittedFilters ?? {
    page: 1,
    limit: 10,
  });

  fetchSubmittedAssets(submittedFilters ?? {
    page: 1,
    limit: 10,
  });
});
</script>

<script lang="ts">
export default {
  name: 'AssetListPage',
};
</script>

<template>
  <div>
    <Box title="Μη Υποβεβλημένα Περιουσιακά Στοιχεία ">
      <UnsubmittedAssetList
        :assets="unsubmittedAssets"
        :filterables="filterables"
        :sortables="sortables"
        :listables="listables"
        :active-filters-cookie-key="unsubmittedAssetsCookie"
        @fetch-data="fetchUnsubmittedAssets"
        @asset-submitted="handleAssetSubmitted"
        @asset-deleted="handleAssetDeleted"
      />
    </Box>
    <Box title="Υποβεβλημένα Περιουσιακά Στοιχεία">
      <SubmittedAssetList
        :assets="submittedAssets"
        :filterables="filterables"
        :sortables="sortables"
        :listables="listables"
        :active-filters-cookie-key="submittedAssetsCookie"
        @fetch-data="fetchSubmittedAssets"
        @asset-withdrawn="handleAssetWithdrawn"
      />
    </Box>
  </div>
</template>
