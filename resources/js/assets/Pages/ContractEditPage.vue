<script setup lang="ts">
import ContractForm from '@/assets/components/ContractForm.vue';
import type { ContractForm as ContractFormType } from '@/assets/types';
import Box from '@/shared/components/ui/Boxes/Box.vue';
import useNavigation from '@/shared/composables/useNavigation';
import useSwal from '@/shared/composables/useSwal';

defineProps<{
  contract: ContractFormType;
}>();

const { success, error } = useSwal();
const { redirectTo } = useNavigation();

const handleSuccess = (data: { response: Record<string, unknown>, isEdit: boolean }) => {
  success({
    message: data.response.message as string,
    confirmButtonText: 'Return to list',
  }, () => {
    redirectTo('/assets/contract');
  });
};

const handleError = (data: { message: string, error: unknown }) => {
  error({
    message: data.message,
  });
};

const handleCancel = () => {
  redirectTo('/assets/contract');
};
</script>

<script lang="ts">
export default {
  name: 'ContractEditPage',
};
</script>

<template>
  <Box title="Επεξεργασία Σύμβασης">
    <ContractForm
      :contract="contract"
      @success="handleSuccess"
      @error="handleError"
      @cancel="handleCancel"
    />
  </Box>
</template>
