<template>
  <div class="box box-primary">
    <div class="box-header">
      <h2 class="box-title">{{ user.name }}</h2>
    </div>
    <div class="box-body">
      <div v-for="app in apps" :key="app.id">
        <div class="info-box">
          <span class="info-box-icon" :style="getAppColor(app)">
            <i :class="['fa', app.icon]"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">{{ app.name }}</span>
            <div class="managed-application">
              <div class="managed-application-roles">
                <el-radio-group v-model="userManagedRoles[app.id]" size="small" :disabled="appHasUnmanagedRoles(app)">
                  <el-radio-button
                      v-for="role in app.managed_roles" :key="role.id"
                      :label="role.id"
                  >
                    {{ role.description }}
                  </el-radio-button>
                </el-radio-group>
              </div>

              <div class="managed-application-roles-remove-btn">
                <span v-if="appHasUnmanagedRoles(app)">
                  <i class="fa fa-warning"></i> Υπάρχουν μη επεξεργάσιμα δικαιώματα
                </span>
                <button v-show="userManagedRoles[app.id]" class="btn btn-danger btn-sm btn-outline clear-roles-button"
                        @click="clearRoles(app.id)">
                  Αφαίρεση Ρόλου
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="user-info-callout" v-if="Object.entries(userUnmanagedRoles).length !== 0 ">
        <p style="font-weight: 600"><i class="fa fa-warning"></i> Μη επεξεργάσιμα δικαιώματα</p>
        <p>Στο χρήστη {{user.name}} εχουν χορηγηθεί επιπλέον τα παρκάτω μη επεξεργάσιμα δικαιώματα:</p>
        <ul class="list-group">
          <li class="list-group-item" v-for="unmanagableRole in userUnmanageableRoles">
            Εφαρμογή {{ unmanagableRole.app ? unmanagableRole.app.name : 'apptree' }} - {{ unmanagableRole.description
            }}
          </li>
        </ul>
        Για την αλλαγή τους παρακαλώ όπως αποστείλετε ticket στην ΔΗΔ κάνοντας κλικ
        <a href="http://support.culture.gr/index.php?_m=tickets&_a=submit&departmantid=24" target="_blank">εδώ</a>
        (Τμήμα: <strong>apptree</strong>)
      </div>
    </div>
    <div class="box-footer">
      <div class="row">
        <div class="col-sm-3">
          <button class="btn btn-block btn-default" onclick="window.history.back();">
            <i class="fa fa-arrow-left"></i> Επιστροφή
          </button>
        </div>
        <div class="col-sm-3">
          <button class="btn btn-block btn-default" onclick="window.location.reload();">
            <i class="fa fa-refresh"></i> Επαναφορά
          </button>
        </div>
        <div class="col-sm-6">
          <button class="btn btn-block btn-primary" @click="updateUserRoles">
            <i class="fa fa-send"></i> Ενημέρωση
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import Box from "../../shared/components/ui/Boxes/Box";

  export default {
    name: "UserEdit",
    props: {
      userData: {
        type: Object,
        required: true
      },
      managedAppsData: {
        type: Array,
        required: true
      },
      managedRolesData: {
        type: Array,
        required: true
      }
    },
    components: {
      Box
    },
    data() {
      return {
        user: this.userData,
        apps: this.managedAppsData,
        userManagedRoles: {},
        userUnmanagedRoles: {}
      }
    },
    computed: {
      userUnmanageableRoles: function () {
        return this.user.roles.filter(role => !this.managedRolesData.includes(role.id))
      }
    },
    created() {
      this.userManagedRoles = this.user.roles
        .filter(role => this.managedRolesData.includes(role.id))
        .reduce((acc, role) => {
          acc[role.app.id] = role.id;
          return acc;
        }, {});

      this.userUnmanagedRoles = this.user.roles
        .filter(role => !this.managedRolesData.includes(role.id))
        .reduce((acc, role) => {
          acc[(role.app ? role.app.id : 'apptree')] = role.id;
          return acc;
        }, {});
    },
    methods: {
      updateUserRoles() {
        this.$http.put(`/roleManagement/users/${this.user.id}`, {...this.userManagedRoles, ...this.userUnmanagedRoles})
          .then(res => swal({
            type: 'success',
            title: `Επιτυχία!`,
            text: res.data.message,
            timer: 3000,
            showConfirmButton: false,
          }, function () {
            window.location.href = `/roleManagement/users`
          }))
          .catch(err => console.log(err))
      },
      clearRoles(appId) {
        this.$delete(this.userManagedRoles, appId);
      },
      getAppColor(app) {
        return {
          backgroundColor: app ? app.color : 'var(--gray-500)'
        }
      },
      appHasUnmanagedRoles(app) {
        if (app.id in this.userUnmanagedRoles) {
          return true;
        } else {
          return false
        }
      }
    }
  }
</script>

<style scoped>
  .user-info-callout {
    border: 1px solid var(--blue-800);
    background-color: var(--blue-100);
    color: var(--blue-800);
    padding: 1rem;
    border-radius: 3px;
  }

  .box-body .info-box {
    background-color: var(--gray-100);
  }

  .box-body .info-box .info-box-icon {
    background-color: var(--gray-400);
  }

  .managed-application {
    display: flex;
    justify-content: space-between;
    margin-top: 0.75rem;
    align-items: last baseline;
    height: 43px;
  }

  .info-box-text {
    font-weight: 600;
    color: var(--gray-700);
  }
</style>
