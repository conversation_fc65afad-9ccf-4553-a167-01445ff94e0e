<template>
  <div class="row">
    <div class="col-sm-12">
      <div class="box box-primary">
        <div class="box-header">
          <h3 class="box-title">
            Λίστα Χρηστών
          </h3>
        </div>
        <div class="box-body">
          <DataViewer
            :listables="listables"
            :items="users"
          >
            <template #default="{ tableData }">
              <div v-if="tableData.column.field === 'roles'">
                <div
                  v-for="role in tableData.row.roles"
                  :key="role.id"
                  class="application-role-box"
                >
                  <div
                    class="application-icon"
                    :style="getAppColor(role.app)"
                  >
                    <i
                      v-if="role.app"
                      :class="['fa','fa-2x', role.app.icon]"
                    />
                    <svg
                      v-else
                      width="32"
                      height="32"
                      viewBox="0 0 512 512"
                      style="margin-top: 10px"
                    >
                      <path
                        fill="#4a5568"
                        d="M26.55,161.483c-10.174,10.644-12.44,28.681-5.259,41.792c7.5,13.69,22.881,19.797,39.508,17.195
                    c6.013-0.94,13.33-0.338,18.672,2.325c33.31,16.599,64.187,36.71,90.597,63.428c4.705,4.76,8.87,9.81,12.616,15.087
                    c-23.609-15.57-51.257-24.437-80.722-25.114c-1.09-0.026-2.037,0.16-2.912,0.437c-1.088-2.343-2.495-4.515-4.187-6.475
                    c0.16-2.11-0.602-4.315-2.733-6.21c-14.427-12.819-37.201-12.467-50.214,2.373c-12.219,13.935-8.917,36.902,4.751,48.505
                    c14.208,12.062,36.906,8.229,48.337-5.704c3.831-4.667,5.97-10.188,6.641-15.793c0.109,0.008,0.204,0.037,0.316,0.04
                    c39.42,0.908,76.257,18.509,101.24,48.757c1.009,2.81,1.985,5.63,2.897,8.477c1.847,5.768,3.483,11.602,5.042,17.461
                    c-12.874-6.396-25.744-12.786-38.618-19.178c-0.189-0.095-0.379-0.135-0.568-0.216c1.144-6.595-0.059-13.599-3.855-19.338
                    c-0.452-0.684-0.966-1.237-1.505-1.733c-0.451-0.813-1.039-1.604-1.884-2.344c-10.112-8.811-28.565-10.622-37.734,1.046
                    c-8.074,10.273-9.962,27.141,0.9,36.615c9.905,8.632,25.305,10.211,35.621,1.163c0.13-0.117,0.243-0.244,0.371-0.361
                    c17.266,8.571,34.53,17.145,51.796,25.72c0.291,0.146,0.576,0.219,0.863,0.328c1.191,5.112,2.361,10.225,3.534,15.341
                    c0.634,28.908-3.064,57.692-10.757,85.827c-1.126,4.121-0.419,7.653,1.33,10.425c0.103,1.995,2.398,5.391,4.22,6.035
                    c21.562,7.612,43.297,4.525,64.859-0.226c0.842-0.186,1.852-1.47,2.58-2.956c3.582-2.893,5.488-7.517,3.36-13.351
                    c-9.202-25.237-16.013-51.085-20.126-77.537c1.644-8.618,6.198-16.926,10.01-25.223c46.658-3.164,89.635-30.472,111.951-71.98
                    c7.069,1.738,14.678,1.28,21.845-2.012c14.397-6.61,21.879-20.185,21.245-35.106c0.729-2.179,0.612-4.624-0.85-6.879
                    c-0.029-0.14-0.044-0.282-0.073-0.419c-0.557-2.585-1.967-4.206-3.698-5.15c-5.221-10.423-15.282-17.124-27.446-16.763
                    c-3.346,0.098-6.563,0.921-9.544,2.28c-3.961,0.397-7.896,1.607-11.689,3.881c-13.905,8.334-21.544,27.582-12.974,42.548
                    c2.133,3.717,4.865,6.974,8.003,9.696c-18.453,33.499-51.205,56.02-88.319,61.623c22.011-45.115,51.873-84.528,89.944-117.988
                    c13.771-12.102,27.389-24.302,40.904-36.371c4.625-1.72,9.321-3.068,14.103-3.99c24.443,18.49,37.616,20.378,52.178,9.146
                    c12.911-9.958,17.298-27.253,10.786-42.521c-6.275-14.719-21.776-22.781-38.885-20.231c-16.602,2.475-24.99,13.104-28.31,36.961
                    c-3.438,0.71-6.826,1.654-10.192,2.707c-3.969-18.609-7.937-37.219-11.905-55.829c1.96-1.866,4.504-3.695,6.497-5.04
                    c12.521-8.465,19.024-22.644,16.081-36.673c-3.291-15.666-13.709-25.646-29.48-28.237c-14.149-2.325-27.836,4.005-35.288,16.313
                    c-11.883,19.631-2.602,45.746,19.499,51.613c1.986,0.529,3.658,1.181,5.138,1.932c3.812,17.882,7.627,35.763,11.438,53.644
                    c-2.396,10.575-8.323,19.966-18.264,28.277c-12.63,10.558-23.431,23.289-35.846,34.134c-3.601,3.146-7.372,4.665-10.669,4.653
                    c-1.849-3.87-3.696-7.739-5.543-11.609c11.729-7.193,19.083-22.01,14.455-35.722c-5.182-15.353-20.574-28.562-37.617-27.367
                    c-6.198-0.331-12.547,0.856-18.194,3.655c-14.324,7.099-21.652,23.271-18.1,39.949c3.695,17.348,15.666,24.829,44.212,27.278
                    c0.762,1.589,1.52,3.182,2.281,4.774c1.757,9.43,0.977,18.796-5.211,26.678c-14.806,18.857-29.76,37.73-46.205,55.129
                    c-13.421,14.202-24.222,12.227-35.697-3.891c-4.627-6.504-8.672-13.22-12.198-20.122c9.407-5.899,16.692-14.691,20.529-25.292
                    c7.498,0.112,15.034-2.475,19.833-8.17c5.34-6.333,7.807-14.263,6.593-21.761c-0.324-9.905-6.362-19.248-16.536-22.973
                    c-11.777-4.312-28.343,0.146-33.966,12.255c-5.622,12.105-3.582,27.155,7.648,35.405c-2.299,5.899-6.137,10.99-11.161,14.685
                    c-6.65-17.293-10.421-35.572-11.891-54.543c-0.647-8.403-1.241-16.836-1.657-25.271c0.474-0.193,0.952-0.441,1.433-0.776
                    c32.314-22.403,67.308-43.159,97.061-68.955c22.793-19.762,40.415-43.597,43.961-73.893c24.881-2.059,49.566-23.463,45.623-47.487
                    c0.995-4.115,0.558-8.477-2.044-11.953c-1.734-2.321-3.729-4.548-5.882-6.661c-0.423-0.579-0.933-1.107-1.596-1.534
                    C359.339,2.754,341.294-4.153,324.852,2.76c-19.801,8.327-33.342,25.53-32.435,47.773c0.212,5.167,2.271,9.11,5.266,11.865
                    c2.146,3.854,5.141,7.506,9.084,10.808c4.609,3.859,9.836,6.296,15.319,7.602c-3.92,34.301-30.099,59.105-57.003,78.254
                    c-23.911,17.018-48.166,33.565-72.304,50.262c-0.095-6.653-0.032-13.305,0.296-19.933c0.589-12.033,4.532-22.753,9.98-32.821
                    c8.65-9.157,17.302-18.318,25.952-27.476c28.896,2.703,39.931-2.249,46.573-19.532c6.086-15.833,0.289-33.692-13.824-42.576
                    c-13.888-8.746-31.904-6.935-43.582,4.376c-13.629,13.202-14.059,29.007-1.393,50.678c-5.805,6.147-11.613,12.295-17.418,18.442
                    c-3.739,1.505-7.743,1.737-12.521,0.834c-13.285-7.772-27.29-13.967-41.935-18.522c-0.776-3.206-1.621-6.417-1.6-9.619
                    c0.131-19.084-10.546-33.004-28.445-36.359c-16.679-3.131-33.27,5.709-39.268,20.916c-6.596,16.722-0.401,35.168,14.772,43.986
                    c13.833,8.038,29.844,5.954,41.881-4.675c16.154,4.34,31.389,10.685,45.848,19.08c2.635,5.517,3.962,11.77,3.718,19.131
                    c-0.699,21.212,0.879,42.499,1.425,63.755c0.098,3.936,0.346,7.926-0.156,11.803c-1.469,11.351-6.178,14.672-15.761,8.705
                    c-25.366-15.786-50.287-32.34-74.705-49.558c-3.899-2.752-6.501-10.769-5.878-15.888c2.069-17.058-3.746-31.563-17.765-39.097
                    C54.945,147.435,37.41,150.133,26.55,161.483z"
                      />
                    </svg>
                  </div>
                  <div
                    class="application-role-details"
                    :style="getRoleColor(role)"
                  >
                    <div class="application-name">
                      {{ role.app ? role.app.name : 'apptree' }}
                    </div>
                    <div class="application-role foo">
                      {{ role.description }}
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-else-if="tableData.column.field === 'actions'"
                class="action-buttons"
              >
                <a
                  :href="`/roleManagement/users/${tableData.row.id}/edit`"
                  class="action-button"
                >
                  <i class="fa fa-2x fa-edit" />
                </a>
                <a
                  href="http://support.culture.gr/index.php?_m=tickets&_a=submit&departmantid=24"
                  class="action-button"
                  data-toggle="tooltip"
                  title="Report wrong user"
                >
                  <i class="fa fa-2x fa-user-times" />
                </a>
              </div>
            </template>
          </DataViewer>
        </div>
        <div class="box-footer">
          <div class="row">
            <div class="col-sm-3">
              <a
                href="/"
                class="btn btn-default"
              >
                <i
                  class="fa fa-arrow-left"
                  aria-hidden="true"
                /> Επιστροφή
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DataViewer from '../../shared/components/ui/Data/DataViewer.vue';

export default {
  name: 'UserListing',
  components: {
    DataViewer,
  },
  props: ['userData', 'managedRolesData'],
  data() {
    return {
      users: this.userData,
      listables: [
        {
          label: 'Ονοματεπώνυμο', field: 'name', type: 'string', width: '50%',
        },
        {
          label: 'Όνομα Χρήστη', field: 'username', type: 'string', width: '20%',
        },
        {
          label: 'Ρόλοι', field: 'roles', type: 'string', width: '30%',
        },
        {
          label: 'Ενέργειες', field: 'actions', type: 'number', width: '100px',
        },
      ],
    };
  },
  methods: {
    getRoleColor(role) {
      return {
        color: this.managedRolesData.includes(role.id) ? 'inherit' : 'var(--gray-500)',
      };
    },
    getAppColor(app) {
      return {
        backgroundColor: app ? app.color : 'var(--gray-500)',
      };
    },
  },
};
</script>

<style scoped>
  .action-buttons {
    display: flex;
    justify-content: space-around;
  }

  .action-button i.fa-edit {
    margin-top: 2px;
  }

  .application-role-box {
    display: flex;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    background-color: var(--gray-100);
    margin-bottom: 0.6rem;
  }

  .application-role-box .application-icon {
    width: 50px;
    height: 50px;
    display: inline-block;
    line-height: 60px;
    text-align: center;
    vertical-align: bottom;
    background-color: var(--gray-400);
  }

  .application-role-box .application-role-details {
    flex-grow: 1;
    padding: 5px;
  }

  .application-name {
    font-weight: 600;
    text-transform: uppercase;
    color: var(--gray-700);
  }
</style>
