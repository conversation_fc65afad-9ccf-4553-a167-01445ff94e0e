<script setup lang="ts">
import { inject, onMounted, ref } from 'vue';
import type { VueCookies } from 'vue-cookies';

import type { ActionIndex } from '@/educational/types';
import type {
  Filterabe, Listable, Paginator, ResourceFilters, Sortable,
} from '@/global';
import ResourceViewer from '@/shared/components/ui/Data/ResourceViewer.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';
import useHttp, { isAxiosError } from '@/shared/composables/useHttp';
import useState from '@/shared/composables/useState';
import useSwal from '@/shared/composables/useSwal';

defineProps<{
  filterables: Filterabe[];
  sortables: Sortable[];
  listables: Listable[];
}>();

const { error, success } = useSwal();

const actions = ref<Paginator<ActionIndex> | {
  data: [],
  meta: Record<string, never>,
  links: Record<string, never>,
}>({
  data: [],
  meta: {},
  links: {},
});

const { get, delete: destroy } = useHttp();

const fetchActions = async (filters: ResourceFilters) => {
  try {
    const response = await get<Paginator<ActionIndex>>('/api/educational/unsubmitted-actions', filters);
    if (response.data) {
      actions.value = response.data;
    }
  } catch (err) {
    if (isAxiosError(err)) {
      error({ message: err.response?.data.message || 'An unknown error occurred' });
    }
  }
};

const [open, setOpen] = useState(false);
const [loading, setLoading] = useState(false);
const [selectedActionId, setSelectedActionId] = useState<number|null>(null);

const askToConfirmDelete = (id: number) => {
  setSelectedActionId(id);
  setOpen(true);
};

const cancelConfirmDelete = () => {
  setSelectedActionId(null);
  setOpen(false);
};

const confirmActionDelete = async () => {
  setLoading(true);
  try {
    await destroy(`/api/educational/actions/${selectedActionId.value}`);
    const index = actions.value.data.findIndex((action) => action.id === selectedActionId.value);
    if (index !== -1) {
      actions.value.data.splice(index, 1);
    }
    setOpen(false);
    success({ message: 'Η δράση διαγράφηκε με επιτυχία' });
  } catch (err) {
    if (isAxiosError(err)) {
      setOpen(false);
      error({ message: err.response?.data.message || 'An unknown error occurred' });
    }
  } finally {
    setLoading(false);
    setSelectedActionId(null);
  }
};

const $cookies = inject<VueCookies>('$cookies');

const activeFiltersCookieKey = 'educational_unsubmitted_actions';

onMounted(() => {
  const activeFilters: ResourceFilters = $cookies?.get(activeFiltersCookieKey) ?? {
    page: 1,
    limit: 10,
  };
  // console.log('activeFilters', activeFilters);
  fetchActions(activeFilters);
});
</script>

<template>
  <div>
    <ResourceViewer
      :paginated-data="actions"
      :filterables="filterables"
      :sortables="sortables"
      :listables="listables"
      :filter-cookie="activeFiltersCookieKey"
      @fetch-data="fetchActions"
    >
      <template #default="{ tableData }">
        <div v-if="tableData.column.field === 'locations'">
          <ul>
            <li
              v-for="(location, index) in tableData.row.locations"
              :key="index"
              v-text="location"
            />
          </ul>
        </div>
        <div v-else-if="tableData.column.field === 'targetTypes'">
          <ul>
            <li
              v-for="(targetType, index) in tableData.row.targetTypes"
              :key="index"
              v-text="targetType"
            />
          </ul>
        </div>
        <div
          v-else-if="tableData.column.field === 'actions'"
          class="action-buttons pull-right"
        >
          <a
            :href="`/educational/actions/${tableData.row.id}`"
            class="action-button"
          >
            <i class="fa fa-2x fa-info-circle" />
          </a>
          <a
            v-if="tableData.row.can.update"
            :href="`/educational/actions/${tableData.row.id}/edit`"
            class="action-button"
          >
            <i class="fa fa-2x fa-edit" />
          </a>
          <button
            v-if="tableData.row.can.update"
            class="action-button action-button-delete"
            @click="askToConfirmDelete(tableData.row.id)"
          >
            <i class="fa fa-2x fa-trash" />
          </button>
        </div>
      </template>
    </ResourceViewer>

    <ConfirmationModal
      cancel-btn-text="Ακύρωση"
      confirm-btn-text="Διαγραφή δράσης"
      :open="open"
      :processing="loading"
      @confirm="confirmActionDelete"
      @cancel="cancelConfirmDelete"
    >
      <div class="max-w-prose mx-auto">
        <p>
          Πρόκειται να διαγράψετε οριστικά αυτή την δράση.
          Μετά την διαγραφή της δεν θα μπορεί να ανακτηθεί.
          Θέλετε να συνεχίσετε;
        </p>
      </div>
    </ConfirmationModal>
  </div>
</template>

<style scoped>
.action-button-delete {
  background: none;
  color: inherit;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  width: 100px;
}

.action-button {
  color: var(--gray-600);
}

.action-button:hover {
  color: var(--brand-color);
}
</style>
