<script setup lang="ts">
defineProps<{
  title: string;
  icon: string;
}>();
</script>

<script lang="ts">
export default {
  name: 'FormSection',
};
</script>

<template>
  <div class="row action-form-section">
    <div class="col-sm-2">
      <div class="form-lead">
        <i
          :class="`fa fa-2x ${icon}`"
          aria-hidden="true"
        />
        <h4>{{ title }}</h4>
      </div>
    </div>
    <div class="col-sm-10">
      <slot />
    </div>
  </div>
</template>

<style scoped>
.form-lead {
  display: flex;
  align-items: center;
  color: var(--gray-600);
}

.form-lead i {
  margin-right: 7px;
}

.form-lead h4 {
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
}

.form-lead .fa.fa-2x {
  vertical-align: middle;
}

.action-form-section {
  margin-top: 2.75rem;
  margin-bottom: 2.75rem;
  padding-left: 1rem;
}
</style>
