<script setup lang="ts">
defineEmits<{
  (e: 'click'): void;
}>();
</script>

<template>
  <div
    href="#"
    class="help-button-wrapper"
    @click="$emit('click')"
  >
    <i class="fa fa-2x fa-question help-button" />
  </div>
</template>

<style scoped>
.help-button-wrapper {
  position: fixed;
  width: 60px;
  height: 60px;
  bottom: 40px;
  right: 40px;
  background-color: var(--brand-color);
  cursor: pointer;
  color: #FFF;
  border-radius: 50px;
  text-align: center;
  box-shadow: 0px 17px 10px -10px var(--gray-500)
}

.help-button-wrapper:hover {
  box-shadow: 0px 37px 20px -15px var(--gray-400);
  outline: 0;
  transition: all 0.3s;
  transform: translate(0px, -10px);
}

.help-button {
  margin-top: 18px;
}
</style>
