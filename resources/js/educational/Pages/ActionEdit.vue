<script setup lang="ts">
import mapValues from 'lodash/mapValues';
import type { Ref } from 'vue';
import {
  del, onMounted, ref, set, shallowRef,
} from 'vue';

import FormSection from '@/educational/components/FormSection.vue';
import UserGuide from '@/educational/components/UserGuide.vue';
import type {
  ActionEdit, CollaboratorTypes, LocationTypes, RelatedModels, TargetTypes,
} from '@/educational/types';
import type { Ui } from '@/global';
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import SuccessAlert from '@/shared/components/ui/Alerts/SuccessAlert.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';
import LoadingButton from '@/shared/components/ui/Buttons/LoadingButton.vue';
import CheckField from '@/shared/components/ui/FormFields/CheckField.vue';
import DateField from '@/shared/components/ui/FormFields/DateField.vue';
import NumberField from '@/shared/components/ui/FormFields/NumberField.vue';
import RadioGroupField from '@/shared/components/ui/FormFields/RadioGroupField.vue';
import SelectField from '@/shared/components/ui/FormFields/SelectfilterField.vue';
import SelectmultipleField from '@/shared/components/ui/FormFields/SelectmultipleField.vue';
import SwitchField from '@/shared/components/ui/FormFields/SwitchField.vue';
import TagField from '@/shared/components/ui/FormFields/TagField.vue';
import TextareaField from '@/shared/components/ui/FormFields/TextareaField.vue';
import TextField from '@/shared/components/ui/FormFields/TextField.vue';
import UploadAjaxField from '@/shared/components/ui/FormFields/UploadAjaxField.vue';
import useForm from '@/shared/composables/useForm';
import useHttp, { isAxiosError } from '@/shared/composables/useHttp';
import useNavigation from '@/shared/composables/useNavigation';
import useSessionStorage from '@/shared/composables/useSessionStorage';
import useState from '@/shared/composables/useState';

const props = defineProps<{
  action: ActionEdit;
  requiredFields: string[];
}>();

const form = useForm<ActionEdit>({
  id: props.action.id ?? '',
  period_id: props.action.period_id ?? '',
  unit: props.action.unit,
  type: props.action.type,
  involvements: props.action.involvements ?? [],
  ongoing: props.action.ongoing ?? false,
  is_digital: props.action.is_digital ?? '',
  title: props.action.title ?? '',
  context_id: props.action.context_id ?? '',
  started_at: props.action.started_at ?? '',
  ended_at: props.action.ended_at ?? '',
  duration_id: props.action.duration_id ?? '',
  frequency: props.action.frequency ?? '',
  description: props.action.description ?? '',
  contributors: props.action.contributors ?? '',
  locations: props.action.locations ?? {},
  targets: props.action.targets ?? {},
  target_types: props.action.target_types ?? {},
  collaborators: props.action.collaborators ?? {},
  funds: props.action.funds ?? [],
  tools: props.action.tools ?? [],
  assessments: props.action.assessments ?? [],
  evaluation: props.action.evaluation ?? '',
  link: props.action.link ?? '',
  attachments: props.action.attachments ?? [],
  disseminations: props.action.disseminations ?? [],
});

const { getItem, removeItem } = useSessionStorage();
const { redirectTo, scrollTop } = useNavigation();
const [successMessage, setSuccessMessage] = useState('');
const [errorMessage, setErrorMessage] = useState('');
const [fetching, setFetching] = useState(false);
// const { success, error } = useSwal();

// In case of validation errors during submission, we show those errors
if (getItem('educational_action_submission_errors')) {
  form.setErrors(getItem('educational_action_submission_errors'));
  removeItem('educational_action_submission_errors');
  setErrorMessage('Παρακαλώ διορθώστε τα παρακάτω σφάλματα και δοκιμάστε να υποβάλετε ξανά.');
}

const locationTypes = ref<LocationTypes>(mapValues(props.action.locations, () => true));
const targetTypes = ref<TargetTypes>(mapValues(props.action.targets, () => true));
const collaboratorTypes = ref<CollaboratorTypes>(mapValues(props.action.collaborators, () => true));

const addNewLocations = (event: boolean, locationTypeId: number) => {
  // We dynamically add/remove reactive properties for allow v-model to work properly.
  if (event) {
    set(form.locations, locationTypeId, []);
  } else {
    del(form.locations, locationTypeId);
  }
};

const addNewTargets = (event: boolean, targetTypeId: number) => {
  if (event) {
    set(form.targets, targetTypeId, []);
    set(form.target_types, targetTypeId, '');
  } else {
    del(form.targets, targetTypeId);
    del(form.target_types, targetTypeId);
  }
};

const addNewCollaborators = (event: boolean, collaboratorTypeId: number) => {
  if (event) {
    set(form.collaborators, collaboratorTypeId, []);
  } else {
    del(form.collaborators, collaboratorTypeId);
  }
};

const save = async () => {
  setSuccessMessage('');
  setErrorMessage('');
  try {
    const response = await form.put(`/api/educational/actions/${props.action.id}`);
    setSuccessMessage(response.message);
    scrollTop();
    // success(response.message);
  } catch (err) {
    if (isAxiosError(err)) {
      setErrorMessage(err.response?.data?.message ?? 'An unknown error occurred');
      scrollTop();
      // error(err.response?.data.message ?? 'Something went wrong');
    }
  }
};

const saveAndPreview = async () => {
  setErrorMessage('');
  try {
    await form.put(`/api/educational/actions/${props.action.id}`);
    redirectTo(`/educational/actions/${props.action.id}/`);
  } catch (err) {
    if (isAxiosError(err)) {
      setErrorMessage(err.response?.data?.message ?? 'An unknown error occurred');
      scrollTop();
      // error(err.response?.data.message ?? 'Something went wrong');
    }
  }
};

const isRequired = (field: string) => props.requiredFields.includes(field);

const formLabelClass = (error: keyof ActionEdit) => [
  'category-label',
  { 'has-errors': form.errors[error] },
];

const component = shallowRef({
  select: SelectmultipleField,
  tag: TagField,
});

const { get } = useHttp();
const relatedOptions = ref({}) as Ref<RelatedModels>;

onMounted(async () => {
  setFetching(true);
  const response = await get<RelatedModels>('/api/educational/action-form-options');
  if (response.data) {
    relatedOptions.value = response.data;
  }
  setFetching(false);
});

const getFilteredLocations = (locationType: Ui.SelectOption) => relatedOptions.value.locations
  .filter((location) => location.location_type_id === locationType.id);

const getFilteredTargets = (targetType: Ui.SelectOption) => relatedOptions.value.targets
  .filter((target) => target.target_type_id === targetType.id);

const getFilteredCollaborators = (collaboratorType: Ui.SelectOption) => relatedOptions.value.collaborators
  .filter((collaborator) => collaborator.collaborator_type_id === collaboratorType.id);
</script>

<script lang="ts">
export default {
  name: 'ActionEdit',
};
</script>

<template>
  <div>
    <SuccessAlert v-if="successMessage">
      <p>{{ successMessage }}</p>
    </SuccessAlert>
    <DangerAlert
      v-if="errorMessage"
      :message="errorMessage"
    />
    <Box
      :title="`${action.type} (${action.unit}) - Καρτέλα τεκμηρίωσης`"
      :is-loading="fetching"
    >
      <form id="applicationForm">
        <FormSection
          title="Γενικά Στοιχεία"
          icon="fa-pencil-square-o"
        >
          <div class="row">
            <div class="col-sm-6">
              <SelectField
                v-model="form.period_id"
                title="Έτος"
                name="period_id"
                :error="form.errors.period_id"
                :options="relatedOptions['periods']"
              />
            </div>
            <div class="col-sm-6">
              <TextField
                v-model="form.title"
                title="Τίτλος"
                name="title"
                :error="form.errors.title"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <SelectmultipleField
                v-model="form.involvements"
                title="Στάδια σχεδιασμού - υλοποίησης"
                name="involvements"
                :error="form.errors.involvements"
                :options="relatedOptions['involvements']"
              />
            </div>
            <div class="col-sm-6">
              <SelectField
                v-model="form.context_id"
                title="Πλαίσιο συμμετοχής"
                name="context_id"
                :error="form.errors.context_id"
                :options="relatedOptions['contexts']"
              />
            </div>
          </div>
          <div class="row">
            <div
              v-if="isRequired('is_digital')"
              class="col-sm-6"
            >
              <RadioGroupField
                v-model="form.is_digital"
                :radios="[
                  { label: 'Μη ψηφιακή', value: '0' },
                  { label: 'Ψηφιακή', value: '1' }
                ]"
                title="Εκδοχή"
                name="is_digital"
                :error="form.errors.is_digital"
              />
            </div>
            <div
              v-if="isRequired('ongoing')"
              class="col-sm-6"
            >
              <SwitchField
                v-model="form.ongoing"
                title="Συνεχιζόμενη"
                :name="`ongoing`"
                :error="form.errors.ongoing"
              />
            </div>
          </div>
          <div
            v-if="isRequired('started_at')"
            class="row"
          >
            <div class="col-sm-6">
              <DateField
                v-model="form.started_at"
                title="Έναρξη"
                name="started_at"
                :error="form.errors.started_at"
              />
            </div>
            <div class="col-sm-6">
              <DateField
                v-model="form.ended_at"
                title="Λήξη"
                name="ended_at"
                :error="form.errors.ended_at"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <SelectField
                v-if="isRequired('duration_id')"
                v-model="form.duration_id"
                title="Χρονική διάρκεια"
                name="duration_id"
                :error="form.errors.duration_id"
                :options="relatedOptions['durations']"
              />
            </div>
            <div class="col-sm-6">
              <NumberField
                v-if="isRequired('frequency')"
                v-model="form.frequency"
                title="Ετήσιος αριθμός επαναλήψεων"
                name="frequency"
                :error="form.errors.frequency"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <TextareaField
                v-model="form.description"
                title="Περιγραφή"
                name="description"
                rows="5"
                :error="form.errors.description"
              />
            </div>
            <div class="col-sm-6">
              <TextareaField
                v-model="form.contributors"
                title="Συντελεστές"
                name="description"
                rows="5"
                :error="form.errors.contributors"
              />
            </div>
          </div>
        </FormSection>
        <hr v-if="isRequired('locations')">
        <FormSection
          v-if="isRequired('locations')"
          title="Χώροι Διεξαγωγής"
          icon="fa-map-marker"
        >
          <p :class="formLabelClass('locations')">
            Κατηγορίες χώρων διεξαγωγής
          </p>
          <span
            v-if="form.errors.locations"
            class="help-block"
            v-text="form.errors.locations"
          />
          <br>
          <div
            v-for="(locationType) in relatedOptions['locationTypes']"
            :key="locationType.id"
            class="row"
          >
            <div class="col-sm-4">
              <CheckField
                v-model="locationTypes[locationType.id]"
                :title="locationType.name"
                :name="`location_types.${ locationType.id }`"
                @input="addNewLocations($event, locationType.id)"
              />
            </div>
            <div
              v-if="locationTypes[locationType.id]"
              class="col-sm-12"
            >
              <div class="category-type-group">
                <Component
                  :is="locationType.id === 2 ? component['select'] : component['tag']"
                  v-model="form.locations[locationType.id]"
                  :name="`locations.${locationType.id}`"
                  :options="getFilteredLocations(locationType)"
                />
              </div>
            </div>
          </div>
        </FormSection>
        <hr>
        <FormSection
          title="Ομάδες κοινού"
          icon="fa-users"
        >
          <p :class="formLabelClass('targets')">
            Κατηγορίες ομάδων κοινού
          </p>
          <span
            v-if="form.errors.targets"
            class="help-block"
            v-text="form.errors.targets"
          />
          <span
            v-if="form.errors.target_types"
            class="help-block"
            v-text="form.errors.target_types"
          />
          <br>
          <div
            v-for="(targetType) in relatedOptions['targetTypes']"
            :key="targetType.id"
            class="row"
          >
            <div class="col-sm-4">
              <CheckField
                v-model="targetTypes[targetType.id]"
                :title="targetType.name"
                :name="`target_types.${ targetType.id }`"
                @input="addNewTargets($event, targetType.id)"
              />
            </div>
            <div
              v-if="targetTypes[targetType.id]"
              class="col-sm-12"
            >
              <div class="category-type-group">
                <SelectmultipleField
                  v-model="form.targets[targetType.id]"
                  :name="`targets.${targetType.id}`"
                  :options="getFilteredTargets(targetType)"
                />
                <NumberField
                  v-if="isRequired('target_types') && targetTypes[targetType.id]"
                  v-model="form.target_types[targetType.id]"
                  title="Αρ. συμμετεχόντων"
                  :name="`target_types[${targetType.id}]`"
                />
              </div>
            </div>
          </div>
        </FormSection>
        <hr>
        <FormSection
          title="Συνεργαζόμενοι Φορείς"
          icon="fa-handshake-o"
        >
          <p :class="formLabelClass('collaborators')">
            Κατηγορίες συνεργαζόμενων φορέων
          </p>
          <span
            v-if="form.errors.collaborators"
            class="help-block"
            v-text="form.errors.collaborators"
          />
          <br>
          <div
            v-for="(collaboratorType) in relatedOptions['collaboratorTypes']"
            :key="collaboratorType.id"
            class="row"
          >
            <div class="col-sm-4">
              <CheckField
                v-model="collaboratorTypes[collaboratorType.id]"
                :title="collaboratorType.name"
                :name="`colaborator_types.${ collaboratorType.id }`"
                @input="addNewCollaborators($event, collaboratorType.id)"
              />
            </div>
            <div
              v-if="collaboratorTypes[collaboratorType.id]"
              class="col-sm-12"
            >
              <div class="category-type-group">
                <Component
                  :is="[4, 15].includes(collaboratorType.id) ? component['select'] : component['tag']"
                  v-model="form.collaborators[collaboratorType.id]"
                  :name="`collaborators.${collaboratorType.id}`"
                  :options="getFilteredCollaborators(collaboratorType)"
                />
              </div>
            </div>
          </div>
        </FormSection>
        <hr>
        <FormSection
          title="Χρηματοδότηση"
          icon="fa-euro"
        >
          <div class="row">
            <div class="col-sm-6">
              <SelectmultipleField
                v-model="form.funds"
                title="Πηγές χρηματοδότησης"
                name="funds"
                :error="form.errors.funds"
                :options="relatedOptions['funds']"
              />
            </div>
          </div>
        </FormSection>
        <hr>
        <FormSection
          title="Παιδαγωγικό Πλαίσιο / Αποτίμηση"
          icon="fa-cubes"
        >
          <div class="row">
            <div class="col-sm-6">
              <SelectmultipleField
                v-model="form.assessments"
                title="Μέθοδοι αξιολόγησης"
                name="assessments"
                :error="form.errors.assessments"
                :options="relatedOptions['assessments']"
              />
            </div>
            <div class="col-sm-6">
              <SelectmultipleField
                v-if="isRequired('tools')"
                v-model="form.tools"
                title="Παιδαγωγικά εργαλεία"
                name="tools"
                :error="form.errors.tools"
                :options="relatedOptions['tools']"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <TextareaField
                v-model="form.evaluation"
                title="Αποτίμηση"
                name="evaluation"
                rows="5"
                :error="form.errors.evaluation"
              />
            </div>
          </div>
        </FormSection>
        <hr>
        <FormSection
          title="Συμπληρωματική Τεκμηρίωση"
          icon="fa-paper-plane"
        >
          <div class="row">
            <div class="col-sm-6">
              <SelectmultipleField
                v-model="form.disseminations"
                title="Μέσα προβολής"
                name="disseminations"
                :error="form.errors.disseminations"
                :options="relatedOptions['disseminations']"
              />
            </div>
            <div class="col-sm-6">
              <TextField
                v-model="form.link"
                title="Υπερσύνδεση"
                name="link"
                :error="form.errors.link"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <UploadAjaxField
                :initial-files="form.attachments"
                title="Φωτογραφικό υλικό"
                name="attachments"
                :base-url="`/api/educational/actions/${action.id}/attachments`"
                :error="form.errors.attachments"
              />
            </div>
          </div>
        </FormSection>
      </form>
      <template #footer>
        <div class="row">
          <div class="col-sm-3">
            <button
              class="btn btn-block btn-default"
              onclick="window.history.back();"
            >
              <i
                class="fa fa-arrow-left"
                aria-hidden="true"
              /> Επιστροφή
            </button>
          </div>
          <div class="col-sm-3">
            <button
              class="btn btn-block btn-default"
              onclick="window.location.reload();"
            >
              <i
                class="fa fa-refresh"
                aria-hidden="true"
              /> Επαναφορά
            </button>
          </div>
          <div class="col-sm-3">
            <LoadingButton
              class="btn btn-block btn-primary btn-outline"
              type="button"
              :loading="form.processing"
              @click="save"
            >
              <i class="fa fa-save" /> Προσωρινή αποθήκευση
            </LoadingButton>
          </div>
          <div class="col-sm-3">
            <button
              class="btn btn-block btn-primary"
              @click="saveAndPreview"
            >
              <i class="fa fa-arrow-right" /> Προς υποβολή
            </button>
          </div>
        </div>
      </template>
    </Box>
    <UserGuide />
  </div>
</template>

<style scoped>
.category-type-group {
  border-left: 2px solid var(--brand-color);
  margin-left: 6px;
  padding-left: 16px;
}
.category-label.has-errors,
span.help-block {
  color: var(--red-500);
}
</style>
