<script setup lang="ts">
import SubmittedActionListing from '@/educational/components/SubmittedActionListing.vue';
import UnsubmittedActionListing from '@/educational/components/UnsubmittedActionListing.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';

const props = defineProps<{
  filterOptions: {
    [key: string]: {id: number, name: string}[];
  };
}>();

const filterables = [
  {
    label: 'Έτος',
    field: 'period_id',
    type: 'select',
    selectOptions: props.filterOptions.periods,
  },
  {
    label: 'Υπηρεσία',
    field: 'unit_id',
    type: 'select-multiple',
    selectOptions: props.filterOptions.units,
  },
  {
    label: 'Περιφέρεια',
    field: 'region_id',
    type: 'select',
    selectOptions: props.filterOptions.regions,
  },
  {
    label: 'Τίτλος',
    field: 'title',
    type: 'string',
  },
  {
    label: 'Στάδια Σχεδιασμού - Υλοποίησης',
    field: 'involvements.id',
    type: 'select-multiple',
    selectOptions: props.filterOptions.involvements,
  },
  {
    label: 'Συνεχιζόμενη Δράση',
    field: 'ongoing',
    type: 'select',
    selectOptions: props.filterOptions.ongoing,
  },
  {
    label: 'Είδος Δράσης',
    field: 'type_id',
    type: 'select',
    selectOptions: props.filterOptions.types,
  },
  {
    label: 'Πλαίσιο Συμμετοχής',
    field: 'context_id',
    type: 'select',
    selectOptions: props.filterOptions.contexts,
  },
  {
    label: 'Χρονική Διάρκεια Δράσης',
    field: 'duration_id',
    type: 'select',
    selectOptions: props.filterOptions.durations,
  },
  {
    label: 'Ετήσιος Συνολικός Αριθμός Δράσεων',
    field: 'frequency',
    type: 'number',
  },
  {
    label: 'Κατηγορίες Χώρων Διεξαγωγής',
    field: 'locations.location_type_id',
    type: 'select-multiple',
    selectOptions: props.filterOptions.locationTypes,
  },
  {
    label: 'Χώροι Διεξαγωγής',
    field: 'locations.id',
    type: 'select-multiple',
    selectOptions: props.filterOptions.locations,
  },
  {
    label: 'Κατηγορίες Ομάδων Κοινού',
    field: 'targets.target_type_id',
    type: 'select-multiple',
    selectOptions: props.filterOptions.targetTypes,
  },
  {
    label: 'Ομάδες Κοινού',
    field: 'targets.id',
    type: 'select-multiple',
    selectOptions: props.filterOptions.targets,
  },
  {
    label: 'Κατηγορίες Συνεργαζόμενων Φορέων',
    field: 'collaborators.collaborator_type_id',
    type: 'select-multiple',
    selectOptions: props.filterOptions.collaboratorTypes,
  },
  {
    label: 'Πηγές Χρηματοδότησης',
    field: 'funds.id',
    type: 'select-multiple',
    selectOptions: props.filterOptions.funds,
  },
  {
    label: 'Παιδαγωγικά Εργαλεία',
    field: 'tools.id',
    type: 'select-multiple',
    selectOptions: props.filterOptions.tools,
  },
  {
    label: 'Μέθοδοι Αξιολόγησης',
    field: 'assessments.id',
    type: 'select-multiple',
    selectOptions: props.filterOptions.assessments,
  },
];

const sortables = [
  { label: 'Περίοδος', field: 'period.name' },
  { label: 'Τίτλος', field: 'title' },
  { label: 'Υπηρεσία', field: 'unit.name' },
];

const listables = [
  { label: 'Έτος', field: 'period' },
  { label: 'Υπηρεσία', field: 'unit' },
  { label: 'Είδος', field: 'type' },
  { label: 'Τίτλος', field: 'title' },
  { label: 'Χώροι διεξαγωγής', field: 'locations' },
  { label: 'Κατηγορίες ομάδων κοινού', field: 'targetTypes' },
  { label: '⚙ Ενέργειες', field: 'actions' },
];
</script>

<template>
  <div>
    <Box title="Υποβληθείσες Δράσεις">
      <SubmittedActionListing
        :filterables="filterables"
        :sortables="sortables"
        :listables="listables"
      />
    </Box>

    <Box title="Προσωρινά αποθηκευμένες Δράσεις">
      <UnsubmittedActionListing
        :filterables="filterables"
        :sortables="sortables"
        :listables="listables"
      />
    </Box>
  </div>
</template>
