<script setup lang="ts">
import FormSection from '@/educational/components/FormSection.vue';
import type { ActionShow } from '@/educational/types';
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';
import WarningAlert from '@/shared/components/ui/Alerts/WarningAlert.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';
import useHttp, { isAxiosError } from '@/shared/composables/useHttp';
import useNavigation from '@/shared/composables/useNavigation';
import useSessionStorage from '@/shared/composables/useSessionStorage';
import useState from '@/shared/composables/useState';
import useSwal from '@/shared/composables/useSwal';

const props = defineProps<{
  action: ActionShow
  requiredFields: string[];
}>();

const [submitModalOpen, setSubmitModalOpen] = useState(false);
const [withdrawModalOpen, setWithdrawModalOpen] = useState(false);
const { redirectTo } = useNavigation();
const [loading, setLoading] = useState(false);
const { post, delete: destroy } = useHttp();
const { setItem } = useSessionStorage();
const { success, error } = useSwal();

const isRequired = (field: string) => props.requiredFields.includes(field);

const submit = async () => {
  setLoading(true);
  try {
    const response = await post('/api/educational/submitted-actions', {
      action_id: props.action.id,
    });
    setSubmitModalOpen(false);
    success({
      message: response.message,
      confirmButtonText: 'Μετάβαση στις δράσεις',
    }, () => redirectTo('/educational/actions'));
  } catch (err) {
    if (isAxiosError(err)) {
      if (err.response?.status === 422) {
        // If validation errors occured we save them to session storage
        // and redirect the user to the action edit form
        setItem('educational_action_submission_errors', err.response.data.errors);
        setSubmitModalOpen(false);
        error({
          message: err.response.data.message,
          confirmButtonText: 'Μετάβαση στην καρτέλα τεκμηρίωσης',
        }, () => redirectTo(`/educational/actions/${props.action.id}/edit`));
      } else {
        // if other error scroll to top
        setSubmitModalOpen(false);
        error({ message: err.response?.data.message });
      }
    }
  } finally {
    setLoading(false);
  }
};

const withdraw = async () => {
  setLoading(true);
  try {
    const response = await destroy(`/api/educational/submitted-actions/${props.action.id}`);
    setWithdrawModalOpen(false);
    success({
      message: response.message,
      confirmButtonText: 'Μετάβαση στις δράσεις',
    }, () => redirectTo('/educational/actions'));
  } catch (err) {
    if (isAxiosError(err)) {
      setWithdrawModalOpen(false);
      error({ message: err.response?.data.message || 'An unknown error occurred' });
    }
  } finally {
    setLoading(false);
  }
};

</script>

<script lang="ts">
export default {
  name: 'ActionShow',
};
</script>

<template>
  <div>
    <Box :title="`${action.type} (${action.user}@${action.unit_abbrv}) - Επισκόπηση`">
      <InfoAlert v-if="action.submitted_at">
        <p>Η Δράση έχει υποβληθεί οριστικά στις {{ action.submitted_at }}.</p>
      </InfoAlert>
      <WarningAlert v-else>
        <p>Η Δράση είναι προσωρινά αποθηκευμένη και δεν έχει υποβληθεί!</p>
        <p>
          Μπορείτε να την υποβάλετε οριστικά πατώντας το κουμπί Υποβολή,
          αφού βεβαιωθείτε για την ορθότητα των υποβαλλόμενων στοιχείων.
        </p>
      </WarningAlert>
      <!-- Γενικά Στοιχεία -->
      <FormSection
        title="Γενικά Στοιχεία"
        icon="fa-pencil-square-o"
      >
        <div class="row">
          <div class="col-sm-6">
            <h5 class="text-bold">
              Έτος
            </h5>
            <p>{{ action.period }}</p>
          </div>
          <div class="col-sm-6">
            <h5 class="text-bold">
              Τίτλος
            </h5>
            <p>{{ action.title }}</p>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-6">
            <h5 class="text-bold">
              Στάδια σχεδιασμού - υλοποίησης
            </h5>
            <ul>
              <li
                v-for="(involvement, index) in action.involvements"
                :key="index"
              >
                {{ involvement }}
              </li>
            </ul>
          </div>
          <div class="col-sm-6">
            <h5 class="text-bold">
              Πλαίσιο συμμετοχής
            </h5>
            <p>{{ action.context }}</p>
          </div>
        </div>
        <div
          v-if="isRequired('started_at')"
          class="row"
        >
          <div class="col-sm-6">
            <h5 class="text-bold">
              Έναρξη - Λήξη
            </h5>
            <p> {{ action.started_at }} - {{ action.ended_at }}</p>
          </div>
        </div>
        <div class="row">
          <div
            v-if="isRequired('is_digital')"
            class="col-sm-6"
          >
            <h5 class="text-bold">
              Εκδοχή
            </h5>
            <p>{{ action.is_digital }}</p>
          </div>
          <div
            v-if="isRequired('ongoing')"
            class="col-sm-6"
          >
            <h5 class="text-bold">
              Συνεχιζόμενη
            </h5>
            <p>{{ action.ongoing }}</p>
          </div>
        </div>
        <div class="row">
          <div
            v-if="isRequired('duration_id')"
            class="col-sm-6"
          >
            <h5 class="text-bold">
              Χρονική διάρκεια
            </h5>
            <p>{{ action.duration }}</p>
          </div>
          <div
            v-if="isRequired('frequency')"
            class="col-sm-6"
          >
            <h5 class="text-bold">
              Ετήσιος αριθμός επαναλήψεων
            </h5>
            <p>{{ action.frequency }}</p>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-6">
            <h5 class="text-bold">
              Περιγραφή
            </h5>
            <p>{{ action.description }}</p>
          </div>
          <div class="col-sm-6">
            <h5 class="text-bold">
              Συντελεστές
            </h5>
            <p>{{ action.contributors }}</p>
          </div>
        </div>
      </FormSection>
      <hr v-if="isRequired('locations')">
      <!-- Χώροι Διεξαγωγής -->
      <FormSection
        v-if="isRequired('locations')"
        title="Χώροι Διεξαγωγής"
        icon="fa-map-marker"
      >
        <div class="row">
          <div class="col-sm-12">
            <table class="table table-condensed">
              <tbody>
                <tr class="bg-gray-light">
                  <th style="width: 50%;">
                    Κατηγορίες χώρων διεξαγωγής
                  </th>
                  <th>Χώροι διεξαγωγής</th>
                </tr>
                <tr
                  v-for="(locationType, index) in action.location_types"
                  :key="index"
                >
                  <td>{{ locationType.name }}</td>
                  <td>
                    <ul>
                      <li
                        v-for="(location, i) in locationType.locations"
                        :key="i"
                      >
                        {{ location }}
                      </li>
                    </ul>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </FormSection>
      <hr>
      <!-- Ομάδες Κοινού -->
      <FormSection
        title="Ομάδες κοινού"
        icon="fa-users"
      >
        <div class="row">
          <div class="col-sm-12">
            <table class="table table-condensed">
              <tbody>
                <tr class="bg-gray-light">
                  <th style="width: 50%;">
                    Κατηγορίες ομάδων κοινού
                  </th>
                  <th>Ομάδες κοινού</th>
                </tr>
                <tr
                  v-for="(targetType, index) in action.target_types"
                  :key="index"
                >
                  <td>
                    {{ targetType.name }}
                    <span
                      v-if="isRequired('target_types')"
                      class="badge"
                      style="margin-left: 1rem"
                    ><i
                      class="fa fa-users"
                    /> {{ targetType.amount }}</span>
                  </td>
                  <td>
                    <ul>
                      <li
                        v-for="(target, i) in targetType.targets"
                        :key="i"
                      >
                        {{ target }}
                      </li>
                    </ul>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          v-if="isRequired('target_types')"
          class="row"
        >
          <div class="col-sm-6">
            <h5 class="text-bold">
              Συνολικός αριθμός συμμετεχόντων
            </h5>
            <p>{{ action.totalNumberOfParticipants }}</p>
          </div>
        </div>
      </FormSection>
      <hr>
      <!-- Συνεργαζόμενοι φορείς -->
      <FormSection
        title="Συνεργαζόμενοι Φορείς"
        icon="fa-handshake-o"
      >
        <div class="row">
          <div class="col-sm-12">
            <table class="table table-condensed">
              <tbody>
                <tr class="bg-gray-light">
                  <th style="width: 50%;">
                    Κατηγορίες συνεργαζόμενων φορέων
                  </th>
                  <th>Συνεργαζόμενοι φορείς</th>
                </tr>
                <tr
                  v-for="(collaboratorType, index) in action.collaborator_types"
                  :key="index"
                >
                  <td>{{ collaboratorType.name }}</td>
                  <td>
                    <ul>
                      <li
                        v-for="(collaborator, i) in collaboratorType.collaborators"
                        :key="i"
                      >
                        {{ collaborator }}
                      </li>
                    </ul>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </FormSection>
      <hr>
      <!-- Χρηματοδότηση -->
      <FormSection
        title="Χρηματοδότηση"
        icon="fa-euro"
      >
        <div class="row">
          <div class="col-sm-6">
            <h5 class="text-bold">
              Πηγές χρηματοδότησης
            </h5>
            <ul>
              <li
                v-for="(fund, index) in action.funds"
                :key="index"
              >
                {{ fund }}
              </li>
            </ul>
          </div>
        </div>
      </FormSection>
      <hr>
      <!-- Παιδαγωγικό Πλαίσιο  / Αποτίμηση -->
      <FormSection
        title="Παιδαγωγικό Πλαίσιο / Αποτίμηση"
        icon="fa-cubes"
      >
        <div class="row">
          <div class="col-sm-6">
            <h5 class="text-bold">
              Μέθοδοι αξιολόγησης
            </h5>
            <ul>
              <li
                v-for="(assessment, index) in action.assessments"
                :key="index"
              >
                {{ assessment }}
              </li>
            </ul>
          </div>
          <div
            v-if="isRequired('tools')"
            class="col-sm-6"
          >
            <h5 class="text-bold">
              Παιδαγωγικά εργαλεία
            </h5>
            <ul>
              <li
                v-for="(tool, index) in action.tools"
                :key="index"
              >
                {{ tool }}
              </li>
            </ul>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-6">
            <h5 class="text-bold">
              Αποτίμηση
            </h5>
            <p>{{ action.evaluation }}</p>
          </div>
        </div>
      </FormSection>
      <hr>
      <!-- Συμπληρωματική Τεκμηρίωση -->
      <FormSection
        title="Συμπληρωματική Τεκμηρίωση"
        icon="fa-paper-plane"
      >
        <div class="row">
          <div class="col-sm-6">
            <h5 class="text-bold">
              Μέσα προβολής
            </h5>
            <ul>
              <li
                v-for="(dissemination, index) in action.disseminations"
                :key="index"
              >
                {{ dissemination }}
              </li>
            </ul>
          </div>
          <div class="col-sm-6">
            <h5 class="text-bold">
              Υπερσύνδεση
            </h5>
            <a :href="action.link">{{ action.link }}</a>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-6">
            <h5 class="text-bold">
              Φωτογραφικό υλικό
            </h5>
            <ul>
              <li
                v-for="(attachment, index) in action.attachments"
                :key="index"
              >
                <a :href="attachment.url">
                  <i class="fa fa-file-o" /> {{ attachment.name }}
                </a>
              </li>
            </ul>
          </div>
        </div>
      </FormSection>
      <template #footer>
        <div class="row">
          <div class="col-sm-3">
            <button
              class="btn btn-default btn-block"
              onclick="window.location.href='/educational/actions/';"
            >
              <i
                class="fa fa-arrow-left"
                aria-hidden="true"
              /> Επιστροφή
            </button>
          </div>
          <div
            v-if="action.can.update"
            :class="['col-sm-3', !action.can.admin && action.submitted_at ? 'col-sm-offset-6': 'col-sm-offset-3']"
          >
            <a
              v-if="!action.submitted_at"
              :href="`/educational/actions/${action.id}/edit`"
              class="btn btn-outline btn-primary btn-block"
            >
              <i class="fa fa-pencil-square-o" /> Επεξεργασία
            </a>
          </div>
          <div class="col-sm-3">
            <button
              v-if="action.submitted_at && action.can.admin"
              type="button"
              class="btn btn-danger btn-outline btn-block"
              @click="setWithdrawModalOpen(true)"
            >
              <i class="fa fa-undo" /> Αναίρεση υποβολής
            </button>
            <button
              v-if="! action.submitted_at"
              type="button"
              class="btn btn-primary btn-block"
              @click="setSubmitModalOpen(true)"
            >
              <i class="fa fa-paper-plane" /> Υποβολή
            </button>
          </div>
        </div>
      </template>
    </Box>
    <ConfirmationModal
      cancel-btn-text="Ακύρωση"
      confirm-btn-text="Υποβολή"
      :open="submitModalOpen"
      :processing="loading"
      @confirm="submit"
      @cancel="setSubmitModalOpen(false)"
    >
      <div class="max-w-prose mx-auto">
        <p>
          Πρόκειται να υποβάλετε οριστικά την δράση σας.
          Μετά την οριστική υποβολή δεν θα μπορείτε να επεξεργαστείτε τα στοιχεία της δράσης.
          Αν έχετε βεβαιωθεί για την ορθότητα των υποβαλλόμενων στοιχείων και θέλετε να συνεχίσετε,
          πατήστε το κουμπί "Υποβολή". Διαφορετικά, πατήστε το κουμπί "Ακύρωση".
        </p>
      </div>
    </ConfirmationModal>
    <ConfirmationModal
      cancel-btn-text="Ακύρωση"
      confirm-btn-text="Αναίρεση υποβολής"
      :open="withdrawModalOpen"
      :processing="loading"
      @confirm="withdraw"
      @cancel="setWithdrawModalOpen(false)"
    >
      <div class="max-w-prose mx-auto">
        <p>
          Πρόκειται να αναιρέσετε την υποβολή της δράσης. Θέλετε να συνεχίσετε;.
        </p>
      </div>
    </ConfirmationModal>
  </div>
</template>
