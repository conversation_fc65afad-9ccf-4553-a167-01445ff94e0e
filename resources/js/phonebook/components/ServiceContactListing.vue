<template>
  <div>
    <DataViewer
      :listables="serviceContactListables"
      :items="serviceContacts"
    >
      <template #default="{ tableData }">
        <div v-if="tableData.column.field === 'telephone'">
          {{ tableData.row.telephone }}
        </div>
        <div
          v-else-if="tableData.column.field === 'actions'"
          class="action-buttons pull-right"
        >
          <button
            v-if="allowEditing && $can('phonebook.update')"
            class="action-button"
            @click="$emit('edit-service-contact', tableData.row.id)"
          >
            <i class="fa fa-2x fa-edit" />
          </button>
          <button
            v-if="allowEditing && $can('phonebook.update')"
            class="action-button action-button-delete"
            @click="$emit('delete-service-contact', tableData.row.id)"
          >
            <i class="fa fa-2x fa-trash" />
          </button>
        </div>
      </template>
    </DataViewer>
  </div>
</template>

<script>
import DataViewer from '../../shared/components/ui/Data/DataViewer.vue';
import Gate from '../../shared/mixins/Gate';

export default {
  name: 'ServiceContactListing',
  components: {
    DataViewer,
  },
  mixins: [Gate],
  props: {
    serviceContacts: {
      type: Array,
      default() {
        return [];
      },
    },
    serviceContactListables: {
      type: Array,
      required: true,
    },
    allowEditing: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style scoped>
.action-button-delete {
  background: none;
  color: inherit;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  width: 100px;
}

.action-button {
  color: var(--gray-600);
  margin-left: 1rem;
}

.action-button:hover {
  color: var(--brand-color);
}
</style>
