<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <UnitTreeField
          v-model="selectedUnit"
          :allow-selecting-multiple-units="false"
          :allow-selecting-organograms="false"
          :allow-showing-departments="false"
          :show-collapsed="false"
          :units-uri="unitsUri"
          :organograms-uri="organogramsUri"
          :authorize-for="authorizeFor"
        />
      </div>
    </div>
    <div class="row filter__buttons">
      <div class="col-sm-9 filter__buttons__search-by-name">
        <a :href="'/'+baseUrl+'/employee-contact'">
          <i class="fa fa-search" /> Αναζήτηση με βάση το όνομα ή το τηλέφωνο
        </a>
      </div>
      <div class="col-sm-3">
        <button
          type="button"
          class="btn btn-primary btn-block"
          :disabled="selectedUnit.length < 1"
          @click="submitFilters"
        >
          <i
            class="fa fa-search"
            aria-hidden="true"
          /> Αναζήτηση
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import UnitTreeField from '../../shared/components/ui/FormFields/UnitTreeField.vue';

export default {
  name: 'ContactIndexFilter',
  components: {
    UnitTreeField,
  },
  props: {
    defaultUnit: {
      type: Array,
      default() {
        return [];
      },
    },
    baseUrl: {
      type: String,
      required: true,
    },
    unitsUri: {
      type: String,
      required: true,
    },
    organogramsUri: {
      type: String,
      required: true,
    },
    authorizeFor: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      selectedUnit: this.defaultUnit,
    };
  },
  created() {
    if (this.defaultUnit.length > 0) {
      this.$http(`/api/${this.baseUrl}/contact`, { params: { unit_id: this.defaultUnit[0].id } })
        .then((res) => this.$emit('contacts-filtered', {
          filteredContacts: res.data,
          filteredUnit: this.selectedUnit[0],
        }));
    }
  },
  methods: {
    submitFilters() {
      this.$http(`/api/${this.baseUrl}/contact`, { params: { unit_id: this.selectedUnit[0].id } })
        .then((res) => this.$emit('contacts-filtered', {
          filteredContacts: res.data,
          filteredUnit: this.selectedUnit[0],
        }));
    },
  },
};
</script>

<style scoped>
.filter__buttons {
  margin-top: 1rem;
}
.filter__buttons__search-by-name {
  padding-top: 5px;
}
</style>
