<template>
  <DataViewer
    :listables="employeeContactListables"
    :items="employeeContacts"
    :searchable="searchable"
  >
    <template #default="{ tableData }">
      <div v-if="tableData.column.field === 'telephones'">
        <div
          v-for="telephone in tableData.row.telephones"
          :key="telephone.id"
        >
          {{ telephone.tel }} <span v-if="telephone.info">({{ telephone.info }})</span>
        </div>
      </div>
      <div
        v-else-if="tableData.column.field === 'actions'"
        class="action-buttons pull-right"
      >
        <button
          v-if="allowFavorites"
          type="button"
          class="action-button"
          @click="$emit('toggle-favorite-contact', tableData.row)"
        >
          <i :class="['fa', 'fa-2x', tableData.row.favorited ? 'fa-star' : 'fa-star-o']" />
        </button>
        <button
          v-if="allowEditing && $can('phonebook.update')"
          class="action-button"
          @click="$emit('edit-employee-contact', tableData.row.id)"
        >
          <i class="fa fa-2x fa-edit" />
        </button>
        <button
          v-if="allowEditing && tableData.row.isGGA === true && $can('phonebook.update')"
          class="action-button action-button-delete"
          @click="$emit('delete-employee-contact', tableData.row.id)"
        >
          <i class="fa fa-2x fa-trash" />
        </button>
      </div>
    </template>
  </DataViewer>
</template>

<script>

import DataViewer from '../../shared/components/ui/Data/DataViewer.vue';
import Gate from '../../shared/mixins/Gate';

export default {
  name: 'EmployeeContactListing',
  components: {
    DataViewer,
  },
  mixins: [Gate],
  props: {
    employeeContacts: {
      type: Array,
      default() {
        return [];
      },
    },
    searchable: {
      type: Boolean,
      default: true,
    },
    allowEditing: {
      type: Boolean,
      default: false,
    },
    allowFavorites: {
      type: Boolean,
      default: false,
    },
    employeeContactListables: {
      type: Array,
      required: true,
    },
  },
};
</script>

<style scoped>
.action-button-delete {
  background: none;
  color: inherit;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  width: 100px;
}

.action-button {
  margin-left: 1rem;
  color: var(--gray-600);
}

.action-button:hover {
  color: var(--brand-color);
}
</style>
