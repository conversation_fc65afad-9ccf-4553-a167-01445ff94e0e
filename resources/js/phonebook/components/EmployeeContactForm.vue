<template>
  <div>
    <div class="row">
      <div class="col-sm-4">
        <TextField
          v-model="form.data.name"
          title="Όνομα *"
          :disabled="!form.data.isGGA"
          :error="form.errors.get('name')"
          @input="form.errors.clear('name')"
        />
      </div>
      <div class="col-sm-4">
        <TextField
          v-model="form.data.surname"
          title="Επίθετο *"
          name="surname"
          :disabled="!form.data.isGGA"
          :error="form.errors.get('surname')"
          @input="form.errors.clear('surname')"
        />
      </div>
      <div class="col-sm-4">
        <SelectfilterField
          v-model="form.data.rank_id"
          title="Βαθμός"
          :options="ranks"
          :disabled="!form.data.isGGA"
          :error="form.errors.get('rank_id')"
          @input="form.errors.clear('rank_id')"
        />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-4">
        <TextField
          v-model="form.data.email"
          title="Email"
          :disabled="!form.data.isGGA"
          :error="form.errors.get('email')"
          @input="form.errors.clear('email')"
        />
      </div>
      <div class="col-sm-4">
        <p class="text-bold">
          Υπηρεσία *
        </p>
        {{ unit.name }}
      </div>
    </div>
    <!-- Work Telephones -->
    <div
      v-for="(telephone, index) in form.data.telephones"
      style="margin-top: 1rem;"
      :key="index"
      class="row"
    >
      <div class="col-sm-4">
        <TextField
          title="Τηλ. αριθμός*"
          v-model="telephone.tel"
          placeholder="Εισάγετε τηλεφωνικό αριθμό"
          :error="form.errors.get(`telephones.${index}.tel`)"
          @input="form.errors.clear(`telephones.${index}.tel`)"
        />
      </div>
      <div class="col-sm-4">
        <TextField
          title="Πληροφορίες"
          v-model="telephone.info"
          placeholder="Εισάγετε πληροφορίες τηλεφώνου"
          :name="`telephones.${index}.info`"
          :error="form.errors.get(`telephones.${index}.info`)"
          @input="form.errors.clear(`telephones.${index}.info`)"
        />
      </div>
      <div class="col-sm-3">
        <SelectfilterField
          title="Είδος τηλ.*"
          v-model="telephone.type_id"
          placeholder="Επιλέξτε είδος τηλ."
          :name="`telephones.${ index }.type_id`"
          :options="telephoneTypes"
          :error="form.errors.get(`telephones.${ index }.type_id`)"
          @input="form.errors.clear(`telephones.${ index }.type_id`)"
        />
      </div>
      <div class="col-sm-1">
        <button
          type="button"
          style="margin-top: 1.75rem;"
          class="action-button"
          @click="removeTelephone(index)"
        >
          <i class="fa fa-2x fa-trash" />
        </button>
      </div>
    </div>
    <div class="row">
      <div
        class="col-sm-12"
        style="margin-top: 1rem;margin-bottom: 2rem;"
      >
        <a
          href="#"
          @click.prevent="addNewTelephone"
        >
          <i class="fa fa-plus" /> Προσθήκη νέου τηλεφώνου
        </a>
        <span class="text-red">{{ form.errors.get('telephones') }}</span>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-6">
        <button
          type="button"
          class="btn btn-default btn-block"
          @click="$emit('discard-changes')"
        >
          Επιστροφή
        </button>
      </div>
      <div class="col-sm-6">
        <button
          type="button"
          class="btn btn-primary btn-block"
          @click="submit"
        >
          Καταχώρηση
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import SelectfilterField from '../../shared/components/ui/FormFields/SelectfilterField.vue';
import Form from '../../shared/Form';

export default {
  name: 'EployeeContactForm',
  components: {
    TextField,
    SelectfilterField,
  },
  props: {
    employeeContactId: {
      type: [Number, null],
      default: null,
    },
    unit: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      form: new Form({
        name: '',
        surname: '',
        email: '',
        rank_id: null,
        telephones: [],
        isGGA: true,
        unit_id: this.unit.id,
      }),
      ranks: [],
      telephoneTypes: [],
    };
  },
  created() {
    if (this.employeeContactId !== null) {
      this.$http.get(`/api/phonebook/employee-contact/${this.employeeContactId}/edit`)
        .then((res) => {
          this.form.populate(res.data.employeeContact);
          this.ranks = res.data.ranks;
          this.telephoneTypes = res.data.telephoneTypes;
        });
    } else {
      this.$http.get('/api/phonebook/employee-contact/create')
        .then((res) => {
          this.ranks = res.data.ranks;
          this.telephoneTypes = res.data.telephoneTypes;
        });
    }
  },
  methods: {
    submit() {
      if (this.employeeContactId !== null) {
        this.form.put(`/api/phonebook/employee-contact/${this.employeeContactId}`)
          .then((res) => {
            this.$emit('employee-contact-updated', res.data);
          });
      } else {
        this.form.post('/api/phonebook/employee-contact')
          .then((res) => {
            this.$emit('employee-contact-stored', res.data);
          });
      }
    },
    addNewTelephone() {
      this.form.data.telephones.push({
        id: '',
        tel: '',
        type_id: '',
        unit_id: this.form.data.unit_id,
        work: 1,
        info: '',
      });
      this.form.errors.clear('telephones');
    },
    removeTelephone(index) {
      this.form.data.telephones.splice(index, 1);
    },
  },
};

</script>

<style scoped>
.foo{
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}
</style>
