<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">
              Αναζήτηση εργαζομένου
            </h3>
          </div>
          <div class="box-body">
            <EmployeeContactIndexFilter
              :base-url="$authUser() ? 'phonebook' : 'phonebook/public'"
              @employee-contacts-filtered="updateEmployeeContacts"
            />
          </div>
          <div class="box-footer">
            <a :href="$authUser() ? '/phonebook/contact' : '/phonebook/public/contact'">
              <i class="fa fa-search" /> Αναζήτηση επαφών με βάση το οργανόγραμμα
            </a>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="employeeContacts.length > 0"
      class="row"
    >
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">
              Τηλέφωνα Εργαζομένων
            </h3>
          </div>
          <div class="box-body">
            <EmployeeContactListing
              :employee-contacts="employeeContacts"
              :employee-contact-listables="employeeContactListables"
              :searchable="false"
              :allow-editing="false"
              :allow-favorites="!isPublic"
              @toggle-favorite-contact="toggleFavoriteContact"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Gate from '../../shared/mixins/Gate';
import EmployeeContactIndexFilter from './EmployeeContactIndexFilter.vue';
import EmployeeContactListing from './EmployeeContactListing.vue';

export default {
  name: 'EmployeeContactIndex',
  components: {
    EmployeeContactIndexFilter,
    EmployeeContactListing,
  },
  mixins: [Gate],
  props: {
    isPublic: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      employeeContacts: [],
      employeeContactListables: [
        { label: 'Όνομα', field: 'name', type: 'string' },
        { label: 'Επίθετο', field: 'surname', type: 'string' },
        { label: 'Βαθμός', field: 'rank', type: 'string' },
        { label: 'Υπηρεσία', field: 'unit', type: 'string' },
        {
          label: 'Τηλέφωνα', field: 'telephones', type: 'string', sortable: false,
        },
        { label: 'Email', field: 'email', type: 'string' },
        {
          label: 'Ενέργειες', field: 'actions', type: 'number', sortable: false,
        },
      ],
    };
  },
  methods: {
    updateEmployeeContacts(filteredContacts) {
      this.employeeContacts = filteredContacts;
    },
    toggleFavoriteContact(favoriteContact) {
      if (favoriteContact.favorited) {
        this.$http.delete(`/api/phonebook/favorite-contact/${favoriteContact.id}`)
          .then((res) => {
            this.employeeContacts = this.employeeContacts.map(
              (employeeContact) => (employeeContact.id === res.data.id ? res.data : employeeContact),
            );
          });
      }
      if (!favoriteContact.favorited) {
        this.$http.post('/api/phonebook/favorite-contact', favoriteContact)
          .then((res) => {
            this.employeeContacts = this.employeeContacts.map(
              (employeeContact) => (employeeContact.id === res.data.id ? res.data : employeeContact),
            );
          });
      }
    },
  },
};
</script>
