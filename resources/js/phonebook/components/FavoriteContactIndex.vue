<template>
  <div class="row">
    <div class="col-sm-12">
      <div class="box box-primary">
        <div class="box-header">
          <h3 class="box-title">
            Τηλέφωνα Εργαζομένων
          </h3>
        </div>
        <div class="box-body">
          <EmployeeContactListing
            :employee-contacts="employeeContacts"
            :employee-contact-listables="employeeContactListables"
            :allow-favorites="true"
            @toggle-favorite-contact="toggleFavoriteContact"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import EmployeeContactListing from './EmployeeContactListing.vue';

export default {
  name: 'FavoriteContactIndex',
  components: {
    EmployeeContactListing,
  },
  props: {
    favoriteContacts: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      employeeContacts: this.favoriteContacts,
      employeeContactListables: [
        { label: 'Όνομα', field: 'name', type: 'string' },
        { label: 'Επίθετο', field: 'surname', type: 'string' },
        { label: 'Βαθμός', field: 'rank', type: 'string' },
        { label: 'Υπηρεσία', field: 'unit', type: 'string' },
        {
          label: 'Τηλέφωνα', field: 'telephones', type: 'string', sortable: false,
        },
        { label: 'Email', field: 'email', type: 'string' },
        {
          label: 'Ενέργειες', field: 'actions', type: 'number', sortable: false,
        },
      ],
    };
  },
  methods: {
    updateEmployeeContacts(updatedEmployeeContact) {
      this.employeeContacts = this.employeeContacts.map(
        (employeeContact) => (employeeContact.id === updatedEmployeeContact.id ? updatedEmployeeContact : employeeContact),
      );
    },
    toggleFavoriteContact(favoriteContact) {
      if (favoriteContact.favorited) {
        this.$http.delete(`/api/phonebook/favorite-contact/${favoriteContact.id}`)
          .then((res) => {
            this.employeeContacts = this.employeeContacts.map(
              (employeeContact) => (employeeContact.id === res.data.id ? res.data : employeeContact),
            );
          });
      }
      if (!favoriteContact.favorited) {
        this.$http.post('/api/phonebook/favorite-contact', favoriteContact)
          .then((res) => {
            this.employeeContacts = this.employeeContacts.map(
              (employeeContact) => (employeeContact.id === res.data.id ? res.data : employeeContact),
            );
          });
      }
    },
  },
};
</script>

<style scoped>

</style>
