<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">
              Αναζήτηση Επαφών Υπηρεσίας
            </h3>
          </div>
          <div class="box-body">
            <ContactIndexFilter
              :default-unit="authUserUnit"
              :base-url="!isPublic ? 'phonebook': 'phonebook/public'"
              :units-uri="!isPublic ? '/api/units' : '/api/public/units'"
              :organograms-uri="!isPublic ? '/api/organograms' : '/api/public/organograms'"
              authorize-for="phonebook"
              @contacts-filtered="updateContacts"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="$authUser() && $can('phonebook.update') && filteredUnit !== null"
      class="row"
    >
      <div class="col-sm-12">
        <div class="actions">
          <a
            class="actions__item"
            href="#"
            @click.prevent="createEmployeeContact"
          >
            <i class="fa fa-plus" /> Προσθήκη επαφής εργαζομένου
          </a>
          <a
            class="actions__item"
            href="#"
            @click.prevent="createServiceContact"
          >
            <i class="fa fa-plus" /> Προσθήκη επαφής γραφείου
          </a>
        </div>
      </div>
    </div>
    <div
      v-if="contacts.employees.length > 0"
      class="row"
    >
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">
              Τηλέφωνα Εργαζομένων
            </h3>
          </div>
          <div class="box-body">
            <EmployeeContactListing
              :employee-contacts="contacts.employees"
              :employee-contact-listables="employeeContactListables"
              :allow-editing="!isPublic"
              :allow-favorites="!isPublic"
              @edit-employee-contact="editEmployeeContact"
              @delete-employee-contact="deleteEmployeeContact"
              @toggle-favorite-contact="toggleFavoriteContact"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="row"
    >
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">
              Τηλέφωνα Εργαζομένων
            </h3>
          </div>
          <div class="box-body">
            <p>
              Δεν υπάρχουν εγγραφές.
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="contacts.services.length > 0"
      class="row"
    >
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">
              Τηλέφωνα γραφείων
            </h3>
          </div>
          <div class="box-body">
            <ServiceContactListing
              :service-contacts="contacts.services"
              :service-contact-listables="serviceContactListables"
              :allow-editing="!isPublic"
              @edit-service-contact="editServiceContact"
              @delete-service-contact="deleteServiceContact"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="row"
    >
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">
              Τηλέφωνα γραφείων
            </h3>
          </div>
          <div class="box-body">
            <p>
              Δεν υπάρχουν εγγραφές.
            </p>
          </div>
        </div>
      </div>
    </div>
    <Modal
      v-if="showEmployeeContactModal"
      @close="showEmployeeContactModal = false"
    >
      <template #header>
        {{ selectedEmployeeContactId ? 'Επεξεργασία Επαφής' : 'Δημιουργία Επαφής' }}
      </template>
      <EmployeeContactForm
        :employee-contact-id="selectedEmployeeContactId"
        :unit="filteredUnit"
        @employee-contact-stored="storeEmployeeContact"
        @employee-contact-updated="updateEmployeeContact"
        @discard-changes="showEmployeeContactModal = false"
      />
    </Modal>
    <Modal
      v-if="showServiceContactModal"
      @close="showServiceContactModal = false"
    >
      <template #header>
        {{ selectedServiceContactId ? 'Επεξεργασία Επαφής' : 'Δημιουργία Επαφής' }}
      </template>
      <ServiceContactForm
        :service-contact-id="selectedServiceContactId"
        :unit="filteredUnit"
        @service-contact-stored="storeServiceContact"
        @service-contact-updated="updateServiceContact"
        @discard-changes="showServiceContactModal = false"
      />
    </Modal>
  </div>
</template>

<script>
import Modal from '../../shared/components/ui/Modal/Modal.vue';
import { askToConfirm } from '../../shared/Helpers';
import Gate from '../../shared/mixins/Gate';
import ContactIndexFilter from './ContactIndexFilter.vue';
import EmployeeContactForm from './EmployeeContactForm.vue';
import EmployeeContactListing from './EmployeeContactListing.vue';
import ServiceContactForm from './ServiceContactForm.vue';
import ServiceContactListing from './ServiceContactListing.vue';

export default {
  name: 'ContactIndex',
  components: {
    ContactIndexFilter,
    EmployeeContactListing,
    EmployeeContactForm,
    ServiceContactListing,
    ServiceContactForm,
    Modal,
  },
  mixins: [Gate],
  props: {
    authUserUnit: {
      type: Array,
      default() {
        return [];
      },
    },
    isPublic: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      contacts: { employees: [], services: [] },
      filteredUnit: {},
      showEmployeeContactModal: false,
      selectedEmployeeContactId: null,
      showServiceContactModal: false,
      selectedServiceContactId: null,
      employeeContactListables: [
        { label: 'Όνομα', field: 'name', type: 'string' },
        { label: 'Επίθετο', field: 'surname', type: 'string' },
        { label: 'Βαθμός', field: 'rank', type: 'string' },
        { label: 'Υπηρεσία', field: 'unit', type: 'string' },
        {
          label: 'Τηλέφωνα', field: 'telephones', type: 'string', sortable: false,
        },
        { label: 'Email', field: 'email', type: 'string' },
        {
          label: 'Ενέργειες', field: 'actions', type: 'number', sortable: false,
        },
      ],
      serviceContactListables: [
        { label: 'Περιγραφή', field: 'description', type: 'string' },
        {
          label: 'Τηλέφωνο', field: 'telephone', type: 'string', sortable: false,
        },
        { label: 'Πληροφορίες', field: 'info', type: 'string' },
        {
          label: 'Ενέργειες', field: 'actions', type: 'number', sortable: false,
        },
      ],
    };
  },
  methods: {
    updateContacts(filters) {
      this.contacts = filters.filteredContacts;
      this.filteredUnit = filters.filteredUnit;
    },
    createEmployeeContact() {
      this.showEmployeeContactModal = true;
      this.selectedEmployeeContactId = null;
    },
    storeEmployeeContact(newEmployeeContact) {
      this.contacts.employees.push(newEmployeeContact);
      this.showEmployeeContactModal = false;
      this.selectedEmployeeContactId = null;
    },
    editEmployeeContact(employeeContactId) {
      this.showEmployeeContactModal = true;
      this.selectedEmployeeContactId = employeeContactId;
    },
    updateEmployeeContact(updatedEmployeeContact) {
      this.contacts.employees = this.contacts.employees.map(
        (employeeContact) => (employeeContact.id === updatedEmployeeContact.id ? updatedEmployeeContact : employeeContact),
      );
      this.showEmployeeContactModal = false;
      this.selectedEmployeeContactId = null;
    },
    deleteEmployeeContact(employeeContactId) {
      askToConfirm({
        title: 'Είστε σίγουροι?',
        body: 'Θα γίνει διαγραφή της επαφής!',
      }).then(() => {
        this.$http.delete(`/api/phonebook/employee-contact/${employeeContactId}`)
          .then(() => {
            this.contacts.employees = this.contacts.employees
              .filter((employeeContact) => employeeContact.id !== employeeContactId);
          })
          .catch((err) => {
            window.swal({
              type: 'error',
              title: `Σφάλμα ${err.response.status}`,
              text: err.response.data.message,
              showConfirmButton: false,
              timer: 2000,
            });
          });
      });
    },
    toggleFavoriteContact(favoriteContact) {
      if (favoriteContact.favorited) {
        this.$http.delete(`/api/phonebook/favorite-contact/${favoriteContact.id}`)
          .then((res) => {
            this.contacts.employees = this.contacts.employees.map(
              (employeeContact) => (employeeContact.id === res.data.id ? res.data : employeeContact),
            );
          });
      }
      if (!favoriteContact.favorited) {
        this.$http.post('/api/phonebook/favorite-contact', favoriteContact)
          .then((res) => {
            this.contacts.employees = this.contacts.employees.map(
              (employeeContact) => (employeeContact.id === res.data.id ? res.data : employeeContact),
            );
          });
      }
    },
    createServiceContact() {
      this.showServiceContactModal = true;
      this.selectedServiceContactId = null;
    },
    storeServiceContact(newServiceContact) {
      this.contacts.services.push(newServiceContact);
      this.showServiceContactModal = false;
      this.selectedServiceContactId = null;
    },
    editServiceContact(serviceContactId) {
      this.showServiceContactModal = true;
      this.selectedServiceContactId = serviceContactId;
    },

    updateServiceContact(updatedServiceContact) {
      this.contacts.services = this.contacts.services.map(
        (serviceContact) => (serviceContact.id === updatedServiceContact.id ? updatedServiceContact : serviceContact),
      );
      this.showServiceContactModal = false;
      this.selectedServiceContactId = null;
    },
    deleteServiceContact(serviceContactId) {
      askToConfirm({
        title: 'Είστε σίγουροι?',
        body: 'Θα γίνει διαγραφή της επαφής!',
      }).then(() => {
        this.$http.delete(`/api/phonebook/service-contact/${serviceContactId}`)
          .then(() => {
            this.contacts.services = this.contacts.services
              .filter((serviceContact) => serviceContact.id !== serviceContactId);
          })
          .catch((err) => {
            window.swal({
              type: 'error',
              title: `Σφάλμα ${err.response.status}`,
              text: err.response.data.message,
              showConfirmButton: false,
              timer: 2000,
            });
          });
      });
    },
  },
};
</script>

<style scoped>
.actions {
  margin-bottom: 1rem;
  display: flex;
  justify-content: flex-end;
}

.actions__item {
  padding: 1rem;
}
</style>
