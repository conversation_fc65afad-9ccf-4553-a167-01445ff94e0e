<template>
  <div>
    <div class="row">
      <div class="col-sm-6">
        <TextField
          v-model="form.data.description"
          title="Περιγραφή *"
          name="description"
          :error="form.errors.get('description')"
        />
      </div>
      <div class="col-sm-6">
        <TextField
          v-model="form.data.telephone"
          title="Τηλέφωνο *"
          name="telephone"
          :error="form.errors.get('telephone')"
        />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-6">
        <TextField
          v-model="form.data.info"
          title="Πληροφορίες"
          name="info"
          :error="form.errors.get('info')"
        />
      </div>
      <div class="col-sm-6">
        <p class="text-bold">
          Υπηρεσία *
        </p>
        {{ unit.name }}
      </div>
    </div>
    <div class="row">
      <div class="col-sm-6">
        <button
          type="button"
          class="btn btn-default btn-block"
          @click="$emit('discard-changes')"
        >
          Επιστροφή
        </button>
      </div>
      <div class="col-sm-6">
        <button
          class="btn btn-primary btn-block"
          @click="submit"
        >
          Καταχώρηση
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import Form from '../../shared/Form';

export default {
  name: 'ServiceContactForm',
  components: {
    TextField,
  },
  props: {
    serviceContactId: {
      type: [Number, null],
      default: null,
    },
    unit: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      form: new Form({
        id: '',
        description: '',
        telephone: '',
        info: '',
        unit_id: this.unit.id,
      }),
    };
  },
  created() {
    if (this.serviceContactId !== null) {
      this.$http.get(`/api/phonebook/service-contact/${this.serviceContactId}/edit`)
        .then((res) => {
          this.form.populate(res.data);
        });
    }
  },
  methods: {
    submit() {
      if (this.serviceContactId !== null) {
        this.form.put(`/api/phonebook/service-contact/${this.serviceContactId}`)
          .then((res) => {
            this.$emit('service-contact-updated', res.data);
          });
      } else {
        this.form.post('/api/phonebook/service-contact')
          .then((res) => {
            this.$emit('service-contact-stored', res.data);
          });
      }
    },
  },
};

</script>

<style scoped>

</style>
