<template>
  <input
    type="text"
    class="filter--text"
    autofocus
    placeholder="Εισάγετε όνομα ή τηλέφωνο (τουλάχιστον 3 χαρακτήρες)"
    @input="fetchEmployeeContacts($event.target.value)"
  >
</template>

<script>
export default {
  name: 'EmployeeContactIndexFilter',
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  methods: {
    fetchEmployeeContacts(searchQuery) {
      if (searchQuery.length < 4) {
        this.$emit('employee-contacts-filtered', []);
        return;
      }
      this.$http.get(`/api/${this.baseUrl}/employee-contact`, { params: { searchQuery } })
        .then((res) => {
          this.$emit('employee-contacts-filtered', res.data);
        });
    },
  },
};
</script>

<style scoped>
.filter--text {
  width: 100%;
  height: 34px;
  padding: 0.5rem 0.8rem;
  border: 1px solid var(--gray-500);
}

.filter--text:focus {
  outline: none;
}
.filter--text:focus-visible {
  border: 1px solid var(--brand-color);
}
</style>
