<script setup lang="ts">
import { Icon } from '@iconify/vue2';
import { inject, ref } from 'vue';
import type { VueCookies } from 'vue-cookies';

import type {
  Filterabe, Listable, Paginator, ResourceFilters, Sortable,
} from '@/global';
import ResourceViewer from '@/shared/components/ui/Data/ResourceViewer.vue';
import YppoActionLink from '@/shared/components/ui/Links/YppoActionLink.vue';
import useHttp from '@/shared/composables/useHttp';

type User = {
  id: number;
  name: string;
  email: string;
  primaryUnit: string;
  roles: string[]; // If you know the structure of roles, you can replace 'any' with a specific type
  editUrl: string;
  impersonateUrl: string;
  isOnline: boolean
  isService: boolean
  isAdmin: boolean
};

const props = defineProps<{
  users: Paginator<User>;
}>();

const listables: Listable<User & { __actions__: unknown }>[] = [
  { label: 'Όνομα', field: 'name' },
  // { label: 'Email', field: 'email' },
  // { label: 'Μονάδα', field: 'primaryUnit' },
  // { label: 'Ρόλοι', field: 'roles' },
  // { label: '⚙ Ενέργειες', field: '__actions__' },
];
const sortables: Sortable[] = [];
const filterables: Filterabe[] = [
  {
    label: 'Όνομα',
    field: 'name',
    type: 'string',
  },
  {
    label: 'Admin',
    field: 'is_admin',
    type: 'select',
    selectOptions: [
      { id: 0, name: 'Όχι' },
      { id: 1, name: 'Ναι' },
    ],
  },
];

const { get } = useHttp();
const activeUsers = ref<Paginator<User> | {
  data: [],
  meta: Record<string, never>,
  links: Record<string, never>,
}>(props.users);

const fetchActiveUsers = async (filters: ResourceFilters) => {
  try {
    const response = await get<Paginator<User>>('/admin/user', filters);
    if (response.data) {
      activeUsers.value = response.data;
    }
  } catch (e) {
    console.log(e);
  }
};

const $cookies = inject<VueCookies>('$cookies');
const activeFilters: ResourceFilters = $cookies?.get('apptree_admin_filters_users_active') ?? {
  page: 1,
  limit: 10,
};

</script>

<template>
  <div>
    <ResourceViewer
      :listables="listables"
      :sortables="sortables"
      :filterables="filterables"
      :paginated-data="activeUsers"
      filter-cookie="apptree_admin_filters_users_active"
      @fetch-data="fetchActiveUsers"
    >
      <template #default="{ tableData: { column, row: user } }">
        <div
          v-if="column.field === 'name'"
          class="tw:flex tw:gap-2 tw:py-3"
        >
          <div class="tw:space-y-2">
            <div class="tw:flex tw:items-center tw:gap-1">
              <h3 class="tw:whitespace-nowrap tw:font-semibold">
                {{ user.name }}
              </h3>
              <a
                :href="`mailto:${user.email}`"
                class="tw:text-sm tw:text-gray-400"
              >{{ user.email }}</a>
              <span
                v-if="user.isAdmin"
                class="tw:text-cyan-600"
              >
                <Icon
                  icon="tabler:balloon-filled"
                  class="tw:size-5"
                />
              </span>
              <span
                v-if="user.isService"
                class="tw:text-gray-500"
              >
                <Icon
                  icon="tabler:building-bank"
                  class="tw:size-5"
                />
              </span>
              <span
                v-if="user.isOnline"
                class="tw:text-green-500"
              >
                <Icon
                  icon="tabler:wifi"
                  class="tw:size-5"
                />
              </span>
            </div>
            <div
              v-if="!user.isService"
              class="tw:flex tw:items-center tw:gap-1"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="tw:size-4 tw:stroke-gray-400"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0 0 12 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75Z"
                />
              </svg>
              <p v-if="user.primaryUnit">
                {{ user.primaryUnit }}
              </p>
              <p
                v-else
                class="tw:text-red-500"
              >
                Δεν έχει οριστεί Υπηρεσία
              </p>
            </div>
            <ul
              v-if="user.roles.length > 0"
              class="tw:flex tw:gap-2 tw:flex-wrap"
            >
              <li
                v-for="role in user.roles"
                :key="role"
                class="tw:bg-gray-100 tw:px-3 tw:py-2 tw:rounded-full tw:text-sm"
              >
                {{ role }}
                <!--                <p v-if="getRoles(role).application">{{ getRoles(role).application }}</p>-->
                <!--                <p>{{ getRoles(role).role }}</p>-->
                <!--                <p v-if="getRoles(role).space">{{ getRoles(role).space }}</p>-->
              </li>
            </ul>
          </div>
          <div class="tw:flex tw:items-end tw:gap-2 tw:ml-auto">
            <YppoActionLink
              :url="user.editUrl"
              button-class="tw:flex tw:items-center tw:justify-center tw:gap-1 tw:p-2 tw:rounded tw:bg-gray-100 tw:shadow"
            >
              <i class="fa fa-edit tw:flex tw:items-center" />
              <span class="tw:flex tw:items-center">Επεξεργασία</span>
            </YppoActionLink>
            <YppoActionLink
              button-class="tw:flex tw:items-center tw:justify-center tw:gap-1 tw:p-2 tw:rounded tw:bg-gray-100 tw:shadow"
              :url="user.impersonateUrl"
              method="POST"
              :data="{user_id: user.id}"
            >
              <i class="fa fa-user-secret tw:flex tw:items-center" />
              <span class="tw:flex tw:items-center">Hijack</span>
            </YppoActionLink>
          </div>
        </div>
        <div
          v-if="column.field === '__actions__'"
          class="tw:flex tw:items-center tw:gap-2"
        >
          <YppoActionLink
            :url="user.editUrl"
            button-class="tw:flex tw:items-center tw:justify-center tw:gap-1 tw:p-2 tw:rounded tw:bg-gray-200 tw:shadow"
          >
            <i class="fa fa-edit" /> Επεξεργασία
          </YppoActionLink>
          <YppoActionLink
            button-class="tw:flex tw:items-center tw:justify-center tw:gap-1 tw:p-2 tw:rounded tw:bg-gray-200 tw:shadow"
            :url="user.impersonateUrl"
            method="POST"
            :data="{user_id: user.id}"
          >
            <i class="fa fa-user-secret" /> Hijack
          </YppoActionLink>
        </div>
      </template>
    </ResourceViewer>
  </div>
</template>
