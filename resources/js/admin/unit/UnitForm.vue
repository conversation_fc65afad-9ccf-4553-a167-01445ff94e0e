<template>
  <div class="box box-primary">
    <div class="box-header">
      <h3 class="box-title">
        Στοιχεία Υπηρεσίας
      </h3>
    </div>
    <div class="box-body">
      <div class="row">
        <div class="col-sm-12">
          <SwitchField
            v-model="makesNewOrganogram"
            title="Δημιουργεί νέα έκδοση"
          />
        </div>
      </div>
      <div
        v-if="makesNewOrganogram"
        class="row"
      >
        <div class="col-sm-3">
          <TextField
            v-model="newOrganogram.name"
            title="Όνομα νέου οργανογράμματος"
          />
        </div>
        <div class="col-sm-3">
          <DateField
            v-model="newOrganogram.date"
            title="Ημερομηνία"
          />
        </div>
      </div>
      <hr>
      <div class="row">
        <div class="col-sm-12">
          <form
            id="unit-form"
            @input="form.errors.clear($event.target.name)"
          >
            <div class="row">
              <div class="col-sm-3">
                <TextField
                  v-model="form.data.name"
                  title="Όνομα *"
                  name="name"
                  :error="form.errors.get('name')"
                />
              </div>
              <div class="col-sm-3">
                <TextField
                  v-model="form.data.abbrv"
                  title="Συντομογραφία *"
                  name="abbrv"
                  :error="form.errors.get('abbrv')"
                />
              </div>
              <div class="col-sm-3">
                <TextField
                  v-model="form.data.order"
                  title="Σειρά *"
                  name="order"
                  :error="form.errors.get('order')"
                />
              </div>
              <div class="col-sm-3">
                <TextField
                  v-model="form.data.email"
                  title="Email"
                  name="email"
                  :error="form.errors.get('email')"
                />
              </div>
            </div>
            <div class="row">
              <div class="col-sm-3">
                <SelectField
                  v-model="form.data.unit_type_id"
                  title="Είδος Υπηρεσίας *"
                  name="unit_type_id"
                  :error="form.errors.get('unit_type_id')"
                  :options="options['unitTypes']"
                  @input="form.errors.clear('unit_type_id')"
                />
              </div>
              <div class="col-sm-3">
                <SelectField
                  v-if="!makesNewOrganogram"
                  v-model="form.data.organogram_id"
                  title="Οργανόγραμμα *"
                  name="organogram_id"
                  :error="form.errors.get('organogram_id')"
                  :options="options['organograms']"
                  @input="form.errors.clear('organogram_id')"
                />
              </div>
              <div class="col-sm-3">
                <SelectField
                  v-model="form.data.prefecture_id"
                  title="Περιφέρεια"
                  name="prefecture_id"
                  :error="form.errors.get('prefecture_id')"
                  :options="options['prefectures']"
                  @input="form.errors.clear('prefecture_id')"
                />
              </div>
              <div class="col-sm-3">
                <SelectField
                  v-model="form.data.parent_id"
                  title="Πατέρας *"
                  name="parent_id"
                  :error="form.errors.get('parent_id')"
                  :options="options['units']"
                  @input="form.errors.clear('parent_id')"
                />
              </div>
            </div>
            <div class="row">
              <div class="col-sm-3">
                <TextField
                  v-model="form.data.compass_id"
                  title="COMPASS ID *"
                  name="compass_id"
                  :error="form.errors.get('compass_id')"
                />
              </div>
              <div class="col-sm-3">
                <DateField
                  v-if="!makesNewOrganogram"
                  v-model="form.data.started_at"
                  title="Έναρξη"
                  name="started_at"
                  :error="form.errors.get('started_at')"
                  @input="form.errors.clear('started_at')"
                />
              </div>
              <div class="col-sm-3">
                <DateField
                  v-if="!makesNewOrganogram"
                  v-model="form.data.ended_at"
                  title="Λήξη"
                  name="ended_at"
                  :error="form.errors.get('ended_at')"
                  @input="form.errors.clear('ended_at')"
                />
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div class="box-footer">
      <div class="row">
        <div class="col-sm-3">
          <button
            class="btn btn-block btn-default"
            onclick="window.history.back();"
          >
            <i
              class="fa fa-arrow-left"
              aria-hidden="true"
            /> Επιστροφή
          </button>
        </div>
        <div class="col-sm-3">
          <button
            class="btn btn-block btn-default"
            onclick="window.location.reload();"
          >
            <i
              class="fa fa-refresh"
              aria-hidden="true"
            /> Επαναφορά
          </button>
        </div>
        <div class="col-sm-6">
          <button
            class="btn btn-block btn-primary"
            @click="askAndSubmit"
          >
            <i
              class="fa fa-paper-plane"
              aria-hidden="true"
            /> Καταχώριση
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Form from '../../shared/Form';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import SelectField from '../../shared/components/ui/FormFields/SelectfilterField.vue';
import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';

export default {
  name: 'UnitForm',
  components: {
    TextField,
    SelectField,
    DateField,
    SwitchField,
  },
  props: {
    unit: {
      type: Object,
      default: null,
    },
    options: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      form: new Form({
        name: '',
        abbrv: '',
        order: '',
        email: '',
        unit_type_id: '',
        organogram_id: '',
        prefecture_id: '',
        parent_id: '',
        // reference_id: '', This is automatically calculated
        compass_id: '',
        started_at: '',
        ended_at: '',
      }),
      makesNewOrganogram: false,
      newOrganogram: {
        name: '',
        date: '',
      },
    };
  },
  created() {
    if (this.unit) {
      this.form.populate(this.unit);
    }
  },
  methods: {
    askAndSubmit() {
      if (this.makesNewOrganogram) {
        window.swal({
          type: 'warning',
          title: 'Ειστε σίγουρος',
          text: 'Θα δημιουργηθεί νέα version',
          showConfirmButton: true,
          showCancelButton: true,
          closeOnConfirm: true,
        }, () => {
          this.submit();
        });
      } else {
        this.submit();
      }
    },
    submit() {
      const method = this.unit ? 'put' : 'post';
      let url = this.unit
        ? `/admin/units/${this.unit.id}`
        : '/admin/units';
      if (this.makesNewOrganogram) {
        url += `?version[name]=${this.newOrganogram.name}&version[date]=${this.newOrganogram.date}`;
      }
      this.$http[method](url, this.form.data)
        .then((res) => {
          window.swal({
            type: 'success',
            title: 'Επιτυχία',
            text: res.data.message,
            showConfirmButton: false,
            timer: 2000,
          }, () => {
            window.location.href = '/admin/units';
          });
        }).catch((err) => {
          window.swal({
            type: 'error',
            title: `Σφάλμα ${err.response.status}`,
            text: err.response.data.message,
            showConfirmButton: false,
            timer: 2000,
          });
        });
    },
  },
};
</script>

<style scoped>

</style>
