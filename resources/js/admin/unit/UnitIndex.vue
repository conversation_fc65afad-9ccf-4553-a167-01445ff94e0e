<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <div class="box box-primary">
          <div class="box-header">
            <h3 class="box-title">
              Προβολή Δέντρου Υπηρεσιών
            </h3>
          </div>
          <div class="box-body">
            <div class="text-right">
              <a
                class="action-button"
                href="/admin/units/create"
              >
                <i class="fa fa-plus" /> Προσθήκη Υπηρεσίας
              </a>
            </div>
            <UnitTreeField
              v-model="selectedUnit"
              :allow-selecting-multiple-units="false"
              :allow-selecting-organograms="true"
              :allow-showing-departments="true"
              :show-collapsed="false"
            />
            <div
              v-if="selectedUnit.length > 0"
              class="selected-unit"
            >
              <div class="row">
                <div class="col-sm-7">
                  <p class="text-bold">
                    Επιλεγμένη Υπηρεσία
                  </p>
                </div>
              </div>
              <div class="row">
                <div class="col-sm-7">
                  {{ selectedUnit[0].name }}
                </div>
                <div class="col-sm-5">
                  <div class="action-buttons">
                    <a
                      :href="`/admin/units/${selectedUnit[0].id}/edit`"
                      class="action-button"
                    >
                      <i class="fa fa-2x fa-edit" />
                    </a>
                    <a
                      class="action-button"
                      @click.prevent="showDeleteContactModal = true"
                    >
                      <i class="fa fa-2x fa-trash" />
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="box-footer">
            <div class="row">
              <div class="col-sm-3">
                <button
                  class="btn btn-block btn-default"
                  onclick="window.location.href='/home'"
                >
                  <i
                    class="fa fa-arrow-left"
                    aria-hidden="true"
                  /> Επιστροφή
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Modal
      v-if="showDeleteContactModal"
      @close="showDeleteContactModal = false"
    >
      <template #header>
        Διαγραφή Υπηρεσίας
      </template>
      <SwitchField
        v-model="deletesWithNewVersion"
        title="Δημιουργεί νέα έκδοση"
      />
      <template #footer>
        <div class="row">
          <div class="col-sm-4">
            <button
              class="btn btn-block btn-default"
              @click="showDeleteContactModal = false"
            >
              <i
                class="fa fa-arrow-left"
                aria-hidden="true"
              /> Επιστροφή
            </button>
          </div>
          <div class="col-sm-8">
            <button
              class="btn btn-block btn-outline btn-danger"
              @click="deleteSelectedUnit"
            >
              <i
                class="fa fa-trash"
                aria-hidden="true"
              /> Διαγραφή
            </button>
          </div>
        </div>
      </template>
      <div
        v-if="deletesWithNewVersion"
        class="row"
      >
        <div class="col-sm-4">
          <TextField
            v-model="newOrganogram.name"
            title="Όνομα νέου οργανογράμματος"
          />
        </div>
        <div class="col-sm-4">
          <DateField
            v-model="newOrganogram.date"
            title="Ημερομηνία"
          />
        </div>
        <div class="col-sm-4">
          <SelectField
            v-model="newParentOfDeletedUnitChildren"
            title="Μεταφορά υποκείμενων μονάδων"
            name="new_parent_id"
            :options="options"
          />
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import SelectField from '../../shared/components/ui/FormFields/SelectfilterField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import UnitTreeField from '../../shared/components/ui/FormFields/UnitTreeField.vue';
import Modal from '../../shared/components/ui/Modal/Modal.vue';

export default {
  name: 'UnitIndex',
  components: {
    UnitTreeField,
    Modal,
    SwitchField,
    TextField,
    DateField,
    SelectField,
  },
  props: {
    options: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      selectedUnit: [],
      showDeleteContactModal: false,
      deletesWithNewVersion: false,
      newOrganogram: {
        name: '',
        date: '',
      },
      newParentOfDeletedUnitChildren: '',
    };
  },
  methods: {
    deleteSelectedUnit() {
      const url = this.deletesWithNewVersion
        ? `/admin/units/${this.selectedUnit[0].id}?version[name]=${this.newOrganogram.name}
        &version[date]=${this.newOrganogram.date}&new_parent_id=${this.newParentOfDeletedUnitChildren}`
        : `/admin/units/${this.selectedUnit[0].id}`;

      this.$http.delete(url)
        .then((res) => {
          this.showDeleteContactModal = false;
          window.swal({
            type: 'success',
            title: 'Επιτυχία',
            text: res.data.message,
            showConfirmButton: false,
            timer: 2000,
          }, () => {
            window.location.reload();
          });
        })
        .catch((err) => {
          window.swal({
            type: 'error',
            title: `Σφάλμα ${err.response.status}`,
            text: err.response.data.message,
            showConfirmButton: false,
            timer: 2000,
          });
        });
    },
  },
};
</script>

<style scoped>

.selected-unit {
  margin-top: 1rem;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
}

.action-button {
  color: var(--gray-600);
  margin-left: 1rem;
}

.action-button:hover {
  color: var(--brand-color);
}
</style>
