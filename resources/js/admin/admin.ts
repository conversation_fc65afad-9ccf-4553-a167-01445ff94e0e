/* eslint-disable @typescript-eslint/ban-ts-comment, no-new */
// @ts-nocheck
import '../bootstrap';

import ActiveUsersList from '@/admin/components/ActiveUsersList.vue';
import UnitTreeFieldHttp from '@/shared/components/ui/FormFields/UnitTreeFieldHttp.vue';

Vue.component('ActiveUsersList', ActiveUsersList);
Vue.component('UnitTreeFieldHttp', UnitTreeFieldHttp);

const cookies = Vue.prototype.$cookies;

new Vue({
  el: '#app',
  provide: {
    $cookies: cookies,
  },
});
