import '../bootstrap'

/**
 * Import Passport components
 */
import Clients from './components/Clients.vue';
import AuthorizedClients from './components/AuthorizedClients.vue';
import PersonalAccessTokens from './components/PersonalAccessTokens.vue';

/**
 * Register the passport components globally
 */
Vue.component('passport-clients', Clients);
Vue.component('passport-authorized-clients', AuthorizedClients);
Vue.component('passport-personal-access-tokens', PersonalAccessTokens);

new Vue().$mount('#app-passport');