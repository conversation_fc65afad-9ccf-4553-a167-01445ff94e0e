/**
 * Global scripts.
 *
 * This file contains:
 * - various JQuery scripts for global UI elements initialization:
 *   - Datatables (JQuery)
 *   - Select2 (JQuery)
 *   - i-Check (JQuery)
 *   - delete button (uses SweetAlert)
 * - Global helper functions
 */

(function (global) {
  $(document).ready(() => {
    /*
     * Initialize select2 inputs
     */
    $('.select2').select2({
      language: 'el',
      placeholder: 'Επιλέξτε...',
      allowClear: true,
    });

    /*
     * Initialize datatables
     */
    $.fn.dataTable.moment('DD-MM-YYYY');
    $.fn.dataTable.moment('DD/MM/YYYY');
    $.fn.dataTable.moment('DD-MM-YYYY, HH:mm');

    $.extend($.fn.dataTable.ext.type.order, {
      "greek-string-asc": function(a, b) {
        return a.localeCompare(b, 'el', { sensitivity: 'base' });
      },
      "greek-string-desc": function(a, b) {
        return b.localeCompare(a, 'el', { sensitivity: 'base' });
      }
    });

    $('table.apptree-dt').dataTable({
      dom: "<'row'<'col-sm-6'l><'col-sm-6 text-right'B>>frtip",
      columnDefs: [
        { type: 'greek-string', targets: '_all' } // Apply this sorting to all columns. Adjust as necessary.
      ],
      buttons: [
        {
          text: '<i class="fa fa-print" aria-hidden="true"></i> Εκτύπωση',
          extend: 'print',
          className: 'btn btn-default btn-xs',
        },
        {
          text: '<i class="fa fa-file-excel-o" aria-hidden="true"></i> Excel',
          extend: 'excel',
          className: 'btn btn-default btn-xs',
        },
        {
          text: '<i class="fa fa-file-pdf-o" aria-hidden="true"></i> PDF',
          extend: 'pdf',
          className: 'btn btn-default btn-xs',
        },
      ],
      paging: true,
      lengthChange: true,
      searching: true,
      ordering: true,
      info: true,
      autoWidth: false,
    });
    $.extend(true, $.fn.dataTable.defaults, {
      language: {
        url: '/js/datatables.greek.lang',
      },
    });

    /*
     * Initialize iCheck checkbox and radio inputs
     */
    $('.is-iCheck').iCheck({
      checkboxClass: 'icheckbox_square-blue',
      radioClass: 'iradio_square-blue',
      increaseArea: '20%', // optional
    });

    /*
     * Initialize delete button confirmation using sweetalert.
     * This is for button element inside form.
     */
    $('.delete-btn').click(function (e) {
      // Prevent default action (eg. delete).
      e.preventDefault();

      const deleteForm = $(this).parents('form');

      // Show the confirmation message
      swal({
        title: 'Είστε σίγουροι?',
        text: 'Θα γίνει διαγραφή της εγγραφής!',
        type: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef6477',
        confirmButtonText: 'Ναι!',
        cancelButtonText: 'Όχι.',
        closeOnConfirm: true,
        closeOnCancel: true,
      },
      (isConfirm) => {
        if (isConfirm) {
          // If user clicks ok, then submit the form.
          deleteForm.submit();
        }
      });
    });
  });

  /**
    * A helper function to update a select's options based on another select's value, getting the data from an ajax request
    *
    * @param  {string}             $value  The value of the object that invokes this
    * @param  {object}             $target The jquery dom element to update with new options
    * @param  {string}             $url    The url to call through ajax
    * @param  {string}             $idattr The attribute of the JSON array to be used as id of the options
    * @param  {string}             $descriptionattr The attribute of the JSON array to be used as text of the options
    * @return {null}
    */
  // TODO: Move it to personnel as it is not a global script
  function updateSelectOptions($value, $target, $url, $idattr, $descriptionattr) {
    // Set default values
    $idattr = typeof $idattr !== 'undefined' ? $idattr : 'id';
    $descriptionattr = typeof $descriptionattr !== 'undefined' ? $descriptionattr : 'name';

    // Empty the current options
    $target.empty();
    $target.append('<option value="" selected="selected">Επιλέξτε...</option>');

    // Get new options through ajax
    if ($value) {
      $.getJSON($url, (data) => {
        $.each(data, (key, val) => {
          $target.append(`<option value="${val[$idattr]}">${val[$descriptionattr]}</option>`);
        });
      });
    }

    // Refresh select object
    $target.trigger('change');
  }

  global.updateSelectOptions = updateSelectOptions; // used by personnel/positions/create.js
}(window));
