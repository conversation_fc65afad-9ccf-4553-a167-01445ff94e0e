/*
 ===========================================================================
 Here we export all required rules for transforming the data received
 from the server (via either XHR response or props)
 ===========================================================================
 */

/*
Rule for preparing data received by ConservatorForm
 */
export function conservatorFormReceiveRule(serverData) {

    let initData = serverData;
    let res = {}; // must be the same type as serverData

    Object.keys(initData).forEach(item => {
        if (item === 'materials') {
            // res[item] = initData[item];
            res[item] = [];
            if (initData[item].length > 0) {
                initData[item].forEach((element) => {
                    res[item].push(
                        {
                            material_id: element.pivot.material_id,
                            issued_at: element.pivot.issued_at,
                            issued_record_number: element.pivot.issued_record_number
                        }
                    );
                });
            }
        }
        /**
         * In all other cases
         */
        else
            res[item] = initData[item];
    });

    return res;
}

/*
Rule to prepare data send by ConservatorForm
 */
export function conservatorFormSendRule(serverData) {
    // This is where the ready for submission data will be stored
    let initData = serverData;
    let readyData = {};

    Object.keys(initData).forEach(field => {
        // If its values is a Date obj
        if (field === 'materials') {
            readyData[field] = [];
            if (initData[field].length > 0) {
                initData[field].forEach((element) => {
                    readyData[field].push(
                        {
                            material_id: element.material_id,
                            issued_at: element.issued_at, // this date is saved inside a pivot table so there is no mutator and we need to send it in sql format (YYYY-MM-DD)
                            issued_record_number: element.issued_record_number
                        }
                    );
                });
            }

        // If not special case, just copy the field value to the readyData
        } else {
            readyData[field] = initData[field];
        }
    });
    return readyData;
}