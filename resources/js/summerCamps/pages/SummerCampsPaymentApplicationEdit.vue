<template>
  <div>
    <SuccessAlert v-if="successMessage">
      <p>{{ successMessage }}</p>
    </SuccessAlert>
    <DangerAlert
      v-if="errorMessage"
      :message="errorMessage"
    />
    <Box
      title="Επεξεργασία Αίτησης Αποπληρωμής"
      :is-loading="form.busy"
    >
      <form
        id="summer-camps-application-form"
        @submit.prevent="save"
      >
        <FormSection
          title="Γενικά Στοιχεία"
          icon="fa-pencil-square-o"
        >
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <TextField
                v-model="form.data.name"
                title="Όνομα"
                name="name"
                :error="form.errors.get('name')"
              />
            </div>
            <div class="col-sm-12 col-md-6">
              <TextField
                v-model="form.data.surname"
                title="Επώνυμο"
                name="surname"
                :error="form.errors.get('surname')"
              />
            </div>
            <div class="col-sm-12 col-md-6">
              <TextField
                v-model="form.data.father_name"
                title="Πατρώνυμο"
                name="father_name"
                :error="form.errors.get('father_name')"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <SelectField
                v-model="form.data.employment_type_id"
                title="Σχέση Εργασίας"
                name="employment_type_id"
                :error="form.errors.get('employment_type_id')"
                :options="employmentTypeOptions"
              />
            </div>
            <div class="col-sm-12 col-md-6">
              <SelectField
                v-model="form.data.employment_sector_id"
                title="Τομέας"
                name="employment_sector_id"
                :error="form.errors.get('employment_sector_id')"
                :options="employmentSectorOptions"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12">
              <TextField
                v-model="form.data.position"
                title="Υπηρεσία Οργανικής Θέσης"
                name="position"
                :error="form.errors.get('position')"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <TextField
                v-model="form.data.payroll_id"
                title="Αρ. Μητρώου Μισθοδοσίας"
                name="payroll_id"
                :error="form.errors.get('payroll_id')"
              />
            </div>
          </div>
        </FormSection>
        <FormSection
          title="Στοιχεία Επικοινωνίας"
          icon="fa-envelope-o"
        >
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <TextField
                v-model="form.data.personal_phone"
                title="Τηλέφωνο Επικοινωνίας"
                name="personal_phone"
                :error="form.errors.get('personal_phone')"
              />
            </div>
            <div class="col-sm-12 col-md-6">
              <TextField
                v-model="form.data.mobile_phone"
                title="Κινητό"
                name="mobile_phone"
                :error="form.errors.get('mobile_phone')"
              />
            </div>
            <div class="col-sm-12 col-md-6">
              <TextField
                v-model="form.data.work_phone"
                title="Τηλέφωνο Εργασίας"
                name="work_phone"
                :error="form.errors.get('work_phone')"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <TextField
                v-model="form.data.email_address"
                title="Email Επικοινωνίας"
                name="email_address"
                :error="form.errors.get('email_address')"
              />
            </div>
          </div>
        </FormSection>
        <FormSection
          title="Ωφελούμενα Τέκνα"
          icon="fa-child"
        >
          <payment-application-children
            :payment-application-id="paymentApplication.id"
            :payment-application-children-data="paymentApplication.payment_application_children"
          />
        </FormSection>
        <FormSection
          title="Επισυνάψεις"
          icon="fa-paperclip"
        >
          <div class="row">
            <div class="col-sm-12">
              <UploadAjaxField
                :initial-files="form.data.attachments"
                title="Δικαιολογητικά"
                name="attachments"
                :base-url="`/api/summer-camps/payment-applications/${paymentApplication.id}/attachments`"
                :error="form.errors.attachments"
              />
            </div>
          </div>
        </FormSection>
        <hr>
        <div class="row">
          <div class="col-sm-4">
            <a
              class="btn btn-default btn-block"
              href="/summer-camps/payment-applications"
            >
              <i
                class="fa fa-arrow-left"
                aria-hidden="true"
              /> Επιστροφή
            </a>
          </div>
          <div class="col-sm-4">
            <LoadingButton
              class="btn btn-block btn-primary btn-outline"
              type="button"
              :loading="form.busy"
              @click="save"
            >
              <i class="fa fa-save" /> Προσωρινή αποθήκευση
            </LoadingButton>
          </div>
          <div class="col-sm-4">
            <LoadingButton
              class="btn btn-block btn-primary"
              type="button"
              :loading="form.busy"
              @click="saveAndPreview"
            >
              <i class="fa fa-arrow-right" /> Προς Υποβολή
            </LoadingButton>
          </div>
        </div>
      </form>
    </Box>
  </div>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import SuccessAlert from '@/shared/components/ui/Alerts/SuccessAlert.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';
import LoadingButton from '@/shared/components/ui/Buttons/LoadingButton.vue';
import SubmitButton from '@/shared/components/ui/Buttons/SubmitButton.vue';
import DateField from '@/shared/components/ui/FormFields/DateField.vue';
import FormSection from '@/shared/components/ui/FormFields/FormSection.vue';
import NumberField from '@/shared/components/ui/FormFields/NumberField.vue';
import SelectField from '@/shared/components/ui/FormFields/SelectField.vue';
import SwitchField from '@/shared/components/ui/FormFields/SwitchField.vue';
import TextareaField from '@/shared/components/ui/FormFields/TextareaField.vue';
import TextField from '@/shared/components/ui/FormFields/TextField.vue';
import UploadAjaxField from '@/shared/components/ui/FormFields/UploadAjaxField.vue';

import Form from '../../shared/Form';
import __ from '../../shared/Helpers';
// import {isAxiosError} from "@/shared/composables/useHttp";

export default {
  name: 'SummerCampsPaymentApplicationEdit',

  components: {
    UploadAjaxField,
    DangerAlert,
    SuccessAlert,
    FormSection,
    Box,
    LoadingButton,
    SubmitButton,
    TextField,
    TextareaField,
    DateField,
    SelectField,
    SwitchField,
    NumberField,
  },

  props: {
    paymentApplication: {
      type: Object,
      default: null,
    },
    employmentSectorOptions: {
      type: Array,
      default: null,
    },
    employmentTypeOptions: {
      type: Array,
      default: null,
    },
  },

  data() {
    return {
      isLoading: false,
      form: new Form({
        name: this.paymentApplication.name,
        surname: this.paymentApplication.surname,
        father_name: this.paymentApplication.father_name,
        employment_sector_id: this.paymentApplication.employment_sector_id,
        employment_type_id: this.paymentApplication.employment_type_id,
        position: this.paymentApplication.position,
        payroll_id: this.paymentApplication.payroll_id,
        personal_phone: this.paymentApplication.personal_phone,
        mobile_phone: this.paymentApplication.mobile_phone,
        work_phone: this.paymentApplication.work_phone,
        email_address: this.paymentApplication.email_address,
        attachments: this.paymentApplication.attachments ?? [],
      }),
      successMessage: '',
      errorMessage: '',
    };
  },

  methods: {

    async save() {
      this.successMessage = '';
      this.errorMessage = '';

      try {
        const response = await this.form.put(`/api/summer-camps/payment-applications/${this.paymentApplication.id}`);
        this.successMessage = response.data.message;
        window.scrollTo(0, 0);
      } catch (err) {
        this.errorMessage = err.response?.data?.message ?? 'Παρουσιάστηκε σφάλμα κατά την καταχώρηση της αίτησης αποπληρωμής';
        window.scrollTo(0, 0);
      }
    },

    async saveAndPreview() {
      this.successMessage = '';
      this.errorMessage = '';

      try {
        await this.form.put(`/api/summer-camps/payment-applications/${this.paymentApplication.id}`);
        window.location.replace(`/summer-camps/payment-applications/${this.paymentApplication.id}/`);
        // success(response.message);
      } catch (err) {
        // if (isAxiosError(err)) {
        this.errorMessage = err.response?.data?.message ?? 'Παρουσιάστηκε σφάλμα κατά την καταχώρηση της αίτησης αποπληρωμής';
        window.scrollTo(0, 0);
        // }
      }
    },
  },
};

</script>
