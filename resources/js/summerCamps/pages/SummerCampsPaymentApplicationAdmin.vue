<template>
  <div>
    <div class="row">
      <div class="col-sm-12">
        <Box
            :title="title"
            :is-loading="isFetchingApplications"
        >
          <ResourceViewer
              v-if="! fetchingApplicationsError"
              :paginated-data="listedApplications"
              :filterables="filterables"
              :sortables="sortables"
              :listables="listables"
              filter-cookie="summer_camps_applications_filters"
              @fetch-data="fetchApplications"
          >
            <template #default="{ tableData }">
              <p
                  v-if="tableData.column.field === 'actions'"
                  class="action-buttons"
              >
                <a
                    :href="`/summer-camps/payment-applications/${tableData.row.id}`"
                    class="action-button"
                >
                  <i class="fa fa-2x fa-info-circle" />
                </a>
              </p>
              <p v-else>
                {{ tableData.row[tableData.column.field] }}
              </p>
            </template>
          </ResourceViewer>
          <DangerAlert
            v-else
            :message="fetchingApplicationsError"
          />
        </Box>
      </div>
    </div>
    <div class="row">
        <div class="col-sm-3">
          <button
              class="btn btn-default btn-block"
              onclick="window.location.href='/summer-camps';"
          >
            <i
                class="fa fa-arrow-left"
                aria-hidden="true"
            /> Επιστροφή
          </button>
        </div>
    </div>
  </div>
</template>
<script>
import ResourceViewer from '@/shared/components/ui/Data/ResourceViewer.vue';
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';

export default {
  name: 'SummerCampsApplicationAdmin',

  components: {
    ResourceViewer,
    DangerAlert,
    Box,
  },

  props: {
    season: {
      type: Object,
      required: true,
    },
    employmentSectorOptions: {
      type: Array,
      default() {
        return [
          { id: 1, name: 'Τομέας Πολιτισμού' },
          { id: 2, name: 'Τομέας Αθλητισμού' },
        ];
      },
    },
    employmentTypeOptions: {
      type: Array,
      default() {
        return [
          { id: 1, name: 'Μόνιμος' },
          { id: 2, name: 'ΙΔΑΧ' },
        ];
      },
    },
  },

  data() {
    return {
      listedApplications: { data: [], meta: {}, links: {} },
      isFetchingApplications: false,
      fetchingApplicationsError: null,
      listables: [
        { label: 'Επίθετο', field: 'surname' },
        { label: 'Όνομα', field: 'name' },
        { label: 'Πατρώνυμο', field: 'father_name' },
        { label: 'Email', field: 'email_address' },
        { label: 'Τηλέφωνο', field: 'phones' },
        { label: 'Αρ. Πρωτ.', field: 'protocol' },
        { label: 'Ημ. Υποβολής', field: 'submitted_at' },
        { label: 'Αρ. Τέκνων', field: 'payment_application_children_count' },
        // { label: 'Ημέρες', field: 'days' },
        { label: 'Ενέργειες', field: 'actions', type: 'number' },
      ],
      filterables: [
        { label: 'Επίθετο', field: 'surname', type: 'string' },
        { label: 'Email', field: 'email_address', type: 'string' },
        { label: 'Αρ. Πρωτ.', field: 'protocol', type: 'string' },
        { label: 'Τομέας', field: 'employment_sector_id', type: 'select', selectOptions: this.employmentSectorOptions },
        { label: 'Σχέση Εργασίας', field: 'employment_type_id', type: 'select', selectOptions: this.employmentTypeOptions },
      ],
      sortables: [
        { label: 'Επίθετο', field: 'surname' },
        { label: 'Email', field: 'email_address' },
        { label: 'Αρ. Πρωτ.', field: 'protocol' },
        { label: 'Ημ. Υποβολής', field: 'submitted_at' },
      ],
    };
  },
  created() {
    // TODO: Consider storing filter information into the session and pass xxxAplicationsData as props
    this.fetchApplications(this.$cookies.get('summer_camps_applications_filters'));
  },
  computed: {
    title() {
      return 'Αιτήσεις Αποπληρωμής';
    },
  },
  methods: {
    applicationIsSubmitted(application) {
      return application.is_submitted === true;
    },
    fetchApplications(filters) {
      this.isFetchingApplications = true;
      this.fetchingApplicationsError = null;

      const url = `/api/summer-camps/application-admin/${this.season.id}/payment-applications`;

      this.$http.get(url, { params: filters })
        .then((res) => {
          this.listedApplications = res.data.data; // res.data: { ... data: { data: [...], meta: {...} }, ... }
        })
        .catch((err) => {
          this.fetchingApplicationsError = err.response.data.message;
        })
        .finally(() => {
          this.isFetchingApplications = false;
        });
    },

  },
}
</script>
