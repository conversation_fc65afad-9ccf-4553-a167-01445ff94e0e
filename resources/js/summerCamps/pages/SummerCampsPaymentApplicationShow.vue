<template>
  <div>
    <SuccessAlert v-if="successMessage">
      <p>{{ successMessage }}</p>
    </SuccessAlert>
    <DangerAlert
      v-if="errorMessage"
      :message="errorMessage"
    />
    <div
      v-if="Object.keys(submitErrors).length !== 0"
      class="alert alert-danger"
    >
      <ul>
        <li
          v-for="(error, index) in submitErrors"
          :key="index"
        >
          {{ error[0] }}
        </li>
      </ul>
    </div>
    <Box
      title="Προβολή Αίτησης Αποπληρωμής"
    >
      <InfoAlert v-if="paymentApplication.submitted_at">
        <p>Η Αίτηση Αποπληρωμής έχει υποβληθεί προς πληρωμή οριστικά στις {{ paymentApplication.submitted_at }}.</p>
        <p><strong>Αρ. Πρωτοκόλλου: {{ paymentApplication.protocol }}</strong></p>
        <p>
          <a
            :href="`/summer-camps/payment-application-exports/${paymentApplication.id}`"
            target="_blank"
            class="btn btn-outline btn-primary"
            style="text-decoration: none;"
            v-if="updatePermission || signedAttachments.length == 0"
          >
            <i class="fa fa-print" /> Εκτύπωση Αίτησης Αποπληρωμής
          </a>
          <a
            :href="signedAttachments[0].url"
            target="_blank"
            class="btn btn-outline btn-primary"
            style="text-decoration: none;"
            v-else
          >
            <i class="fa fa-print" /> Εκτύπωση Υπογεγραμμένης Αίτησης Αποπληρωμής
          </a>
        </p>
      </InfoAlert>
      <WarningAlert v-else>
        <p>Η Αίτηση Αποπληρωμής είναι προσωρινά αποθηκευμένη και δεν έχει υποβληθεί!</p>
        <p>
          Μπορείτε να την υποβάλετε οριστικά πατώντας το κουμπί "Οριστική Υποβολή",
          αφού βεβαιωθείτε για την ορθότητα των υποβαλλόμενων στοιχείων.
        </p>
      </WarningAlert>
      <WarningAlert v-if="paymentApplication.submitted_at && signedAttachments.length == 0">
        <p>
          Εκκρεμεί η επισύναψη της υπογεγραμμένης αίτησης.
        </p>
        <p v-if="updatePermission">
          Αφού εκτυπώσετε την αίτηση, υπογράψτε την και επισυνάψτε την υπογεγραμμένη αίτηση.
        </p>
      </WarningAlert>
      <FormSection
        title="Γενικά Στοιχεία"
        icon="fa-pencil-square-o"
      >
        <div class="row">
          <div class="col-sm-12 col-md-6">
            <h5 class="text-bold">
              Όνομα
            </h5>
            <p>{{ paymentApplication.name }}</p>
          </div>
          <div class="col-sm-12 col-md-6">
            <h5 class="text-bold">
              Επώνυμο
            </h5>
            <p>{{ paymentApplication.surname }}</p>
          </div>
          <div class="col-sm-12 col-md-6">
            <h5 class="text-bold">
              Πατρώνυμο
            </h5>
            <p>{{ paymentApplication.father_name }}</p>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 col-md-6">
            <h5 class="text-bold">
              Σχέση Εργασίας
            </h5>
            <p>{{ paymentApplication.employment_type ? paymentApplication.employment_type.name : '' }}</p>
          </div>
          <div class="col-sm-12 col-md-6">
            <h5 class="text-bold">
              Τομέας
            </h5>
            <p>{{ paymentApplication.employment_sector ? paymentApplication.employment_sector.name : '' }}</p>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12">
            <h5 class="text-bold">
              Υπηρεσία Οργανικής Θέσης
            </h5>
            <p>{{ paymentApplication.position }}</p>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 col-md-6">
            <h5 class="text-bold">
              Αρ. Μητρώου Μισθοδοσίας
            </h5>
            <p>{{ paymentApplication.payroll_id }}</p>
          </div>
        </div>
      </FormSection>
      <hr>
      <FormSection
        title="Στοιχεία Επικοινωνίας"
        icon="fa-envelope-o"
      >
        <div class="row">
          <div class="col-sm-12 col-md-6">
            <h5 class="text-bold">
              Τηλέφωνο Επικοινωνίας
            </h5>
            <p>{{ paymentApplication.personal_phone }}</p>
          </div>
          <div class="col-sm-12 col-md-6">
            <h5 class="text-bold">
              Κινητό
            </h5>
            <p>{{ paymentApplication.mobile_phone }}</p>
          </div>
          <div class="col-sm-12 col-md-6">
            <h5 class="text-bold">
              Τηλέφωνο Εργασίας
            </h5>
            <p>{{ paymentApplication.work_phone }}</p>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 col-md-6">
            <h5 class="text-bold">
              Email Επικοινωνίας
            </h5>
            <p>{{ paymentApplication.email_address }}</p>
          </div>
        </div>
      </FormSection>
      <hr>
      <FormSection
        title="Ωφελούμενα Τέκνα"
        icon="fa-child"
      >
        <div class="row">
          <div class="col-sm-12">
            <table
              v-if="paymentApplication.payment_application_children.length > 0"
              class="table table-condensed"
            >
              <thead>
                <tr>
                  <th>A/A</th>
                  <th>Ονοματεπώνυμο</th>
                  <th>ΑμεΑ</th>
                  <th>Πάροχοι</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(child, index) in paymentApplication.payment_application_children"
                  :key="index"
                >
                  <td>{{ index + 1 }}</td>
                  <td>{{ child.full_name }}</td>
                  <td>{{ child.has_disability ? 'NAI ' : '' }}</td>
                  <td>{{ child.summer_camps }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </FormSection>
      <hr>
      <FormSection
        title="Επισυνάψεις"
        icon="fa-paperclip"
      >
        <div class="row">
          <div class="col-sm-12">
            <h5 class="text-bold">
              Δικαιολογητικά
            </h5>
            <ul>
              <li
                v-for="(attachment, index) in paymentApplication.attachments"
                :key="index"
              >
                <a :href="attachment.url">
                  <i class="fa fa-file-o" /> {{ attachment.name }}
                </a>
              </li>
            </ul>
          </div>
          <div
            v-if="paymentApplication.attachments.length && !updatePermission"
            class="col-sm-4"
          >
            <a
              :href="`/summer-camps/payment-application-attachment-zip/${paymentApplication.id}`"
              class="btn btn-outline btn-primary btn-block"
            >
              <i class="fa fa-download" /> Λήψη Όλων
            </a>
          </div>
        </div>
      </FormSection>
      <hr v-if="paymentApplication.submitted_at && updatePermission">
      <FormSection
        v-if="paymentApplication.submitted_at && updatePermission"
        title="Υπογεγραμμένη Αίτηση"
        icon="fa-pencil"
      >
        <div class="row">
          <div class="col-sm-12">
            <SingleUploadAjaxField
              :initial-files="signedAttachments"
              title="Υπογεγραμμένη Αίτηση"
              name="attachments"
              :base-url="`/api/summer-camps/payment-applications/${paymentApplication.id}/signed-attachments`"
              :error="attachmentsError"
              :files-change="updateSignedAttachments"
            />
          </div>
        </div>
      </FormSection>
      <template #footer>
        <div class="row">
          <div class="col-sm-3">
            <a
              class="btn btn-default btn-block"
              href="/summer-camps/payment-applications"
            >
              <i
                class="fa fa-arrow-left"
                aria-hidden="true"
              /> Επιστροφή
            </a>
          </div>
          <div
            v-if="updatePermission"
            :class="['col-sm-3', paymentApplication.submitted_at ? 'col-sm-offset-6': 'col-sm-offset-3']"
          >
            <a
              v-if="!paymentApplication.submitted_at"
              :href="`/summer-camps/payment-applications/${paymentApplication.id}/edit`"
              class="btn btn-outline btn-primary btn-block"
            >
              <i class="fa fa-pencil-square-o" /> Επεξεργασία
            </a>
          </div>
          <div class="col-sm-3">
            <button
              v-if="paymentApplication.submitted_at && updatePermission"
              type="button"
              class="btn btn-danger btn-outline btn-block"
              @click="withdrawModalOpen = true"
            >
              <i class="fa fa-undo" /> Αναίρεση οριστικής υποβολής
            </button>
            <button
              v-if="! paymentApplication.submitted_at && updatePermission"
              type="button"
              class="btn btn-primary btn-block"
              @click="submitModalOpen = true"
            >
              <i class="fa fa-paper-plane" /> Οριστική Υποβολή
            </button>
          </div>
        </div>
      </template>
    </Box>
    <ConfirmationModal
      cancel-btn-text="Ακύρωση"
      confirm-btn-text="Οριστική Υποβολή"
      :open="submitModalOpen"
      :processing="loading"
      @confirm="submit"
      @cancel="submitModalOpen = false"
    >
      <div class="max-w-prose mx-auto">
        <p>
          Πρόκειται να υποβάλετε οριστικά την αίτησή σας.
          Αν έχετε βεβαιωθεί για την ορθότητα των υποβαλλόμενων στοιχείων και θέλετε να συνεχίσετε,
          πατήστε το κουμπί "Οριστική Υποβολή". Διαφορετικά, πατήστε το κουμπί "Ακύρωση".
        </p>
      </div>
    </ConfirmationModal>
    <ConfirmationModal
      cancel-btn-text="Ακύρωση"
      confirm-btn-text="Αναίρεση υποβολής"
      :open="withdrawModalOpen"
      :processing="loading"
      @confirm="withdraw"
      @cancel="withdrawModalOpen = false"
    >
      <div class="max-w-prose mx-auto">
        <p>
          Πρόκειται να αναιρέσετε την υποβολή της αίτησης. Θέλετε να συνεχίσετε;.
        </p>
      </div>
    </ConfirmationModal>
  </div>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';
import SuccessAlert from '@/shared/components/ui/Alerts/SuccessAlert.vue';
import WarningAlert from '@/shared/components/ui/Alerts/WarningAlert.vue';
import Box from '@/shared/components/ui/Boxes/Box.vue';
import FormSection from '@/shared/components/ui/FormFields/FormSection.vue';
import SingleUploadAjaxField from '@/shared/components/ui/FormFields/SingleUploadAjaxField.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';

import __ from '../../shared/Helpers';

export default {
  name: 'SummerCampsPaymentApplicationShow',

  components: {
    SingleUploadAjaxField,
    ConfirmationModal,
    WarningAlert,
    InfoAlert,
    DangerAlert,
    SuccessAlert,
    FormSection,
    Box,
  },

  props: {
    paymentApplication: {
      type: Object,
      default: null,
    },
    updatePermission: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      isLoading: false,
      successMessage: '',
      errorMessage: '',
      submitErrors: {},
      withdrawModalOpen: false,
      submitModalOpen: false,
      loading: false,
      attachmentsError: '',
      signedAttachments: [],
    };
  },

  created() {
    this.signedAttachments = this.paymentApplication.signedAttachments;
  },

  methods: {
    async submit() {
      this.successMessage = '';
      this.errorMessage = '';
      this.submitErrors = {};

      try {
        const response = await this.$http.post(`/api/summer-camps/submitted-payment-applications/${this.paymentApplication.id}`, [this.paymentApplication]);
        window.location.replace(`/summer-camps/payment-applications/${this.paymentApplication.id}/`);
        window.scrollTo(0, 0);
      } catch (err) {
        this.errorMessage = err.response?.data?.message ?? 'Παρουσιάστηκε σφάλμα κατά την υποβολή της αίτησης';
        this.submitErrors = err.response?.data?.errors;
        window.scrollTo(0, 0);
      }
      this.submitModalOpen = false;
    },

    async withdraw() {
      console.log('withrdraw submit');
      this.successMessage = '';
      this.errorMessage = '';

      try {
        await this.$http.delete(`/api/summer-camps/submitted-payment-applications/${this.paymentApplication.id}`);
        window.location.replace(`/summer-camps/payment-applications/${this.paymentApplication.id}/`);
      } catch (err) {
        this.errorMessage = err.response?.data?.message ?? 'Παρουσιάστηκε σφάλμα κατά την αναίρεση υποβολής της αίτησης';
        window.scrollTo(0, 0);
      }

      this.withdrawModalOpen = false;
    },

    updateSignedAttachments(files) {
      this.signedAttachments = files;
    },
  },
};

</script>
