<template>
  <div>
    <a
      href="#"
      @click.prevent="openModal"
    >
      <i class="fa fa-pencil-square-o fa-2x" />
    </a>
    <BasicModal
      :open="isOpen"
      @close="closeModal"
    >
      <template #header>
        <h4>Επεξεργασία</h4>
      </template>
      <ApplicationChildForm
        :application-child="applicationChild"
        :application-id="applicationId"
        @update="onUpdate"
        @cancel="closeModal"
      />
    </BasicModal>
  </div>
</template>

<script>
import BasicModal from '@/shared/components/ui/Modal/BasicModal.vue';
import ApplicationChildForm from '@/summerCamps/components/application/applicationChild/ApplicationChildForm.vue'

export default {
  name: 'EditApplicationChildButton',
  components: {
    BasicModal,
    ApplicationChildForm,
  },
  props: {
    applicationChild: {
      type: Object,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      isOpen: false,
    };
  },
  methods: {
    onUpdate(child) {
      this.$emit('update', child);
      this.closeModal();
    },
    openModal() {
      this.isOpen = true;
    },
    closeModal() {
      this.isOpen = false;
    },
  },
};
</script>

<style scoped>
a {
  color: var(--gray-600);
  text-decoration: none;
}

a:hover {
  color: var(--yellow-600);
}
</style>
