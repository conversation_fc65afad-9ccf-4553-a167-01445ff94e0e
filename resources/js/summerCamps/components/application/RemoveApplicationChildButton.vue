<template>
  <div>
    <a
      href="#"
      @click.prevent="openModal"
    >
      <i class="fa fa-trash-o fa-2x" />
    </a>
    <ConfirmationModal
      :open="isOpen"
      :is-loading="isLoading"
      @confirm="removeApplicationChild"
      @cancel="closeModal"
    >
      <DangerAlert v-if="errorMessage" message="errormessage" />
      Είστε βέβαιος/η ότι θέλετε να προχωρήσετε στην διαγραφή;
    </ConfirmationModal>
  </div>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';

export default {
  name: 'RemoveApplicationChildButton',
  components: {
    ConfirmationModal,
    DangerAlert,
  },
  props: {
    applicationChild: {
      type: Object,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      isOpen: false,
      isLoading: false,
      errorMessage: null,
    };
  },
  methods: {
    removeApplicationChild() {
      this.isLoading = true;
      this.errorMessage = null;
      this.$http.delete(`/api/summer-camps/applications/${this.applicationId}/application-children/${this.applicationChild.id}`)
        .then(() => {
          this.$emit('remove', this.applicationChild);
          this.closeModal();
        })
        .catch((error) => {
          this.errorMessage = error.response.data.message;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    openModal() {
      this.isOpen = true;
    },
    closeModal() {
      this.isOpen = false;
    },
  },
};
</script>

<style scoped>
a {
  color: var(--gray-600);
  text-decoration: none;
}

a:hover {
  color: var(--red-600);
}
</style>
