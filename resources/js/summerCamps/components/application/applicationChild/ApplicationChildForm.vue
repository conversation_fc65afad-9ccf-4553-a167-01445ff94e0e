<template>
  <form @submit.prevent="upsert">
    <div class="row">
      <div class="col-sm-12">
        <DangerAlert v-if="errorMessage" :message="errorMessage" />
      </div>
    </div>
    <div class="row" style="margin-bottom: 20px">
      <div class="col-sm-12">
        <TextField
          v-model="form.data.full_name"
          title="Ονοματεπώνυμο"
          name="full_name"
          :error="form.errors.get('full_name')"
        />
      </div>
      <div class="col-sm-12">
        <DateField
            v-model="form.data.birthdate"
            title="Ημερομηνία Γέννησης"
            name="birthdate"
            format="dd-MM-yyyy"
            value-format="dd-MM-yyyy"
            :error="form.errors.get('birthdate')"
        />
      </div>
    </div>
    <div class="row" style="margin-bottom: 20px">
      <div class="col-sm-12">
        <CheckField
          v-model="form.data.has_disability"
          title="ΑμεΑ"
          name="has_disability"
          :error="form.errors.get('has_disability')"
          @input="form.errors.clear('has_disability')"
        />
        <NumberField
          v-model="form.data.disability_percentage"
          title="Ποσοστό Αναπηρίας"
          name="disability_percentage"
          :error="form.errors.get('disability_percentage')"
          v-if="form.data.has_disability"
        />
      </div>
    </div>
    <div class="row" style="margin-bottom: 20px">
      <div class="col-sm-12">
        <NumberField
          v-model="form.data.days"
          title="Αριθμός Ημερών Διαμονής"
          name="days"
          :error="form.errors.get('days')"
        />
      </div>
      <div class="col-sm-12">
        <TextareaField
          v-model="form.data.summer_camps"
          title="Πάροχοι Κατασκήνωσης"
          name="summer_camps"
          :error="form.errors.get('summer_camps')"
          @input="form.errors.clear('summer_camps')"
        />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-6">
        <CancelButton @cancel="$emit('cancel')">
          <i class="fa fa-arrow-left" /> Επιστροφή
        </CancelButton>
      </div>
      <div class="col-sm-6">
        <SubmitButton :busy="form.busy">
          <i class="fa fa-floppy-o" /> Αποθήκευση
        </SubmitButton>
      </div>
    </div>
  </form>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import CancelButton from '@/shared/components/ui/Buttons/CancelButton.vue';
import SubmitButton from '@/shared/components/ui/Buttons/SubmitButton.vue';
import CheckField from '@/shared/components/ui/FormFields/CheckField.vue';
import TextField from '@/shared/components/ui/FormFields/TextField.vue';
import Form from '@/shared/Form';
import DateField from "@/shared/components/ui/FormFields/DateField.vue";
import NumberField from "@/shared/components/ui/FormFields/NumberField.vue";
import TextareaField from "@/shared/components/ui/FormFields/TextareaField.vue";

export default {
  name: 'ApplicationChildForm',
  components: {
    TextareaField,
    NumberField,
    DateField,
    CheckField,
    TextField,
    SubmitButton,
    CancelButton,
    DangerAlert,
  },
  props: {
    applicationChild: {
      type: Object,
      default: () => null,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      form: new Form({
        full_name: this.applicationChild ? this.applicationChild.full_name : '',
        birthdate: this.applicationChild ? this.applicationChild.birthdate : '',
        has_disability: this.applicationChild ? this.applicationChild.has_disability : false,
        disability_percentage: this.applicationChild ? this.applicationChild.disability_percentage : 0,
        days: this.applicationChild ? this.applicationChild.days : 0,
        summer_camps: this.applicationChild ? this.applicationChild.summer_camps : '',
      }),
      errorMessage: null,
    };
  },
  computed: {
    isEditing() {
      return this.applicationChild !== null;
    },
  },
  watch: {
    'form.data.has_disability': function () {
      console.log('check disability');
      if (!this.form.data.has_disability) {
        console.log('reset disability');
        this.form.data.disability_percentage = 0;
      }
    },
  },
  methods: {
    async upsert() {
      const url = this.isEditing
        ? `/api/summer-camps/applications/${this.applicationId}/application-children/${this.applicationChild.id}`
        : `/api/summer-camps/applications/${this.applicationId}/application-children`;

      const method = this.isEditing ? 'put' : 'post';

      const event = this.isEditing ? 'update' : 'create';

      this.errorMessage = null;

      try {
        const res = await this.form[method](url);
        this.$emit(event, res.data.data);
        console.log('kakdakdkasd');
      } catch (e) {
        this.errorMessage = e.response.data.message;
      }
    },
  },
};
</script>
