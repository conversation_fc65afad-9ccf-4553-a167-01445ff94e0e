<template>
  <div>
    <div class="row">
      <div class="col-sm-12 text-right">
        <AddApplicationChildButton
          :application-id="applicationId"
          @create="addApplicationChild"
        />
      </div>
      <div class="col-sm-12">
        <ApplicationChildList
          :application-children="applicationChildren"
          :application-id="applicationId"
          @update="updateApplicationChild"
          @remove="removeApplicationChild"
        />
      </div>
    </div>
  </div>
</template>

<script>

import AddApplicationChildButton from '@/summerCamps/components/application/AddApplicationChildButton.vue';
import ApplicationChildList from '@/summerCamps/components/application/applicationChild/ApplicationChildList.vue';
import ApplicationChildForm from "@/summerCamps/components/application/applicationChild/ApplicationChildForm";

export default {
  name: 'ApplicationChildren',
  components: {
    ApplicationChildForm,
    ApplicationChildList,
    AddApplicationChildButton,
  },
  props: {
    applicationChildrenData: {
      type: Array,
      required: true,
    },
    applicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      applicationChildren: this.applicationChildrenData,
    };
  },
  methods: {
    addApplicationChild(child) {
      this.applicationChildren.push(child);
    },
    updateApplicationChild(child) {
      const index = this.applicationChildren.findIndex((d) => d.id === child.id);
      this.applicationChildren.splice(index, 1, child);
    },
    removeApplicationChild(child) {
      this.applicationChildren = this.applicationChildren.filter((d) => d.id !== child.id);
    },
  },
};
</script>
