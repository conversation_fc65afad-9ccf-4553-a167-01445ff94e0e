<template>
  <div>
    <a
      href="#"
      @click.prevent="openModal"
    >
      <i class="fa fa-plus-square-o fa-2x" /> Προσθήκη
    </a>
    <BasicModal
      :open="isOpen"
      @close="closeModal"
    >
      <template #header>
        <h4>Προσθήκη</h4>
      </template>
      <PaymentApplicationChildForm
        :payment-application-id="paymentApplicationId"
        @create="onCreate"
        @cancel="closeModal"
      />
    </BasicModal>
  </div>
</template>

<script>
import BasicModal from '@/shared/components/ui/Modal/BasicModal.vue';
import PaymentApplicationChildForm from "@/summerCamps/components/paymentApplication/paymentApplicationChild/PaymentApplicationChildForm.vue";

export default {
  name: 'AddPaymentApplicationChildButton',
  components: {
    PaymentApplicationChildForm,
    BasicModal,
  },
  props: {
    paymentApplicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      isOpen: false,
    };
  },
  methods: {
    onCreate(child) {
      this.$emit('create', child);
      this.closeModal();
    },
    openModal() {
      this.isOpen = true;
    },
    closeModal() {
      this.isOpen = false;
    },
  },
};
</script>

<style scoped>
a {
  color: var(--gray-600);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  margin-bottom: 1rem;
}

a i {
  margin-right: 0.5rem;
}

a:hover {
  color: var(--blue-600);
}
</style>
