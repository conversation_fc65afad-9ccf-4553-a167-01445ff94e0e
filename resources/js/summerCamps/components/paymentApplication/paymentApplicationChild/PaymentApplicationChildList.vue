<template>
  <table
    v-if="paymentApplicationChildren.length > 0"
    class="table table-condensed"
  >
    <thead>
      <tr>
        <th>A/A</th>
        <th>Ονοματεπώνυμο</th>
        <th>ΑμεΑ</th>
        <th><i class="fa fa-cog" /></th>
      </tr>
    </thead>
    <tbody>
      <tr
        v-for="(child, index) in paymentApplicationChildren"
        :key="index"
      >
        <td>
          {{ index + 1 }}
        </td>
        <td>{{ child.full_name }}</td>
        <td>{{ child.has_disability ? 'NAI' : '' }}</td>
        <td>
          <div>
            <EditPaymentApplicationChildButton
              :payment-application-child="child"
              :payment-application-id="paymentApplicationId"
              @update="$emit('update', $event)"
              style="float:left"
            />
            <RemovePaymentApplicationChildButton
              :payment-application-child="child"
              :payment-application-id="paymentApplicationId"
              @remove="$emit('remove', $event)"
              style="float:left; margin-left:10px"
            />
          </div>
        </td>
      </tr>
    </tbody>
  </table>
  <InfoAlert v-else>
    <i class="fa fa-exclamation-circle" /> Δεν έχουν καταχωρισθεί τέκνα
  </InfoAlert>
</template>

<script>
import EditPaymentApplicationChildButton from '@/summerCamps/components/paymentApplication/EditPaymentApplicationChildButton.vue';
import RemovePaymentApplicationChildButton from '@/summerCamps/components/paymentApplication/RemovePaymentApplicationChildButton.vue';
import InfoAlert from '@/shared/components/ui/Alerts/InfoAlert.vue';

export default {
  name: 'PaymentApplicationChildList',
  components: {
    InfoAlert,
    EditPaymentApplicationChildButton,
    RemovePaymentApplicationChildButton,
  },
  props: {
    paymentApplicationChildren: {
      type: Array,
      required: true,
    },
    paymentApplicationId: {
      type: Number,
      required: true,
    },
  },
};
</script>

<style scoped>
table.assets-table thead,
table.positions-table thead {
  background-color: var(--gray-400)
}

table.assets-table tbody tr td {
  border: unset;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  vertical-align: middle;
}

table.assets-table thead tr th:first-child,
table.assets-table tbody tr td:first-child {
  width: 32px;
  text-align: center;
}

table.assets-table thead tr th:last-child,
table.assets-table tbody tr td:last-child {
  width: 100px;
  text-align: center;
}

table.assets-table tbody tr td:last-child div {
  display: flex;
  justify-content: space-around;
}
</style>
