<template>
  <form @submit.prevent="upsert">
    <div class="row">
      <div class="col-sm-12">
        <DangerAlert v-if="errorMessage" :message="errorMessage" />
      </div>
    </div>
    <div class="row" style="margin-bottom: 20px">
      <div class="col-sm-12">
        <TextField
          v-model="form.data.full_name"
          title="Ονοματεπώνυμο"
          name="full_name"
          :error="form.errors.get('full_name')"
        />
      </div>
    </div>
    <div class="row" style="margin-bottom: 20px">
      <div class="col-sm-12">
        <CheckField
          v-model="form.data.has_disability"
          title="ΑμεΑ"
          name="has_disability"
          :error="form.errors.get('has_disability')"
          @input="form.errors.clear('has_disability')"
        />
      </div>
    </div>
    <div class="row" style="margin-bottom: 20px">
      <div class="col-sm-12">
        <TextareaField
          v-model="form.data.summer_camps"
          title="Πάροχοι (Όνομα κατασκήνωσης)"
          name="summer_camps"
          :required="true"
          :error="form.errors.get('summer_camps')"
          @input="form.errors.clear('summer_camps')"
        />
      </div>
    </div>
    <div class="row">
      <div class="col-sm-6">
        <CancelButton @cancel="$emit('cancel')">
          <i class="fa fa-arrow-left" /> Επιστροφή
        </CancelButton>
      </div>
      <div class="col-sm-6">
        <SubmitButton :busy="form.busy">
          <i class="fa fa-floppy-o" /> Αποθήκευση
        </SubmitButton>
      </div>
    </div>
  </form>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import CancelButton from '@/shared/components/ui/Buttons/CancelButton.vue';
import SubmitButton from '@/shared/components/ui/Buttons/SubmitButton.vue';
import CheckField from '@/shared/components/ui/FormFields/CheckField.vue';
import TextField from '@/shared/components/ui/FormFields/TextField.vue';
import Form from '@/shared/Form';
import DateField from "@/shared/components/ui/FormFields/DateField.vue";
import NumberField from "@/shared/components/ui/FormFields/NumberField.vue";
import TextareaField from "@/shared/components/ui/FormFields/TextareaField.vue";

export default {
  name: 'PaymentApplicationChildForm',
  components: {
    TextareaField,
    NumberField,
    DateField,
    CheckField,
    TextField,
    SubmitButton,
    CancelButton,
    DangerAlert,
  },
  props: {
    paymentApplicationChild: {
      type: Object,
      default: () => null,
    },
    paymentApplicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      form: new Form({
        full_name: this.paymentApplicationChild ? this.paymentApplicationChild.full_name : '',
        has_disability: this.paymentApplicationChild ? this.paymentApplicationChild.has_disability : false,
        summer_camps: this.paymentApplicationChild ? this.paymentApplicationChild.summer_camps : '',
      }),
      errorMessage: null,
    };
  },
  computed: {
    isEditing() {
      return this.paymentApplicationChild !== null;
    },
  },
  methods: {
    async upsert() {
      const url = this.isEditing
        ? `/api/summer-camps/payment-applications/${this.paymentApplicationId}/payment-application-children/${this.paymentApplicationChild.id}`
        : `/api/summer-camps/payment-applications/${this.paymentApplicationId}/payment-application-children`;

      const method = this.isEditing ? 'put' : 'post';

      const event = this.isEditing ? 'update' : 'create';

      this.errorMessage = null;

      try {
        const res = await this.form[method](url);
        this.$emit(event, res.data.data);
      } catch (e) {
        this.errorMessage = e.response.data.message;
      }
    },
  },
};
</script>
