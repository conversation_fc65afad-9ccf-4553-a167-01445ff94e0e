<template>
  <div>
    <div class="row">
      <div class="col-sm-12 text-right">
        <AddPaymentApplicationChildButton
          :payment-application-id="paymentApplicationId"
          @create="addChild"
        />
      </div>
      <div class="col-sm-12">
        <PaymentApplicationChildList
          :payment-application-children="children"
          :payment-application-id="paymentApplicationId"
          @update="updateChild"
          @remove="removeChild"
        />
      </div>
    </div>
  </div>
</template>

<script>

import AddPaymentApplicationChildButton from '@/summerCamps/components/paymentApplication/AddPaymentApplicationChildButton.vue';
import PaymentApplicationChildList from '@/summerCamps/components/paymentApplication/paymentApplicationChild/PaymentApplicationChildList.vue';
import PaymentApplicationChildForm from "@/summerCamps/components/paymentApplication/paymentApplicationChild/PaymentApplicationChildForm.vue";


export default {
  name: 'PaymentApplicationChildren',
  components: {
    AddPaymentApplicationChildButton,
    PaymentApplicationChildList,
    PaymentApplicationChildForm
  },
  props: {
    paymentApplicationChildrenData: {
      type: Array,
      required: true,
    },
    paymentApplicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      children: this.paymentApplicationChildrenData,
    };
  },
  methods: {
    addChild(child) {
      this.children.push(child);
    },
    updateChild(child) {
      const index = this.children.findIndex((d) => d.id === child.id);
      this.children.splice(index, 1, child);
    },
    removeChild(child) {
      this.children = this.children.filter((d) => d.id !== child.id);
    },
  },
};
</script>
