<template>
  <div>
    <a
      href="#"
      @click.prevent="openModal"
    >
      <i class="fa fa-trash-o fa-2x" />
    </a>
    <ConfirmationModal
      :open="isOpen"
      :is-loading="isLoading"
      @confirm="removeChild"
      @cancel="closeModal"
    >
      <DangerAlert v-if="errorMessage" message="errormessage" />
      Είστε βέβαιος/η ότι θέλετε να προχωρήσετε στην διαγραφή;
    </ConfirmationModal>
  </div>
</template>

<script>
import DangerAlert from '@/shared/components/ui/Alerts/DangerAlert.vue';
import ConfirmationModal from '@/shared/components/ui/Modal/ConfirmationModal.vue';

export default {
  name: 'RemovePaymentApplicationChildButton',
  components: {
    ConfirmationModal,
    DangerAlert,
  },
  props: {
    paymentApplicationChild: {
      type: Object,
      required: true,
    },
    paymentApplicationId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      isOpen: false,
      isLoading: false,
      errorMessage: null,
    };
  },
  methods: {
    removeChild() {
      this.isLoading = true;
      this.errorMessage = null;
      this.$http.delete(`/api/summer-camps/payment-applications/${this.paymentApplicationId}/payment-application-children/${this.paymentApplicationChild.id}`)
        .then(() => {
          this.$emit('remove', this.paymentApplicationChild);
          this.closeModal();
        })
        .catch((error) => {
          this.errorMessage = error.response.data.message;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    openModal() {
      this.isOpen = true;
    },
    closeModal() {
      this.isOpen = false;
    },
  },
};
</script>

<style scoped>
a {
  color: var(--gray-600);
  text-decoration: none;
}

a:hover {
  color: var(--red-600);
}
</style>
