import '../bootstrap';

import SummerCampsApplicationEdit from "@/summerCamps/pages/SummerCampsApplicationEdit.vue";
import SummerCampsApplicationShow from "@/summerCamps/pages/SummerCampsApplicationShow.vue";
import SummerCampsApplicationAdmin from "@/summerCamps/pages/SummerCampsApplicationAdmin.vue";
import AddApplicationChildButton from "@/summerCamps/components/application/AddApplicationChildButton.vue";
import EditApplicationChildButton from "@/summerCamps/components/application/EditApplicationChildButton.vue";
import RemoveApplicationChildButton from "@/summerCamps/components/application/RemoveApplicationChildButton.vue";
import ApplicationChildForm from "@/summerCamps/components/application/applicationChild/ApplicationChildForm.vue";
import ApplicationChildList from "@/summerCamps/components/application/applicationChild/ApplicationChildList.vue";
import ApplicationChildren from "@/summerCamps/components/application/applicationChild/ApplicationChildren.vue";

import SummerCampsPaymentApplicationEdit from "@/summerCamps/pages/SummerCampsPaymentApplicationEdit.vue";
import SummerCampsPaymentApplicationShow from "@/summerCamps/pages/SummerCampsPaymentApplicationShow.vue";
import SummerCampsPaymentApplicationAdmin from "@/summerCamps/pages/SummerCampsPaymentApplicationAdmin.vue";
import AddPaymentApplicationChildButton from "@/summerCamps/components/paymentApplication/AddPaymentApplicationChildButton.vue";
import EditPaymentApplicationChildButton from "@/summerCamps/components/paymentApplication/EditPaymentApplicationChildButton.vue";
import RemovePaymentApplicationChildButton from "@/summerCamps/components/paymentApplication/RemovePaymentApplicationChildButton.vue";
import PaymentApplicationChildForm from "@/summerCamps/components/paymentApplication/paymentApplicationChild/PaymentApplicationChildForm.vue";
import PaymentApplicationChildList from "@/summerCamps/components/paymentApplication/paymentApplicationChild/PaymentApplicationChildList.vue";
import PaymentApplicationChildren from "@/summerCamps/components/paymentApplication/paymentApplicationChild/PaymentApplicationChildren.vue";

Vue.component('SummerCampsApplicationEdit', SummerCampsApplicationEdit);
Vue.component('SummerCampsApplicationShow', SummerCampsApplicationShow);
Vue.component('SummerCampsApplicationAdmin', SummerCampsApplicationAdmin);
Vue.component('AddApplicationChildButton', AddApplicationChildButton);
Vue.component('EditApplicationChildButton', EditApplicationChildButton);
Vue.component('RemoveApplicationChildButtonChildButton', RemoveApplicationChildButton);
Vue.component('ApplicationChildForm', ApplicationChildForm);
Vue.component('ApplicationChildList', ApplicationChildList);
Vue.component('ApplicationChildren', ApplicationChildren);

Vue.component('SummerCampsPaymentApplicationEdit', SummerCampsPaymentApplicationEdit);
Vue.component('SummerCampsPaymentApplicationShow', SummerCampsPaymentApplicationShow);
Vue.component('SummerCampsPaymentApplicationAdmin', SummerCampsPaymentApplicationAdmin);
Vue.component('AddPaymentApplicationChildButton', AddPaymentApplicationChildButton);
Vue.component('EditPaymentApplicationChildButton', EditPaymentApplicationChildButton);
Vue.component('RemovePaymentApplicationChildButtonChildButton', RemovePaymentApplicationChildButton);
Vue.component('PaymentApplicationChildForm', PaymentApplicationChildForm);
Vue.component('PaymentApplicationChildList', PaymentApplicationChildList);
Vue.component('PaymentApplicationChildren', PaymentApplicationChildren);

/**
 * Instantiate the route Vue instance
 */
new Vue({
  el: '#app',
});
