/* eslint-disable camelcase */
declare namespace Ui {
    export type SelectOption = {
        id: number;
        name: string;
    }
}

export type Links = {
    first: string;
    last: string;
    prev: string|null;
    next: string|null;
}

export type Meta = {
    current_page: number;
    from: number;
    last_page: number;
    path: string;
    per_page: number;
    to: number;
    total: number;
}

export type Paginator<T> = {
    data: T[];
    links: Links;
    meta: Meta;
}

export type FilterObject = {
    field: string;
    operator: string;
    value: number | string | (number | string)[];
};

export type SortObject = {
    column: string;
    direction: string;
};

export type ResourceFilters = {
    filters?: FilterObject[];
    sorts?: SortObject[];
    page: number;
    limit: number;
}

export type Filterabe<T = Record<string, unknown>> = {
    label: string;
    field: keyof T;
    type: string;
    selectOptions?: Ui.SelectOption[];
}

export type Sortable<T = Record<string, unknown> > = {
    label: string;
    field: keyof T;
}

export type Listable<T = Record<string, unknown>> = {
    label: string;
    field: keyof T;
}
/**
 * What is a user authorized to do?
 */
declare namespace Auth {
    type User = {
        id: number;
        name: string;
        unit_id: number;
        can?: { [ability in Auth.Ability]?: boolean };
    }
    type Ability = 'admin' | 'create' | 'read' | 'update' | 'delete';
}

declare global {
  interface Window {
    Laravel: {
      csrfToken: string;
    };
  }
}
