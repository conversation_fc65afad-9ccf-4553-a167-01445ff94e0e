import { createLocalVue, shallowMount } from '@vue/test-utils';
import Element<PERSON> from 'element-ui';

import ContestApllications from '../../contractuals/components/application/ContestApplications.vue';

describe('ContestApllications', () => {
  let wrapper;
  const contest = {
    id: 1,
    name: 'Προκύρηξη πρόσληψης επιστημονικού και εργατοτεχνικού προσωπικού με σύμβαση εργασίας ιδιωτικού δικαίου ορισμένου χρόνου για την εκτέλεση του Υποέργου: «Αρχαιολογικές έρευνες και εργασίες της ΕΑΠΝ στο πλαίσιο του ενταγμένου στον «Άξονα Προτεραιότητας 01 –Ενίσχυση Υποδομών Προσπελασιμότητας – Ενέργειας» του Ε.Π. Αττική έργου: «Βελτίωση Επαρχιακής οδού Δρυόπης (Κόμβος Καλλονής) – Γαλατάς (Διακριτό Τμήμα Βελτίωσης)» με κωδικό MIS 379433, του Δήμου Τροιζηνίας», που υλοποιείται με τη μέθοδο της αρχαιολ',
    type: 'SOX',
    protocol_number: '1',
    protocol_date: '2017-03-27 00:00:00',
    ada: 'ΗΞΣ76896ΚΙΞΔ',
    start_date: '2017-03-28 00:00:00',
    end_date: '2018-10-31 00:00:00',
    locked_at: null,
    finalized_at: '-0001-11-30 00:00:00',
    year: '2017',
    total_positions: '7',
    total_applications: 1,
    directorates: [{
      id: 4,
      name: 'Γραμματεία Κεντρικού Αρχαιολογικού Συμβουλίου',
      abbrv: 'ΚΑΣ',
      order_no: 170,
      email: '<EMAIL>',
      gdirectorate_id: 1,
      dircategory_id: 1,
      prefecture_id: null,
      active: 1,
      compass_id: 'Α37',
      created_at: '1999-12-31 22:00:00',
      updated_at: null,
      deleted_at: null,
      pivot: { contest_id: 1, directorate_id: 4 },
    }],
    positions: [
      {
        id: 1,
        specialization_id: 1,
        amount: 5,
        location: 'Αρχαία Μεσσήνη',
        contest_id: 1,
        directorate_id: 337,
        directorate: {
          id: 337,
          name: 'Εφορεία Αρχαιοτήτων Μεσσηνίας',
          abbrv: 'ΕΦΑ ΜΕΣΣΗ',
          order_no: 1070,
          email: '<EMAIL>',
          gdirectorate_id: 1,
          dircategory_id: 30,
          prefecture_id: null,
          active: 1,
          compass_id: 'Β08',
          created_at: '2014-10-28 22:00:00',
          updated_at: null,
          deleted_at: null,
        },
        specialization: {
          id: 1,
          name: 'ΠΕ Αρχαιολόγοι',
          shortname: 'ΠΕ Αρχαιολόγοι',
          specialization_type_id: 1,
        },
      },
      {
        id: 5,
        specialization_id: 3,
        amount: 2,
        location: 'Πάτρα',
        contest_id: 1,
        directorate_id: 26,
        directorate: {
          id: 26,
          name: 'Διεύθυνση Προϊστορικών & Κλασικών Αρχαιοτήτων',
          abbrv: 'ΔΙΠΚΑ',
          order_no: 110,
          email: '<EMAIL>',
          gdirectorate_id: 1,
          dircategory_id: 1,
          prefecture_id: null,
          active: 1,
          compass_id: 'Α31',
          created_at: '1999-12-31 22:00:00',
          updated_at: null,
          deleted_at: null,
        },
        specialization: {
          id: 3,
          name: 'ΠΕ Γεωλόγοι',
          shortname: 'ΠΕ Γεωλόγοι',
          specialization_type_id: 1,
        },
      },
    ],
    applications: [
      {
        id: 6,
        applicant_id: 8,
        contest_id: 1,
        name: 'John',
        surname: 'Doe',
        fathername: 'Jack',
        mothername: 'Jane',
        birthdate: '1980-11-15 00:00:00',
        greek_nationality: 1,
        eu_citizen: 0,
        policeid_number: 'AA123456',
        policeid_date: '2000-12-10 00:00:00',
        afm: '114865648',
        doy: 'A Athens',
        amka: '01118102258',
        street: 'Example st.',
        street_number: '123',
        postcode: '123456',
        city: 'Athens',
        phonenumber1: '2131234567',
        phonenumber2: '',
        email: '<EMAIL>',
        applicant_category: 1,
        meets_general_requirements: 1,
        submitted_at: '-0001-11-30 00:00:00',
        protocol_number: '000001',
        protocol_date: '2018-10-02 00:00:00',
        invalidated: 0,
        invalidation_description: '',
        locked_at: '2018-11-06 00:00:00',
        rated_at: null,
        rejected: null,
        applicant_full_name: 'John Doe',
        evaluations: [
          {
            id: 131,
            application_id: 6,
            qualification_id: 435,
            position_id: 1,
            requirement_id: 8,
            auxiliary_level: 0,
            relevant: false,
            points: '0.00',
          },
          {
            id: 144,
            application_id: 6,
            qualification_id: 435,
            position_id: 5,
            requirement_id: 8,
            auxiliary_level: 1,
            relevant: false,
            points: '0.00',
          },
        ],
      },
    ],
  };
  const applications = [
    {
      id: 6,
      applicant_id: 8,
      contest_id: 1,
      name: 'John',
      surname: 'Doe',
      fathername: 'Jack',
      mothername: 'Jane',
      birthdate: '1980-11-15 00:00:00',
      greek_nationality: 1,
      eu_citizen: 0,
      policeid_number: 'AA123456',
      policeid_date: '2000-12-10 00:00:00',
      afm: '114865648',
      doy: 'A Athens',
      amka: '01118102258',
      street: 'Example st.',
      street_number: '123',
      postcode: '123456',
      city: 'Athens',
      phonenumber1: '2131234567',
      phonenumber2: '',
      email: '<EMAIL>',
      applicant_category: 1,
      meets_general_requirements: 1,
      submitted_at: '-0001-11-30 00:00:00',
      protocol_number: '000001',
      protocol_date: '2018-10-02 00:00:00',
      invalidated: 0,
      invalidation_description: '',
      locked_at: '2018-11-06 00:00:00',
      rated_at: null,
      rejected: null,
      applicant_full_name: 'John Doe',
      has_evaluations: true,
      evaluations: [
        {
          id: 131,
          application_id: 6,
          qualification_id: 435,
          position_id: 1,
          requirement_id: 8,
          auxiliary_level: 0,
          relevant: false,
          points: '0.00',
        },
        {
          id: 144,
          application_id: 6,
          qualification_id: 435,
          position_id: 5,
          requirement_id: 8,
          auxiliary_level: 1,
          relevant: false,
          points: '0.00',
        },
      ],
    },
  ];
  const props = {
    contestData: contest,
    applicationData: applications,
  };

  beforeEach(() => {
    const localVue = createLocalVue();
    localVue.use(ElementUI);

    wrapper = shallowMount(ContestApllications, {
      propsData: props,
      localVue,
    });
  });

  describe('when created', () => {
    it('initializes vue-good-table rows', () => {
      expect(wrapper.vm.rows).toEqual(props.applicationData);
    });
  });

  it('shows the rate application button if a locked application has evaluations', () => {
    expect(wrapper.vm.applicationHasEvaluations(wrapper.vm.applicationData[0])).toBe(true);
  });

  it('hides the rate application button if a locked application does not have evaluations', () => {
    wrapper.setProps({
      contest: {
        applications: [
          {
            id: 6,
            applicant_id: 8,
            contest_id: 1,
            name: 'John',
            surname: 'Doe',
            fathername: 'Jack',
            mothername: 'Jane',
            birthdate: '1980-11-15 00:00:00',
            greek_nationality: 1,
            eu_citizen: 0,
            policeid_number: 'AA123456',
            policeid_date: '2000-12-10 00:00:00',
            afm: '114865648',
            doy: 'A Athens',
            amka: '01118102258',
            street: 'Example st.',
            street_number: '123',
            postcode: '123456',
            city: 'Athens',
            phonenumber1: '2131234567',
            phonenumber2: '',
            email: '<EMAIL>',
            applicant_category: 1,
            meets_general_requirements: 1,
            submitted_at: '-0001-11-30 00:00:00',
            protocol_number: '000001',
            protocol_date: '2018-10-02 00:00:00',
            invalidated: 0,
            invalidation_description: '',
            locked_at: '2018-11-06 00:00:00',
            rated_at: null,
            rejected: null,
            applicant_full_name: 'John Doe',
            evaluations: [],
          },
        ],
      },
    });

    expect(wrapper.vm.applicationHasEvaluations(wrapper.vm.contest.applications[0])).toBe(false);
  });

  // Helper functions

  /**
   * Assert that the user sees the given text
   * @param {String} text
   * @param {String} selector
   */
  const see = (text, selector = null) => {
    const wrap = selector ? wrapper.find(selector) : wrapper;
    expect(wrap.html()).to.contain(text);
  };

  /**
   * Simulates user clicking on a button
   * @param {String} selector
   */
  const click = (selector) => {
    wrapper.find(selector).trigger('click');
  };

  /**
   * Simulate user typing some text in the input
   *
   * @param {string} selector - The input's HTML selector.
   * @param {string} text - The user's input text
   */
  const type = (selector, text) => {
    const input = wrapper.find(selector);

    input.element.value = text;
    input.trigger('input');
  };
});
