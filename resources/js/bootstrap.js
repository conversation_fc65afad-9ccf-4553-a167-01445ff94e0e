import '../admin-lte/plugins/datatables/datatables';
import '../admin-lte/plugins/datatables/plugins/sorting/datetime-moment';
import '../admin-lte/plugins/select2/i18n/el';
import '../admin-lte/plugins/select2/select2.full.min';
import '../element-ui/element-variables.scss';

import axios from 'axios';
import ElementUI from 'element-ui';
import locale from 'element-ui/lib/locale/lang/el';
import PortalVue from 'portal-vue';
// import Echo from 'laravel-echo';
import qs from 'qs';
import Vue from 'vue';
import VueCookies from 'vue-cookies';

// window.Pusher = require('pusher-js');

// window.Echo = new Echo({
//   broadcaster: 'pusher',
//   key: process.env.MIX_PUSHER_APP_KEY,
//   wsHost: window.location.hostname,
//   wsPort: process.env.MIX_LARAVEL_WEBSOCKETS_PORT,
//   wssPort: process.env.MIX_LARAVEL_WEBSOCKETS_PORT,
//   forceTLS: process.env.MIX_LARAVEL_WEBSOCKETS_FORCETLS === 'true',
//   disableStats: true,
// });

Vue.use(ElementUI, { locale });
Vue.use(VueCookies);
Vue.use(PortalVue);

axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
// Format nested params correctly
axios.defaults.paramsSerializer = (params) => qs.stringify(params);

// Swal
const swal = {
  error: (err, config = {}, cb = null) => window.swal({
    type: 'error',
    title: `Σφάλμα ${err.response.status}`,
    text: err.response.data.message,
    showConfirmButton: true,
    ...config,
  }, () => {
    if (cb) {
      cb();
    }
    window.previousActiveElement = null;
  }),
  success: (message, config = {}, cb = null) => window.swal({
    type: 'success',
    title: 'Επιτυχία!',
    text: message,
    showConfirmButton: true,
    ...config,
  }, () => {
    if (cb) {
      cb();
    }
    window.previousActiveElement = null;
  }),
};

Vue.prototype.$http = axios;
Vue.prototype.$echo = window.Echo;
Vue.prototype.$swal = swal;

/**
 * Expose useful libraries to window object
 * Exposing Vue to global window allows us to share the same Vue instance across multiple entrypoints
 * See https://itnext.io/sharing-global-vue-instance-between-multiple-webpack-projects-abfb0aa9d3e
 */
window.Vue = Vue;
window.axios = axios;
