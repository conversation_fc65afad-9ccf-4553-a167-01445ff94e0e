// Show licence select options when needed
function showLicencedDetails(currentStatus) {
    if (currentStatus) {
        $('#licence_details').fadeIn(800, 'swing');
    } else {
        $('#licence_details').fadeOut(800, 'swing');
    }
}

// Show personaldata_details radio buttons when needed
function showPersonaldataDetails(currentStatus) {
    if (currentStatus) {
        $('#personaldata_details').fadeIn(800, 'swing');
        $('#personaldata_info').prop('required',true);
    } else {
        $('#personaldata_info').removeAttr('required');
        $('#personaldata_details').fadeOut(800, 'swing');
    }
}

// Show obtainable_details radio buttons when needed
function showObtainableDetails(currentStatus) {
    if (!currentStatus) {
        $('#obtainable_details').fadeIn(800, 'swing');
        // $('#obtainable_details input[type="checkbox"]').prop('required',true);
    } else {
        // $('#obtainable_details input[type="checkbox"]').removeAttr('required');
        $('#obtainable_details input[type="checkbox"]').iCheck('uncheck');
        $('#obtainable_details').fadeOut(800, 'swing');
    }
}

// Show restriction_details radio buttons when needed
function showRestrictedDetails(currentStatus) {
    if (currentStatus) {
        $('#restricted_details').fadeIn(800, 'swing');
        // $('#restricted_details input[type="checkbox"]').prop('required',true);
    } else {
        // $('#restricted_details input[type="checkbox"]').removeAttr('required');
        $('#restricted_details input[type="checkbox"]').iCheck('uncheck');
        $('#restricted_details').fadeOut(800, 'swing');
    }
}

// Set required for restriction_other when needed
function showRestrictionOther(currentStatus) {
    if (currentStatus) {
        $('#restriction_other').prop('required',true);
    } else {
        $('#restriction_other').removeAttr('required');
    }
}

// Show relevant inputs on selection of dataset type
function showTypeDetails(currentValue) {
    if (currentValue === '1') {
        // Hide Physical
        $('#physical_details #physical_format').removeAttr('required');
        $('#physical_details input[type="text"]').val(null);
        $('#physical_details input[type="checkbox"]').iCheck('uncheck');
        $('#physical_details').fadeOut(400);
        // Show Digital
        $('#digital_details').fadeIn(800);
        $('#digital_details #filetypes').prop('required', true);
    } else if (currentValue === '2') {
        // Hide Digital
        $('#digital_details #filetypes').removeAttr('required');
        $('#digital_details input[type="text"]').val(null);
        $('#filetypes').val(null).trigger("change");
        $('#digital_details').fadeOut(400);
        // Show Physical
        $('#physical_details').fadeIn(800);
        $('#physical_details #physical_format').prop('required', true);
    } else {
        // Nothing selected. Hide both Digital and Physical
        $('#physical_details input[type="text"]').val(null);
        $('#physical_details input[type="checkbox"]').iCheck('uncheck');
        $('#digital_details input[type="text"]').val(null);
        $('#filetypes').val(null).trigger("change");
        $('#physical_details #physical_format').removeAttr('required');
        $('#digital_details #filetypes').removeAttr('required');
        $('#digital_details').fadeOut(400);
        $('#physival_details').fadeOut(400);
    }
}


$(document).ready(function(){

    // Initialize hidden fields
    showPersonaldataDetails($('#personaldata').attr('checked'));
    showObtainableDetails($('#obtainable').attr('checked'));
    showRestrictedDetails($('#restricted').attr('checked'));
    showRestrictionOther($('#restricted_details input.has_other').attr('checked'));
    showLicencedDetails($('#licenced').attr('checked'));
    showTypeDetails($('#type_id').val());

    // Initialize checkbox switches
    $('.switch').checkboxpicker();

    // Initialize popovers
    $('[data-toggle="popover"]').popover();

    // Setup handlers for change of inputs

    $('#type_id').on('change', function(){
        showTypeDetails(this.value);
    });

    $('#personaldata').on('change', function(){
        showPersonaldataDetails(this.checked);
    });

    $('#obtainable').on('change', function(){
        showObtainableDetails(this.checked);
    });

    $('#restricted').on('change', function(){
        showRestrictedDetails(this.checked);
    });

    $('#restricted_details input.has_other').on('ifToggled', function(){
        showRestrictionOther(this.checked);
    });

    $('#licenced').on('change', function(){
        showLicencedDetails(this.checked);
    });
});