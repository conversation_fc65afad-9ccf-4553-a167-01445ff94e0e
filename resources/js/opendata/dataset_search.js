// Function to toggle between two checkboxes (yes/no, true/false etc.)
function toggleiCheckboxes(input1, input2) {
    input1.on('ifChecked', function(){
        input2.iCheck('uncheck');
    });
    input2.on('ifChecked', function(){
        input1.iCheck('uncheck');
    });
}

$(document).ready(function(){
    toggleiCheckboxes($('#obtainable'), $('#obtainable_not'));
    toggleiCheckboxes($('#personaldata'), $('#personaldata_not'));
    toggleiCheckboxes($('#restricted'), $('#restricted_not'));
    toggleiCheckboxes($('#fees'), $('#fees_not'));
    toggleiCheckboxes($('#licenced'), $('#licenced_not'));

    // Handle supporting checkboxes of obtainable
    $('#obtainable_not').on('ifChecked', function(){
        $('#unobtainable_reasons').fadeIn(500, 'swing');
    });
    $('#obtainable_not').on('ifUnchecked', function(){
        $('#unobtainable_reasons').fadeOut(500, 'swing');
    });
});