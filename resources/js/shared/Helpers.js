import { has, pick, reduce } from 'lodash';

/**
 * Displays a sweet alert confirmation box
 *
 * @param message
 * @returns {Promise<any>}
 */
function askToConfirm(message) {
  return new Promise((resolve) => {
    window.swal({
      title: message.title,
      text: message.body,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#e53e3e',
      confirmButtonText: 'Ναι',
      cancelButtonText: 'Όχι',
      closeOnConfirm: true,
      closeOnCancel: true,
    },
    (isConfirm) => {
      if (isConfirm) {
        resolve();
      }
    });
  });
}

/**
 * Return any additional properties of obj1 compared to obj2
 *
 * @link http://stackoverflow.com/a/31686152/2235814
 * @param obj1 the object to be tested
 * @param obj2 the object against which we perform the test
 * @returns object the additional properties of obj1
 * FIXME does not deeply-compare the objects....
 */
function pickAdditionalProperties(obj1, obj2) {
  // Find the extra keys that exist within obj1
  const extraKeys = reduce(obj1, (result, value, key) => (has(obj2, key) ? result : result.concat(key)), []);

  // return the key/value pair
  return pick(obj1, extraKeys);
}

export { askToConfirm, pickAdditionalProperties };
export default { askToConfirm, pickAdditionalProperties };
