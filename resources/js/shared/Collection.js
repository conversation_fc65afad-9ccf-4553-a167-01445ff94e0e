export default Vue.extend({
    props: ['data'],

    data() {
        return {
            items: this.data
        };
    },

    computed: {
        hasItems() {
            if (typeof this.items === "undefined" || Object.keys(this.items).length === 0) {
                return false;
            }

            return true;
        }
    },

    methods: {
        add(item) {
            let new_item = {};

            this.items.push(Object.assign(new_item, item));

            this.$emit('added');
        },

        update(index, item) {
            Object.assign(this.items[index], item);

            this.$emit('updated');
        },

        remove(index) {
            this.items.splice(index, 1);

            this.$emit('removed');
        }
    }
});
