import axios from 'axios';
import cloneDeep from 'lodash/cloneDeep';

import FormErrors from './FormErrors';

class Form {
  constructor(fields, rules = { incoming: null, outgoing: null }) {
    this.data = fields;
    this.errors = new FormErrors();
    this.rules = rules;
    this.initData = cloneDeep(fields);
    this.busy = false;
  }

  put(url, extra = {}) {
    return this.submit('put', url, extra);
  }

  post(url) {
    return this.submit('post', url);
  }

  submit(method, url, extra = {}) {
    const preparedData = this.rules.outgoing ? this.rules.outgoing(this.data) : this.data;

    return new Promise((resolve, reject) => {
      this.busy = true;
      axios[method](url, { ...preparedData, ...extra })
        .then((res) => {
          this.errors.clear();
          resolve(res);
        })
        .catch((error) => {
          if (error.response.status === 422) {
            this.errors.set(error.response.data.errors);
          }
          reject(error);
        })
        .finally(() => {
          this.busy = false;
        });
    });
  }

  populate(data) {
    const preparedData = this.rules.incoming ? this.rules.incoming(data) : data;
    this.initData = cloneDeep(preparedData);
    this.data = cloneDeep(preparedData);
  }

  reset() {
    this.data = cloneDeep(this.initData);
    this.errors.clear();
  }
}

export default Form;
