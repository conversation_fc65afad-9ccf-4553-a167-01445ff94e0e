export default {
  data() {
    return {
      $_gate_user: null,
      $_gate_userPermissions: [],
      $_gate_authorizedModels: [],
      /**
       * The models that should be authorized
       *
       * This property contains an array of all models that should be authorized against
       * auth user's policies. It should be overriten in the consuming component in
       * cases we want to authorize user actions using the $can() method. Models
       * permissions are fetched on created lifecycle hook
       * Examples:
       * { model: { class: 'Phonebook\\ServiceContact', id: null }, context: { unitId: 331 } },
       * { model: { class: 'Phonebook\\ServiceContact', id: 10886 } },
       */
      $gate_authorizables: [],
    };
  },

  created() {
    this.$_gate_fetchUser()
      .then((user) => {
        this.$data.$_gate_user = user;
      });

    this.$_gate_fetchUserPermissions()
      .then((userPermissions) => {
        this.$data.$_gate_userPermissions = userPermissions;
      });

    Promise.all(this.$data.$gate_authorizables
      .map((authorizable) => this.$_gate_fetchUserAuthorizedActions(authorizable.model, authorizable.context ?? null)))
      .then((authorizedModels) => {
        this.$data.$_gate_authorizedModels = authorizedModels;
      });
  },

  methods: {
    async $_gate_fetchUser() {
      try {
        const res = await this.$http.get('/api/auth/user');
        return res.data;
      } catch (e) {
        return null;
      }
    },

    async $_gate_fetchUserPermissions() {
      try {
        const res = await this.$http.get('/api/auth/user/permissions');
        return res.data;
      } catch (e) {
        return [];
      }
    },

    async $_gate_fetchUserAuthorizedActions(model, context) {
      try {
        const res = await this.$http.get(`/api/auth/user/authorized-actions/${model.class}`, {
          params: context,
        });
        return res.data;
      } catch (e) {
        return [];
      }
    },

    // TODO we don't check for context
    $_gate_existsInAuthorizablesGuard(model) {
      if (
        model === null
        || this.$data.$gate_authorizables.find(
          (authorizable) => authorizable.model.class === model.class && authorizable.model.id === model.id,
        ) !== undefined
      ) {
        return;
      }
      throw new Error(`Model with class name ${model.class} and id ${model.id} not found in authorizable models`);
    },

    $can(ability, model = null) {
      this.$_gate_existsInAuthorizablesGuard(model);

      if (model === null) {
        return this.$data.$_gate_userPermissions.includes(ability)
          || this.$data.$_gate_userPermissions.includes('administer');
      }
      if (ability === 'create') {
        const authorizedModel = this.$data.$_gate_authorizedModels
          .find((authModel) => authModel.class === model.class && authModel.id === null);
        return authorizedModel?.permissions.create ?? false;
      }
      const authorizedModel = this.$data.$_gate_authorizedModels
        .find((authModel) => authModel.class === model.class && authModel.id === model.id);
      return authorizedModel?.permissions[ability] ?? false;
    },

    $isAdmin() {
      return this.$data.$_gate_userPermissions.includes('administer');
    },

    $authUser() {
      return this.$data.$_gate_user;
    },

    $sameUnit(model) {
      if (model.unit_id) {
        return this.$_gate_user.unit_id === model.unit_id;
      }
      return false;
    },

    async $authorize(ability, model = null, context = null) {
      if (model === null) {
        const userPermissions = await this.$_gate_fetchUserPermissions();
        return userPermissions.includes(ability) || userPermissions.includes('administer');
      }
      const data = await this.$_gate_fetchUserAuthorizedActions(model, context);
      return data.permissions[ability] ?? false;
    },

    async $getAuthUser() {
      return this.$_gate_fetchUser();
    },
  },
};
