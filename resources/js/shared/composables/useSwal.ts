type Callback = () => void;
type Config = {
  message: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
} | Record<string, never>;
export default function useSwal() {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const { swal } = window;
  return {
    confirm: (message: string, config: Config = {}) => new Promise<void>((resolve) => {
      swal(
        {
          title: 'Θέλετε σίγουρα να προχωρήσετε;',
          text: message,
          type: 'warning',
          showCancelButton: true,
          confirmButtonText: config.confirmButtonText || 'Ναι',
          cancelButtonText: config.cancelButtonText || 'Όχι',
          closeOnConfirm: true,
          closeOnCancel: true,
        },
        (isConfirm: boolean) => {
          if (isConfirm) {
            resolve();
          }
        },
      );
    }),
    success: (config: Config, cb: Callback|null = null) => swal({
      title: 'Επιτυχία!',
      text: config.message,
      confirmButtonText: config.confirmButtonText || 'Οκ',
      type: 'success',
    }, () => {
      if (cb) {
        cb();
      }
    }),
    error: (config: Config, cb: Callback|null = null) => swal({
      title: 'Σφάλμα!',
      text: config.message,
      confirmButtonText: config.confirmButtonText || 'Οκ',
      type: 'error',
    }, () => {
      if (cb) {
        cb();
      }
    }),
    warning: (message: string) => swal({
      title: 'Προσοχή!',
      text: message,
      type: 'warning',
    }),
    info: (message: string) => swal({
      title: 'Σημείωση',
      text: message,
      type: 'info',
    }),
  };
}
