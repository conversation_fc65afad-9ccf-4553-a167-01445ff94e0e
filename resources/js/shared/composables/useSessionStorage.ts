export default function useSessionStorage() {
  const hasItem = (key: string) => sessionStorage.getItem(key) !== null;

  const getItem = (key: string) => {
    const value = sessionStorage.getItem(key);
    return value ? JSON.parse(value) : null;
  };

  const setItem = (key: string, value: unknown) => {
    sessionStorage.setItem(key, JSON.stringify(value));
  };

  const removeItem = (key: string) => {
    sessionStorage.removeItem(key);
  };

  return {
    hasItem,
    getItem,
    setItem,
    removeItem,
  };
}
