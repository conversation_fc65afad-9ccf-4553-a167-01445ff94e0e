import type { AxiosError, AxiosRequestConfig } from 'axios';
import axios from 'axios';
import qs from 'qs';

export type SuccessPayload<T> = {
  message: string,
  data?: T
}

export type ErrorPayload<T> = {
  message: string,
  errors?: Partial<{ [K in keyof T]: string[] }>
}

axios.defaults.headers.common = {
  'X-Requested-With': 'XMLHttpRequest',
  'Content-Type': 'application/json',
  Accept: 'application/json',
};

axios.defaults.paramsSerializer = (params) => qs.stringify(params);
//
// /*
// If a "Location" response header exists, we redirect to the provided URL.
// We use this interceptor to handle the 201 response. The XMLHttpRequest
// will transparently follow any redirect response (3xx).
// See https://github.com/axios/axios/issues/932#issuecomment-307390761
//  */
// axios.interceptors.response.use((response) => {
//   if (response.headers.location) {
//     window.location.replace(response.headers.location);
//   }
//   return response;
// }, (error) => {
//   if (axios.isAxiosError(error)) {
//     if (error.response?.headers.location) {
//       window.location.replace(error.response.headers.location);
//     }
//   }
//   return Promise.reject(error);
// });

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isAxiosError(error: any): error is AxiosError {
  return axios.isAxiosError(error);
}

export default function useHttp() {
  const request = async<T, D = T>(config: AxiosRequestConfig) => {
    const response = await axios.request<SuccessPayload<D>>(config);

    return response.data;
  };
  return {
    request,
    get: async <T>(url: string, params = {}, config = {}) => request<T>({
      ...config, method: 'get', url, params,
    }),
    post: async <T>(url: string, data = {} as T, config = {}) => request<T>({
      ...config, method: 'post', url, data,
    }),
    put: async <T>(url: string, data = {} as T, config = {}) => request<T>({
      ...config, method: 'put', url, data,
    }),
    patch: async <T>(url: string, data = {} as T, config = {}) => request<T>({
      ...config, method: 'patch', url, data,
    }),
    delete: async <T>(url: string, data = {} as T, config = {}) => request<T>({
      ...config, method: 'delete', url, data,
    }),
  };
}
