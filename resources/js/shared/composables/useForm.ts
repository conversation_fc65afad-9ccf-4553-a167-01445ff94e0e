import type { Method } from 'axios';
import { reactive } from 'vue';

import useHttp, { isAxiosError } from '@/shared/composables/useHttp';

export type ServerValidationErrors<T> = Partial<{ [K in keyof T]: string[] }>;

export type ValidationErrors<T> = Partial<{ [K in keyof T]: string }>;

const { request } = useHttp();

export default function useForm<T extends object>(inputs: T) {
  return reactive({
    ...inputs,
    errors: {} as ValidationErrors<T>,
    hasErrors: false,
    processing: false,
    wasSuccessful: false,
    setErrors(serverErrors: ServerValidationErrors<T>) {
      const normalizedErrors = (Object.keys(serverErrors) as Array<keyof typeof serverErrors>)
        .reduce<ValidationErrors<T>>((carry, key) => {
          const error = serverErrors[key];
          // eslint-disable-next-line no-param-reassign
          carry[key] = error ? error.join(' ') : '';
          return carry;
        }, {});
      Object.assign(this.errors, normalizedErrors);

      this.hasErrors = Object.keys(this.errors).length > 0;

      return this;
    },
    clearErrors(...fields: (keyof T)[]) {
      this.errors = (Object.keys(this.errors) as (keyof T)[])
        .reduce((carry, field) => ({
          ...carry,
          ...(fields.length > 0 && !fields.includes(field) ? { [field]: this.errors[field] } : {}),
        }), {});

      this.hasErrors = Object.keys(this.errors).length > 0;

      return this;
    },
    data(): T {
      // Create a new object with only the input properties
      const result = {} as T;

      // Copy only the properties from the original inputs
      Object.keys(inputs).forEach((key) => {
        const k = key as keyof T;
        // @ts-expect-error - This is safe because we're only accessing properties that exist in inputs
        result[k] = this[k];
      });

      return result;
    },
    get(url: string, options = {}) {
      return this.submit('get', url, options);
    },
    post<D = T>(url: string, options = {}) {
      return this.submit<D>('post', url, options);
    },
    put(url: string, options = {}) {
      return this.submit('put', url, options);
    },
    patch(url: string, options = {}) {
      return this.submit('patch', url, options);
    },
    delete(url: string, options = {}) {
      return this.submit('delete', url, options);
    },
    async submit<D>(method: Method, url: string, options = {}) {
      this.wasSuccessful = false;
      this.processing = true;
      try {
        const response = await request<T, D>({
          method, url, data: this.data(), ...options,
        });
        this.clearErrors();
        this.wasSuccessful = true;
        return response;
      } catch (err) {
        if (isAxiosError(err)) {
          this.clearErrors().setErrors(err.response?.data.errors || {});
        }
        throw err;
      } finally {
        this.processing = false;
      }
    },
  });
}
