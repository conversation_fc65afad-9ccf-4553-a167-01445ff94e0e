
<template>
  <div class="tabs">
    <!--Tabs list-->
    <ul class="tabs-list">
      <li
        v-for="tab in tabs"
        :key="tab.name"
        :class="{'is-active': tab.isActive, 'has-errors': tab.hasErrors}"
        @click="selectTab(tab)"
      >
        {{ tab.name }}
      </li>
    </ul>

    <!--Tab content-->
    <div class="tabs-details">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: 'VerticalTabs',

  data() {
    return {
      tabs: [],
    };
  },

  created() {
    // Target all children tab-items to figure out their names
    this.tabs = this.$children;
  },

  methods: {
    /**
     * Mark the selected tab as active
     *
     * Filters through all child tab-items to find the one that was clicked
     * ant thus, update its isActive property to true
     *
     * @param selectedTab
     */
    selectTab(selectedTab) {
      this.tabs.forEach((tab) => {
        tab.isActive = (tab.name == selectedTab.name);
      });
    },
  },

};
</script>

<style lang="scss" rel="stylesheet/scss">
.tabs {
  // Define a flex container
  display: flex;
  // define the flex tabs container's main and cross axes
  flex-flow: row nowrap; //shorthand for flex-direction=row; flex-wrap=nowrap

  overflow-x: hidden;
  overflow-y: auto;

  /*
  This is the right area
  */
  .tabs-details {
    flex: 1 1 auto; //shorthand for flex-grow, flex-shrink and flex-basis
    padding: 15px 30px;

    // this fixes the broken label/input
    .form-group {
      display: flex;
      flex-flow: column;
    }
  }

  /*
  This is the left area
  */
  ul.tabs-list {
    display: flex;
    flex: 0 0 20%;
    flex-flow: column nowrap;
    justify-content: flex-start;
    list-style: none;
    padding: 5px;
    color: var(--gray-700);
    background-color: var(--gray-100);

    li {
      display: block;
      width: 100%;
      padding: 5px 10px;
      margin-bottom: 5px;
      line-height: 24px;
      cursor: pointer;
      border-radius: 3px;

      &:hover {
        background-color: var(--gray-200);
      }

      &.is-active {
        background-color: var(--gray-400);
        font-weight: 800;

        //&.has-errors {
        //  background-color: var(--red-600) !important;
        //  color: var(--red-100) !important;
        //}
      }

      &.has-errors::after {
        content: "❗";
        color: red;
        font-weight: 600;
      }
    }
  }
}
</style>
