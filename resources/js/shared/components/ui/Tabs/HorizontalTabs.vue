<template>
    <div>
        <!-- Nav tabs -->
        <ul class="nav nav-tabs">
            <li v-for="tab in tabs"
                :class="{'active': tab.isActive}"
                @click="selectTab(tab)"
            >
                <a href="#">{{ tab.name }}</a>
            </li>
        </ul>

        <!--Tab content-->
        <div class="tab-content">
            <slot></slot>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'HorizontalTabs',

        data() {
            return {
                tabs: [],
            }
        },

        methods: {
            /**
             * Mark the selected tab as active
             *
             * Filters through all child tab-items to find the one that was clicked
             * ant thus, update its isActive property to true
             *
             * @param selectedTab
             */
            selectTab(selectedTab) {
                this.tabs.forEach(tab => {
                    tab.isActive = (tab.name == selectedTab.name)
                });
            }
        },

        created() {
            // Target all children tab-items to figure out their names
            this.tabs = this.$children;
        }

    };
</script>

<style lang="scss" rel="stylesheet/scss">
    .nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
        color: #000;
        background-color: #fff;
        border: 1px solid #ddd;
        border-bottom-color: transparent;
        cursor: default;
    }
</style>