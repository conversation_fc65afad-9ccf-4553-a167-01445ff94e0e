<template>
  <div class="stats-box">
    <span
      class="stats-box-icon"
      :style="{
        backgroundColor: color,
        // boxShadow: '0 0 0 3px ' + color
      }"
    ><i :class="['fa', icon]" /></span>
    <div class="stats-box-content">
      <span class="stats-box-text">{{ text }}</span>
      <span class="stats-box-number">{{ number.toLocaleString('el-GR') }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StatsBox',
  props: {
    icon: {
      type: String,
      required: true,
    },
    text: {
      type: String,
      required: true,
    },
    number: {
      type: Number,
      required: true,
    },
    color: {
      type: String,
      default: '#4a5568',
    },
  },
};
</script>

<style scoped>
.stats-box {
  display: flex;
  align-items: center;
  min-width: 200px;
  padding: 8px;
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.stats-box-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  /*border: 3px solid #fff;*/
  width: 48px;
  height: 48px;
}

.stats-box-icon i {
  font-size: 22px;
  color: white;
}
.stats-box-content {
  display: flex;
  flex-direction: column;
  gap: 3px;
  margin-left: 8px;
  font-size: 1rem;
}

.stats-box-text {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--gray-500);
}

.stats-box-number {
  font-weight: 700;
}

</style>
