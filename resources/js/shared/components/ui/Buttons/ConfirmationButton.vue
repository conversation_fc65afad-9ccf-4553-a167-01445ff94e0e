<template>
  <div>
    <button
      type="button"
      :class="targetClasses"
      v-bind="$attrs"
      @click="openModal = true"
    >
      <slot />
    </button>
    <Modal
      v-if="isOpen"
      @close="$emit('cancel')"
    >
      <template #header>
        <h3><i class="fa fa-exclamation-circle"> Ζητείται επιβεβαίωση</i></h3>
      </template>
      <template #default>
        {{ confirmationText }}
      </template>
      <template #footer>
        <div class="row">
          <div class="col-sm-6">
            <button
              type="button"
              class="btn btn-default btn-block"
              @click="$emit('cancel')"
            >
              <i
                class="fa fa-arrow-left"
                aria-hidden="true"
              />
              Επιστροφή
            </button>
          </div>
          <div class="col-sm-6">
            <LoadingButton
              :loading="isLoading"
              class="btn btn-block btn-danger"
              @click="$emit('confirm')"
            >
              <i
                class="fa fa-check-circle-o"
                aria-hidden="true"
              />
              Επιβεβαίωση
            </LoadingButton>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
import Modal from '../Modal/Modal.vue';
import LoadingButton from './LoadingButton.vue';

export default {
  name: 'ConfirmationButton',
  components: { Modal, LoadingButton },
  props: {
    targetClasses: {
      type: String,
      default: 'btn btn-default',
    },
    isOpen: {
      type: Boolean,
      default: false,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    confirmationText: {
      type: String,
      default: 'Επιβεβαίωση',
    },
  },
  data() {
    return {
      openModal: false,
    };
  },
};
</script>

<style scoped>

</style>
