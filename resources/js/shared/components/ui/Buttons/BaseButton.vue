<script setup lang="ts">
import { Icon } from '@iconify/vue2';
import { computed, useSlots } from 'vue';

// Get slots
const slots = useSlots();

type BaseButtonProps = {
  as?: 'button' | 'a';
  type?: 'button' | 'submit' | 'reset';
  href?: string;
  variation?: 'primary' | 'secondary' | 'outline' | 'plain' | 'destructive';
  title?: string;
  disabled?: boolean;
  icon?: string;
  iconRight?: boolean;
  expand?: boolean;
  iconOnly?: boolean;
}

const props = withDefaults(defineProps<BaseButtonProps>(), {
  as: 'button',
  type: 'button',
  variation: 'secondary',
  disabled: false,
  iconRight: false,
  href: '',
  title: '',
  icon: '',
  iconOnly: false,
});

const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void;
}>();

const classes = computed(() => {
  const baseClasses = 'tw:flex tw:items-center tw:justify-center tw:rounded tw:transition-colors';

  // Padding classes based on iconOnly prop
  const paddingClasses = props.iconOnly
    ? 'tw:p-2' // Equal padding on all sides
    : 'tw:py-2 tw:px-4'; // Original padding

  // Gap classes
  const gapClasses = props.iconOnly && !slots.default
    ? '' // No gap needed for icon-only buttons without content
    : 'tw:gap-1'; // Original gap

  // Variation-specific classes
  const variationClasses = {
    primary: 'tw:bg-cyan-600 tw:text-white tw:shadow tw:hover:bg-cyan-700 tw:hover:text-white tw:focus:text-white tw:active:text-white',
    secondary: 'tw:bg-slate-100 tw:shadow tw:hover:bg-slate-200',
    outline: 'tw:border tw:border-gray-300 tw:bg-white tw:hover:bg-gray-50',
    plain: 'tw:text-cyan-600 tw:bg-transparent tw:hover:bg-gray-100',
    destructive: 'tw:bg-red-500 tw:text-white tw:shadow tw:hover:bg-red-600',
  };

  // Width classes
  const widthClasses = props.expand ? 'tw:w-full' : '';

  // Disabled state
  const disabledClasses = props.disabled ? 'tw:opacity-50 tw:cursor-not-allowed' : '';

  // Combine all classes
  return [
    baseClasses,
    paddingClasses,
    gapClasses,
    variationClasses[props.variation],
    widthClasses,
    disabledClasses,
  ].join(' ');
});

const iconClasses = computed(() => 'tw:size-6');

const contentClasses = computed(() => (props.icon && !props.iconRight ? 'tw:ml-1' : ''));

// Simple class to prevent color changes on hover for link buttons
const linkOverrideClasses = computed(() => {
  if (props.as === 'a') {
    return 'tw:hover:text-current tw:active:text-current tw:focus:text-current';
  }
  return '';
});
</script>

<script lang="ts">
export default {
  name: 'BaseButton',
  // inheritAttrs: false,
};
</script>

<template>
  <component
    :is="as"
    :type="as === 'button' ? type : undefined"
    :href="as === 'a' ? href : undefined"
    :disabled="as === 'button' ? disabled : undefined"
    :class="[classes, linkOverrideClasses]"
    :title="title"
    v-bind="$attrs"
    @click="!disabled && $emit('click', $event)"
  >
    <!-- Left-positioned icon -->
    <Icon
      v-if="icon && !iconRight"
      :icon="icon"
      :class="iconClasses"
    />

    <!-- Content -->
    <span
      v-if="slots.default"
      :class="contentClasses"
    >
      <slot />
    </span>

    <!-- Right-positioned icon -->
    <Icon
      v-if="icon && iconRight"
      :icon="icon"
      :class="iconClasses"
    />
  </component>
</template>
