<script setup lang="ts">
import LoadingButton from '@/shared/components/ui/Buttons/LoadingButton.vue';
import BasicModal from '@/shared/components/ui/Modal/BasicModal.vue';

withDefaults(defineProps<{
      open: boolean;
      cancelBtnText?: string;
      confirmBtnText?: string;
      processing?: boolean;
    }>(),
    {
      cancelBtnText: "OXI",
      confirmBtnText: "ΝΑΙ",
      processing: false,
    }
);

defineEmits<{
  (e: 'confim'): void;
  (e: 'cancel'): void;
}>();
</script>

<script lang="ts">
export default {
  name: 'ConfirmationModal',
};
</script>

<template>
  <BasicModal
    :open="open"
    @close="$emit('cancel')"
  >
    <template #header>
      <i class="fa fa-exclamation-circle" /> Προσοχή
    </template>
    <slot />
    <template #footer>
      <div class="row">
        <div class="col-sm-6">
          <button
            type="button"
            class="btn btn-default btn-block"
            @click="$emit('cancel')"
          >
            {{ cancelBtnText }}
          </button>
        </div>
        <div class="col-sm-6">
          <LoadingButton
            type="button"
            class="btn btn-primary btn-block"
            :loading="processing"
            @click="$emit('confirm')"
          >
            {{ confirmBtnText }}
          </LoadingButton>
        </div>
      </div>
    </template>
  </BasicModal>
</template>
