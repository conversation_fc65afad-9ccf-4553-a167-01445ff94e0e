<template>
  <portal
    v-if="open"
    to="modal-container"
  >
    <transition name="modal">
      <div class="modal-mask">
        <div class="modal-wrapper">
          <FocusLoop
            :is-visible="open"
            :auto-focus="false"
          >
            <div class="modal-container">
              <!--Header-->
              <div
                v-if="hasHeaderSlot"
                class="modal-header"
              >
                <div class="modal-title">
                  <slot name="header">
                    Default Header
                  </slot>
                </div>
                <button
                  type="button"
                  @click="$emit('close')"
                >
                  &times;
                </button>
              </div>
              <!--Body-->
              <div class="modal-body">
                <slot>
                  Default Body
                </slot>
              </div>
              <!--Footer-->
              <div
                v-if="$slots.footer"
                class="modal-footer"
              >
                <slot name="footer" />
              </div>
            </div>
          </FocusLoop>
        </div>
      </div>
    </transition>
  </portal>
</template>

<script>
import { FocusLoop } from '@vue-a11y/focus-loop';

export default {
  name: 'BasicModal',
  components: {
    FocusLoop,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
  },
  computed: {
    hasHeaderSlot() {
      return !!this.$slots.header;
    },
  },
  watch: {
    open: {
      immediate: true,
      handler(open) {
        if (open) {
          document.body.style.setProperty('overflow', 'hidden');
        } else {
          document.body.style.removeProperty('overflow');
        }
      },
    },
  },
  created() {
    const escapeHandler = (e) => {
      if (e.key === 'Escape' && this.open) {
        this.close();
      }
    };
    document.addEventListener('keydown', escapeHandler);
    this.$once('hook:destroyed', () => {
      document.removeEventListener('keydown', escapeHandler);
    });
  },
  methods: {
    close() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.modal-mask {
  position: fixed;
  z-index: 2000; // must be less than datepicker, which has z-index 2005
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: table;
  transition: opacity .2s ease-out;
}

.modal-wrapper {
  display: table-cell;
  vertical-align: middle;
}

.modal-container {
  width: 600px; // TODO make it parametric
  margin: 0 auto;
  /*padding: 20px 30px;*/
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 9px 46px 8px rgba(0, 0, 0, 0.14),
  0 11px 15px -7px rgba(0, 0, 0, 0.12),
  0 24px 38px 3px rgba(0, 0, 0, 0.20);
  transition: all .2s ease-out;
}

.modal-header {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  margin-top: 0;
  padding: 0.3rem 1.5rem;
  //background-color: #0C90AD;
  color: var(--gray-700);
  font-size: 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .modal-title {
    flex: 0 1 auto;
  }

  button {
    font-size: 2rem;
    font-weight: 300;
    color: var(--gray-600);
    background-color: inherit;
    border: none;
    flex: 0 1 auto;

    &:focus {
      outline: none; // stackoverflow.com/questions/19053181/how-to-remove-focus-around-buttons-on-click
      font-weight: 800;
    }

    &:hover {
      font-weight: 800;
    }
  }
}

.modal-body {
  padding: 1.5rem 1.5rem;
  max-height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.modal-footer {
  margin-bottom: 0;
  padding: 0.75rem 1rem;
}

/*
 * The following styles are auto-applied to elements with
 * transition="modal" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the modal transition by editing
 * these styles.
 */

.modal-enter {
  opacity: 0;
}

.modal-leave-active {
  opacity: 0;
}

.modal-enter .modal-container,
.modal-leave-active .modal-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

@media (max-width: 1024px) and (min-width: 768px) {
  .modal-container {
    width: 820px;
  }

  .modal-body {
    max-height: 540px;
  }
}
</style>
