<template>
  <transition name="modal">
    <div class="modal-mask">
      <div class="modal-wrapper">
        <div class="modal-container">
          <!--Header-->
          <div class="modal-header">
            <h4 class="modal-title">
              <slot name="header">
                Default Header
              </slot>
            </h4>
            <button
              v-if="! isStatic"
              type="button"
              @click="$emit('close')"
            >
              &times;
            </button>
          </div>
          <!--Body-->
          <div class="modal-body">
            <slot>
              Default Body
            </slot>
          </div>
          <!--Footer-->
          <div class="modal-footer">
            <slot name="footer" />
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'Modal',
  props: {
    isStatic: {
      type: Boolean,
      default: false,
    },
  },

};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.modal-mask {
  position: fixed;
  z-index: 2000; // must be less than datepicker, which has z-index 2005
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .7);
  display: table;
  transition: opacity .3s ease;
}

.modal-wrapper {
  display: table-cell;
  vertical-align: middle;
}

.modal-container {
  width: 600px; // TODO make it parametric
  margin: 0 auto;
  /*padding: 20px 30px;*/
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, .33);
  transition: all .3s ease;
  /*font-family: Helvetica, Arial, sans-serif;*/
}

.modal-header {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  margin-top: 0;
  padding: 0.3rem 1.5rem;
  background-color: #0C90AD;
  color: #fff;
  font-size: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .modal-title {
    flex: 0 1 auto;
  }

  button {
    font-size: 2.3rem;
    color: #fff;
    background-color: inherit;
    border: none;
    flex: 0 1 auto;

    &:focus {
      outline: none; // stackoverflow.com/questions/19053181/how-to-remove-focus-around-buttons-on-click
    }

    &:hover {
      font-weight: 800;
    }
  }
}

.modal-body {
  // margin: 1.4rem 0;
  padding: 1.5rem 1.5rem;
  max-height: 80vh;
  // max-height: 720px;
  overflow-y: auto;
  overflow-x: hidden;
}

.modal-footer {
  margin-bottom: 0;
  padding: 0.75rem 1rem;
}

/*
 * The following styles are auto-applied to elements with
 * transition="modal" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the modal transition by editing
 * these styles.
 */

.modal-enter {
  opacity: 0;
}

.modal-leave-active {
  opacity: 0;
}

.modal-enter .modal-container,
.modal-leave-active .modal-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

@media (max-width: 1024px) and (min-width: 768px) {
  .modal-container {
    width: 820px;
  }

  .modal-body {
    max-height: 540px;
  }
}
</style>
