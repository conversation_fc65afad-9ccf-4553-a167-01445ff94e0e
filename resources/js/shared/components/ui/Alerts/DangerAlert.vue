<script setup lang="ts">
defineProps<{
  message: string;
}>();
</script>

<script lang="ts">
export default {
  name: 'DangerAlert',
};
</script>

<template>
  <div class="alert">
    <h4><i class="icon fa fa-ban" /> Σφάλμα!</h4>
    {{ message }}
  </div>
</template>

<style scoped>
.alert {
  background-color: var(--red-100);
  border: 1px solid var(--red-700);
  border-radius: 3px;
  color: var(--red-700);
  display: block;
  margin-bottom: 1.25rem;
  padding: 1rem 1.25rem;
}
</style>
