<template>
    <table id="vue-dt" class="table table-striped table-condensed"></table>
</template>

<script>
import get from 'lodash/get'
    export default {

        name: 'DataTable',

//        props: ['tableData', 'tableHeaders', 'tableScroll', 'tableAction'],
        props: {
            tableData: {type: Array},
            tableHeaders: {type: Array},
            tableScroll: {type: Boolean, default: false},
            tableActions: {type: Array, default: function () { return []}},
            printableColumns: {type: Array, default: function () { return []}}
        },

        data() {
            return {
                headers: this.tableHeaders,
                rows: [],
                hasAction: false,
                dtHandle: null,

            }
        },

        watch: {
            tableData(val, oldVal) {
                this.prepareRows(val);
                this.emptyTable();
                this.fillTable();
            }
        },

        created() {
            // If table has actions then add an extra column to table headers
            if (this.tableActions.length > 0) {
                this.headers.push({title: '<i class="fa fa-cog"></i>', class: 'text-center'});
                this.hasAction = true;
            }
        },

        mounted() {
            // Datatables greek lang
            $.extend(true, $.fn.dataTable.defaults, {
                language: {
                    url: "/js/datatables.greek.lang"
                },
            });

            // Set date format for datatable sorting
            $.fn.dataTable.moment('DD-MM-YYYY');
            $.fn.dataTable.moment('DD/MM/YYYY');
            $.fn.dataTable.moment('DD-MM-YYYY, HH:mm');

            // Instantiate the datatable and store the reference to the instance in our dtHandle element.
            this.dtHandle = $(this.$el).DataTable({
                // Specify whatever options you want, at a minimum these:
                columns: this.headers,
                data: this.rows,
                dom: `
                <"flex-container"
                    <"flex-item" f>
                    <"flex-item" B>
                >
                <
                    < rt>
                >
                <"flex-container"
                    <"flex-item" l>
                    <"flex-item" i>
                    <"flex-item" p>
                >`,
                buttons: [
                    {
                        text: '<i class="fa fa-print" aria-hidden="true"></i> Εκτύπωση',
                        extend: 'print',
                        exportOptions: {
                            columns: this.printableColumns.join(",")
                        },
                        className: 'btn btn-default btn-flat btn-xs',
                    },
                    {
                        text: '<i class="fa fa-file-excel-o" aria-hidden="true"></i> Excel',
                        extend: 'excel',
                        className: 'btn btn-default btn-flat btn-xs'
                    },
                    {
                        text: '<i class="fa fa-file-pdf-o" aria-hidden="true"></i> PDF',
                        extend: 'pdfHtml5',
                        exportOptions: {
                            columns: this.printableColumns.join(",")
                        },
                        orientation: 'landscape',
                        className: 'btn btn-default btn-flat btn-xs',
                    }
                ],
                order: [0, 'asc'],
                paging: !this.tableScroll,
                scrollY: (this.tableScroll) ? '50vh' : null,
                scrollCollapse: this.tableScroll,
                lengthChange: true,
                searching: true,
                ordering: true,
                info: true,
                autoWidth: false,
            });

            // Draw rows if we have initial data
            if (this.tableData) {
                this.prepareRows(this.tableData);
                this.fillTable();
            }
        },

        methods: {
            fillTable() {
                // Here's the magic to keeping the DataTable in sync.
                // New rows added, then redrawn!
                this.dtHandle.rows.add(this.rows);
                this.dtHandle.draw();
            },

            emptyTable() {
                // Datatable rows cleared
                this.dtHandle.clear();
            },

            prepareRows(val) {
                // let vm = this;
                this.rows = [];
                // You should _probably_ check that this is changed data... but we'll skip that for this example.
                val.forEach(item => {
                    // Fish out the specific column data for each item in your data set and push it to the appropriate place.
                    // Basically we're just building a multi-dimensional array here. If the data is _already_ in the right format
                    // you could skip this loop...
                    let row = [];

                    this.headers.forEach(function (header) {
                        if (typeof header['field'] !== "undefined") {
                            row.push(get(item, header['field']));
                        }
                    });

                    if (this.hasAction) {
                        let resourceActions = this.setResourceActions(this.tableActions, item.id);
                        row.push(resourceActions);
                    }

                    this.rows.push(row);
                });

            },
            /**
             * Create the html action buttons for a given resource id
             * @param actions {Array} - An array of available actions
             * @param resourceId {Number} - The id of the resource; this will replace the {id} placeholder in `actions` array
             */
            setResourceActions(actions, resourceId) {
                let htmlActions = '';
                actions.forEach(action => {
                    let url = action.url.replace('{id}', resourceId);  // we replace the {id} placeholder with the resource's id
                    htmlActions += `<a href="${url}" class="btn ${action.btnType}"><i class="fa ${action.icon}"></i></a>`;
                });
                return htmlActions;
            }
        }
    }
</script>

<style lang="scss" rel="stylesheet/scss">
    .flex-container {
        display: flex;
        // flex-flow: row nowrap; // shorthand for flex-direction: row; flex-wrap: nowrap;
        justify-content: space-between; // align items in x-axis; default: flex-start
        align-items: center; // align items in y-axis; default: flex-start
        padding: 10px 0px;
    }

    .flex-item {
        flex: 0 1 auto; //shorthand for flex-grow: 0; flex-shrink: 1; flex-basis: auto
    }

    .flex-item-grow {
        flex: 1 1 auto;
    }

    /*Datatable overwrites*/

    .dataTables_info {
        padding: 8px;
    }

    /* pagination */
    #vue-dt_paginate {
        .pagination > .active > a,
        .pagination > .active > a:focus,
        .pagination > .active > a:hover,
        .pagination > .active > span,
        .pagination > .active > span:focus,
        .pagination > .active > span:hover {
            background-color: #DDD;
            border-color: #DDD;
            color: #000;
        }
    }

    /*Define a space between the data-table's action buttons*/
    #vue-dt > tbody > tr > td.text-center > a {
        margin: 0 2px;
    }

</style>
