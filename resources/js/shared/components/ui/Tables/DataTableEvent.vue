<template>
    <table id="vue-dt" class="table table-striped table-condensed"></table>
</template>

<script>
import get from 'lodash/get'
    export default {

        name: 'DataTableEvent',

        props: ['tableData', 'tableHeaders', 'tableScroll', 'tableAction'],

        data() {
            return {
                headers: this.tableHeaders,
                rows: [],
                hasAction: false,
                dtHandle: null,

            }
        },

        created() {
            if ((typeof this.tableAction !== "undefined") && (this.tableAction != '')) {
                this.headers.push({ title: '<i class="fa fa-cog"></i>', class: 'text-center'});
                this.hasAction = true;
            }
        },

        methods: {
            fillTable() {
                // Here's the magic to keeping the DataTable in sync.
                // New rows added, then redrawn!
                this.dtHandle.rows.add(this.rows);
                this.dtHandle.draw();
            },

            emptyTable() {
                // Datatable rows cleared
                this.dtHandle.clear();
            },

            prepareRows(val) {
                // let vm = this;
                this.rows = [];
                let showAction = '';
                let itemVal = '';
                // You should _probably_ check that this is changed data... but we'll skip that for this example.
                if (typeof val !== "undefined") {
                    val.forEach(item => {
                    // Fish out the specific column data for each item in your data set and push it to the appropriate place.
                    // Basically we're just building a multi-dimensional array here. If the data is _already_ in the right format you could
                    // skip this loop...
                        let row = [];
                        this.headers.forEach(function (header) {
                            if (typeof header['field'] !== "undefined") {

                                if (header['type'] == 'boolean') {
                                    itemVal = (get(item, header['field'])==true) ? '<i class="fa fa-check"></i> NAI</span>' : '<i class="fa fa-times"></i>';
                                } else {
                                    itemVal = get(item, header['field']);
                                }

                                if (typeof itemVal === 'undefined')
                                    itemVal = '';

                                row.push(itemVal);
                            }
                        });

                        if (this.hasAction) {
                            showAction = `<a href="#" item_id="${item.id}" item="${item}" class="btn btn-info callable"><i class="fa fa-info-circle"></i></a>`;
                            row.push(showAction);
                        }

                        this.rows.push(row);
                    });
                } else {

                }


            }
        },

        watch: {
            tableData(val, oldVal) {
                this.prepareRows(val);
                this.emptyTable();
                this.fillTable();
            }
        },

        mounted() {
            var vm = this;

            $(() => {
                $(document).on('click', '.dataTable .callable', function(event) {
                    event.preventDefault();
                    vm.$emit('showdetails', $(this).attr('item_id'));
                });
            });

            // Datatables greek lang
            $.extend(true, $.fn.dataTable.defaults, {
                "language": {
                    "url": "/js/datatables.greek.lang"
                }
            });
            // Set date format for datatable sorting
            $.fn.dataTable.moment( 'DD-MM-YYYY' );
            $.fn.dataTable.moment( 'DD/MM/YYYY' );
            $.fn.dataTable.moment( 'DD-MM-YYYY, HH:mm' );

            // Instantiate the datatable and store the reference to the instance in our dtHandle element.
            this.dtHandle = $(this.$el).DataTable({
                // Specify whatever options you want, at a minimum these:
                columns: this.headers,
                data: this.rows,

                dom: "<'row'<'col-sm-6'l><'col-sm-6 text-right'B>>frtip",
                buttons: [
                    {
                        text: '<i class="fa fa-print" aria-hidden="true"></i> Εκτύπωση',
                        extend: 'print',
                        className: 'btn btn-default btn-xs',
                    },
                    {
                        text: '<i class="fa fa-file-excel-o" aria-hidden="true"></i> Excel',
                        extend: 'excel',
                        className: 'btn btn-default btn-xs'
                    },
                    {
                        text: '<i class="fa fa-file-pdf-o" aria-hidden="true"></i> PDF',
                        extend: 'pdfHtml5',
                        orientation: 'landscape',
                        className: 'btn btn-default btn-xs',
                    }
                ],
                paging: !this.tableScroll,
                scrollY:  (this.tableScroll) ? '50vh':null,
                scrollCollapse: this.tableScroll,
                lengthChange: true,
                searching: true,
                ordering: true,
                info: true,
                autoWidth: false
            });

            // Draw rows if we have initial data
            if (this.tableData) {
                this.prepareRows(this.tableData);
                this.fillTable();
            }
        },
    }
</script>
