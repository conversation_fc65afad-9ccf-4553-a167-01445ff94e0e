<template>
  <div
    v-if="url"
    class="row"
  >
    <div class="col-sm-2 col-sm-offset-5 text-center">
      <button
        type="button"
        class="btn btn-outline btn-block"
        @click="fetchExport"
      >
        <i
          class="fa fa-download"
          aria-hidden="true"
        /> Export
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResourceExport',

  props: {
    url: String,
    query: Object,
  },

  methods: {
    fetchExport() {
      // const filterParameters = {};
      // for (const [i, filter] of Object.entries(this.query.filters)) {
      //   filterParameters[`filters[${i}][column]`] = filter.column;
      //   filterParameters[`filters[${i}][operator]`] = filter.operator;
      //   filterParameters[`filters[${i}][query_1]`] = filter.query_1;
      //   filterParameters[`filters[${i}][query_2]`] = filter.query_2;
      // }
      //
      // const sortParameters = {};
      // for (const [i, sort] of Object.entries(this.query.sorts)) {
      //   sortParameters[`sorts[${i}][column]`] = sort.column;
      //   sortParameters[`sorts[${i}][direction]`] = sort.direction;
      // }

      // We are using URLSearchParams to parse the querystring into an object with nested array data
      // FIXME use a computed property instead of URLSearchParams
      // TODO check we we canot use GET method (see https://gist.github.com/javilobo8/097c30a233786be52070986d8cdb1743)
      // this.$http.post(this.url, new URLSearchParams({ ...filterParameters, ...sortParameters }), { responseType: 'blob' })
      const payload = {
        ...(this.query.filters.length > 0 && { filters: this.query.filters }), // if condition is true 'b' will be added.
        ...(this.query.sorts.length > 0 && { sorts: this.query.sorts }), // if condition is true 'b' will be added.
      };
      this.$http.post(this.url, payload, { responseType: 'blob' })
        .then((response) => {
          const url = window.URL.createObjectURL(new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', response.headers['content-disposition'].split('filename=')[1]);
          document.body.appendChild(link);
          link.click();
          link.remove();
          window.URL.revokeObjectURL(url);
        })
        .catch(() => {
          window.swal({
            type: 'error',
            title: 'Παρουσιάστηκε σφάλμα',
            showConfirmButton: false,
            timer: 2500,
          });
        });
    },
  },
};
</script>
