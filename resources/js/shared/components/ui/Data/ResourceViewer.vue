<template>
  <div id="resource-viewer">
    <ResourceFilters
      :filterables="filterables"
      :sortables="sortables"
      :active-filters="selectedQuery"
      @query-submitted="submitQuery"
      @query-reset="resetQuery"
    />
    <ResourceListing
      :listables="listables"
      :items="paginatedData.data"
    >
      <template #default="{ tableData }">
        <slot :table-data="tableData" />
      </template>
    </ResourceListing>
    <ResourcePagination
      :paginator-meta="paginatedData.meta"
      @page-changed="updatePage"
      @limit-changed="updateLimit"
    />
    <ResourceExport
      :url="exportUrl"
      :query="selectedQuery"
    />
  </div>
</template>

<script>
import ResourceExport from './ResourceExport.vue';
import ResourceFilters from './ResourceFilters.vue';
import ResourceListing from './ResourceListing.vue';
import ResourcePagination from './ResourcePagination.vue';

export default {
  name: 'ResourceViewer',

  components: {
    ResourceExport,
    ResourcePagination,
    ResourceFilters,
    ResourceListing,
  },

  props: {
    paginatedData: {
      type: Object,
      required: true,
      // TODO: add validation for "data" and "meta"
    },
    filterables: {
      type: Array,
      required: true,
    },
    sortables: {
      type: Array,
      required: true,
    },
    listables: {
      type: Array,
      required: true,
    },
    filterCookie: { // TODO: Maybe pass directly the active filters (if exist) from parent component
      type: String,
      default: null,
    },
    exportUrl: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      selectedQuery: { filters: [], sorts: [] },
      selectedPagination: { page: 1, limit: this.paginatedData.meta.per_page },
    };
  },
  created() {
    // This runs every time the page reloads
    const activeFilters = this.$cookies.get(this.filterCookie);

    // console.log('Searching for active filters in cookie...');

    if (activeFilters) {
      // console.log('Active filters cookie found: ', activeFilters);

      const {
        filters = [],
        sorts = [],
        page = 1,
        limit = this.paginatedData.meta.per_page,
      } = activeFilters;

      this.selectedQuery = { filters, sorts };
      this.selectedPagination = { page, limit };
    } else {
      // console.log('No active filters cookie found.');
    }
  },
  methods: {
    submitQuery(selectedQuery) {
      this.selectedQuery = selectedQuery;
      this.resetPage();
      this.fetchData();
    },
    resetQuery() {
      this.resetFilters();
      this.resetLimit();
      this.resetPage();
      this.fetchData();
    },
    updateLimit(newLimit) {
      this.selectedPagination.limit = newLimit;
      this.resetPage();
      this.fetchData();
    },
    updatePage(newPage) {
      this.selectedPagination.page = newPage;
      this.fetchData();
    },
    resetFilters() {
      this.selectedQuery = { filters: [], sorts: [] };
    },
    resetLimit() {
      this.selectedPagination.limit = this.paginatedData.meta.per_page;
    },
    resetPage() {
      this.selectedPagination.page = 1;
    },
    fetchData() {
      this.$emit('fetch-data', { ...this.selectedQuery, ...this.selectedPagination });
    },
  },
};
</script>
