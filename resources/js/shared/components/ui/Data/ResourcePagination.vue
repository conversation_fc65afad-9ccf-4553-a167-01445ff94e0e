<template>
  <div class="paginator row">
    <div class="col-sm-6">
      <div
        v-if="paginatorMeta.total > paginatorMeta.per_page"
        class="pagination-pages"
      >
        <div
          v-if="paginatorMeta.current_page > 1"
          class="pagination-page"
          @click="updatePage(--paginatorMeta.current_page)"
        >
          Προηγούμενη
        </div>
        <div
          v-for="(page, index) in pagesToDisplay"
          :key="index"
          :class="[{ 'active': page === paginatorMeta.current_page }, {'disabled': typeof(page) === 'string'}, 'pagination-page']"
          @click="updatePage(page)"
        >
          {{ page }}
        </div>
        <div
          v-if="paginatorMeta.current_page < paginatorMeta.last_page"
          class="pagination-page"
          @click="updatePage(++paginatorMeta.current_page)"
        >
          Επόμενη
        </div>
        <p>Προβολή {{ paginatorMeta.from }} έως {{ paginatorMeta.to }} από {{ paginatorMeta.total }}</p>
      </div>
    </div>
    <div class="col-sm-6">
      <div class="pagination-limits">
        <p>Αποτελέσματα ανά σελίδα</p>
        <div
          v-for="(limitOption, index) in limitOptions"
          :key="index"
          :class="[{ active: limitOption === paginatorMeta.per_page }, 'pagination-limit']"
          @click="updateLimit(limitOption)"
        >
          {{ limitOption }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResourcePagination',

  props: {
    paginatorMeta: Object,
  },

  data() {
    return {
      limit: this.paginatorMeta.per_page,
    };
  },

  computed: {
    pagesToDisplay() {
      const totalPages = this.paginatorMeta.last_page;
      const selectedPage = this.paginatorMeta.current_page;
      const range = (start, end) => Array.from(Array.from(Array(end - start + 1).keys()), (x) => start + x);

      if (totalPages <= 7) {
        return range(1, totalPages);
      }
      if (selectedPage <= 1 + 3) {
        return [...range(1, 1 + 4), ...['...'], ...range(totalPages - 1, totalPages)];
      } if (selectedPage >= totalPages - 3) {
        return [...range(1, 1 + 1), ...['...'], ...range(totalPages - 4, totalPages)];
      }
      return [...range(1, 1 + 1), ...['...'], ...range(selectedPage - 1, selectedPage + 1), ...['...'], ...range(totalPages - 1, totalPages)];
    },
    limitOptions() {
      const defaultLimitOptions = [5, 10, 20, 50];
      if (defaultLimitOptions.indexOf(this.paginatorMeta.per_page) === -1) {
        defaultLimitOptions.push(this.paginatorMeta.per_page);
      }
      return defaultLimitOptions
        .sort((a, b) => a - b);
    },
  },

  methods: {
    updatePage(selectedPage) {
      if (typeof (selectedPage) === 'string') {
        return;
      }
      this.$emit('page-changed', selectedPage);
    },
    updateLimit(limit) {
      this.$emit('limit-changed', limit);
    },
  },
};
</script>

<style scoped>
.paginator {
  margin-top: 1rem;
}

.pagination-pages,
.pagination-limits {
  display: flex;
  align-items: center;
}

.pagination-limits {
  justify-content: end;
}

.pagination-page,
.pagination-limit {
  text-align: center;
  border: 1px solid var(--gray-400);
  cursor: pointer;
  padding: 0.5rem;
  min-width: 2.5rem;
}

.pagination-page:first-child,
.pagination-limit:first-child {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.pagination-page:last-child,
.pagination-limit:last-child {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.pagination-page.active,
.pagination-limit.active {
  background-color: var(--gray-400);
}

.pagination-page.disabled,
.pagination-page.disabled:hover {
  color: var(--gray-500);
  cursor: default;
}

.pagination-page:hover,
.pagination-limit:hover {
  color: var(--brand-color);
}

.pagination-pages > p {
  margin: 0 0 0 1rem;
  color: var(--gray-600);
}

.pagination-limits > p {
  margin: 0 1rem 0 0;
  color: var(--gray-600);
}
</style>
