<template>
  <div>
    <div class="heading">
      <div class="heading__search">
        <input
          v-if="searchable"
          v-model="searchQuery"
          class="form-control"
          type="text"
          placeholder="Αναζήτηση"
          name="query"
          @input="foo"
        >
      </div>
      <div
        v-if="localStorageUuid"
        class="heading__refresh"
        @click="reset"
      >
        <i class="fa fa-undo" /> Επαναφορά αποτελεσμάτων
      </div>
    </div>
    <table class="table table-bordered">
      <thead>
        <tr>
          <th
            v-for="(listable, index) in listables"
            :key="index"
            :class="['text-nowrap', { active: sortKey === listable.field }]"
            @click="sortBy(listable.field)"
          >
            {{ listable.label }}
            <span
              class="arrow"
              :class="sortOrders[listable.field] > 0 ? 'asc' : 'dsc'"
            />
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(paginatedItem, index) in paginatedItems"
          :key="paginatedItem.id ? paginatedItem.id : index"
        >
          <td
            v-for="listable in listables"
            :key="listable.field"
            :class="{ 'text-right': listable.type === 'number' }"
          >
            <slot :table-data="{row: paginatedItem, column: listable}">
              {{ paginatedItem[listable.field] }}
            </slot>
          </td>
        </tr>
      </tbody>
    </table>
    <ResourcePagination
      :paginator-meta="paginatorMeta"
      @page-changed="changePage"
      @limit-changed="changeLimit"
    />
  </div>
</template>
<script>
import ResourcePagination from './ResourcePagination.vue';

const LOCAL_STORAGE_TIMEOUT = 1.2e+6; // 20 minutes

export default {
  name: 'DataViewer',
  components: {
    ResourcePagination,
  },
  props: {
    listables: {
      type: Array,
      required: true,
    },
    items: {
      type: [Object, Array],
      required: true,
    },
    searchable: {
      type: Boolean,
      default: true,
    },
    localStorageUuid: {
      type: String,
      default: null,
    },
  },
  data() {
    const sortOrders = {};
    this.listables.map((listable) => listable.field).forEach((field) => { sortOrders[field] = 1; });
    return {
      searchQuery: this.getLocalStorage('search-query') || '',
      sortKey: this.getLocalStorage('sort-key') || '',
      sortOrders: this.getLocalStorage('sort-orders') || sortOrders,
      startRow: this.getLocalStorage('start-row') || 0,
      rowsPerPage: this.getLocalStorage('rows-per-page') || 10,
    };
  },
  computed: {
    filteredItems() {
      return this.items
        .filter((item) => Object.values(item)
          .some((val) => this.deburrGreek(String(val))
            .indexOf(this.deburrGreek(String(this.searchQuery))) > -1));
    },
    sortedItems() {
      if (this.sortKey === '') {
        return this.filteredItems;
      }
      return this.filteredItems.slice().sort((a, b) => {
        const { type } = this.listables.filter((e) => e.field === this.sortKey)[0];

        a = a[this.sortKey];
        b = b[this.sortKey];

        if (type === 'number') {
          a = Number(a);
          b = Number(b);
        }

        return (a === b ? 0 : a > b ? 1 : -1) * this.sortOrders[this.sortKey];
      });
    },
    paginatedItems() {
      return this.sortedItems.slice(this.startRow, this.startRow + this.rowsPerPage);
    },
    paginatorMeta() {
      return {
        last_page: Math.ceil(this.filteredItems.length / this.rowsPerPage),
        current_page: (this.startRow / this.rowsPerPage) + 1,
        per_page: this.rowsPerPage,
        from: this.startRow + 1,
        to: (this.startRow + this.rowsPerPage) > this.filteredItems.length ? this.filteredItems.length : this.startRow + this.rowsPerPage,
        total: this.filteredItems.length,
      };
    },
  },
  watch: {
    searchQuery(newQs, oldQs) {
      this.setLocalStorage('search-query', newQs, LOCAL_STORAGE_TIMEOUT);
    },
  },
  methods: {
    changePage(page) {
      this.startRow = ((page - 1) * this.rowsPerPage);
      this.setLocalStorage('start-row', this.startRow, LOCAL_STORAGE_TIMEOUT);
    },
    changeLimit(limit) {
      this.rowsPerPage = limit;
      this.setLocalStorage('rows-per-page', this.rowsPerPage, LOCAL_STORAGE_TIMEOUT);
    },
    deburrGreek(string) {
      return string.normalize('NFD').replace(/[\u0300-\u036f]/g, '').toLowerCase();
    },
    sortBy(field) {
      this.sortKey = field;
      this.sortOrders[field] *= -1;
      this.setLocalStorage('sort-key', this.sortKey, LOCAL_STORAGE_TIMEOUT);
      this.setLocalStorage('sort-orders', this.sortOrders, LOCAL_STORAGE_TIMEOUT);
    },
    setLocalStorage(key, value, ttl) {
      if (this.localStorageUuid === null) {
        return;
      }
      const localStorageKey = `apptree-dataviewer-${this.localStorageUuid}-${key}`;
      const now = new Date();
      const item = {
        value,
        expiry: now.getTime() + ttl,
      };
      localStorage.setItem(localStorageKey, JSON.stringify(item));
    },
    getLocalStorage(key) {
      if (this.localStorageUuid === null) {
        return null;
      }
      const localStorageKey = `apptree-dataviewer-${this.localStorageUuid}-${key}`;
      const value = localStorage.getItem(localStorageKey);
      if (!value) {
        return null;
      }
      const item = JSON.parse(value);
      const now = new Date();
      if (now.getTime() > item.expiry) {
        localStorage.removeItem(localStorageKey);
        return null;
      }
      return item.value;
    },
    clearLocalStorage() {
      [
        'search-query',
        'start-row',
        'rows-per-page',
        'sort-key',
        'sort-orders',
      ].forEach((key) => {
        localStorage.removeItem(`apptree-dataviewer-${this.localStorageUuid}-${key}`);
      });
    },
    reset() {
      this.clearLocalStorage();
      this.searchQuery = '';
      this.sortKey = '';
      this.startRow = 0;
      this.rowsPerPage = 10;
      const sortOrders = {};
      this.listables.map((listable) => listable.field).forEach((field) => { sortOrders[field] = 1; });
      this.sortOrders = sortOrders;
    },
    foo() {
      this.clearLocalStorage();
      this.sortKey = '';
      this.startRow = 0;
      this.rowsPerPage = 10;
      const sortOrders = {};
      this.listables.map((listable) => listable.field).forEach((field) => { sortOrders[field] = 1; });
      this.sortOrders = sortOrders;
    },
  },
};
</script>
<style scoped>
.table > thead {
  background-color: var(--gray-400);
}

.table > thead > tr > th {
  cursor: pointer;
}

th.active {
  background-color: var(--gray-200);
}

.arrow {
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 0;
  margin-left: 5px;
}

.arrow.asc {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid var(--gray-700);
}

.arrow.dsc {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--gray-700);
}

.heading {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  align-items: center;
}

.heading__search {
  min-width: 800px;
}

.heading__refresh {
  color: var(--gray-600);
  border-radius: 3px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.heading__refresh i {
  margin-right: 4px;
}

.heading__refresh:hover {
  color: var(--brand-color);
}
</style>
