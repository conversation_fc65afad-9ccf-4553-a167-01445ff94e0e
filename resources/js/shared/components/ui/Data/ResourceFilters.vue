<template>
  <div>
    <BaseButton
      variation="secondary"
      :icon="filtersOpen ? 'tabler:x' : 'tabler:adjustments-horizontal'"
      @click="filtersOpen = !filtersOpen"
    >
      Φίλτρα και ταξινόμηση
    </BaseButton>
    <div
      v-show="filtersOpen"
      class="filters-box"
    >
      <el-tabs>
        <el-tab-pane>
          <span
            slot="label"
            class="tab__title"
          >
            <Icon
              icon="tabler:filter"
              :width="18"
            /> Φίλτρα
          </span>
          <div class="filters row">
            <div class="col-sm-12">
              <div
                v-for="(filter, index) in filters"
                :key="index"
                class="filter row"
              >
                <!-- Select field/column -->
                <div class="filter__column col-sm-3">
                  <div class="form-group">
                    <el-select
                      v-model="filter.column"
                      clearable
                      placeholder="Επιλογή πεδίου..."
                      size="mini"
                      @input="clearOperatorAndQueryInputs(filter)"
                    >
                      <el-option
                        v-for="filterable in filterables"
                        :key="filterable.field"
                        :label="filterable.label"
                        :value="filterable.field"
                      />
                    </el-select>
                  </div>
                </div>
                <!-- Select operator -->
                <div
                  v-if="filter.column"
                  class="filter__operator col-sm-2"
                >
                  <div class="form-group">
                    <el-select
                      v-model="filter.operator"
                      clearable
                      placeholder="που να είναι..."
                      size="mini"
                      @input="clearQueryInputs(filter)"
                    >
                      <el-option
                        v-for="applicableOperator in applicableOperators(filter)"
                        :key="applicableOperator.value"
                        :label="applicableOperator.label"
                        :value="applicableOperator.value"
                      />
                    </el-select>
                  </div>
                </div>
                <!-- Query Input 1 -->
                <div
                  v-if="filter.operator"
                  class="filter__query_1 col-sm-3"
                >
                  <Component
                    :is="queryComponent(filter)"
                    v-model="filter.query_1"
                    :options="['select', 'select-multiple'].includes(getFilterType(filter)) ? queryComponentsOptions(filter) : null"
                    size="mini"
                  />
                </div>
                <!-- Query Input 2 -->
                <div
                  v-if=" filter.operator && hasRange(filter.operator)"
                  class="filter__query_2 col-sm-3"
                >
                  <Component
                    :is="queryComponent(filter)"
                    v-model="filter.query_2"
                    size="mini"
                  />
                </div>
                <!-- Remove filter -->
                <div
                  v-if="filter.column !== undefined"
                  class="filter__remove col-sm-1"
                >
                  <i
                    class="fa fa-trash"
                    @click="removeFilter(filter)"
                  />
                </div>
              </div>
              <div class="filters__actions row">
                <div class="col-sm-12">
                  <!-- Add new filter -->
                  <span
                    class="filter__add"
                    @click="addFilter"
                  >
                    <i
                      class="fa fa-plus"
                      aria-hidden="true"
                    /> Προσθήκη φίλτρου
                  </span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane>
          <span
            slot="label"
            class="tab__title"
          >
            <Icon
              icon="tabler:arrows-sort"
              :width="18"
            /> Ταξινόμηση
          </span>
          <div class="sorts row">
            <div class="col-sm-12">
              <div
                v-for="(sort, index) in sorts"
                :key="index"
                class="sort row"
              >
                <!-- Select field/column -->
                <div class="sort__column col-sm-3">
                  <div class="form-group">
                    <el-select
                      v-model="sort.column"
                      clearable
                      placeholder="Επιλογή πεδίου..."
                      size="mini"
                      @input="clearDirection(sort)"
                    >
                      <el-option
                        v-for="sortable in sortables"
                        :key="sortable.field"
                        :label="sortable.label"
                        :value="sortable.field"
                      />
                    </el-select>
                  </div>
                </div>
                <!-- Select direction -->
                <div
                  v-if="sort.column"
                  class="sort__direction col-sm-3"
                >
                  <div class="form-group">
                    <el-select
                      v-model="sort.direction"
                      clearable
                      placeholder="Σειρά..."
                      size="mini"
                    >
                      <el-option
                        v-for="direction in availableSortDirections"
                        :key="direction.value"
                        :label="direction.label"
                        :value="direction.value"
                      />
                    </el-select>
                  </div>
                </div>
                <!-- Remove Sort -->
                <div
                  v-if="sort.column !== undefined"
                  class="sort__remove col-sm-1"
                >
                  <i
                    class="fa fa-trash"
                    @click="removeSort(sort)"
                  />
                </div>
              </div>
              <div class="sorts__actions row">
                <div class="col-sm-12">
                  <!-- Add new sort -->
                  <span
                    class="sort__add"
                    @click="addSort"
                  >
                    <i
                      class="fa fa-plus"
                      aria-hidden="true"
                    /> Προσθήκη ταξινόμισης
                  </span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <div
          v-if="hasSelectedFilters || hasSelectedSorts"
          class="actions"
        >
          <!-- Submit filters -->
          <button
            class="btn btn-default btn-sm filter__submit"
            :disabled="!(areValidFilters && areValidSorts)"
            @click="submit"
          >
            <i
              class="fa fa-search"
              aria-hidden="true"
            /> Αναζήτηση
          </button>
          <button
            class="btn btn-default btn-outline btn-sm filter__reset"
            @click="reset"
          >
            <i
              class="fa fa-undo"
              aria-hidden="true"
            /> Επαναφορά
          </button>
        </div>
      </el-tabs>
    </div>
    <div
      v-show="!filtersOpen"
      class="tw:mb-4"
    >
      <ul
        v-if="activeFilters.filters.length > 0"
        class="filters-summary"
      >
        <li
          v-for="(filter, index) in activeFilters.filters"
          :key="index"
          class="filters-summary__item"
        >
          <i
            class="fa fa-filter"
            aria-hidden="true"
          /> {{ getActiveFilterSummary(filter) }}
        </li>
      </ul>
      <p
        v-if="activeFilters.sorts.length > 0"
      >
        [<i
          class="fa fa-sort"
          aria-hidden="true"
        /> {{ activeSortsNames }}]
      </p>
    </div>
  </div>
</template>

<script>
import { Icon } from '@iconify/vue2';
import { cloneDeep } from 'lodash';

import BaseButton from '@/shared/components/ui/Buttons/BaseButton.vue';

import BaseSelect from '../FormFields/BaseSelect.vue';
import DateField from '../FormFields/DateField.vue';
import NumberField from '../FormFields/NumberField.vue';
import SelectfilterField from '../FormFields/SelectfilterField.vue';
import SelectmultipleField from '../FormFields/SelectmultipleField.vue';
import TextField from '../FormFields/TextField.vue';

export default {
  name: 'ResourceFilter',
  components: {
    TextField,
    DateField,
    NumberField,
    SelectfilterField,
    SelectmultipleField,
    BaseSelect,
    BaseButton,
    Icon,
  },
  props: {
    filterables: {
      type: Array,
      required: true,
    },
    sortables: {
      type: Array,
      required: true,
    },
    activeFilters: {
      type: Object,
      default: () => ({ fitlers: [], sorts: [] }),
    },
  },
  data() {
    return {
      filtersOpen: false,
      filters: cloneDeep(this.activeFilters.filters),
      sorts: cloneDeep(this.activeFilters.sorts),
      availableFilterOperators: [
        {
          label: 'ίσο με',
          value: 'equal_to',
          types: ['number', 'string', 'select'],
          hasRange: false,
        },
        {
          label: 'διαφορετικό από',
          value: 'not_equal_to',
          types: ['number', 'string'],
          hasRange: false,
        },
        {
          label: 'μικρότερο από',
          value: 'less_than',
          types: ['number'],
          hasRange: false,
        },
        {
          label: 'μεγαλύτερο από',
          value: 'greater_than',
          types: ['number'],
          hasRange: false,
        },
        {
          label: 'μεταξύ των',
          value: 'between',
          types: ['number'],
          hasRange: true,
        },
        {
          label: 'όχι μεταξύ των',
          value: 'not_between',
          types: ['number'],
          hasRange: true,
        },
        {
          label: 'περιέχει',
          value: 'contains',
          types: ['string'],
          hasRange: false,
        },
        {
          label: 'ξεκινάει με',
          value: 'starts_with',
          types: ['string'],
          hasRange: false,
        },
        {
          label: 'between dates',
          value: 'between_date',
          types: ['date'],
          hasRange: true,
        },
        {
          label: 'ανήκει στα',
          value: 'in',
          types: ['select-multiple'],
          hasRange: false,
        },
      ],
      availableSortDirections: [
        { label: 'Άυξουσα', value: 'asc' },
        { label: 'Φθίνουσα', value: 'desc' },
      ],
      foo: '',
    };
  },
  computed: {
    areValidFilters() {
      return this.filters.reduce((acc, filter) => acc
          && (filter.column !== null && filter.column !== '')
          && (filter.operator !== null && filter.operator !== '')
          && (filter.query_1 !== null && filter.query_1 !== '' && (Array.isArray(filter.query_1) ? filter.query_1.length > 0 : true))
          && (this.availableFilterOperators.find((operator) => operator.value === filter.operator).hasRange ? filter.query_2 !== null && filter.query_1 !== '' : true), true);
    },
    areValidSorts() {
      return this.sorts.reduce((acc, sort) => acc
          && (sort.column !== null && sort.column !== '')
          && (sort.operator !== null && sort.direction !== ''), true);
    },
    hasSelectedFilters() {
      return this.filters.length > 0;
    },
    hasSelectedSorts() {
      return this.sorts.length > 0;
    },
    activeFiltersNames() {
      return this.activeFilters.filters.map((filter) => this.filterables.find((filterable) => filterable.field === filter.column)?.label).join(', ');
    },
    activeSortsNames() {
      return this.activeFilters.sorts.map((sort) => this.sortables.find((sortable) => sortable.field === sort.column)?.label).join(', ');
    },
  },
  // watch: {
  //   filters: {
  //     deep: true,
  //     handler(oldValue, newValue) {
  //       this.foo = newValue;
  //       console.log('foo',this.foo);
  //     },
  //   },
  // },
  methods: {
    addFilter() {
      this.filters.push({
        column: '',
        operator: '',
        query_1: null,
        query_2: null,
      });
    },
    queryComponent(filter) {
      switch (this.getFilterType(filter)) {
        case 'number':
          return 'NumberField';
        case 'string':
          return 'TextField';
        case 'date':
          return 'DateField';
        case 'select':
          return 'SelectfilterField';
        case 'select-multiple':
          return 'SelectmultipleField';
        default:
          return 'TextField';
      }
    },
    queryComponentsOptions(filter) {
      return this.filterables.find((filterable) => filterable.field === filter.column).selectOptions;
    },
    applicableOperators(filter) {
      return this.availableFilterOperators.filter((operator) => operator.types.includes(this.getFilterType(filter)));
    },
    removeFilter(filter) {
      this.filters.splice(this.filters.indexOf(filter), 1);
    },
    getFilterType(filter) {
      return this.filterables.find((filterable) => filterable.field === filter.column).type;
    },
    getFilterName(filter) {
      return this.filterables.find((filterable) => filterable.field === filter.column)?.label ?? '';
    },
    getActiveFilterSummary(filter) {
      const operator = this.availableFilterOperators.find((_operator) => _operator.value === filter.operator);
      const filterName = this.getFilterName(filter);
      const filterQueryName = this.getActiveFilterQueryLabel(filter);

      if (operator.hasRange) {
        return `${filterName} ${operator.label} ${filterQueryName} και ${filter.query_2}`;
      }
      return `${filterName} ${operator.label} ${filterQueryName}`;
    },
    getActiveFilterQueryLabel(filter) {
      const filterType = this.getFilterType(filter);
      const componentOptions = this.queryComponentsOptions(filter);
      const filterQueryValue = filter.query_1;

      let selectedOption;
      // console.log({componentOptions});
      if (componentOptions && componentOptions.length > 0) {
        selectedOption = componentOptions.find((option) => {
          const foo = option.id.toString();
          const bar = filterQueryValue.toString();
          // console.log({foo,bar});
          return foo === bar;
        });
      }
      // console.log({ selectedOption });

      let filterQueryLabel;
      if (selectedOption) {
        filterQueryLabel = selectedOption.name;
      } else {
        filterQueryLabel = filterQueryValue;
      }

      // console.log({
      //   filterType, componentOptions, filterQueryValue, filterQueryLabel,
      // });

      return filterQueryLabel;
    },
    hasRange(operatorValue) {
      return this.availableFilterOperators.find((operator) => operator.value === operatorValue).hasRange;
    },
    clearQueryInputs(filter) {
      filter.query_1 = filter.operator === 'in' ? [] : null;
      filter.query_2 = null;
    },
    clearOperatorAndQueryInputs(filter) {
      filter.operator = '';
      filter.query_1 = null;
      filter.query_2 = null;
    },
    addSort() {
      this.sorts.push({
        column: '',
        direction: '',
      });
    },
    removeSort(sort) {
      this.sorts.splice(this.sorts.indexOf(sort), 1);
    },
    clearDirection(sort) {
      sort.direction = '';
    },
    submit() {
      this.$emit('query-submitted', { filters: [...this.filters], sorts: [...this.sorts] });
      this.filtersOpen = false;
    },
    reset() {
      this.filters = [];
      this.sorts = [];
      this.filtersOpen = false;
      this.$emit('query-reset');
    },
  },
};
</script>

<style scoped>
.btn-toggle {
  all: unset;
  padding: 10px;
  cursor: pointer;
  color: var(--gray-600);
}

.btn-toggle:hover {
  color: var(--gray-700);
}

.tab__title {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--gray-600);
  display: flex;
  align-items: center;
  gap: 4px;
}

.tab__title:hover {
  color: var(--gray-700);
}

.filters-box {
  margin-block: 1rem;
  padding: 1rem;
  background-color: var(--gray-100);
  border-radius: 0px;
  box-shadow:
    rgba(0, 0, 0, 0.1) 0px 4px 6px -1px,
    rgba(0, 0, 0, 0.06) 0px 2px 4px -1px;
}

.filters,
.sorts {
  margin-top: 10px;
}

.filters .filter__remove,
.sorts .sort__remove {
  cursor: pointer;
  font-size: 15px;
  color: var(--gray-500);
  padding-left: 0;
  padding-top: 3px;
}

.filters .filter__remove:hover,
.sorts .sort__remove:hover {
  color: var(--gray-600);
}

.filters .filter__add,
.sorts .sort__add {
  cursor: pointer;
  color: var(--gray-600);
  /*font-weight: 600;*/
  font-size: 0.9rem;
  margin-right: 0.75rem;
}

.filters .filter__add:hover,
.sorts .sort__add:hover {
  color: var(--gray-700);
}

.actions {
  margin-top: 1rem;
}

/*.actions .filter__submit,*/
/*.actions .filter__reset {*/
/*  margin-top: 3px;*/
/*  margin-left: 15px;*/
/*  margin-bottom: 15px;*/
/*  width: 110px;*/
/*}*/

.actions .filter__reset {
  margin-left: 0.5rem;
  color: var(--gray-700);
}

.actions .filter__reset:hover {
  background-color: var(--gray-200);
}

.filters-summary {
  display: flex;
  gap: 8px;
}

.filters-summary__item {
  /*color: var(--brand-color);*/
  background-color: #ecfeff;
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  padding-inline: 8px;
  padding-block: 4px;
  border-radius: 32px;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
</style>
