<template>
  <field
    :name="name"
    :error="error"
    :title="title"
    :required="required"
  >
    <el-date-picker
      :id="name"
      type="datetime"
      placeholder="Επιλέξτε..."
      format="dd-MM-yyyy HH:mm:ss"
      value-format="yyyy-MM-dd HH:mm:ss"
      :name="name"
      :value="value"
      :disabled="!editing"
      :size="size || null"
      @input="updateValue"
      @keydown.enter.prevent
    />
  </field>
</template>

<script>
import Field from './Field.vue';

export default {
  name: 'DateTimePicker',
  components: {
    Field,
  },
  props: ['value', 'name', 'title', 'error', 'open', 'required', 'size'],
  computed: {
    editing() {
      if (typeof this.open !== 'undefined') {
        return this.open;
      }

      if (typeof this.$parent.editing !== 'undefined') {
        return this.$parent.editing;
      }

      return true;
    },
  },
  methods: {
    updateValue(value) {
      // // sanitize, validate etc
      // // Set empty value if undefined
      // if (!value) {
      //   value = '';
      // }

      // on<PERSON><PERSON><PERSON> must emit the input event to update the parent's <date-field> :value attribute>
      this.$emit('input', value);
    },
  },

};
</script>

<style scoped>

</style>
