<template>
    <field :name="name" :error="this.error" :title="title" :required="required">
        <div class="input-group">
            <input type="text"
                v-digitsonly
                :id="name"
                class="form-control"
                style="text-align: right;"
                :name="name"
                :value="value"
                @keydown.enter.prevent
                @input="updateValue($event.target.value)"
                :disabled="!this.editing"
            >

            <div class="input-group-addon"
                v-bind:class="{ addondisabled: !this.editing }"
            ><i class="fa fa-euro"></i></div>
        </div>

    </field>
</template>

<script>
    import Field from './Field.vue';
    import {JustDigits} from '../../../VueDirectives.js';

    JustDigits();

    export default {
        name: 'MoneyField',

        components: {
            Field,
        },

        props: [ "name", "title", "value", "error", "open", "required" ],

        computed: {
            editing: function() {
                if (typeof this.open !== "undefined") {
                    return this.open;
                }

                if (typeof this.$parent.editing !== "undefined") {
                    return this.$parent.editing;
                }

                return true;
            }
        },

        methods: {
            updateValue(value) {
                // sanitize, validate etc
                this.$emit('valid');

                this.$emit('input', value);
            }
        }
    }
</script>

<style lang="scss" rel="stylesheet/scss">
    input.form-control[disabled], fieldset[disabled] input.form-control {
        cursor: default;
        background-color: #FFF;
        border-color: #FFF;
        color: #333;
        border-bottom: 1px solid #dedede;
        opacity: 1;
    }

    .input-group-addon {
        color: #333;
    }

    .input-group-addon.addondisabled {
        background-color: #FFF;
        border-color: #FFF;
        color: #333;
    }

    // input[type='number'] {
    //     -moz-appearance:textfield;
    // }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
</style>