<template>
  <field
    :name="name"
    :error="error"
    :title="title"
    :required="required"
    :description="helpText"
  >
    <el-input-number
      :id="name"
      :min="0"
      :precision="precision || 0"
      :name="name"
      :value="updatedValue"
      :disabled="!editing"
      :size="size || null"
      @keydown.enter.prevent
      @input="updateValue"
    />
  </field>
</template>

<script>
import Field from './Field.vue';

export default {
  name: 'NumberField',

  components: {
    Field,
  },

  props: {
    name: { type: String, default: '' },
    title: { type: String, default: '' },
    value: { type: [Number, String], default: null },
    error: { type: String, default: '' },
    open: { type: Boolean, default: undefined },
    required: { type: Boolean, default: false },
    size: { type: String, default: '' },
    precision: { type: Number, default: 0 },
    helpText: { type: String, default: '' },
  },

  computed: {
    updatedValue() {
      return this.value === null ? undefined : this.value;
    },
    editing() {
      if (typeof this.open !== 'undefined') {
        return this.open;
      }

      if (typeof this.$parent.editing !== 'undefined') {
        return this.$parent.editing;
      }

      return true;
    },
  },

  methods: {
    updateValue(value) {
      // sanitize, validate etc
      if (Number.isInteger(value)) {
        this.$emit('valid');
      }

      //                this.$refs.input.value = value;   or
      //                this.updatedValue = arguments[0];
      this.$emit('input', value);
    },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.form-group {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
</style>
