<template>
  <field
    :name="name"
    :error="error"
    :title="title"
    :required="required"
    :description="helpText"
  >
    <el-date-picker
      :id="name"
      type="date"
      placeholder="Επιλέξτε..."
      :format="format ?? 'dd-MM-yyyy'"
      :value-format="valueFormat ?? 'yyyy-MM-dd'"
      :name="name"
      :value="value"
      :disabled="!editing"
      :size="size || null"
      @input="updateValue"
      @keydown.enter.prevent
    />
  </field>
</template>

<script>
import Field from './Field.vue';

export default {
  name: 'DateField',

  components: {
    Field,
  },

  // the value prop is passed through parent's <date-filed> v-model
  // ▶ http://vuejs.org/v2/guide/components.html#Form-Input-Components-using-Custom-Events
  props: {
    value: {},
    name: {
      type: String,
    },
    title: {
      type: String,
      default: '',
    },
    error: {
      type: String,
      default: '',
    },
    open: {},
    required: {},
    size: {
      type: String,
      default: '',
    },
    format: {
      type: String,
      default: '',
    },
    valueFormat: {
      type: String,
      default: '',
    },
    helpText: {
      type: String,
      default: '',
    },
  },

  computed: {
    editing() {
      if (typeof this.open !== 'undefined') {
        return this.open;
      }

      if (typeof this.$parent.editing !== 'undefined') {
        return this.$parent.editing;
      }

      return true;
    },
  },

  methods: {
    updateValue(value) {
      // sanitize, validate etc
      // console.log(value);
      this.$emit('valid');

      // onChange must emit the input event to update the parent's <date-field> :value attribute>
      this.$emit('input', value);
    },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
  .el-input {
    width: 100%;
  }

  .el-input.is-disabled .el-input__inner::placeholder {
    color: #FFF;
  }

  .el-input.is-disabled .el-input__inner {
    background-color: #FFF;
    border-color: #FFF;
    border-bottom: 1px solid #dedede;
    color: #333;
    cursor: default;
  }

  .el-input.is-disabled .el-input__icon {
    display: none;
  }
</style>
