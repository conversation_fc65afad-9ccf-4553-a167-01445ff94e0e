<template>
  <div>
    <field
      :name="name"
      :error="error"
      :title="title"
      :required="required"
    >
      <input
        :id="name"
        type="file"
        :name="name"
        class="file-upload-input"
        :disabled="!editing"
        multiple
        @change="addFiles"
      >
      <div
        class="attached-files-buttons"
        style="display: flex; align-items: center"
      >
        <label
          :for="name"
          :class="['file-upload-button', 'btn', error ? 'btn-danger' : 'btn-primary']"
        >
          <i
            class="fa fa-plus"
            aria-hidden="true"
          /> Προσθήκη αρχείων...
        </label>
        <span
          v-if="files.length > 0"
          class="attached-files-buttons-clear"
        >
          <i
            class="fa fa-2x fa-trash-o"
            aria-hidden="true"
            @click="clearFiles"
          />
        </span>
      </div>
    </field>

    <!-- Selected files listing -->
    <div
      v-if="files.length > 0"
      class="attached-files-list"
    >
      <h5 style="margin-bottom: 1rem; font-style: italic">
        Έχουν επιλεχθεί συνολικά <strong>{{ files.length }}</strong>
        αρχεία (<strong>{{ totalSize }} MB</strong>)
      </h5>
      <ul class="list-unstyled">
        <li
          v-for="(file, index) in files"
          :key="index"
        >
          <i
            :class="getFileIconClass(file)"
            aria-hidden="true"
          /> {{ file.name }}
          <span
            class="attached-files-buttons-clear-file"
            @click="removeFile(index)"
          >
            <i
              class="fa fa-times"
              aria-hidden="true"
            />
          </span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Field from './Field.vue';

export default {
  name: 'UploadField',

  components: {
    Field,
  },

  props: {
    name: { type: String },
    title: { type: String },
    error: { type: String },
    required: { type: Boolean, default: false },
  },

  data() {
    return {
      files: [],
    };
  },

  computed: {
    editing() {
      if (typeof this.open !== 'undefined') {
        return this.open;
      }

      if (typeof this.$parent.editing !== 'undefined') {
        return this.$parent.editing;
      }

      return true;
    },
    totalSize() {
      return (this.files.reduce((acc, file) => acc + file.size, 0) / 1048576).toFixed(2);
    },
  },

  methods: {
    addFiles(e) {
      const { files } = e.target;

      if (!files.length) {
        return;
      }

      Array.from(files).forEach((file) => this.files.push(file));
      this.$emit('files-change', this.files);
    },
    removeFile(index) {
      this.files.splice(index, 1);
      this.$emit('files-change', this.files);
    },
    clearFiles() {
      this.files = [];
      this.$emit('files-clear');
    },
    getFileIconClass(file) {
      const fileExt = file.name.split('.').pop();
      switch (fileExt) {
        case 'doc':
        case 'docx':
        case 'odt':
          return 'fa fa-file-word-o text-blue';
        case 'zip':
        case 'rar':
          return 'fa fa-file-archive-o text-yellow';
        case 'pdf':
          return 'fa fa-file-pdf-o text-red';
        case 'xls':
        case 'xlsx':
        case 'ods':
        case 'csv':
          return 'fa fa-file-excel-o text-green';
        case 'png':
        case 'jpeg':
        case 'jpg':
        case 'gif':
          return 'fa fa-picture-o text-purple';
        default:
          return 'fa fa-file-o';
      }
    },
  },
};
</script>

<style scoped>
  .file-upload-input {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
  }

  .attached-files-buttons {
    display: flex;
    align-items: center;
  }

  .attached-files-buttons .attached-files-buttons-clear {
    margin-left: 1rem;
    cursor: pointer;
    color: #9A9EA6;
  }

  .attached-files-buttons .attached-files-buttons-clear:hover {
    color: #64A4D9;
  }

  .attached-files-list .attached-files-buttons-clear-file {
    color: #9A9EA6;
    cursor: pointer;
    margin-left: 1rem;
  }

  .attached-files-list .attached-files-buttons-clear-file:hover {
    color: #64A4D9;
  }

  .attached-files-buttons label.file-upload-button {
    display: inline-block;
    width: max-content;
    height: 40px;
    line-height: 26px;
    color: white;
  }
</style>
