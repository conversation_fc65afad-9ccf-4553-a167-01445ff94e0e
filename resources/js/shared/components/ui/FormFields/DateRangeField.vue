<template>
    <field :name="name" :error="error" :title="title" :required="required">
        <el-date-picker
            :id="name"
            type="daterange"
            range-separator="Έως"
            start-placeholder="Start date"
            end-placeholder="End date"
            format="dd-MM-yyyy"
            value-format="yyyy-MM-dd HH:mm:ss"
            :name="name"
            :value="updatedValue"
            @input="updateValue"
            @keydown.enter.prevent
            :disabled="!this.editing"
        >
        </el-date-picker>
    </field>
</template>

<script>
    import Field from './Field.vue';

    export default {
        name: 'DateField',

        components: {
            Field,
        },

        // the value prop is passed through parent's <date-filed> v-model
        // ▶ http://vuejs.org/v2/guide/components.html#Form-Input-Components-using-Custom-Events
        props: ['value', 'name', 'title', 'error', "open", "required" ],

        computed: {
            updatedValue() {
                return this.value
            },
            editing: function() {
                if (typeof this.open !== "undefined") {
                    return this.open;
                }

                if (typeof this.$parent.editing !== "undefined") {
                    return this.$parent.editing;
                }

                return true;
            }
        },

        methods: {
            updateValue(value) {
                // sanitize, validate etc
                // console.log(value);
                this.$emit('valid');

                // Set empty value if undefined
                if (!value)
                    value = '';

                // onChange must emit the input event to update the parent's <date-field> :value attribute>
                this.$emit('input', value);
            }
        }
    }
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
    .el-input {
        width: 100%;
    }

    .el-input__inner::placeholder {
        color: #444;
    }
    .el-input.is-disabled .el-input__inner::placeholder {
        color: #FFF;
    }
    .el-input.is-disabled .el-input__inner {
        background-color: #FFF;
        border-color: #FFF;
        border-bottom: 1px solid #dedede;
        color: #333;
        cursor: default;
    }

    .el-input.is-disabled .el-input__icon {
        display: none;
    }

    .el-input .el-range-separator {
        width: 15%;
    }
</style>