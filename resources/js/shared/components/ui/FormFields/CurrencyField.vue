<script setup lang="ts">
import type { PropType } from 'vue';
import { computed, ref } from 'vue';

import Field from './Field.vue';

// Props definition - only value prop needed for v-model in Vue 2.7
const props = defineProps({
  value: {
    type: [Number, String] as PropType<number | string | null>,
    default: null,
  },
  name: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
  error: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  helpText: {
    type: String,
    default: '',
  },
});

// Emit definition - only input event needed for v-model in Vue 2.7
const emit = defineEmits<{
  (e: 'input', value: number | string | null): void;
}>();

// Parse string to get only integers
const parseIntegerString = (str: string): number | null => {
  // If the string is empty, return null
  if (!str.trim()) {
    return null;
  }

  // Remove all non-digit characters
  const digitsOnly = str.replace(/\D/g, '');

  // Convert to number (or null if empty)
  return digitsOnly ? parseInt(digitsOnly, 10) : null;
};

/**
 * Trims whitespace from both ends of a string.
 *
 * @param str - The input string to trim
 * @returns The trimmed string, or null if empty/null/undefined
 */
const trimString = (str: string): string | null => {
  // Return null if input is null or undefined
  if (!str) return null;

  // Trim whitespace from both ends
  const trimmed = str.trim();

  // Return null if empty after trimming
  return trimmed === '' ? null : trimmed;
};

/**
 * Converts all commas in a string to dots.
 *
 * @param str - The input string
 * @returns The string with commas converted to dots
 */
const convertCommasToDots = (str: string): string => str.replace(/,/g, '.');

/**
 * Removes a leading dot from a string if present.
 *
 * @param str - The input string
 * @returns The string without leading dot, or null if empty after removal
 */
const removeLeadingDot = (str: string): string | null => {
  if (!str) return null;

  // If it starts with a dot, remove it
  if (str.startsWith('.')) {
    const result = str.substring(1);
    // Return null if empty after removing the dot
    return result === '' ? null : result;
  }

  return str;
};

/**
 * Checks if a string matches the valid decimal format.
 * Valid format: starts with at least one digit, optionally followed by a dot and up to 2 digits.
 *
 * @param str - The string to validate
 * @returns True if the string matches the valid format
 */
const isValidDecimalFormat = (str: string): boolean => /^\d+\.?\d{0,2}$/.test(str);

/**
 * Removes all characters except digits and dots.
 *
 * @param str - The input string
 * @returns The cleaned string with only digits and dots
 */
const cleanInvalidCharacters = (str: string): string => str.replace(/[^0-9.]/g, '');

/**
 * Adds thousand separators (commas) to the integer part of a number.
 *
 * @param integerPart - The integer part to format
 * @returns The formatted integer part with thousand separators
 */
const addThousandSeparators = (integerPart: string): string => integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

/**
 * Keeps only the first decimal point in a string, removing any additional ones.
 *
 * @param input - The string to process
 * @returns The string with only one decimal point (the first one)
 */
const keepFirstDecimalPoint = (input: string): string => {
  const firstDotIndex = input.indexOf('.');

  // If there's no decimal point, return the input as is
  if (firstDotIndex === -1) {
    return input;
  }

  // Get everything before the first dot
  const beforeDot = input.substring(0, firstDotIndex + 1);

  // Get everything after the first dot, but remove any additional dots
  const afterDot = input.substring(firstDotIndex + 1).replace(/\./g, '');

  // Combine them back together
  return beforeDot + afterDot;
};

/**
 * Limits the number of digits after the decimal point to a maximum of two.
 *
 * @param input - The string to process
 * @returns The string with at most 2 digits after the decimal point
 */
const limitToTwoDecimalPlaces = (input: string): string => {
  const parts = input.split('.');

  // If there's no decimal point or only one part, return as is
  if (parts.length <= 1) {
    return input;
  }

  // Get the integer part
  const integerPart = parts[0];

  // Get the decimal part and limit to 2 digits
  let decimalPart = parts[1];
  if (decimalPart.length > 2) {
    decimalPart = decimalPart.substring(0, 2);
  }

  // Combine them back together
  return `${integerPart}.${decimalPart}`;
};

/**
 * Parses a string into a valid float string representation.
 * Handles various input formats and normalizes them according to rules:
 * - Must start with a digit (not a dot)
 * - Only one decimal point allowed
 * - Maximum of 2 digits after decimal point
 * - Commas are converted to dots
 *
 * @param str - The input string to parse
 * @returns A normalized string representation, a number, or null if invalid/empty
 */
const parseFloatString = (str: string): string | number | null => {
  // Step 1: Trim the input
  const trimmed = trimString(str);
  if (trimmed === null) return null;

  // Step 2: Convert commas to dots
  const withDotsOnly = convertCommasToDots(trimmed);

  // Step 3: Remove leading dot if present
  const withoutLeadingDot = removeLeadingDot(withDotsOnly);
  if (withoutLeadingDot === null) return null;

  // Step 4: Fast path - check if already in valid format
  if (isValidDecimalFormat(withoutLeadingDot)) {
    return withoutLeadingDot;
  }

  // Step 5: Clean invalid characters
  const cleaned = cleanInvalidCharacters(withoutLeadingDot);

  // Step 6: Remove leading dot again (might appear after cleaning)
  const cleanedWithoutLeadingDot = removeLeadingDot(cleaned);
  if (cleanedWithoutLeadingDot === null) return null;

  // Step 7: Keep only the first decimal point
  const withOneDecimalPoint = keepFirstDecimalPoint(cleanedWithoutLeadingDot);

  // Step 8: Limit to two decimal places
  return limitToTwoDecimalPlaces(withOneDecimalPoint);
};

// Track whether the input is focused
const isFocused = ref(false); // Start as false to show formatted value initially

// Handle focus event
const handleFocus = (): void => {
  isFocused.value = true;
};

// Handle blur event
const handleBlur = (): void => {
  isFocused.value = false;
};

/**
 * Ensures a number string has exactly two decimal places.
 * If there's no decimal part, it doesn't add one.
 * If there's one decimal digit, it adds a trailing zero.
 *
 * @param value - The number string to format
 * @returns The formatted string with exactly two decimal places if it has a decimal part
 */
const ensureTwoDecimalPlaces = (value: string): string => {
  const parts = value.split('.');

  // If there's no decimal part, return as is
  if (parts.length === 1) {
    return value;
  }

  // Get the integer and decimal parts
  const integerPart = parts[0];
  const decimalPart = parts[1];

  // Ensure exactly two decimal places
  const formattedDecimalPart = decimalPart.substring(0, 2).padEnd(2, '0');

  // Combine the parts
  return `${integerPart}.${formattedDecimalPart}`;
};

/**
 * Formats a number for display with thousand separators and exactly two decimal places.
 *
 * @param value - The number or string to format
 * @returns The formatted string for display
 */
const formatNumberForDisplay = (value: number | string | null): string => {
  // Handle null/undefined/empty values
  if (value === null || value === undefined || value === '') {
    return '';
  }

  // Convert to string if it's a number
  const strValue = typeof value === 'number' ? value.toString() : value;

  // Split into integer and decimal parts
  const parts = strValue.split('.');
  const integerPart = parts[0];

  // Add thousand separators to the integer part
  const formattedIntegerPart = addThousandSeparators(integerPart);

  // If there's no decimal part, return just the formatted integer part
  if (parts.length === 1) {
    return formattedIntegerPart;
  }

  // Ensure exactly two decimal places
  const formattedWithDecimals = ensureTwoDecimalPlaces(strValue);

  // Get the decimal part from the formatted string
  const formattedParts = formattedWithDecimals.split('.');
  const formattedDecimalPart = formattedParts[1];

  // Combine the formatted integer part with the formatted decimal part
  return `${formattedIntegerPart}.${formattedDecimalPart}`;
};

// Format the display value
const displayValue = computed(() => {
  // If value is null or undefined, show empty string
  if (props.value === null || props.value === undefined || props.value === '') {
    return '';
  }

  // If focused, show the unformatted value
  if (isFocused.value) {
    return props.value !== null ? String(props.value) : '';
  }

  // If blurred, show the formatted value with thousand separators and two decimal places
  return formatNumberForDisplay(props.value);
});

/**
 * Calculates the adjusted cursor position after cleaning invalid characters
 * Handles commas as potential decimal points that will be converted to dots
 *
 * @param originalValue - The original input value
 * @param cleanValue - The cleaned input value
 * @param originalCursorPos - The original cursor position
 * @returns The adjusted cursor position
 */
const calculateAdjustedCursorPosition = (
  originalValue: string,
  cleanValue: string,
  originalCursorPos: number | null,
): number => {
  // If cursor position is null or at the beginning, return 0
  if (originalCursorPos === null || originalCursorPos === 0) {
    return 0;
  }

  // If original value is empty, return 0
  if (!originalValue) {
    return 0;
  }

  // Count valid characters in the original value up to the cursor position
  let validCharCount = 0;
  let hasDecimalPoint = false;

  // For each character in the original value up to the cursor position
  for (let i = 0; i < originalCursorPos; i += 1) {
    const char = originalValue[i];

    // If it's a digit, count it as valid
    if (/[0-9]/.test(char)) {
      validCharCount += 1;
    } else if (/[.,]/.test(char)) { // If it's a dot or comma (potential decimal point)
      // If we haven't seen a decimal point yet, count it
      if (!hasDecimalPoint) {
        validCharCount += 1;
        hasDecimalPoint = true;
      }
      // Otherwise, it's an additional decimal point, so skip it
    }
    // Any other character is invalid and skipped
  }

  // Ensure we don't go beyond the end of the clean value
  return Math.min(validCharCount, cleanValue.length);
};

// Handle input changes
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement;

  // Save original value and cursor position before changes
  const originalValue = target.value;
  const cursorPos = target.selectionStart;

  // Parse the input to get a clean value
  const numValue = parseFloatString(originalValue);

  // Get the clean string representation
  const cleanValue = numValue === null ? '' : numValue.toString();

  // Update the input field directly to remove non-numeric characters
  if (originalValue !== cleanValue) {
    // Only update if the value has changed
    target.value = cleanValue;

    // Calculate adjusted cursor position and restore it
    if (cleanValue !== '') {
      const newPos = calculateAdjustedCursorPosition(originalValue, cleanValue, cursorPos);
      target.setSelectionRange(newPos, newPos);
    }
  }

  // Emit the numeric value (null for empty input)
  emit('input', numValue);
};
</script>

<template>
  <Field
    :name="name"
    :error="error"
    :title="title"
    :required="required"
    :description="helpText"
  >
    <div class="input-container">
      <span class="euro-icon">€</span>
      <input
        type="text"
        inputmode="numeric"
        :value="displayValue"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      >
    </div>
  </Field>
</template>

<script lang="ts">
export default {
  name: 'CurrencyField',
};
</script>

<style scoped>
.input-container {
  position: relative;
  width: 100%;
  max-width: 100%;
}

.euro-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: hsl(214deg, 20.3%, 69%);
  z-index: 1;
  font-size: 16px;
}

input {
  border: 1px solid hsl(214deg, 20.3%, 69%);
  border-radius: 0px;
  padding: 8px 12px 8px 35px;
  /* Added left padding for the icon */
  font-size: 16px;
  width: 100%;
  height: 34px;
}

input:focus {
  outline: none;
  border-color: hsl(191deg, 87%, 36.3%);
  /* box-shadow: 0 0 5px rgba(52, 152, 219, 0.5); */
}
</style>
