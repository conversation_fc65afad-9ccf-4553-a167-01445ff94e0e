<template>
  <field
    :name="name"
    :error="error"
    :title="title"
    :required="required"
  >
    <el-switch
      :id="name"
      active-text="NAI"
      inactive-text="OXI"
      :name="name"
      :value="updatedValue"
      :disabled="!editing"
      active-color="#0C90AD"
      @input="updateValue"
    />
  </field>
</template>

<script>
import Field from './Field.vue';

export default {
  name: 'SwitchField',

  components: {
    Field,
  },

  props: ['value', 'name', 'title', 'error', 'open', 'required'],

  computed: {
    updatedValue() {
      return Boolean(this.value);
    },

    editing() {
      if (typeof this.open !== 'undefined') {
        return this.open;
      }

      if (typeof this.$parent.editing !== 'undefined') {
        return this.$parent.editing;
      }

      return true;
    },
  },

  methods: {
    updateValue(value) {
      // sanitize, validate etc
      this.$emit('valid');

      this.$emit('input', value);
    },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss">
  .el-switch {
    margin-top: 7px;
    margin-bottom: 7px;
    margin-left: 1rem;
  }

  .el-switch.is-disabled .el-switch__core,
  .el-switch.is-disabled .el-switch__label {
      cursor: default;
  }

  .el-switch.is-disabled .el-switch__input:checked+.el-switch__core {
    border-color: #0C90AD !important;
    background-color: #0C90AD !important;
  }

  .el-switch.is-disabled .el-switch__core {
      border-color: #bfcbd9 !important;
      background: #bfcbd9 !important;
  }
</style>
