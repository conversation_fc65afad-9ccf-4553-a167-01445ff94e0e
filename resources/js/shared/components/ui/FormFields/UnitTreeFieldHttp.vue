<template>
  <div>
    <UnitTreeField
      v-model="units"
      :allow-selecting-multiple-units="allowSelectingMultipleUnits"
      :allow-selecting-organograms="allowSelectingOrganograms"
      :allow-showing-departments="allowShowingDepartments"
      :show-collapsed="showCollapsed"
      :authorize-for="authorizeFor"
      :units-uri="unitsUri"
      :organograms-uri="organogramsUri"
    />
    <input
      v-for="unit in units"
      :key="unit.id"
      type="hidden"
      :name="`${inputName}[]`"
      :value="unit.id"
    >
  </div>
</template>

<script>
import UnitTreeField from './UnitTreeField.vue';

export default {
  name: 'UnitTreeFieldHttp',
  components: {
    UnitTreeField,
  },
  props: {
    inputValue: {
      type: Array,
      default() {
        return [];
      },
    },
    inputName: {
      type: String,
      default: 'unit_ids',
    },
    allowSelectingMultipleUnits: {
      type: Boolean,
      default: true,
    },
    allowSelectingOrganograms: {
      type: <PERSON>olean,
      default: true,
    },
    allowShowingDepartments: {
      type: Boolean,
      default: true,
    },
    showCollapsed: {
      type: Boolean,
      default: true,
    },
    authorizeFor: {
      type: String,
      default: null,
    },
    unitsUri: {
      type: String,
      default: '/api/units',
    },
    organogramsUri: {
      type: String,
      default: '/api/organograms',
    },
  },
  data() {
    return {
      units: this.inputValue,
    };
  },
};
</script>
