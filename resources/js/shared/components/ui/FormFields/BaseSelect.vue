<script setup>
const props = defineProps({
  label: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  value: {
    type: [String, Number],
    default: null,
  },
  options: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits([
  'update:value',
  'change',
]);

const uuid = crypto.randomUUID();

const inputChanged = (event) => {
  const { value } = event.target;
  console.log({value});
  emit('update:value', value);
  emit('change', +value);
  emit('input', +value);
};
</script>

<template>
  <div>
    <label
      v-if="label"
      :for="uuid"
    >
      {{ label }}
    </label>
    <select
      v-bind="$attrs"
      :id="uuid"
      class="tw:mt-1 tw:block tw:w-full tw:rounded-md tw:border tw:border-gray-300 tw:py-2 tw:px-3 tw:shadow-sm tw:focus:border-blue-500 tw:focus:ring-blue-500 tw:sm:text-sm"
      :value="value"
      @change="inputChanged"
    >
      <option
        v-for="option in options"
        :key="option.id"
        :value="option.id"
        :selected="option.id === value"
      >
        {{ option.name }}
      </option>
    </select>
  </div>
</template>
