<template>
  <div>
    <!-- Upload files -->
    <field
      v-if="files.length == 0"
      :name="name"
      :error="error"
      :title="title"
      :required="required"
    >
      <input
        :id="name"
        class="file-upload-input"
        type="file"
        :name="name"
        multiple
        @change="uploadFiles"
      >
      <label
        :for="name"
        class="file-upload-button btn btn-primary btn-outline"
        :disabled="!!uploadProgress"
      >
        <i
          class="fa fa-upload"
          aria-hidden="true"
        /> Μεταφόρτωση αρχείων...
      </label>
    </field>
    <!-- Progress bar -->
    <div
      v-if="uploadProgress"
      class="row"
    >
      <div class="col-sm-12">
        <div class="progress progress-xs active">
          <div
            class="progress-bar progress-bar-striped progress-bar-blue"
            role="progressbar"
            :style="{width: uploadProgress+'%'}"
          />
        </div>
      </div>
    </div>
    <!-- Uploaded files listing -->
    <div
      v-if="files.length > 0"
      class="row"
    >
      <div class="col-sm-12">
        <ul class="list-unstyled uploaded-files-list">
          <li
            v-for="(file, index) in files"
            :key="index"
            :class="['uploaded-files-list__item', removeProcessing ? 'uploaded-files-list__item--disabled' : '']"
          >
            <i
              :class="getFileIconClass(file)"
              aria-hidden="true"
            />
            <a :href="file.url">{{ file.name }}</a>
            <span
              class="uploaded-files-list__button"
              @click="removeFile(file, index)"
            >
              <i
                v-if="removeProcessing && isSelected(file)"
                class="fa fa-spinner fa-spin"
                aria-hidden="true"
              />
              <i
                v-else
                class="fa fa-trash"
                aria-hidden="true"
              />
            </span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import Field from './Field.vue';

export default {
  name: 'SingleUploadAjaxField',

  components: {
    Field,
  },

  props: {
    initialFiles: {
      type: Array,
      default: () => [],
    },
    name: { type: String },
    title: { type: String },
    error: { type: String },
    required: { type: Boolean, default: false },
    baseUrl: { type: String, required: true },
  },

  data() {
    return {
      files: this.initialFiles, // The FileList contains the already uploaded files
      uploadProgress: 0, // The percentage of file(s) upload
      removeProcessing: false, // The flag to prevent multiple remove requests
      selectedAttachmentId: null, // The id of the attachment to be removed
    };
  },
  methods: {
    uploadFiles(event) {
      console.log('uploadFiles');
      const { files } = event.target;
      const formData = new FormData();

      for (const file of files) {
        formData.append(`${this.name}[]`, file);
      }

      const config = {
        onUploadProgress: (progressEvent) => {
          this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        },
      };

      this.$http.post(`${this.baseUrl}`, formData, config)
        .then((res) => {
          this.uploadProgress = 0;
          res.data.forEach((file) => this.files.push(file));
          this.$emit('files-change', this.files);
        })
        .catch((err) => {
          this.uploadProgress = 0;
          if (err.response) {
            if (err.response.status === 422) {
              window.swal({
                type: 'error',
                title: 'Παρουσιάστηκε σφάλμα κατά την μεταφόρτωση',
                html: true,
                text: this.getValidationErrorsHtml(err.response.data.errors[this.name]),
                showConfirmButton: true,
              });
            } else if (err.response.status === 413) {
              window.swal({
                type: 'error',
                title: 'Παρουσιάστηκε σφάλμα κατά την μεταφόρτωση',
                text: 'Το αρχείο υπερβαίνει το μέγιστο μέγεθος (2MB)',
                showConfirmButton: true,
              });
            } else {
              window.swal({
                type: 'error',
                title: 'Παρουσιάστηκε σφάλμα κατά την μεταφόρτωση',
                text: err.response?.data.message || 'Σφάλμα κατά την μεταφόρτωση του αρχείου',
                showConfirmButton: true,
              });
            }
          } else {
            window.swal({
              type: 'error',
              title: 'Σφάλμα',
              text: 'Παρακαλώ επικοινωνήστε με τους διαχειριστές του συστήματος',
              showConfirmButton: false,
              timer: 3000,
            });
          }
        });
    },
    removeFile(file, index) {
      this.selectedAttachmentId = file.id;
      this.removeProcessing = true;
      this.$http.delete(`${this.baseUrl}/${file.id}`)
        .then((res) => {
          this.files.splice(index, 1);
          this.$emit('files-change', this.files);
        })
        .catch((err) => {
          window.swal({
            type: 'error',
            title: 'Σφάλμα Διαγραφής',
            text: err.response.data.message,
            showConfirmButton: true,
          });
        }).finally(() => {
          this.removeProcessing = false;
          this.selectedAttachmentId = null;
        });
    },

    getFileIconClass(file) {
      const fileExt = file.name.split('.').pop();
      switch (fileExt) {
        case 'doc':
        case 'docx':
        case 'odt':
          return 'fa fa-file-word-o text-blue';
        case 'zip':
        case 'rar':
          return 'fa fa-file-archive-o text-yellow';
        case 'pdf':
          return 'fa fa-file-pdf-o text-red';
        case 'xls':
        case 'xlsx':
        case 'ods':
        case 'csv':
          return 'fa fa-file-excel-o text-green';
        case 'png':
        case 'jpeg':
        case 'jpg':
        case 'gif':
          return 'fa fa-picture-o text-purple';
        default:
          return 'fa fa-file-o';
      }
    },
    getValidationErrorsHtml(errors) {
      console.log(errors);
      let html = '';
      for (const error of errors) {
        html += `<li>${error}</li>`;
      }
      return `<ul>${html}</ul>`;
    },
    isSelected(file) {
      return this.selectedAttachmentId === file.id;
    },
  },
};
</script>

<style scoped>
  .file-upload-input {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
  }

  .file-upload-button {
    display: block;
    width: max-content;
    height: 34px;
  }

  .uploaded-files-list__button {
    color: var(--gray-600);
    cursor: pointer;
    margin-left: 5px;
    vertical-align: middle;
  }

  .uploaded-files-list__button:hover {
    color: var(--red-500);
  }

  .uploaded-files-list__item--disabled > i,
  .uploaded-files-list__item--disabled > a,
  .uploaded-files-list__item--disabled > .uploaded-files-list__button {
    color: var(--gray-500) !important;
    text-decoration: none;
    background-color: transparent;
    cursor: not-allowed;
    pointer-events: none;
  }
</style>
