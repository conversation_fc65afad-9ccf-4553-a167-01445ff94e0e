<template>
  <field
    :name="name"
    :error="error"
    :title="title"
    :required="required"
  >
    <textarea
      :id="name"
      :name="name"
      :value="value"
      :rows="rows"
      class="form-control"
      :disabled="!editing"
      @input="updateValue($event.target.value)"
    />
  </field>
</template>

<script>
import Field from './Field.vue';

export default {
  name: 'TextareaField',

  components: {
    Field,
  },

  props: ['name', 'title', 'value', 'error', 'open', 'required', 'rows'],

  computed: {
    editing() {
      if (typeof this.open !== 'undefined') {
        return this.open;
      }

      if (typeof this.$parent.editing !== 'undefined') {
        return this.$parent.editing;
      }

      return true;
    },
  },

  methods: {
    updateValue(value) {
      // sanitize, validate etc
      if (value) {
        this.$emit('valid');
      }

      this.$emit('input', value);
    },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
    textarea.form-control[disabled], fieldset[disabled] textarea.form-control {
        cursor: default;
        background-color: #FFF;
        border-color: #FFF;
        color: #333;
        border-bottom: 1px solid #dedede;
        opacity: 1;
    }
</style>
