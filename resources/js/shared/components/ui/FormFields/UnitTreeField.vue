<template>
  <ElCollapse v-model="collapsedPanel">
    <ElCollapseItem name="tree-view">
      <template slot="title">
        <div class="tree-label">
          <div class="tree-label__name">
            Υπηρεσίες
          </div>
          <div
            v-show="selectedUnits.length > 0"
            class="tree-label__selected"
          >
            Επιλεγμένες {{ selectedUnits.length }}
          </div>
        </div>
      </template>
      <div class="row">
        <!-- Select Input -->
        <div class="col-sm-7">
          <!-- Search -->
          <div class="row">
            <div class="col-sm-12">
              <ElInput
                v-model="filterText"
                class="organogram-tree-search"
                placeholder="Αναζήτηση Υπηρεσίας"
                prefix-icon="el-icon-search"
                :clearable="true"
              />
            </div>
          </div>
          <!-- Filters -->
          <div class="row">
            <div
              v-if="allowSelectingOrganograms"
              class="col-sm-8"
            >
              <ElSelect
                v-model="selectedOrganogramId"
                class="organogram-tree-organograms"
                @change="reinitializeOrganogram"
              >
                <ElOption
                  v-for="organogram in organograms"
                  :key="organogram.id"
                  :label="organogram.name"
                  :value="organogram.id"
                />
              </ElSelect>
            </div>
            <div class="col-sm-4">
              <ElCheckbox
                v-if="allowShowingDepartments"
                v-model="showDepartments"
                class="organogram-tree-departments"
                size="medium"
                @change="reinitializeOrganogram"
              >
                Προβολή Τμημάτων
              </ElCheckbox>
            </div>
          </div>
          <!-- Tree -->
          <div class="row">
            <div class="col-sm-12">
              <ElTree
                ref="tree"
                class="filter-tree"
                :data="units"
                :props="{label: 'name'}"
                node-key="id"
                show-checkbox
                :check-strictly="true"
                :default-expanded-keys="expandedUnitIds"
                :default-checked-keys="selectedUnitIds"
                :expand-on-click-node="false"
                :filter-node-method="searchUnits"
                @check-change="changeSelectedUnits"
                @check="changeSelectedUnit"
              >
                <span
                  slot-scope="{ node, data }"
                  class="custom-tree-node"
                >
                  <span>{{ node.label }}</span>
                  <span v-if="data.children && node.expanded === true && allowSelectingMultipleUnits">
                    <i
                      data-toggle="tooltip"
                      title="Επιλογή υποκείμενων μονάδων"
                      class="fa fa-check-square-o select-units-btn"
                      aria-hidden="true"
                      @click="() => selectUnitChildren(data)"
                    />
                    <i
                      data-toggle="tooltip"
                      title="Αποεπιλογή υποκείμενων μονάδων"
                      class="fa fa-square-o select-units-btn"
                      aria-hidden="true"
                      @click="() => deselectUnitChildren(data)"
                    />
                  </span>
                </span>
              </ElTree>
            </div>
          </div>
        </div>
        <!-- Select Results -->
        <div
          v-if="allowSelectingMultipleUnits"
          class="col-sm-5"
        >
          <div v-show="selectedUnits.length > 0">
            <p class="text-label">
              Επιλεγμένες Υπηρεσίες
              <i
                class="fa fa-trash-o select-units-btn"
                aria-hidden="true"
                @click="resetSelectedUnits"
              />
            </p>
            <ul>
              <li
                v-for="unit in selectedUnits"
                :key="unit.id"
              >
                {{ unit.name }}
                <i
                  class="fa fa-trash-o select-units-btn"
                  aria-hidden="true"
                  @click="deselectUnit(unit)"
                />
              </li>
            </ul>
          </div>
        </div>
      </div>
    </ElCollapseItem>
  </ElCollapse>
</template>

<script>
export default {
  name: 'UnitTreeField',
  model: {
    prop: 'selectedUnits',
    event: 'units-selected',
  },
  props: {
    selectedUnits: {
      type: Array,
      default() {
        return [];
      },
    },
    allowSelectingMultipleUnits: {
      type: Boolean,
      default: true,
    },
    allowSelectingOrganograms: {
      type: Boolean,
      default: true,
    },
    allowShowingDepartments: {
      type: Boolean,
      default: true,
    },
    showCollapsed: {
      type: Boolean,
      default: true,
    },
    authorizeFor: {
      type: String,
      default: null,
    },
    unitsUri: {
      type: String,
      default: '/api/units',
    },
    organogramsUri: {
      type: String,
      default: '/api/organograms',
    },
  },
  data() {
    return {
      units: [],
      organograms: [],
      expandedUnitIds: [],
      selectedOrganogramId: '',
      showDepartments: false,
      filterText: '',
      collapsedPanel: this.showCollapsed ? [] : 'tree-view',
    };
  },
  computed: {
    selectedUnitIds() {
      return this.selectedUnits.map((unit) => unit.id);
    },
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    selectedUnits(newSelectedUnits, oldSelectedUnits) {
      oldSelectedUnits.forEach((oldUnit) => this.deselectUnit(oldUnit));
      newSelectedUnits.forEach((newUnit) => this.selectUnit(newUnit));
    },
  },
  async created() {
    await this.initializeOrganograms();
    await this.initializeOrganogramUnits();
  },
  methods: {
    changeSelectedUnits() {
      if (this.allowSelectingMultipleUnits) {
        this.$emit('units-selected', this.$refs.tree.getCheckedNodes());
      }
    },
    changeSelectedUnit(data) {
      if (!this.allowSelectingMultipleUnits) {
        this.resetSelectedUnits();
        if (this.selectedUnits.includes(data)) {
          this.deselectUnit(data);
        } else {
          this.selectUnit(data);
        }
        this.$emit('units-selected', this.$refs.tree.getCheckedNodes());
      }
    },
    async reinitializeOrganogram() {
      // We have to first unselect the already selected units
      // and then await for re-initializing the organogram
      this.$emit('units-selected', []);
      await this.initializeOrganogramUnits();
    },
    async initializeOrganograms() {
      this.organograms = await this.fetchOrganograms();
      this.selectedOrganogramId = this.getDefaultSelectedOrganogramId();
    },
    async initializeOrganogramUnits() {
      const units = await this.fetchOrganogramUnits();
      if (Array.isArray(units) && units.length === 0) {
        this.units = [];
      } else {
        this.units = [units];
        this.expandedUnitIds = this.getDefaultExpandedUnits(units);
      }
    },
    async fetchOrganogramUnits() {
      try {
        const res = await this.$http.get(this.unitsUri, {
          params: {
            filters: [
              {
                fieldName: 'selectedOrganogramId',
                type: 'eq',
                value: this.selectedOrganogramId,
              },
              {
                fieldName: 'showDepartments',
                type: 'eq',
                value: this.showDepartments,
              },
              {
                fieldName: 'authorizeFor',
                type: 'eq',
                value: this.authorizeFor,
              },
            ],
          },
        });
        return res.data;
      } catch (err) {
        window.swal({
          type: 'error',
          title: `Σφάλμα ${err.status}`,
          text: err.statusText,
          showConfirmButton: false,
          timer: 3000,
        });
        return {};
      }
    },
    async fetchOrganograms() {
      const res = await this.$http.get(this.organogramsUri);
      return res.data;
    },
    selectUnitChildren(unit) {
      unit.children
        .filter((child) => child.id !== null)
        .forEach((child) => {
          this.selectUnit(child);
        });
    },
    deselectUnitChildren(unit) {
      unit.children
        .filter((child) => child.id !== null)
        .forEach((child) => {
          this.deselectUnit(child);
        });
    },
    resetSelectedUnits() {
      this.selectedUnits
        .filter((unit) => unit.id !== null)
        .forEach((unit) => {
          this.deselectUnit(unit);
        });
    },
    selectUnit(unit) {
      this.$refs.tree.setChecked(unit.id, true);
    },
    deselectUnit(unit) {
      this.$refs.tree.setChecked(unit.id, false);
    },
    searchUnits(value, data) {
      if (!value) return true;
      return this.deburrGreek(data.name).toLowerCase().indexOf(this.deburrGreek(value).toLowerCase()) !== -1;
    },
    deburrGreek(string) {
      return string.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    },
    getActiveOrganogram() {
      return this.organograms.find((organogram) => organogram.ended_at === null);
    },
    // If pre-selected units exist and they belong to the same organogram, then
    // this will be selected as the default. If no pre-selected units exist,
    // the active organogram will be selected as the default.
    // Caveat: If pre-selected units exist but they belong to different organograms,
    // the OrganogramTreeView component cannot properly list the pre-selected units
    getDefaultSelectedOrganogramId() {
      const organogamIds = this.selectedUnits.map((unit) => unit.organogram_id);
      const uniqueOrganogramIds = [...new Set(organogamIds)];
      return uniqueOrganogramIds.length === 1
        ? uniqueOrganogramIds[0]
        : this.getActiveOrganogram().id;
    },
    // If pre-selected units exist, then by default expand their parents.
    // Otherwise by default expand root children.
    getDefaultExpandedUnits(units) {
      if (this.selectedUnitIds.length > 0) {
        return this.selectedUnitIds;
      }
      return units.children
        ? units.children.filter((child) => child.id !== null).map((child) => child.id)
        : [];
    },
  },
};
</script>

<style scoped>
.organogram-tree-search {
  margin-bottom: 1rem;
}

.organogram-tree-organograms {
  margin-bottom: 1rem;
}

.organogram-tree-departments {
  padding-top: 5px;
  margin-bottom: 1rem;
}

.select-units-btn {
  color: var(--gray-700);
  margin-left: 3px;
  cursor: pointer;
}

.select-units-btn:hover {
  color: var(--brand-color);
}

.tree-label {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.tree-label__name {
  font-size: 1rem;
  font-weight: 800;
  color: var(--gray-700);
}

.tree-label__selected {
  font-weight: 400;
  font-size: 0.9rem;
  color: var(--gray-700);
  padding-right: 0.5rem;
}
</style>
