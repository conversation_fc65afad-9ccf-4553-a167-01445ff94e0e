<template>
  <field
    :name="name"
    :error="error"
    :title="title"
    :required="required"
    :description="helpText"
  >
    <el-select
      :id="name"
      :value="updatedValue"
      clearable
      filterable
      :placeholder="placeholder"
      :name="name"
      :disabled="!editing || disabled"
      :size="size || null"
      @input="updateValue"
    >
      <el-option
        v-for="option in options"
        :key="option[Object.keys(option)[0]]"
        :value="option[Object.keys(option)[0]]"
        :label="option[Object.keys(option)[1]]"
        :selected="option[Object.keys(option)[0]] == value"
      />
    </el-select>
  </field>
</template>

<script>
import Field from './Field.vue';

export default {
  name: 'SelectfilterField',

  components: {
    Field,
  },

  // props: ['name', 'title', 'value', 'options', 'error', 'open', 'required', 'disabled'],

  props: {
    name: String,
    title: String,
    value: [Number, String],
    options: Array,
    error: [Object, String],
    open: {
      type: <PERSON>olean,
      default: undefined,
    },
    required: <PERSON><PERSON>an,
    disabled: {
      type: <PERSON>olean,
      default: false,
    },
    size: String,
    placeholder: {
      type: String,
      default: 'Επιλέξτε...',
    },
    helpText: { type: String, default: '' },
  },

  computed: {
    editing() {
      if (typeof this.open !== 'undefined') {
        return this.open;
      }

      if (typeof this.$parent.editing !== 'undefined') {
        return this.$parent.editing;
      }

      return true;
    },
    updatedValue() {
      return this.value;
    },
  },

  methods: {
    updateValue(value) {
      // sanitize, validate etc
      this.$emit('valid');

      this.$emit('input', value);
    },

  },
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>

  .el-select .el-input.is-disabled .el-input__inner:hover {
    border-color: transparent;
    border-bottom: 1px solid #dedede;
  }
</style>
