<script setup lang="ts">
import { computed } from 'vue';

interface YppoActionLinkProps {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  url: string;
  data?: Record<string, any>;
  buttonClass?: string;
}

const props = withDefaults(defineProps<YppoActionLinkProps>(), {
  method: 'GET',
});

const computedMethod = computed(() => (props.method === 'GET' ? 'GET' : 'POST'));
const shouldAddCsrfToken = computed(() => props.method !== 'GET');
const shouldAddMethodField = computed(() => props.method !== 'GET' && props.method !== 'POST');
const { csrfToken } = window.Laravel;
</script>

<script lang="ts">
export default {
  inheritAttrs: false,
};
</script>

<template>
  <form
    :method="computedMethod"
    :action="url"
  >
    <input
      v-if="shouldAddCsrfToken"
      type="hidden"
      name="_token"
      :value="csrfToken"
    >
    <input
      v-if="shouldAddMethodField"
      type="hidden"
      name="_method"
      :value="method"
    >
    <template v-if="data">
      <input
        v-for="(value, key) in data"
        :key="key"
        type="hidden"
        :name="key"
        :value="value"
      >
    </template>
    <button
      v-bind="$attrs"
      :class="buttonClass"
      type="submit"
    >
      <slot />
    </button>
  </form>
</template>
