<template>
  <div :class="['box', type, color]">
    <div
      class="box-header"
      :class="{ 'with-border': headingHasBorder }"
    >
      <h3 class="box-title">
        {{ title }}
      </h3>
      <div class="box-tools pull-right">
        <slot name="header-buttons">
          <!-- place for slotting extra header buttons -->
        </slot>
        <!--Edit resource-->
        <button
          v-if="buttons.edit"
          type="button"
          class="header-button"
          @click="edit"
        >
          <i class="text-blue hover-text-yellow fa fa-edit fa-2x" />
        </button>
        <!--Delete resource-->
        <button
          v-if="buttons.destroy"
          type="button"
          class="header-button"
          @click="destroy"
        >
          <i class="text-blue hover-text-red fa fa-times fa-2x" />
        </button>
      </div>
    </div>
    <div class="box-body">
      <slot>
        <!-- place for slotting the main box content -->
      </slot>
    </div>
    <div
      v-if="isLoading"
      class="overlay"
    >
      <i class="fa fa-refresh fa-spin" />
    </div>
    <div
      v-if="$slots.footer"
      class="box-footer"
    >
      <slot name="footer">
        <!-- place for slotting the footer content -->
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Box',

  props: {
    title: { // the header title
      type: String,
    },
    type: { // the css class of the box; default: box-primary
      type: String,
      default: '',
    },
    color: {
      type: String,
      default: 'box-primary',
    },
    headingHasBorder: {
      type: Boolean,
      default: true,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    buttons: { // The default header buttons {edit: editURI, show: showURI, etc....}
      type: Object,
      default() {
        return {
          edit: '',
          destroy: '',
        };
      },
    },
  },

  methods: {
    /**
             * Edit the resource
             * Makes a HTTP/XHR call to the server
             */
    edit() {
      this.$http.get(this.buttons.edit);
    },

    /**
             * Delete the resource
             * Makes a HTTP/XHR call to the server
             */
    destroy() {
      this.$http.delete(this.buttons.destroy);
    },
  },

};
</script>

<style lang="scss" rel="stylesheet/scss">

    // Heading buttons
    .header-button {
        padding: 5px 10px;
        background-color: inherit;
        border: none;
        transition: all .2s ease-in-out;

        /*i:hover {*/
            /*!*text-shadow: 1px 1px 8px rgba(0, 0, 0, 0.3);*!*/
            /*tranform:red;*/
        /*}*/

        &:focus {
            outline: none; // stackoverflow.com/questions/19053181/how-to-remove-focus-around-buttons-on-click
        }

        &:hover {
            transform: scale(1.2);
        }
    }

</style>
