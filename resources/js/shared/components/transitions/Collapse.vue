<template>
    <transition
      name="collapse"
      mode="out-in"
    >
      <slot></slot>
    </transition>
</template>

<script>

</script>

<style>
    .collapse-enter-active, .collapse-leave-active {
        transition: all 0.7s ease;
    }
    .collapse-enter, .collapse-leave-to {
        overflow: hidden;
        max-height: 0px;
    }
    .collapse-enter-to, .collapse-leave {
        max-height: 1500px;
        overflow: hidden;
    }
</style>
