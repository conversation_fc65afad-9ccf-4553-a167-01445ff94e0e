export default {
  methods: {
    // Return an object of CRUD permissions for a model for the current user
    $getModelPermissions(vm, app, model, id) {
      this.$http.get(`/api/registry/authorize-model/${app}/${model}/${id}`)
        .then((response) => {
          vm.permissions = response.data;
        })
        .catch((error) => {
          console.log('there was an error getting permission value!');
        });
    },

    // Check if a specific permission passes authorization for current user
    $getPermission(vm, permission) {
      // Get current user through api call
      this.$http.get(`/api/registry/authorize-permission/${encodeURIComponent(permission)}`)
        .then((response) => {
          vm.permission = response.data;
        })
        .catch((error) => {
          console.log('there was an error getting permission value!');
        });
    },

    // Check if current user can create a model
    $getCreateModelPermission(vm, app, model) {
      // Get current user through api call
      this.$http.get(`/api/registry/authorize-create-model/${app}/${model}`)
        .then((response) => {
          vm.permissionCreate[model] = response.data;
        })
        .catch((error) => {
          console.log('there was an error getting permission value!');
        });
    },
  },
};
