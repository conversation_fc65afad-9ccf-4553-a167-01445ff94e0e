import get from 'lodash/get';
/*
 ===========================================================================
 Here we export all required rules for transforming the data received
 from the server (via either XHR response or props)
 ===========================================================================
 */
/*
Rule for preparing data received by VehicleForm
 */
export function vehicleFormReceiveRule(serverData) {
  return serverData;
}

/*
Rule to prepare data send by VehicleForm
 */
export function vehicleFormSendRule(serverData) {
  return serverData;
}

/*
Rule for preparing data received by BuildingForm
 */
export function buildingFormReceiveRule(serverData) {
  const initData = serverData;
  const res = {}; // must be the same type as serverData

  Object.keys(initData).forEach((item) => {
    if (item === 'hosted_units') {
      const ids = [];
      initData[item].forEach((hosted_unit) => {
        ids.push(get(hosted_unit, 'id'));
      });
      res[item] = ids;
    }
    /**
         * In all other cases
         */
    else res[item] = initData[item];
  });

  return res;
}

/*
Rule for preparing data received by a common form
 */
export function formReceiveRule(serverData) {
  return serverData;
}

/*
Rule to prepare data send by BuildingForm
 */
export function formSendRule(serverData) {
  return serverData;
}
