<template>
  <form
    action="/registry/building/associate"
    method="POST"
    class="card-panel"
    :class="{'bg-paper': editing}"
    @submit.prevent="onSubmit(onUpdate)"
    @keydown.enter.prevent
  >
    <div
      v-show="loading"
      class="loading-overlay"
      tabindex="-1"
      style="opacity: 1.1; display: block;"
    />
    <div
      v-show="loading"
      id="loader"
    />

    <input
      v-if="form.data.id"
      v-model="form.data.id"
      type="hidden"
      name="id"
    >
    <input
      v-model="form.data.building_id"
      type="hidden"
      name="building_id"
    >

    <div class="row">
      <div class="col-sm-3">
        <select-field
          v-model="form.data.associate_type_id"
          name="associate_type_id"
          :options="related_models.associateTypes"
          title="Σχέση"
          :required="true"
          :error="form.errors.get('associate_type_id')"
          @valid="form.errors.clear('associate_type_id')"
        />
      </div>
      <div class="col-sm-6">
        <text-field
          v-model="form.data.surname"
          name="surname"
          title="Επώνυμο"
          :required="true"
          :error="form.errors.get('surname')"
          @valid="form.errors.clear('surname')"
        />
      </div>
      <div class="col-sm-3">
        <text-field
          v-model="form.data.name"
          name="name"
          title="Όνομα"
          :required="true"
          :error="form.errors.get('name')"
          @valid="form.errors.clear('name')"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-2">
        <text-field
          v-model="form.data.afm"
          name="afm"
          title="ΑΦΜ"
          :required="true"
          :error="form.errors.get('afm')"
          @valid="form.errors.clear('afm')"
        />
      </div>
      <div class="col-sm-4">
        <text-field
          v-model="form.data.doy"
          name="doy"
          title="ΔΟΥ"
          :error="form.errors.get('doy')"
          @valid="form.errors.clear('doy')"
        />
      </div>
      <div class="col-sm-6">
        <text-field
          v-model="form.data.iban"
          name="iban"
          title="IBAN"
          :error="form.errors.get('iban')"
          @valid="form.errors.clear('iban')"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-9">
        <text-field
          v-model="form.data.address"
          name="address"
          title="Διεύθυνση Επικοινωνίας"
          :required="true"
          :error="form.errors.get('address')"
          @valid="form.errors.clear('address')"
        />
      </div>
      <div class="col-sm-3">
        <text-field
          v-model="form.data.phone"
          name="phone"
          title="Τηλέφωνο"
          :required="true"
          :error="form.errors.get('phone')"
          @valid="form.errors.clear('phone')"
        />
      </div>
    </div>

    <div
      v-if="editing"
      class="row"
    >
      <div class="col-sm-3">
        <a
          class="btn btn-default btn-block"
          @click.prevent="editOff"
        >
          <i class="fa fa-arrow-circle-left" /> Ακύρωση
        </a>
      </div>
      <div class="col-sm-9">
        <button
          class="btn btn-success btn-block"
          :disabled="form.errors.has()"
        >
          <i class="fa fa-check-circle" /> Αποθήκευση
        </button>
      </div>
    </div>
    <div
      v-else
      class="row"
    >
      <div class="col-sm-3 col-sm-offset-6">
        <a
          v-if="this.permissions.update"
          class="btn btn-danger btn-block"
          href="#"
          @click.prevent="removeItem(onDelete)"
        >
          <i class="fa fa-times" /> Διαγραφή
        </a>
      </div>

      <div class="col-sm-3">
        <a
          v-if="this.permissions.update"
          class="btn btn-warning btn-block"
          @click.prevent="editOn"
        >
          <i class="fa fa-edit" /> Επεξεργασία
        </a>
      </div>
    </div>
  </form>
</template>

<script>

import SelectField from '../../shared/components/ui/FormFields/SelectField.vue';
import TextareaField from '../../shared/components/ui/FormFields/TextareaField.vue';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import Form from '../../shared/Form';
import __ from '../../shared/Helpers';
import formControls from '../formControls';
import { formReceiveRule, formSendRule } from '../ServerDataRules';

export default {
  name: 'BuildingAssociateForm',

  components: {
    TextField,
    TextareaField,
    SelectField,
  },

  mixins: [formControls],

  props: {
    associate: {
      type: Object,
      default: () => ({}),
    },
    related_models: {
      type: Object,
      default: () => ({}),
    },
    permissions: {
      type: Object,
      default: {
        create: false,
        read: false,
        update: false,
        delete: false,
      },
    },
  },

  data() {
    return {
      loading: false,
      editing: true,
      endpoint: '/registry/associate',
      model: this.associate,
      form: new Form({
        id: '',
        associate_type_id: '',
        surname: '',
        name: '',
        afm: '',
        doy: '',
        iban: '',
        address: '',
        phone: '',
        building_id: '',
      },
      {
        // these are the rules for incoming and outgoing data
        incoming: formReceiveRule,
        outgoing: formSendRule,
      }),
    };
  },

  created() {
    if (this.model) {
      console.log('updating form with associate model');
      this.form.populate(this.model);
    }

    this.editing = !this.assetExistsOnServer;
  },

  methods: {
    onDelete(data) {
      this.$emit('deleted', this.model);
    },

    onUpdate(data) {
      this.model = data.associate;
      this.form.populate(this.model);
      this.$emit('updated', this.model);
      this.editing = false;
      this.loading = false;
    },
  },
};
</script>
