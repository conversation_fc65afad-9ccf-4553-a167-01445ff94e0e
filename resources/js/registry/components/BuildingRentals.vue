<template>
  <div>
    <div
      class="row"
      style="padding-top:20px"
    >
      <div class="col-sm-12">
        <h5 v-show="!hasItems">
          Δεν έχουν καταχωρηθεί στοιχεία εκμίσθωσης μέχρι στιγμής
        </h5>
      </div>
    </div>

    <div
      v-show="hasItems"
      class="row"
      style="padding-top:10px"
    >
      <div class="col-sm-12">
        <building-rental-form
          v-for="(item, index) in this.items"
          :key="item.id"
          :rental="item"
          :index="index"
          :related_models="related_models"
          :permissions="permissions"
          @updated="update(index, $event)"
          @deleted="remove(index)"
        >
          >
        </building-rental-form>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-12">
        <!--Add Building Rental-->
        <button
          v-if="this.permissions.update"
          class="btn bg-light-blue"
          type="button"
          @click="add(new_rental)"
        >
          <i
            class="fa fa-plus"
            aria-hidden="true"
          />
          Προσθήκη
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import Collection from '../../shared/Collection.js';
import __ from '../../shared/Helpers';
import BuildingRentalForm from './BuildingRentalForm.vue';

export default Collection.extend({
  name: 'BuildingRentals',

  components: {
    BuildingRentalForm,
  },

  props: {
    building: {
      type: Object,
      default: () => ({}),
    },
    related_models: {
      type: Object,
      default: () => ({}),
    },
    permissions: {
      type: Object,
      default: {
        create: false,
        read: false,
        update: false,
        delete: false,
      },
    },
  },

  data() {
    return {
      new_rental: {
        id: '',
        contract: '',
        rental_process_id: '',
        monthly_rent: '',
        valid_from: '',
        valid_to: '',
        building_id: this.building.id,
      },
    };
  },
});

</script>
