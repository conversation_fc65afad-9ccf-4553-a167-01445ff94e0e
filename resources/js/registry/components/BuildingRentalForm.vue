<template>
  <form
    action="/registry/building/rental"
    method="POST"
    class="card-panel"
    :class="{'bg-paper': editing}"
    @submit.prevent="onSubmit(onUpdate)"
    @keydown.enter.prevent
  >
    <div
      v-show="loading"
      class="loading-overlay"
      tabindex="-1"
      style="opacity: 1.1; display: block;"
    />
    <div
      v-show="loading"
      id="loader"
    />

    <input
      v-if="form.data.id"
      v-model="form.data.id"
      type="hidden"
      name="id"
    >
    <input
      v-model="form.data.building_id"
      type="hidden"
      name="building_id"
    >

    <div class="row">
      <div class="col-sm-6">
        <text-field
          v-model="form.data.contract"
          name="contract"
          title="Σύμβαση"
          :required="true"
          :error="form.errors.get('contract')"
          @valid="form.errors.clear('contract')"
        />
      </div>
      <div class="col-sm-6">
        <select-field
          v-model="form.data.rental_process_id"
          name="rental_process_id"
          title="Διαδικασία Μίσθωσης"
          :options="related_models.rentalProcesses"
          :required="true"
          :error="form.errors.get('rental_process_id')"
          @valid="form.errors.clear('rental_process_id')"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-4">
        <date-field
          v-model="form.data.valid_from"
          title="Έναρξη Εκμίσθωσης"
          :required="true"
          name="valid_from"
          :error="form.errors.get('valid_from')"
          @valid="form.errors.clear('valid_from')"
        />
      </div>
      <div class="col-sm-4">
        <date-field
          v-model="form.data.valid_to"
          title="Λήξη Εκμίσθωσης"
          :required="true"
          name="valid_to"
          :error="form.errors.get('valid_to')"
          @valid="form.errors.clear('valid_to')"
        />
      </div>
      <div class="col-sm-4">
        <money-field
          v-model="form.data.monthly_rent"
          title="Μηνιαίο Μίσθωμα"
          :required="true"
          name="monthly_rent"
          :error="form.errors.get('monthly_rent')"
          @valid="form.errors.clear('monthly_rent')"
        />
      </div>
    </div>

    <div
      v-if="editing"
      class="row"
    >
      <div class="col-sm-3">
        <a
          class="btn btn-default btn-block"
          @click.prevent="editOff"
        >
          <i class="fa fa-arrow-circle-left" /> Ακύρωση
        </a>
      </div>
      <div class="col-sm-9">
        <button
          class="btn btn-success btn-block"
          :disabled="form.errors.has()"
        >
          <i class="fa fa-check-circle" /> Αποθήκευση
        </button>
      </div>
    </div>
    <div
      v-else
      class="row"
    >
      <div class="col-sm-3 col-sm-offset-6">
        <a
          v-if="this.permissions.update"
          class="btn btn-danger btn-block"
          href="#"
          @click.prevent="removeItem(onDelete)"
        >
          <i class="fa fa-times" /> Διαγραφή
        </a>
      </div>

      <div class="col-sm-3">
        <a
          v-if="this.permissions.update"
          class="btn btn-warning btn-block"
          @click.prevent="editOn"
        >
          <i class="fa fa-edit" /> Επεξεργασία
        </a>
      </div>
    </div>
  </form>
</template>

<script>

import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import MoneyField from '../../shared/components/ui/FormFields/MoneyField.vue';
import SelectField from '../../shared/components/ui/FormFields/SelectField.vue';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import Form from '../../shared/Form';
import __ from '../../shared/Helpers';
import formControls from '../formControls';
import { formReceiveRule, formSendRule } from '../ServerDataRules';

export default {
  name: 'BuildingRentalForm',

  components: {
    TextField,
    SelectField,
    MoneyField,
    DateField,
  },

  mixins: [formControls],

  props: {
    rental: {
      type: Object,
      default: () => ({}),
    },
    related_models: {
      type: Object,
      default: () => ({}),
    },
    permissions: {
      type: Object,
      default: {
        create: false,
        read: false,
        update: false,
        delete: false,
      },
    },
  },

  data() {
    return {
      loading: false,
      editing: true,
      endpoint: '/registry/rental',
      model: this.rental,
      form: new Form({
        id: '',
        contract: '',
        rental_process_id: '',
        monthly_rent: '',
        valid_from: '',
        valid_to: '',
        building_id: '',
      },
      {
        // these are the rules for incoming and outgoing data
        incoming: formReceiveRule,
        outgoing: formSendRule,
      }),
    };
  },

  created() {
    if (this.model) {
      console.log('updating form with rental model');
      this.form.populate(this.model);
    }

    this.editing = !this.assetExistsOnServer;
  },

  methods: {
    onDelete(data) {
      this.$emit('deleted', this.model);
    },

    onUpdate(data) {
      this.model = data.rental;
      this.form.populate(this.model);
      this.$emit('updated', this.model);
      this.editing = false;
      this.loading = false;
    },
  },
};
</script>
