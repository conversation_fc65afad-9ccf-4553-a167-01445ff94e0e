<template>
  <form id="filter-form" action="search_url" method="POST" @submit.prevent="onSubmit">

    <input name="_token" type="hidden" :value="csrf">

    <div class="row">
      <div class="col-sm-4">
        <text-field name="number" title="Αριθμός Τηλεφωνικής Σύνδεσης" v-model="form.data.number"
                    :error="form.errors.get('number')" @valid="form.errors.clear('number')"></text-field>
      </div>
      <div class="col-sm-4">
        <text-field name="holder" title="Κάτοχος" v-model="form.data.holder" :error="form.errors.get('holder')"
                    @valid="form.errors.clear('holder')"></text-field>
      </div>
      <div class="col-sm-4">
        <select-field name="mobile_provider_id" title="Πάροχος" v-model="form.data.mobile_provider_id"
                      :options="mobile_providers" @valid="form.errors.clear('mobile_provider_id')"></select-field>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-4">
        <select-field name="paid_centrally" title="Σύνδεση ΚΕΛΕΣΣ" v-model="form.data.paid_centrally"
                      :options="booleanOptions" @valid="form.errors.clear('paid_centrally')"></select-field>
      </div>
      <div class="col-sm-4">
        <select-field name="keles_linked" title="Πληρωμή Κεντρικά (ΓΔΟΥ)" v-model="form.data.keles_linked"
                      :options="booleanOptions" @valid="form.errors.clear('keles_linked')"></select-field>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-12">
        <UnitTreeField
            v-model="form.data.units"
            :allow-showing-departments="false"
            :show-collapsed="! unit_filter"
            authorize-for="registry"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-4">
        <switch-field name="show_all" title="Εμφάνιση Όλων (και ανενεργών)" v-model="form.data.show_all"
                      :error="form.errors.get('show_all')" @valid="form.errors.clear('show_all')"></switch-field>
      </div>
      <div class="col-sm-4">
        <date-field v-model="form.data.valid_on"
                    title="Ημερομηνία Προβολής"
                    name="valid_on"
                    :error="form.errors.get('valid_on')"
                    @valid="form.errors.clear('valid_on')"
                    v-if="!form.data.show_all"
        >
        </date-field>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-3">
        <a class="btn btn-default btn-block" href="/registry">
          <i class="fa fa-arrow-circle-left"></i> Επιστροφή
        </a>
      </div>
      <div class="col-sm-6 col-sm-offset-3">
        <button class="btn btn-success btn-block" :disabled="form.errors.has()">
          <i class="fa fa-check-circle"></i> Αναζήτηση
        </button>
      </div>
    </div>
  </form>
</template>

<script>
import Form from '../../shared/Form';
import {eventHub, store} from './ResourceEventHub.js';
import {formReceiveRule, formSendRule} from '../ServerDataRules';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import SelectField from '../../shared/components/ui/FormFields/SelectField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';
import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import UnitTreeField from '../../shared/components/ui/FormFields/UnitTreeField.vue';
import moment from 'moment';

export default {
  name: 'MobileFilters',

  components: {
    TextField,
    SelectField,
    SwitchField,
    DateField,
    UnitTreeField,
  },

  props: {
    filters: {default: ''},
    mobile_providers: {type: Array},
    unit_filter: { default: false },
    selected_units: { type: Array },
  },

  data() {
    return {
      shared: store,
      search_url: '/registry/mobile/search',
      csrf: window.Laravel.csrfToken,
      resourceFilters: '',
      form: new Form(
          {
            id: '',
            holder: '',
            number: '',
            mobile_provider_id: '',
            paid_centrally: '',
            keles_linked: '',
            units: [],
            show_all: true,
            valid_on: '',
          },
          {
            // these are the rules for incoming and outgoing data
            incoming: formReceiveRule,
            outgoing: formSendRule
          }
      ),
      booleanOptions: [
        {id: '1', name: 'NAI'},
        {id: '0', name: 'OXI'},
      ]
    }
  },

  mounted() {
    console.log('Filter Form mounted.');
  },

  created() {
    var currentTime = moment().format('YYYY-MM-DD');
    this.form.data.valid_on = currentTime;
    if (this.selected_units.length > 0) {
      this.form.data.units = this.selected_units;
    }
    if (this.filters) {
      this.form.populate(this.filters);
    }
    eventHub.$on('resource-updated', this.searchResource);
  },

  beforeDestroy: function () {
    eventHub.$off('resource-updated', this.searchResource);
  },

  methods: {
    // Filter form submitted - do the search
    onSubmit() {
      this.searchResource();
    },

    // Search the resource based on form filters
    searchResource() {
      eventHub.$emit('search-start');

      let submitUrl = this.search_url;
      let submitMethod = 'post';

      this.form[submitMethod](submitUrl)
          .then(response => {
            this.shared.resourceSet = response.data.resource;

            this.resourceFilters = response.data.filters;
            this.form.populate(this.resourceFilters);

            if (this.shared.resourceSet.length == 0) {
              swal({
                type: 'info',
                title: 'Αποτελέσματα Αναζήτησης: 0',
                text: 'Δε βρέθηκαν αποτελέσματα για τα φίλτρα που επιλέξατε.',
                showConfirmButton: false,
                timer: 2000
              });
            }

            eventHub.$emit('search-complete');

          })
          .catch(error => {
            swal({
              type: 'error',
              title: 'Σφάλμα αναζήτησης!',
              text: 'Παρουσιάστηκε σφάλμα κατά την αναζήτηση. Ελέγξτε τα φίλτρα.',
              showConfirmButton: false,
              timer: 2500,
            });

            eventHub.$emit('search-fail');
          });
    }
  }
}
</script>
