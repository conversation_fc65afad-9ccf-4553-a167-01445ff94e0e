<template>
  <resource-index
      title="Αναζήτηση Ασφαλίσεων Οχημάτων"
      modal_title="Προβολή Οχήματος"
      :table_headers="headers"
  >

    <div class="row" style="padding-top:20px">
      <div class="col-sm-12">
        <vehicle-insurance-filters
            :vehicle_types="this.vehicle_types"
            :unit_filter="unit_filter"
            :selected_units="selected_units"
        ></vehicle-insurance-filters>
      </div>
    </div>

    <vehicle-tabs slot="modal_data"
                  :vehicle="this.shared.selectedResource.vehicle"
                  :vehicle_types="this.vehicle_types"
                  :employees="this.employees"
                  :edit_form="false"
                  :inside_modal="true"
                  @update-vehicle="updateResource"
                  @delete-vehicle="deleteResource"
    ></vehicle-tabs>

  </resource-index>
</template>

<script>
import {eventHub, store} from './ResourceEventHub.js';
import ResourceIndex from './ResourceIndex.vue';
import Authorization from "../mixins/Authorization";
import VehicleInsuranceFilters from './VehicleInsuranceFilters.vue';
import VehicleTabs from './VehicleTabs.vue';


export default {
  name: 'VehicleSearch',

  components: {
    ResourceIndex,
    VehicleTabs,
    VehicleInsuranceFilters,
  },

  mixins: [Authorization],

  props: ['filters', 'vehicle_types', 'employees', 'unit_filter', 'selected_units'],

  data() {
    return {
      // formRelatedModels: '', // will be populated with form's related models through an API ajax call
      shared: store,
      permission: '',
      headers: [
        {title: 'Υπηρεσία', field: 'unit.abbrv', class: 'hidden'},
        {title: 'Εταιρεία', field: 'provider'},
        {title: 'Συμβόλαιο', field: 'contract_number'},
        {title: 'Από', field: 'valid_from'},
        {title: 'Έως', field: 'valid_to'},
        {title: 'Ποσό', field: 'amount', class: 'text-right'},
        {title: 'Είδος Οχήματος', field: 'vehicle.vehicle_type.name'},
        {title: 'Αρ. Κυκλ.', field: 'vehicle.registration_number', class: 'text-right'},
      ],
    }
  },

  created() {
    this.$getPermission(this, 'registry.read');

    if (this.unit_filter == true) {
      this.headers[1].class = '';
    }
  },

  methods: {
    updateResource(value) {
      eventHub.$emit('resource-updated');
    },

    deleteResource(value) {
      this.shared.resourceSet = this.shared.resourceSet.filter(function (el) {
        return el.id != value.id;
      });
      eventHub.$emit('resource-deleted');
    },
  }

}
</script>
