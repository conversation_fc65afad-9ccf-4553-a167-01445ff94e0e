<template>
  <resource-index
    title="Προβολή Σταθερών Τηλεφώνων"
    modal_title="Προβολή Κτιρίου"
    :table_headers="headers"
  >
    <div
      class="row"
      style="padding-top:20px"
    >
      <div class="col-sm-12">
        <building-phone-filters
          :unit_filter="this.unit_filter"
        />
      </div>
    </div>

    <building-tabs
      slot="modal_data"
      :building="this.shared.selectedResource.building"
      :related_models="this.related_models"
      :edit_form="false"
      :inside_modal="true"
      @update-building="updateResource"
      @delete-building="deleteResource"
    />
  </resource-index>
</template>

<script>
import Authorization from '../mixins/Authorization';
import BuildingPhoneFilters from './BuildingPhoneFilters.vue';
import BuildingTabs from './BuildingTabs.vue';
import { eventHub, store } from './ResourceEventHub';
import ResourceIndex from './ResourceIndex.vue';

export default {
  name: 'BuildingPhoneSearch',

  components: {
    ResourceIndex,
    BuildingTabs,
    BuildingPhoneFilters,
  },

  mixins: [Authorization],

  props: [
    'filters',
    'related_models',
    'unit_filter',
  ],

  data() {
    return {
      // formRelatedModels: '', // will be populated with form's related models through an API ajax call
      shared: store,
      permission: '',
      headers: [
        { title: 'Υπηρεσία', field: 'unit.abbrv', class: 'hidden' },
        { title: 'Αριθμός', field: 'number', class: 'text-right' },
        { title: 'Αριθμός ΕΣΥΠ', field: 'supply_number' },
        {
          title: 'Internet', field: 'has_internet', class: 'text-center', type: 'boolean',
        },
        {
          title: 'Εντός Σύζευξης', field: 'by_syzefxis', class: 'text-center', type: 'boolean',
        },
        {
          title: 'ΚΕΛΕΣΣ', field: 'keles_linked', class: 'text-center', type: 'boolean',
        },
        {
          title: 'Πληρ. ΓΔΟΥ', field: 'paid_centrally', class: 'text-center', type: 'boolean',
        },
        { title: 'AM Κτιρίου', field: 'building.id', class: 'text-right' },
        { title: 'Χρήση Κτιρίου', field: 'building.building_usage.name' },
        { title: 'Διεύθυνση', field: 'building.address', class: 'hidden' },
        { title: 'T.K.', field: 'building.postcode', class: 'hidden' },
        { title: 'Πόλη', field: 'building.city', class: 'hidden' },
        { title: 'T.M. (ωφέλιμα)', field: 'building.interior_area', class: 'hidden' },
        { title: 'T.M. (εξωτερικά)', field: 'building.exterior_area', class: 'hidden' },
      ],
    };
  },

  created() {
    this.$getPermission(this, 'registry.read');

    if (this.unit_filter == true) {
      this.headers[0].class = '';
    }
  },

  methods: {
    updateResource(value) {
      eventHub.$emit('resource-updated');
    },

    deleteResource(value) {
      this.shared.resourceSet = this.shared.resourceSet.filter((el) => el.id != value.id);
      eventHub.$emit('resource-deleted');
    },
  },

};
</script>
