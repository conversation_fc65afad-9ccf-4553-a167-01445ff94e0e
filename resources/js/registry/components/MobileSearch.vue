<template>
  <resource-index
      title="Αναζήτηση Κινητών Τηλεφώνων"
      modal_title="Προβολή Κινητού Τηλεφώνου"
      :table_headers="headers"
  >

    <div class="row" style="padding-top:20px">
      <div class="col-sm-12">
        <mobile-filters
            :mobile_providers="this.mobile_providers"
            :unit_filter="unit_filter"
            :selected_units="selected_units"
        ></mobile-filters>
      </div>
    </div>

    <mobile-form slot="modal_data"
                 :mobile="this.shared.selectedResource"
                 :mobile_providers="this.mobile_providers"
                 :inside_modal="true"
                 @updated="updateResource"
                 @deleted="deleteResource"
    ></mobile-form>

  </resource-index>
</template>

<script>
import {eventHub, store} from './ResourceEventHub.js';
import ResourceIndex from './ResourceIndex.vue';
import Authorization from "../mixins/Authorization";
import MobileFilters from './MobileFilters.vue';
import MobileForm from './MobileForm.vue';


export default {
  name: 'MobileSearch',

  components: {
    ResourceIndex,
    MobileForm,
    MobileFilters
  },

  mixins: [Authorization],

  props: ['filters', 'mobile_providers', 'unit_filter', 'selected_units'],

  data() {
    return {
      shared: store,
      permission: '',
      headers: [
        {title: 'Υπηρεσία', field: 'unit.abbrv', class: 'hidden'},
        {title: 'Αριθμός', field: 'number'},
        {title: 'Κάτοχος', field: 'holder'},
        {title: 'Πάροχος', field: 'mobileProvider_name'},
        {title: 'ΚΕΛΕΣΣ', field: 'keles_linked', type: 'boolean'},
        {title: 'Πληρ. ΓΔΟΥ', field: 'paid_centrally', type: 'boolean'}
      ],
    }
  },

  created() {
    this.$getPermission(this, 'registry.read');

    if (this.unit_filter == true) {
      this.headers[0].class = '';
    }
  },

  methods: {
    updateResource(value) {
      eventHub.$emit('resource-updated');
    },

    deleteResource(value) {
      this.shared.resourceSet = this.shared.resourceSet.filter(function (el) {
        return el.id != value.id;
      });
      eventHub.$emit('resource-deleted');
    },
  }

}
</script>
