<template>
    <form action="/registry/vehicle/insurance" method="POST"
        @submit.prevent="onSubmit(onUpdate)"
        @keydown.enter.prevent
        class="form-inline card-panel"
        v-bind:class="{'bg-paper': editing}"
    >
        <div class="loading-overlay" v-show="loading" tabindex="-1" style="opacity: 1.1; display: block;"></div>
        <div id="loader" v-show="loading"></div>

        <div class="row">
            <div class="col-sm-12">
                <input type="hidden" name="id" v-model="form.data.id" v-if="form.data.id">
                <input type="hidden" name="vehicle_id" v-model="form.data.vehicle_id">

                <text-field v-model="form.data.provider"
                    name="provider"
                    title="Ασφαλιστική Εταιρεία"
                    :error="form.errors.get('provider')"
                    @valid="form.errors.clear('provider')"
                    :required="true"
                ></text-field>

                <text-field v-model="form.data.contract_number"
                    name="contract_number"
                    title="Αρ. Ασφαλ. Συμβολαίου"
                    :error="form.errors.get('contract_number')"
                    @valid="form.errors.clear('contract_number')"
                    :required="true"
                ></text-field>

            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <date-field v-model="form.data.valid_from"
                    title="Περίοδος Ασφάλισης: Από"
                    name="valid_from"
                    :error="form.errors.get('valid_from')"
                    @valid="form.errors.clear('valid_from')"
                    :required="true"
                ></date-field>
                <date-field v-model="form.data.valid_to"
                    title="Έως"
                    name="valid_to"
                    :error="form.errors.get('valid_to')"
                    @valid="form.errors.clear('valid_to')"
                    :required="true"
                ></date-field>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <money-field v-model="form.data.amount"
                    name="amount"
                    title="Τελικό Ποσό Ασφαλίστρου"
                    :error="form.errors.get('amount')"
                    @valid="form.errors.clear('amount')"
                    :required="true"
                ></money-field>
            </div>
        </div>

        <div v-if="editing" class="row">
            <div class="col-sm-3">
                <a class="btn btn-default btn-block" @click.prevent="editOff">
                    <i class="fa fa-arrow-circle-left"></i> Ακύρωση
                </a>
            </div>
            <div class="col-sm-9">
                <button class="btn btn-success btn-block" :disabled="form.errors.has()">
                    <i class="fa fa-check-circle"></i> Αποθήκευση
                </button>
            </div>
        </div>
        <div v-else class="row">
            <div class="col-sm-3 col-sm-offset-6">
                <a class="btn btn-danger btn-block" href="#" @click.prevent="removeItem(onDelete)" v-if="this.permissions.update">
                    <i class="fa fa-times"></i> Διαγραφή
                </a>
            </div>

            <div class="col-sm-3">
                <a class="btn btn-warning btn-block" @click.prevent="editOn" v-if="this.permissions.update">
                    <i class="fa fa-edit"></i> Επεξεργασία
                </a>
            </div>
        </div>

    </form>

</template>

<script>

    import Form from '../../shared/Form';
    import {formReceiveRule, formSendRule} from '../ServerDataRules';
    import TextField from '../../shared/components/ui/FormFields/TextField.vue';
    import MoneyField from '../../shared/components/ui/FormFields/MoneyField.vue';
    import DateField from '../../shared/components/ui/FormFields/DateField.vue';
    import formControls from '../formControls';
    import __ from '../../shared/Helpers'

    export default {
        name: 'VehicleInsuranceForm',

        components: {
            TextField,
            MoneyField,
            DateField,
        },

        mixins: [ formControls ],

        props:{
            vehicle_insurance: {
                type: Object,
                default: () => {
                    return {}
                }
            },
            permissions: {
                type: Object,
                default: {
                    create: false,
                    read: false,
                    update: false,
                    delete: false
                }
            }
        },

        data() {
            return {
                loading: false,
                editing: true,
                endpoint: '/registry/vehicle-insurance',
                model: this.vehicle_insurance,
                form: new Form({
                    id: '',
                    provider: '',
                    contract_number: '',
                    valid_from: '',
                    valid_to: '',
                    amount: '',
                    vehicle_id: ''
                },
                {
                    // these are the rules for incoming and outgoing data
                    incoming: formReceiveRule,
                    outgoing: formSendRule
                }),
            }
        },

        created() {
            if (this.model) {
                console.log('updating form with vehicleInsurance model');
                this.form.populate(this.model);
            }

            this.editing = !this.assetExistsOnServer;

        },

        methods: {
            onDelete(data) {
                this.$emit('deleted', this.model);
            },

            onUpdate(data) {
                this.model = data.vehicle_insurance;
                this.form.populate(this.model);
                this.$emit('updated', this.model);
                this.editing = false;
                this.loading = false;
            }
        }
    }
</script>
