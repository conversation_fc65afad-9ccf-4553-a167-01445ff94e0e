<template>
  <form
    action="/registry/staff"
    method="POST"
    class="form-inline card-panel-clear"
    :class="{'bg-paper': editing}"
    @submit.prevent="onSubmit(onUpdate)"
    @keydown.enter.prevent
  >
    <div
      v-show="loading"
      class="loading-overlay"
      tabindex="-1"
      style="opacity: 1.1; display: block;"
    />
    <div
      v-show="loading"
      id="loader"
    />

    <input
      v-if="form.data.id"
      v-model="form.data.id"
      type="hidden"
      name="id"
    >
    <input
      v-model="form.data.building_id"
      type="hidden"
      name="building_id"
    >
    <input
      v-model="form.data.unit_id"
      type="hidden"
      name="unit_id"
    >

    <div class="row">
      <div class="col-sm-12">
        <h4>{{ this.model.unit.name }}</h4>

        <table class="table table-condensed table-bordered table-staff">
          <thead>
            <tr :class="{'bg-primary': editing, 'bg-gray': !editing}">
              <th>Ομάδα</th>
              <th>Περιγραφή</th>
              <th>Αριθμός Υπαλλήλων</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td rowspan="6">
                Μόνιμοι - ΙΔΑΧ
              </td>
              <td>Επιστημονικό Προσωπικό (Αρχαιολόγοι, Αρχιτέκτονες, Μηχανικοί)</td>
              <td>
                <text-field
                  v-model="form.data.regular_scientists"
                  name="regular_scientists"
                  :error="form.errors.get('regular_scientists')"
                  @valid="form.errors.clear('regular_scientists')"
                />
              </td>
            </tr>
            <tr>
              <td>Διοικητικό Προσωπικό (Διοικητικοί, Οικονομικοί, Λογιστές, Γραμματείς)</td>
              <td>
                <text-field
                  v-model="form.data.regular_administratives"
                  name="regular_administratives"
                  :error="form.errors.get('regular_administratives')"
                  @valid="form.errors.clear('regular_administratives')"
                />
              </td>
            </tr>
            <tr>
              <td>Τεχνικοί (Συντηρητές, Τεχνίτες κλπ)</td>
              <td>
                <text-field
                  v-model="form.data.regular_technicians"
                  name="regular_technicians"
                  :error="form.errors.get('regular_technicians')"
                  @valid="form.errors.clear('regular_technicians')"
                />
              </td>
            </tr>
            <tr>
              <td>Φυλακτικό Προσωπικό (Φύλακες, Νυχτοφύλακες)</td>
              <td>
                <text-field
                  v-model="form.data.regular_guards"
                  name="regular_guards"
                  :error="form.errors.get('regular_guards')"
                  @valid="form.errors.clear('regular_guards')"
                />
              </td>
            </tr>
            <tr>
              <td>Εργατικό Προσωπικό</td>
              <td>
                <text-field
                  v-model="form.data.regular_workers"
                  name="regular_workers"
                  :error="form.errors.get('regular_workers')"
                  @valid="form.errors.clear('regular_workers')"
                />
              </td>
            </tr>
            <tr>
              <td>Λοιπό Προσωπικό</td>
              <td>
                <text-field
                  v-model="form.data.regular_others"
                  name="regular_others"
                  :error="form.errors.get('regular_others')"
                  @valid="form.errors.clear('regular_others')"
                />
              </td>
            </tr>
            <tr>
              <td rowspan="6">
                ΙΔΟΧ
              </td>
              <td>Επιστημονικό Προσωπικό (Αρχαιολόγοι, Αρχιτέκτονες, Μηχανικοί)</td>
              <td>
                <text-field
                  v-model="form.data.contractual_scientists"
                  name="contractual_scientists"
                  :error="form.errors.get('contractual_scientists')"
                  @valid="form.errors.clear('contractual_scientists')"
                />
              </td>
            </tr>
            <tr>
              <td>Διοικητικό Προσωπικό (Διοικητικοί, Οικονομικοί, Λογιστές, Γραμματείς)</td>
              <td>
                <text-field
                  v-model="form.data.contractual_administratives"
                  name="contractual_administratives"
                  :error="form.errors.get('contractual_administratives')"
                  @valid="form.errors.clear('contractual_administratives')"
                />
              </td>
            </tr>
            <tr>
              <td>Τεχνικοί (Συντηρητές, Τεχνίτες κλπ)</td>
              <td>
                <text-field
                  v-model="form.data.contractual_technicians"
                  name="contractual_technicians"
                  :error="form.errors.get('contractual_technicians')"
                  @valid="form.errors.clear('contractual_technicians')"
                />
              </td>
            </tr>
            <tr>
              <td>Φυλακτικό Προσωπικό (Φύλακες, Νυχτοφύλακες)</td>
              <td>
                <text-field
                  v-model="form.data.contractual_guards"
                  name="contractual_guards"
                  :error="form.errors.get('contractual_guards')"
                  @valid="form.errors.clear('contractual_guards')"
                />
              </td>
            </tr>
            <tr>
              <td>Εργατικό Προσωπικό</td>
              <td>
                <text-field
                  v-model="form.data.contractual_workers"
                  name="contractual_workers"
                  :error="form.errors.get('contractual_workers')"
                  @valid="form.errors.clear('contractual_workers')"
                />
              </td>
            </tr>
            <tr>
              <td>Λοιπό Προσωπικό</td>
              <td>
                <text-field
                  v-model="form.data.contractual_others"
                  name="contractual_others"
                  :error="form.errors.get('contractual_others')"
                  @valid="form.errors.clear('contractual_others')"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div
      v-if="editing"
      class="row"
    >
      <div class="col-sm-3">
        <a
          class="btn btn-default btn-block"
          @click.prevent="editOff"
        >
          <i class="fa fa-arrow-circle-left" /> Ακύρωση
        </a>
      </div>
      <div class="col-sm-9">
        <button
          class="btn btn-success btn-block"
          :disabled="form.errors.has()"
        >
          <i class="fa fa-check-circle" /> Αποθήκευση
        </button>
      </div>
    </div>
    <div
      v-else
      class="row"
    >
      <div class="col-sm-3 col-sm-offset-9">
        <a
          v-if="this.permissions.update"
          class="btn btn-warning btn-block"
          @click.prevent="editOn"
        >
          <i class="fa fa-edit" /> Επεξεργασία
        </a>
      </div>
    </div>
  </form>
</template>

<script>
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import Form from '../../shared/Form';
import formControls from '../formControls';
import Authorization from '../mixins/Authorization';
import { formReceiveRule, formSendRule } from '../ServerDataRules';

export default {
  name: 'BuildingStaffForm',

  components: {
    TextField,
  },

  mixins: [formControls, Authorization],

  props: {
    staff: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      // formRelatedModels: '', // will be populated with form's related models through an API ajax call
      loading: false,
      editing: true,
      endpoint: '/registry/staff',
      model: this.staff,
      form: new Form({
        id: '',
        regular_scientists: '',
        regular_administratives: '',
        regular_technicians: '',
        regular_guards: '',
        regular_workers: '',
        regular_others: '',
        contractual_scientists: '',
        contractual_administratives: '',
        contractual_technicians: '',
        contractual_guards: '',
        contractual_workers: '',
        contractual_others: '',
        building_id: '',
        unit_id: '',
      },
      {
        // these are the rules for incoming and outgoing data
        incoming: formReceiveRule,
        outgoing: formSendRule,
      }),
      permissions: {
        create: false,
        read: false,
        update: false,
        delete: false,
      },
    };
  },

  created() {
    if (this.model) {
      console.log('updating form with staff model');
      this.form.populate(this.model);
      this.editing = false;
      this.$getModelPermissions(this, 'registry', 'staff', this.model.id);
    }
  },

  methods: {
    onUpdate(data) {
      this.model = data.staff;
      this.form.populate(this.model);
      this.$emit('updated', this.model);
      this.editing = false;
      this.loading = false;
    },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss">
.table-staff > thead > tr > th,
.table-staff > tbody > tr > th,
.table-staff > tfoot > tr > th,
.table-staff > thead > tr > td,
.table-staff > tbody > tr > td,
.table-staff > tfoot > tr > td {
  border: 1px solid #d2d6de;
  vertical-align: middle;
  font-size: 0.9rem;
}

.table-staff > thead > tr > td,
.table-staff > tbody > tr > td,
.table-staff > tfoot > tr > td {
  padding: 2px 4px;
}

.table-staff > thead > tr > th {
  text-align: center;
}
</style>
