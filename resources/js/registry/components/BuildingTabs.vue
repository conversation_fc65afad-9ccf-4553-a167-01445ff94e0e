<template>
  <div>
    <!--Resource Buttons (Print, Delete)-->
    <div
      v-if="assetExistsOnServer"
      class="row"
      style="padding-bottom: 0.7rem"
    >
      <div
        class="col-sm-12"
        style="text-align: right"
      >
        <div>
          <a
            v-if="permissions.read"
            class="btn btn-flat btn-info btn-outline btn-sm"
            role="button"
            @click="printout"
          >
            <i class="fa fa-download" /> Εξαγωγή PDF
          </a>&nbsp;
          <a
            v-if="permissions.delete"
            class="btn btn-flat btn-danger btn-outline btn-sm"
            role="button"
            @click="deleteResource(onDelete)"
          >
            <i class="fa fa-times" /> Διαγραφή Ακινήτου
          </a>
        </div>
      </div>
    </div>
    <!--TABS-->
    <horizontal-tabs style="min-height: 640px;">
      <tab
        name="Στοιχεία Ακινήτου"
        :selected="true"
      >
        <building-form
          :building="model"
          :related_models="related_models"
          :hosted_units_field="hosted_units_field"
          :permissions="permissions"
          @updated="updateResource"
        />
      </tab>

      <tab
        v-if="assetExistsOnServer"
        name="Προσωπικό"
        :selected="false"
      >
        <div
          class="row"
          style="padding-top: 20px"
        >
          <div class="col-sm-12">
            <building-staff-form
              v-for="(item, index) in model.staff"
              :key="item.id"
              :staff="item"
              :index="index"
              @updated="$emit('updated', model)"
            />
          </div>
        </div>
      </tab>

      <tab
        v-if="assetExistsOnServer && model.ownership_type_id == '2'"
        name="Εκμισθώσεις"
        :selected="false"
      >
        <building-rentals
          :data="model.rentals"
          :building="model"
          :related_models="related_models"
          :permissions="permissions"
          @updated="$emit('updated', model)"
          @removed="$emit('updated', model)"
        />
      </tab>

      <tab
        v-if="assetExistsOnServer && model.ownership_type_id == '2'"
        name="Συσχετιζόμενοι"
        :selected="false"
      >
        <building-associates
          :data="model.associates"
          :building="model"
          :related_models="related_models"
          :permissions="permissions"
          @updated="$emit('updated', model)"
          @removed="$emit('updated', model)"
        />
      </tab>

      <tab
        v-if="assetExistsOnServer"
        name="Τηλεφωνία"
        :selected="false"
      >
        <building-phones
          :data="model.phones"
          :building="model"
          :related_models="related_models"
          :permissions="permissions"
          @updated="$emit('updated', model)"
          @removed="$emit('updated', model)"
        />
      </tab>

      <tab
        v-if="assetExistsOnServer"
        name="Παροχές"
        :selected="false"
      >
        <building-utilities
          :data="model.utilities"
          :building="model"
          :related_models="related_models"
          :permissions="permissions"
          @updated="$emit('updated', model)"
          @removed="$emit('updated', model)"
        />
      </tab>
    </horizontal-tabs>
  </div>
</template>

<script>

import HorizontalTabs from '../../shared/components/ui/Tabs/HorizontalTabs.vue';
import Tab from '../../shared/components/ui/Tabs/TabItem.vue';
import formControls from '../formControls';
import Authorization from '../mixins/Authorization';
import BuildingAssociates from './BuildingAssociates.vue';
import BuildingForm from './BuildingForm.vue';
import BuildingPhones from './BuildingPhones.vue';
import BuildingRentals from './BuildingRentals.vue';
import BuildingStaffForm from './BuildingStaffForm.vue';
import BuildingUtilities from './BuildingUtilities.vue';

export default {
  name: 'BuildingTabs',

  components: {
    HorizontalTabs,
    Tab,
    BuildingForm,
    BuildingStaffForm,
    BuildingRentals,
    BuildingAssociates,
    BuildingPhones,
    BuildingUtilities,
  },

  mixins: [formControls, Authorization],

  props: {
    building: {
      type: Object,
      default: () => ({}),
    },
    related_models: {
      type: Object,
      default: () => ({}),
    },
    hosted_units_field: {
      type: Boolean,
      default: false,
    },
    inside_modal: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      loading: false,
      editing: true,
      endpoint: '/registry/building',
      permissions: {
        create: false,
        read: false,
        update: false,
        delete: false,
      },
      model: this.building,
      permissionCreate: {
        phone: false,
      },
    };
  },

  watch: {
    // whenever the building Model changes
    model(newModel, oldModel) {
      // Check if we have a new ID (after first save and renew permissions for this model)
      if (newModel.id !== oldModel.id) {
        this.$getModelPermissions(this, 'registry', 'building', newModel.id);
        this.$getCreateModelPermission(this, 'registry', 'phone');
      }
    },
  },

  created() {
    if (this.assetExistsOnServer) {
      this.editing = false;
    }

    // Get crud permissions for building. Also used for rentals, associates and utilities
    this.$getModelPermissions(this, 'registry', 'building', this.model.id);

    // See if the user can add phones.
    // This only checks if a user can create a phone in general.
    // We take as granted that if a user can see a building's details, he should be able to add phones to it,
    // as long as he has editor role.
    // Since the create authorization for phones and buildings is produced by the same permission (registry.create),
    // any user that can create a building could also create a phone.
    // However, a user may be able to only read a building (because he is in the Hosted Units),
    // but add phones to it if he has registry.create.
    // This is needed for buildings of central Units, which are created by GDOY and used by many Units.
    this.$getCreateModelPermission(this, 'registry', 'phone');
  },

  methods: {
    onDelete() {
      if (this.inside_modal) {
        this.$emit('deleted', this.model);
      } else {
        window.location.replace('/registry/');
      }
    },

    // Updates main resource object with value
    updateResource(value) {
      this.model = value;
      this.$emit('updated', this.model);
    },
  },
};

</script>
