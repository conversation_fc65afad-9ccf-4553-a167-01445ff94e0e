<template>
  <form
    action="/registry/building/utility"
    method="POST"
    class="form-inline card-panel"
    :class="{'bg-paper': editing}"
    @submit.prevent="onSubmit(onUpdate)"
    @keydown.enter.prevent
  >
    <div
      v-show="loading"
      class="loading-overlay"
      tabindex="-1"
      style="opacity: 1.1; display: block;"
    />
    <div
      v-show="loading"
      id="loader"
    />

    <input
      v-if="form.data.id"
      v-model="form.data.id"
      type="hidden"
      name="id"
    >
    <input
      v-model="form.data.building_id"
      type="hidden"
      name="building_id"
    >

    <div class="row">
      <div class="col-sm-6">
        <select-field
          v-model="form.data.utility_type_id"
          name="utility_type_id"
          title="Είδος Παροχής"
          :options="related_models.utilityTypes"
          :error="form.errors.get('utility_type_id')"
          :required="true"
          @valid="clearErrorsAndProviderName"
          @input="updateProviders"
        />
      </div>
      <div class="col-sm-6">
        <select-field
          v-model="form.data.utility_provider_id"
          name="utility_provider_id"
          title="Επωνυμία Παρόχου"
          :options="providersList"
          :error="form.errors.get('utility_provider_id')"
          :required="true"
          @valid="form.errors.clear('utility_provider_id')"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-6">
        <text-field
          v-model="form.data.supply_number"
          name="supply_number"
          title="Αρ. Παροχής/Μετρητή"
          :required="true"
          :error="form.errors.get('supply_number')"
          @valid="form.errors.clear('supply_number')"
        />
      </div>
      <div class="col-sm-6">
        <switch-field
          v-model="form.data.paid_centrally"
          name="paid_centrally"
          title="Πληρωμή Κεντρικά (ΓΔΟΥ)"
          :error="form.errors.get('paid_centrally')"
          @valid="form.errors.clear('paid_centrally')"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-12">
        <date-field
          v-model="form.data.valid_from"
          title="Έναρξη Χρήσης"
          :required="true"
          name="valid_from"
          :error="form.errors.get('valid_from')"
          @valid="form.errors.clear('valid_from')"
        />
        <date-field
          v-model="form.data.valid_to"
          title="Λήξη Χρήσης"
          name="valid_to"
          :error="form.errors.get('valid_to')"
          @valid="form.errors.clear('valid_to')"
        />
      </div>
    </div>

    <div
      v-if="editing"
      class="row"
    >
      <div class="col-sm-3">
        <a
          class="btn btn-default btn-block"
          @click.prevent="editOff"
        >
          <i class="fa fa-arrow-circle-left" /> Ακύρωση
        </a>
      </div>
      <div class="col-sm-9">
        <button
          class="btn btn-success btn-block"
          :disabled="form.errors.has()"
        >
          <i class="fa fa-check-circle" /> Αποθήκευση
        </button>
      </div>
    </div>
    <div
      v-else
      class="row"
    >
      <div class="col-sm-3 col-sm-offset-6">
        <a
          v-if="this.permissions.update"
          class="btn btn-danger btn-block"
          href="#"
          @click.prevent="removeItem(onDelete)"
        >
          <i class="fa fa-times" /> Διαγραφή
        </a>
      </div>

      <div class="col-sm-3">
        <a
          v-if="this.permissions.update"
          class="btn btn-warning btn-block"
          @click.prevent="editOn"
        >
          <i class="fa fa-edit" /> Επεξεργασία
        </a>
      </div>
    </div>
  </form>
</template>

<script>

import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import SelectField from '../../shared/components/ui/FormFields/SelectField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import Form from '../../shared/Form';
import __ from '../../shared/Helpers';
import formControls from '../formControls';
import { formReceiveRule, formSendRule } from '../ServerDataRules';

export default {
  name: 'BuildingUtilityForm',

  components: {
    DateField,
    TextField,
    SelectField,
    SwitchField,
  },

  mixins: [formControls],

  props: {
    utility: {
      type: Object,
      default: () => ({}),
    },
    related_models: {
      type: Object,
      default: () => ({}),
    },
    permissions: {
      type: Object,
      default: {
        create: false,
        read: false,
        update: false,
        delete: false,
      },
    },
  },

  data() {
    return {
      loading: false,
      editing: true,
      model: this.utility,
      endpoint: '/registry/utility',
      providersList: '',
      form: new Form({
        id: '',
        utility_type_id: '',
        utility_provider_id: '',
        supply_number: '',
        paid_centrally: '',
        valid_from: '',
        valid_to: '',
        building_id: '',
      },
      {
        // these are the rules for incoming and outgoing data
        incoming: formReceiveRule,
        outgoing: formSendRule,
      }),
    };
  },

  created() {
    if (this.model) {
      console.log('updating form with buildingUtility model');
      this.form.populate(this.model);
    }

    this.editing = !this.assetExistsOnServer;

    if (this.model.utility_type_id) this.updateProviders(this.form.data.utility_type_id);
  },

  methods: {
    // Update provider options based on utility type chosen
    updateProviders(type_id) {
      this.providersList = this.related_models.utilityProviders.filter((el) => el.utility_type_id == type_id);
    },

    onDelete(data) {
      this.$emit('deleted', this.model);
    },

    onUpdate(data) {
      this.model = data.utility;
      this.form.populate(this.model);
      this.$emit('updated', this.model);
      this.editing = false;
      this.loading = false;
    },

    clearErrorsAndProviderName() {
      this.form.errors.clear('utility_type_id');
      this.form.data.utility_provider_id = '';
    },
  },
};
</script>
