<template>
  <form
    id="vehicleForm"
    action="/registry/vehicle"
    method="POST"
    :class="{'bg-paper': editing}"
    style="padding:5px 5px 10px 5px"
    @submit.prevent="onSubmit(onUpdate)"
    @keydown.enter.prevent
  >
    <div
      v-show="loading"
      class="loading-overlay"
      tabindex="-1"
      style="opacity: 1.1; display: block;"
    />
    <div
      v-show="loading"
      id="loader"
    />

    <div
      class="row"
      style="padding-top:20px"
    >
      <div
        v-if="this.form.data.id"
        class="col-sm-2"
      >
        <label
          for="id"
          class="control-label"
        >Α.Μ.</label>
        <input
          v-if="form.data.id"
          v-model="form.data.id"
          type="text"
          name="id"
          readonly="readonly"
          class="form-control"
        >
      </div>
      <div class="col-sm-3">
        <text-field
          v-model="form.data.brand"
          name="brand"
          title="Μάρκα"
          :error="form.errors.get('brand')"
          :required="true"
          @valid="form.errors.clear('brand')"
        />
      </div>
      <div class="col-sm-3">
        <text-field
          v-model="form.data.model"
          name="model"
          title="Μοντέλο"
          :error="form.errors.get('model')"
          :required="true"
          @valid="form.errors.clear('model')"
        />
      </div>
      <div class="col-sm-4">
        <selectfilter-field
          v-model="form.data.vehicle_type_id"
          name="vehicle_type_id"
          title="Είδος Οχήματος"
          :options="vehicle_types"
          :error="form.errors.get('vehicle_type_id')"
          :required="true"
          @valid="form.errors.clear('vehicle_type_id')"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-3">
        <text-field
          v-model="form.data.registration_number"
          name="registration_number"
          title="Αρ. Κυκλοφορίας"
          :error="form.errors.get('registration_number')"
          @valid="form.errors.clear('registration_number')"
        />
      </div>
      <div class="col-sm-3">
        <text-field
          v-model="form.data.cc"
          name="cc"
          title="Κυβικά Εκατοστά"
          :error="form.errors.get('cc')"
          @valid="form.errors.clear('cc')"
        />
      </div>
      <div class="col-sm-6">
        <text-field
          v-model="form.data.frame_number"
          name="frame_number"
          title="Αρ. Πλαισίου"
          :error="form.errors.get('frame_number')"
          @valid="form.errors.clear('frame_number')"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-4 col-md-3">
        <date-field
          v-model="form.data.license_date"
          name="license_date"
          title="Ημερομηνία 1ης Αδείας"
          :error="form.errors.get('license_date')"
          @valid="form.errors.clear('license_date')"
        />
      </div>
      <div class="col-sm-4 col-md-3">
        <text-field
          v-model="form.data.license_number"
          name="license_number"
          title="Αριθμός Άδειας"
          :error="form.errors.get('license_number')"
          @valid="form.errors.clear('license_number')"
        />
      </div>
      <div class="col-sm-4 col-md-3">
        <switch-field
          v-model="form.data.insurance_obligation"
          name="insurance_obligation"
          title="Υποχρέωση Ασφάλισης"
          :error="form.errors.get('insurance_obligation')"
          @valid="form.errors.clear('insurance_obligation')"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-6">
        <select-ajax-field
          v-model="form.data.employee_id"
          name="employee_id"
          title="Χρήστης/Οδηγός (υπάλληλος ΥΠΠΟΑ)"
          url="/api/registry/driver/search"
          :error="form.errors.get('employee_id')"
          :parent="parent_element"
          @valid="form.errors.clear('employee_id')"
        >
          <option
            v-if="form.data.employee_id && form.data.employee !== null"
            selected
            value="form.data.employee_id"
            v-text="employee_fullname"
          />
          <!-- <option v-else disabled value="0">Επιλέξτε...</option> Not needed, we set it in select2 instantiation-->
        </select-ajax-field>
      </div>
      <div class="col-sm-6">
        <text-field
          v-model="form.data.employee_other"
          name="employee_other"
          title="Χρήστης/Οδηγός (άλλος)"
          :error="form.errors.get('employee_other')"
          @valid="form.errors.clear('employee_other')"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-4 col-md-3">
        <date-field
          v-model="form.data.valid_from"
          name="valid_from"
          title="Έναρξη Χρήσης"
          :error="form.errors.get('valid_from')"
          :required="true"
          @valid="form.errors.clear('valid_from')"
        />
      </div>
      <div class="col-sm-4 col-md-3">
        <date-field
          v-model="form.data.valid_to"
          name="valid_to"
          title="Λήξη Χρήσης"
          :error="form.errors.get('valid_to')"
          @valid="form.errors.clear('valid_to')"
        />
      </div>
      <div
        v-if="form.data.id != ''"
        class="col-sm-4 col-md-6"
      >
        <label for="unitName">Υπηρεσία</label><br>
        <input
          v-if="form.data.id"
          id="unitName"
          type="text"
          readonly="readonly"
          class="form-control"
          :value="this.form.data.unit.name"
        >
      </div>
    </div>

    <div
      v-if="editing"
      class="row"
    >
      <div class="col-sm-3">
        <a
          v-if="this.assetExistsOnServer"
          class="btn btn-default btn-block"
          @click="editOff"
        >
          <i class="fa fa-arrow-circle-left" /> Ακύρωση
        </a>
        <a
          v-else
          class="btn btn-default btn-block"
          href="/registry"
        >
          <i class="fa fa-arrow-circle-left" /> Ακύρωση
        </a>
      </div>
      <div class="col-sm-9">
        <button
          class="btn btn-success btn-block"
          :disabled="form.errors.has()"
        >
          <i class="fa fa-check-circle" /> Αποθήκευση
        </button>
      </div>
    </div>
    <div
      v-else
      class="row"
    >
      <div class="col-sm-12">
        <a
          v-if="this.permissions.update"
          class="btn btn-warning btn-block"
          style=""
          @click="editOn"
        >
          <i class="fa fa-edit" /> Επεξεργασία
        </a>
      </div>
    </div>
  </form>
</template>

<script>

import Form from '../../shared/Form';
import { vehicleFormReceiveRule, vehicleFormSendRule } from '../ServerDataRules';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';
import SelectAjaxField from '../../shared/components/ui/FormFields/SelectAjaxField.vue';
import SelectfilterField from '../../shared/components/ui/FormFields/SelectfilterField.vue';
import formControls from '../formControls';

export default {
  name: 'VehicleForm',

  components: {
    TextField,
    DateField,
    SwitchField,
    SelectAjaxField,
    SelectfilterField,
  },

  mixins: [formControls],

  props: {
    vehicle: {
      type: Object,
      default: () => ({}),
    },
    vehicle_types: {
      type: Array,
      default: () => [],
    },
    inside_modal: {
      type: Boolean,
      default: false,
    },
    permissions: {
      type: Object,
      default: {
        create: false,
        read: false,
        update: false,
        delete: false,
      },
    },
  },

  data() {
    return {
      loading: false,
      editing: true,
      model: this.vehicle,
      endpoint: '/registry/vehicle',
      form: new Form({
        id: '',
        brand: '',
        model: '',
        vehicle_type_id: '',
        frame_number: '',
        cc: '',
        registration_number: '',
        license_date: '',
        license_number: '',
        insurance_obligation: true,
        employee_id: '',
        employee_other: '',
        valid_from: '',
        valid_to: '',
        vehicle_insurances: [],
      },
      {
        // these are the rules for incoming and outgoing data
        incoming: vehicleFormReceiveRule,
        outgoing: vehicleFormSendRule,
      }),
    };
  },

  computed: {
    employee_fullname() {
      if (this.form.data.employee_id != '' && typeof this.form.data.employee !== 'undefined' && this.form.data.employee.id != '') {
        return this.form.data.employee.fullname;
      }
      return '';
    },

    // We need this to pass the parent modal element to Select2 field.
    // Select2 needs special declaration in case it is inside a Bootstrap modal.
    parent_element() {
      if (this.inside_modal) {
        return '#vue-modal';
      }

      return '';
    },
  },

  created() {
    if (this.assetExistsOnServer) {
      console.log('updating vehicle form with values');
      this.form.populate(this.model);
      this.editing = false;
    }
  },

  mounted() {
    console.log('Vehicle Component mounted.');
  },

  methods: {
    onUpdate(data) {
      this.model = data.vehicle;
      this.form.populate(this.model);
      this.$emit('updated', this.model);
      this.editing = false;
      this.loading = false;
    },
  },
};
</script>
