<template>
  <form
    id="filter-form"
    action="search_url"
    method="POST"
    @submit.prevent="onSubmit"
  >
    <input
      name="_token"
      type="hidden"
      :value="csrf"
    >

    <div class="row">
      <div class="col-sm-6">
        <select-field
          v-model="form.data.utility_type_id"
          name="utility_type_id"
          title="Είδος Παροχής"
          :options="related_models.utilityTypes"
          :error="form.errors.get('utility_type_id')"
          @valid="form.errors.clear('utility_type_id')"
          @input="updateProviders"
        />
      </div>
      <div class="col-sm-6">
        <select-field
          v-model="form.data.utility_provider_id"
          name="utility_provider_id"
          title="Επωνυμία Παρόχου"
          :options="providersList"
          :error="form.errors.get('utility_provider_id')"
          @valid="form.errors.clear('utility_provider_id')"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-6">
        <text-field
          v-model="form.data.supply_number"
          name="supply_number"
          title="Αρ. Παροχής/Μετρητή"
          :error="form.errors.get('supply_number')"
          @valid="form.errors.clear('supply_number')"
        />
      </div>
      <div class="col-sm-6">
        <select-field
          v-model="form.data.paid_centrally"
          name="paid_centrally"
          title="Πληρωμή Κεντρικά (ΓΔΟΥ)"
          :options="booleanOptions"
          :error="form.errors.get('paid_centrally')"
          @valid="form.errors.clear('paid_centrally')"
        />
      </div>
    </div>

    <div
      v-if="unit_filter"
      class="row"
    >
      <div class="col-sm-12">
        <UnitTreeField v-model="form.data.units" />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-4">
        <switch-field
          v-model="form.data.show_all"
          name="show_all"
          title="Εμφάνιση Όλων (και ανενεργών)"
          :error="form.errors.get('show_all')"
          @valid="form.errors.clear('show_all')"
        />
      </div>
      <div class="col-sm-4">
        <date-field
          v-if="!form.data.show_all"
          v-model="form.data.valid_on"
          title="Ημερομηνία Προβολής"
          name="valid_on"
          :error="form.errors.get('valid_on')"
          @valid="form.errors.clear('valid_on')"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-3">
        <a
          class="btn btn-default btn-block"
          href="/registry"
        >
          <i class="fa fa-arrow-circle-left" /> Επιστροφή
        </a>
      </div>
      <div class="col-sm-6 col-sm-offset-3">
        <button
          class="btn btn-success btn-block"
          :disabled="form.errors.has()"
        >
          <i class="fa fa-check-circle" /> Αναζήτηση
        </button>
      </div>
    </div>
  </form>
</template>

<script>
import moment from 'moment';

import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import SelectField from '../../shared/components/ui/FormFields/SelectField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import UnitTreeField from '../../shared/components/ui/FormFields/UnitTreeField.vue';
import Form from '../../shared/Form';
import { formReceiveRule, formSendRule } from '../ServerDataRules';
import { eventHub, store } from './ResourceEventHub';

export default {
  name: 'BuildingPhoneFilters',

  components: {
    TextField,
    DateField,
    SelectField,
    SwitchField,
    UnitTreeField,
  },

  props: {
    filters: { default: '' },
    related_models: { type: Object },
    unit_filter: { default: false },
  },

  data() {
    return {
      shared: store,
      search_url: '/registry/utility/search',
      csrf: window.Laravel.csrfToken,
      resourceFilters: '',
      providersList: '',
      form: new Form(
        {
          show_all: true,
          valid_on: '',
          utility_type_id: '',
          utlitity_provider_id: '',
          supply_number: '',
          paid_centrally: '',
          units: '',
        },
        {
          // these are the rules for incoming and outgoing data
          incoming: formReceiveRule,
          outgoing: formSendRule,
        },
      ),
      booleanOptions: [
        { id: '1', name: 'NAI' },
        { id: '0', name: 'OXI' },
      ],
    };
  },

  mounted() {
    console.log('Filter Form mounted.');
  },

  created() {
    const currentTime = moment().format('YYYY-MM-DD');
    this.form.data.valid_on = currentTime;
    if (this.filters) {
      this.form.populate(this.filters);
    }

    if (this.form.data.utility_type_id) this.updateProviders(this.form.data.utility_type_id);
    if (this.form.data.utility_provider_id) this.form.data.utility_provider_id = this.form.data.utility_provider_id;

    eventHub.$on('resource-updated', this.searchResource);
  },

  beforeDestroy() {
    eventHub.$off('resource-updated', this.searchResource);
  },

  methods: {
    // Update provider options based on utility type chosen
    updateProviders(type_id) {
      this.form.data.utility_provider_id = '';
      this.providersList = this.related_models.utilityProviders.filter((el) => el.utility_type_id == type_id);
    },

    // Filter form submitted - do the search
    onSubmit() {
      this.searchResource();
    },

    // Search the resource based on form filters
    searchResource() {
      eventHub.$emit('search-start');

      const submitUrl = this.search_url;
      const submitMethod = 'post';

      this.form[submitMethod](submitUrl)
        .then((response) => {
          this.shared.resourceSet = response.data.resource;

          this.resourceFilters = response.data.filters;
          this.form.populate(this.resourceFilters);

          if (this.shared.resourceSet.length == 0) {
            swal({
              type: 'info',
              title: 'Αποτελέσματα Αναζήτησης: 0',
              text: 'Δε βρέθηκαν σταθερά τηλέφωνα για τα φίλτρα που επιλέξατε.',
              showConfirmButton: false,
              timer: 2000,
            });
          }

          eventHub.$emit('search-complete');
        })
        .catch((error) => {
          swal({
            type: 'error',
            title: 'Σφάλμα αναζήτησης!',
            text: 'Παρουσιάστηκε σφάλμα κατά την αναζήτηση. Ελέγξτε τα φίλτρα.',
            showConfirmButton: false,
            timer: 2500,
          });

          eventHub.$emit('search-fail');
        });
    },
  },
};
</script>
