<template>
  <form id="filter-form" action="search_url" method="POST" @submit.prevent="onSubmit">

    <input name="_token" type="hidden" :value="csrf">

    <div class="row">
      <div class="col-sm-4">
        <text-field name="id" title="Αρ. Μητρώου" v-model="form.data.id" @valid="form.errors.clear('id')"></text-field>
      </div>
      <div class="col-sm-4">
        <text-field name="registration_number" title="Αρ. Κυκλοφορίας" v-model="form.data.registration_number"
                    @valid="form.errors.clear('registration_number')"></text-field>
      </div>
      <div class="col-sm-4">
        <select-field name="vehicle_type_id" title="Είδος Οχήματος" v-model="form.data.vehicle_type_id"
                      :options="vehicle_types" @valid="form.errors.clear('vehicle_type_id')"></select-field>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-4">
        <text-field name="brand" title="Μάρκα" v-model="form.data.brand" :error="form.errors.get('brand')"
                    @valid="form.errors.clear('brand')"></text-field>
      </div>
      <div class="col-sm-4">
        <text-field name="model" title="Μοντέλο" v-model="form.data.model" :error="form.errors.get('model')"
                    @valid="form.errors.clear('model')"></text-field>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-12">
        <UnitTreeField
            v-model="form.data.units"
            :allow-showing-departments="false"
            :show-collapsed="! unit_filter"
            authorize-for="registry"
        />
      </div>
    </div>

    <div class="row">
      <div class="col-sm-4">
        <switch-field
            v-model="form.data.show_all"
            name="show_all"
            title="Εμφάνιση Όλων (και ανενεργών)"
            :error="form.errors.get('show_all')"
            @valid="form.errors.clear('show_all')"
        ></switch-field>
      </div>
      <div class="col-sm-4">
        <date-field
            v-if="!form.data.show_all"
            v-model="form.data.valid_on"
            title="Ημερομηνία Προβολής"
            name="valid_on"
            :error="form.errors.get('valid_on')"
            @valid="form.errors.clear('valid_on')"
        >
        </date-field>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-3">
        <a class="btn btn-default btn-block" href="/registry">
          <i class="fa fa-arrow-circle-left"></i> Επιστροφή
        </a>
      </div>
      <div class="col-sm-6 col-sm-offset-3">
        <button class="btn btn-success btn-block" :disabled="form.errors.has()">
          <i class="fa fa-check-circle"></i> Αναζήτηση
        </button>
      </div>
    </div>
  </form>
</template>

<script>
import Form from '../../shared/Form';
import {eventHub, store} from './ResourceEventHub.js';
import {formReceiveRule, formSendRule} from '../ServerDataRules';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import SelectField from '../../shared/components/ui/FormFields/SelectField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';
import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import UnitTreeField from '../../shared/components/ui/FormFields/UnitTreeField.vue';
import moment from 'moment';

export default {
  name: 'VehicleFilters',

  components: {
    TextField,
    SelectField,
    UnitTreeField,
    SwitchField,
    DateField
  },

  props: {
    filters: {default: ''},
    vehicle_types: {type: Array},
    unit_filter: {default: false},
    selected_units: {type: Array},
  },

  data() {
    return {
      shared: store,
      search_url: '/registry/vehicle/search',
      csrf: window.Laravel.csrfToken,
      resourceFilters: '',
      form: new Form(
          {
            show_all: true,
            id: '',
            registration_number: '',
            brand: '',
            model: '',
            vehicle_type_id: '',
            valid_on: '',
            units: [],
          },
          {
            // these are the rules for incoming and outgoing data
            incoming: formReceiveRule,
            outgoing: formSendRule
          }
      ),
    }
  },

  mounted() {
    console.log('Filter Form mounted.');
  },

  created() {
    const currentTime = moment().format('YYYY-MM-DD');
    console.log(currentTime);
    this.form.data.valid_on = currentTime;
    if (this.selected_units.length > 0) {
      this.form.data.units = this.selected_units;
    }
    if (this.filters) {
      this.form.populate(this.filters);
    }
    eventHub.$on('resource-updated', this.searchResource);
  },

  beforeDestroy: function () {
    eventHub.$off('resource-updated', this.searchResource);
  },

  methods: {
    // Filter form submitted - do the search
    onSubmit() {
      this.searchResource();
    },

    // Search the resource based on form filters
    searchResource() {
      eventHub.$emit('search-start');

      let submitUrl = this.search_url;
      let submitMethod = 'post';

      this.form[submitMethod](submitUrl)
          .then(response => {
            this.shared.resourceSet = response.data.resource;

            this.resourceFilters = response.data.filters;
            this.form.populate(this.resourceFilters);

            if (this.shared.resourceSet.length == 0) {
              swal({
                type: 'info',
                title: 'Αποτελέσματα Αναζήτησης: 0',
                text: 'Δε βρέθηκαν οχήματα για τα φίλτρα που επιλέξατε.',
                showConfirmButton: false,
                timer: 2000
              });
            }

            eventHub.$emit('search-complete');

          })
          .catch(error => {
            swal({
              type: 'error',
              title: 'Σφάλμα αναζήτησης!',
              text: 'Παρουσιάστηκε σφάλμα κατά την αναζήτηση. Ελέγξτε τα φίλτρα.',
              showConfirmButton: false,
              timer: 2500,
            });

            eventHub.$emit('search-fail');
          });
    }
  }
}
</script>
