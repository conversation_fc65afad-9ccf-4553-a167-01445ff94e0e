<template>
  <div>
    <div
      class="row"
      style="padding-top:20px"
    >
      <div class="col-sm-12">
        <h5 v-show="!hasItems">
          Δεν έχουν καταχωρηθεί στοιχεία τηλεφωνικών γραμμών μέχρι στιγμής
        </h5>
      </div>
    </div>

    <div
      v-show="hasItems"
      class="row"
      style="padding-top:10px"
    >
      <div class="col-sm-12">
        <building-phone-form
          v-for="(item, index) in this.items"
          :key="item.id"
          :phone="item"
          :index="index"
          :permissions="permissions"
          @updated="update(index, $event)"
          @deleted="remove(index)"
        >
          >
        </building-phone-form>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-12">
        <!--Add Building Rental-->
        <button
          v-if="this.permissions.update"
          class="btn bg-light-blue"
          type="button"
          @click="add(new_phone)"
        >
          <i
            class="fa fa-plus"
            aria-hidden="true"
          />
          Προσθήκη
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import Collection from '../../shared/Collection.js';
import __ from '../../shared/Helpers';
import BuildingPhoneForm from './BuildingPhoneForm.vue';

export default Collection.extend({
  name: 'BuildingPhones',

  components: {
    BuildingPhoneForm,
  },

  props: {
    building: {
      type: Object,
      default: () => ({}),
    },
    related_models: {
      type: Object,
      default: () => ({}),
    },
    permissions: {
      type: Object,
      default: {
        create: false,
        read: false,
        update: false,
        delete: false,
      },
    },
  },

  data() {
    return {
      new_phone: {
        id: '',
        phone_provider_id: '1', // ΟΤΕ
        number: '',
        supply_number: '',
        has_internet: false,
        by_syzefxis: false,
        paid_centrally: false,
        keles_linked: false,
        valid_from: '',
        valid_to: '',
        building_id: this.building.id,
      },
    };
  },
});

</script>
