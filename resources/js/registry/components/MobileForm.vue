<template>
  <div>
    <!--<PERSON> Buttons (Print, Delete)-->
    <div class="row" style="padding-bottom: 0.7rem" v-if="this.assetExistsOnServer">
      <div class="col-sm-12" style="text-align: right">
        <div>
          <a class="btn btn-flat btn-info btn-outline btn-sm"
             role="button"
             @click="printout"
             v-if="this.permissions.read">
            <i class="fa fa-download"></i> Εξαγωγή PDF
          </a>&nbsp;
          <a class="btn btn-flat btn-danger btn-outline btn-sm"
             role="button"
             @click.prevent="deleteResource(onDelete)"
             v-if="this.permissions.delete">
            <i class="fa fa-times"></i> Διαγραφή Τηλεφώνου
          </a>
        </div>
      </div>
    </div>

    <form action="/registry/mobile" method="POST"
          @submit.prevent="onSubmit(onUpdate)"
          @keydown.enter.prevent
          v-bind:class="{'bg-paper': editing}"
          style="padding:5px 5px 10px 5px"
    >

      <div class="loading-overlay" v-show="loading" tabindex="-1" style="opacity: 1.1; display: block;"></div>
      <div id="loader" v-show="loading"></div>

      <input type="hidden" name="id" v-model="form.data.id" v-if="form.data.id">

      <div class="row">
        <div class="col-sm-6">
          <text-field name="number" title="Αριθμός Τηλεφωνικής Σύνδεσης" :required=true v-model="form.data.number"
                      :error="form.errors.get('number')" @valid="form.errors.clear('number')"></text-field>
        </div>

        <div class="col-sm-6">
          <select-field name="mobile_provider_id" title="Πάροχος" v-model="form.data.mobile_provider_id" :required=true
                        :options="mobile_providers" :error="form.errors.get('mobile_provider_id')"
                        @valid="form.errors.clear('mobile_provider_id')"></select-field>
        </div>
      </div>
      <div class="row">
        <div class="col-sm-5">
          <text-field name="holder" title="Κάτοχος (όχι πολιτική ηγεσία)" :required=true v-model="form.data.holder"
                      :error="form.errors.get('holder')" @valid="form.errors.clear('holder')"></text-field>
        </div>
        <div class="col-sm-3">
          <switch-field name="keles_linked" title="Σύνδεση ΚΕΛΕΣΣ" v-model="form.data.keles_linked"
                        :error="form.errors.get('keles_linked')"
                        @valid="form.errors.clear('keles_linked')"></switch-field>
        </div>
        <div class="col-sm-4">
          <switch-field name="paid_centrally" title="Πληρωμή Κεντρικά (ΓΔΟΥ)" v-model="form.data.paid_centrally"
                        :error="form.errors.get('paid_centrally')"
                        @valid="form.errors.clear('paid_centrally')"></switch-field>
        </div>

      </div>
      <div class="row">
      </div>

      <div class="row">
        <div class="col-sm-4 col-md-3">
          <date-field v-model="form.data.valid_from"
                      title="Έναρξη Χρήσης"
                      :required=true
                      name="valid_from"
                      :error="form.errors.get('valid_from')"
                      @valid="form.errors.clear('valid_from')"
          >
          </date-field>
        </div>
        <div class="col-sm-4 col-md-3">
          <date-field v-model="form.data.valid_to"
                      title="Λήξη Χρήσης"
                      name="valid_to"
                      :error="form.errors.get('valid_to')"
                      @valid="form.errors.clear('valid_to')"
          >
          </date-field>
        </div>

        <div class="col-sm-4 col-md-6" v-if="form.data.id != ''">
          <label for="unitName">Υπηρεσία</label><br>
          <input
              id="unitName"
              type="text"
              v-if="form.data.id"
              readonly="readonly"
              class="form-control"
              :value="this.form.data.unit.name"
          >
        </div>
      </div>

      <div v-if="editing" class="row">
        <div class="col-sm-3">
          <a v-if="this.assetExistsOnServer" class="btn btn-default btn-block" @click="editOff">
            <i class="fa fa-arrow-circle-left"></i> Ακύρωση
          </a>
          <a v-else class="btn btn-default btn-block" href="/registry">
            <i class="fa fa-arrow-circle-left"></i> Ακύρωση
          </a>
        </div>
        <div class="col-sm-9">
          <button class="btn btn-success btn-block" :disabled="form.errors.has()">
            <i class="fa fa-check-circle"></i> Αποθήκευση
          </button>
        </div>
      </div>
      <div v-else class="row">
        <div class="col-sm-12">
          <a class="btn btn-warning btn-block" @click.prevent="editOn" v-if="this.permissions.update">
            <i class="fa fa-edit"></i> Επεξεργασία
          </a>
        </div>
      </div>
    </form>

  </div>

</template>
<script>

import DateField from '../../shared/components/ui/FormFields/DateField.vue';
import SelectField from '../../shared/components/ui/FormFields/SelectField.vue';
import SwitchField from '../../shared/components/ui/FormFields/SwitchField.vue';
import TextField from '../../shared/components/ui/FormFields/TextField.vue';
import Form from '../../shared/Form';
import formControls from '../formControls'
import Authorization from "../mixins/Authorization";
import {formReceiveRule, formSendRule} from '../ServerDataRules';

export default {
  name: 'MobileForm',

  components: {
    TextField,
    SwitchField,
    SelectField,
    DateField,
  },

  mixins: [formControls, Authorization],

  props: {
    mobile: {
      type: Object,
      default: () => {
        return {}
      }
    },
    mobile_providers: {
      type: Array,
      default: () => []
    },
    inside_modal: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      loading: false,
      editing: true,
      endpoint: '/registry/mobile',
      permissions: {
        create: false,
        read: false,
        update: false,
        delete: false
      },
      model: this.mobile,
      form: new Form({
            id: '',
            number: '',
            mobile_provider_id: '',
            holder: '',
            keles_linked: false,
            paid_centrally: false,
            valid_from: '',
            valid_to: '',
          },
          {
            // these are the rules for incoming and outgoing data
            incoming: formReceiveRule,
            outgoing: formSendRule
          })
    }
  },

  computed: {},

  created() {
    if (this.assetExistsOnServer) {
      console.log('updating mobile model with incoming data...');
      this.form.populate(this.model);
      this.editing = false;
      this.$getModelPermissions(this, 'registry', 'mobile', this.model.id);
    }
  },

  mounted() {
    console.log('Component mounted.');
  },

  watch: {
    // whenever the mobile Model changes
    model: function (newModel, oldModel) {
      // Check if we have a new ID (after first save and get permissions for this model)
      if (newModel.id != oldModel.id) {
        this.$getModelPermissions(this, 'registry', 'mobile', newModel.id);
      }
    }
  },

  methods: {
    onDelete(data) {
      if (this.inside_modal) {
        this.$emit('deleted', this.model);
      } else {
        window.location.replace('/registry/');
      }
    },

    onUpdate(data) {
      this.model = data.mobile;
      this.form.populate(this.model);
      this.$emit('updated', this.model);
      this.editing = false;
      this.loading = false;
    }
  }
}
</script>
