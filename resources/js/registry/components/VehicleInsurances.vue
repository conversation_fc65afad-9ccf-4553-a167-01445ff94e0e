<template>
    <div>
        <div class="row" style="padding-top:20px">
            <div class="col-sm-12">
                <h5 v-show="!hasItems">Δεν έχουν καταχωρηθεί στοιχεία ασφάλισης μέχρι στιγμής</h5>
            </div>
        </div>

        <div class="row" style="padding-top:10px" v-show="hasItems">
            <div class="col-sm-12">
                <vehicle-insurance-form
                    v-for="(item, index) in this.items"
                    :vehicle_insurance="item"
                    :index="index"
                    :key="item.id"
                    :permissions="permissions"
                    @updated="update(index, $event)"
                    @deleted="remove(index)">
                >
                </vehicle-insurance-form>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <!--Add Vehicle Insurance-->
                <button class="btn bg-light-blue"
                        type="button"
                        @click="add(new_insurance)"
                        v-if="this.permissions.update"
                >
                    <i class="fa fa-plus" aria-hidden="true"></i>
                    Προσθήκη
                </button>
            </div>
        </div>
    </div>
</template>

<script>
    import Collection from '../../shared/Collection.js';
    import VehicleInsuranceForm from './VehicleInsuranceForm.vue';
    import __ from '../../shared/Helpers';

    export default Collection.extend({
        name: 'VehicleInsurances',

        components: {
            VehicleInsuranceForm,
        },

        props: {
            vehicle: {
                type: Object,
                default: () => {
                    return {}
                }
            },
            permissions: {
                type: Object,
                default: {
                    create: false,
                    read: false,
                    update: false,
                    delete: false
                }
            }
        },

        data() {
            return {
                new_insurance: {
                    id: '',
                    provider: '',
                    contract_number: '',
                    valid_from: '',
                    valid_to: '',
                    amount: '',
                    vehicle_id: this.vehicle.id
                }
            }
        },
    });

</script>
