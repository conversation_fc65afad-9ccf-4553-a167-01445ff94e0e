<template>
  <div>
    <div
      class="row"
      style="padding-top:20px"
    >
      <div class="col-sm-12">
        <h5 v-show="!hasItems">
          Δεν έχουν καταχωρηθεί στοιχεία παροχών (ηλεκτρισμού, ύδρευσης κτλ.) μέχρι στιγμής
        </h5>
      </div>
    </div>

    <div
      v-show="hasItems"
      class="row"
      style="padding-top:10px"
    >
      <div class="col-sm-12">
        <building-utility-form
          v-for="(item, index) in this.items"
          :key="item.id"
          :utility="item"
          :index="index"
          :related_models="related_models"
          :permissions="permissions"
          @updated="update(index, $event)"
          @deleted="remove(index)"
        >
          >
        </building-utility-form>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-12">
        <!--Add Building Rental-->
        <button
          v-if="this.permissions.update"
          class="btn bg-light-blue"
          type="button"
          @click="add(new_utility)"
        >
          <i
            class="fa fa-plus"
            aria-hidden="true"
          />
          Προσθήκη
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import Collection from '../../shared/Collection.js';
import __ from '../../shared/Helpers';
import BuildingUtilityForm from './BuildingUtilityForm.vue';

export default Collection.extend({
  name: 'BuildingUtilities',

  components: {
    BuildingUtilityForm,
  },

  props: {
    building: {
      type: Object,
      default: () => ({}),
    },
    related_models: {
      type: Object,
      default: () => ({}),
    },
    permissions: {
      type: Object,
      default: {
        create: false,
        read: false,
        update: false,
        delete: false,
      },
    },
  },

  data() {
    return {
      new_utility: {
        id: '',
        utility_type_id: '',
        utility_provider_id: '',
        supply_number: '',
        paid_centrally: false,
        valid_from: '',
        valid_to: '',
        building_id: this.building.id,
      },
    };
  },
});

</script>
