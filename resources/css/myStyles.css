/*
|-------------------------------------------------------------------------------
| Color pallet
|-------------------------------------------------------------------------------
|
*/
:root {
    --brand-color: hsl(191, 87%, 36.3%);
    --gray-100: hsl(204, 45.5%, 97.8%);
    --gray-200: hsl(210, 38.5%, 94.9%);
    --gray-300: hsl(214, 31.8%, 91.4%);
    --gray-400: hsl(211, 25.3%, 83.7%);
    --gray-500: hsl(214, 20.3%, 69%);
    --gray-600: hsl(216, 15%, 51.6%);
    --gray-700: hsl(218, 16.9%, 34.9%);
    --gray-800: hsl(218, 23.1%, 22.9%);
    --gray-900: hsl(220, 25.7%, 13.7%);
    --red-100: hsl(0, 100%, 98%);
    --red-200: hsl(0, 95.1%, 92%);
    --red-300: hsl(0, 97.4%, 84.7%);
    --red-400: hsl(0, 95.3%, 74.7%);
    --red-500: hsl(0, 87.8%, 67.8%);
    --red-600: hsl(0, 76.3%, 57.1%);
    --red-700: hsl(0, 60.8%, 48%);
    --red-800: hsl(0, 55.8%, 39%);
    --red-900: hsl(0, 46.8%, 31%);
    --orange-100: hsl(40, 100%, 97.1%);
    --orange-200: hsl(39, 96.4%, 89%);
    --orange-300: hsl(38, 93.2%, 76.9%);
    --orange-400: hsl(33, 89.9%, 64.9%);
    --orange-500: hsl(27, 83.6%, 57.1%);
    --orange-600: hsl(24, 74.7%, 49.6%);
    --orange-700: hsl(20, 70.7%, 44.1%);
    --orange-800: hsl(16, 65.1%, 37.1%);
    --orange-900: hsl(14, 60.8%, 30%);
    --yellow-100: hsl(60, 100%, 97.1%);
    --yellow-200: hsl(58, 96.9%, 87.3%);
    --yellow-300: hsl(55, 91.9%, 75.9%);
    --yellow-400: hsl(51, 89.4%, 66.7%);
    --yellow-500: hsl(47, 80.9%, 61%);
    --yellow-600: hsl(40, 67.2%, 51%);
    --yellow-700: hsl(36, 71%, 42%);
    --yellow-800: hsl(32, 74.6%, 33.9%);
    --yellow-900: hsl(30, 75.8%, 25.9%);
    --green-100: hsl(136, 100%, 97.1%);
    --green-200: hsl(139, 72.7%, 87.1%);
    --green-300: hsl(141, 60.3%, 75.3%);
    --green-400: hsl(143, 54.9%, 61.8%);
    --green-500: hsl(145, 45.8%, 50.8%);
    --green-600: hsl(148, 48.4%, 42.5%);
    --green-700: hsl(150, 47.8%, 35.3%);
    --green-800: hsl(152, 45.1%, 27.8%);
    --green-900: hsl(152, 42.4%, 23.1%);
    --teal-100: hsl(168, 100%, 95.1%);
    --teal-200: hsl(170, 77%, 82.9%);
    --teal-300: hsl(172, 66.9%, 70.4%);
    --teal-400: hsl(175, 58.6%, 56.5%);
    --teal-500: hsl(177, 52.1%, 45.9%);
    --teal-600: hsl(179, 51%, 39.2%);
    --teal-700: hsl(181, 47.3%, 32.7%);
    --teal-800: hsl(183, 41.6%, 26.9%);
    --teal-900: hsl(185, 40.2%, 22.9%);
    --blue-100: hsl(185, 96%, 90%);
    --blue-200: hsl(186, 94%, 82%);
    --blue-300: hsl(187, 92%, 69%);
    --blue-400: hsl(188, 86%, 53%);
    --blue-500: hsl(189, 94%, 43%);
    --blue-600: hsl(191, 87%, 36.3%);
    --blue-700: hsl(193, 82%, 31%);
    --blue-800: hsl(194, 70%, 27%);
    --blue-900: hsl(196, 64%, 24%);
    --indigo-100: hsl(213, 100%, 96.1%);
    --indigo-200: hsl(217, 96.7%, 88%);
    --indigo-300: hsl(221, 89.7%, 81%);
    --indigo-400: hsl(225, 85.5%, 72.9%);
    --indigo-500: hsl(229, 75.9%, 65.9%);
    --indigo-600: hsl(234, 61.8%, 60%);
    --indigo-700: hsl(237, 47.3%, 52.4%);
    --indigo-800: hsl(242, 37.8%, 41%);
    --indigo-900: hsl(247, 32.9%, 31.6%);
    --purple-100: hsl(270, 100%, 98%);
    --purple-200: hsl(268, 90.2%, 92%);
    --purple-300: hsl(265, 86.1%, 85.9%);
    --purple-400: hsl(262, 81.4%, 76.9%);
    --purple-500: hsl(260, 72.7%, 69.8%);
    --purple-600: hsl(259, 59.4%, 59.4%);
    --purple-700: hsl(258, 49.8%, 51.6%);
    --purple-800: hsl(256, 43.9%, 42%);
    --purple-900: hsl(254, 41%, 33.9%);
    --pink-100: hsl(348, 100%, 98%);
    --pink-200: hsl(343, 95.1%, 92%);
    --pink-300: hsl(339, 89.6%, 84.9%);
    --pink-400: hsl(336, 86%, 74.7%);
    --pink-500: hsl(331, 79.2%, 66.1%);
    --pink-600: hsl(329, 64.1%, 54.1%);
    --pink-700: hsl(325, 57.3%, 45.9%);
    --pink-800: hsl(322, 59.8%, 37.1%);
    --pink-900: hsl(318, 51.4%, 29%);
}

::selection {
    background: var(--brand-color); /* WebKit/Blink Browsers */
    color: var(--gray-100);
}

::-moz-selection {
    background: var(--brand-color); /* Gecko Browsers */
    color: var(--gray-100);
}

/*
|-------------------------------------------------------------------------------
| Login page       https://www.design-seeds.com/seasons/summer/color-creature-5/
|-------------------------------------------------------------------------------
|
| Gradient goes from gray-100 to brand-blue
|
*/

.login-page {
    background-color: #f7fafc;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25'%3E%3Cdefs%3E%3ClinearGradient id='a' gradientUnits='userSpaceOnUse' x1='0' x2='0' y1='0' y2='100%25' gradientTransform='rotate(240)'%3E%3Cstop offset='0' stop-color='%23f7fafc'/%3E%3Cstop offset='1' stop-color='%230c90ad'/%3E%3C/linearGradient%3E%3Cpattern patternUnits='userSpaceOnUse' id='b' width='540' height='450' x='0' y='0' viewBox='0 0 1080 900'%3E%3Cg fill-opacity='0.1'%3E%3Cpolygon fill='%23444' points='90 150 0 300 180 300'/%3E%3Cpolygon points='90 150 180 0 0 0'/%3E%3Cpolygon fill='%23AAA' points='270 150 360 0 180 0'/%3E%3Cpolygon fill='%23DDD' points='450 150 360 300 540 300'/%3E%3Cpolygon fill='%23999' points='450 150 540 0 360 0'/%3E%3Cpolygon points='630 150 540 300 720 300'/%3E%3Cpolygon fill='%23DDD' points='630 150 720 0 540 0'/%3E%3Cpolygon fill='%23444' points='810 150 720 300 900 300'/%3E%3Cpolygon fill='%23FFF' points='810 150 900 0 720 0'/%3E%3Cpolygon fill='%23DDD' points='990 150 900 300 1080 300'/%3E%3Cpolygon fill='%23444' points='990 150 1080 0 900 0'/%3E%3Cpolygon fill='%23DDD' points='90 450 0 600 180 600'/%3E%3Cpolygon points='90 450 180 300 0 300'/%3E%3Cpolygon fill='%23666' points='270 450 180 600 360 600'/%3E%3Cpolygon fill='%23AAA' points='270 450 360 300 180 300'/%3E%3Cpolygon fill='%23DDD' points='450 450 360 600 540 600'/%3E%3Cpolygon fill='%23999' points='450 450 540 300 360 300'/%3E%3Cpolygon fill='%23999' points='630 450 540 600 720 600'/%3E%3Cpolygon fill='%23FFF' points='630 450 720 300 540 300'/%3E%3Cpolygon points='810 450 720 600 900 600'/%3E%3Cpolygon fill='%23DDD' points='810 450 900 300 720 300'/%3E%3Cpolygon fill='%23AAA' points='990 450 900 600 1080 600'/%3E%3Cpolygon fill='%23444' points='990 450 1080 300 900 300'/%3E%3Cpolygon fill='%23222' points='90 750 0 900 180 900'/%3E%3Cpolygon points='270 750 180 900 360 900'/%3E%3Cpolygon fill='%23DDD' points='270 750 360 600 180 600'/%3E%3Cpolygon points='450 750 540 600 360 600'/%3E%3Cpolygon points='630 750 540 900 720 900'/%3E%3Cpolygon fill='%23444' points='630 750 720 600 540 600'/%3E%3Cpolygon fill='%23AAA' points='810 750 720 900 900 900'/%3E%3Cpolygon fill='%23666' points='810 750 900 600 720 600'/%3E%3Cpolygon fill='%23999' points='990 750 900 900 1080 900'/%3E%3Cpolygon fill='%23999' points='180 0 90 150 270 150'/%3E%3Cpolygon fill='%23444' points='360 0 270 150 450 150'/%3E%3Cpolygon fill='%23FFF' points='540 0 450 150 630 150'/%3E%3Cpolygon points='900 0 810 150 990 150'/%3E%3Cpolygon fill='%23222' points='0 300 -90 450 90 450'/%3E%3Cpolygon fill='%23FFF' points='0 300 90 150 -90 150'/%3E%3Cpolygon fill='%23FFF' points='180 300 90 450 270 450'/%3E%3Cpolygon fill='%23666' points='180 300 270 150 90 150'/%3E%3Cpolygon fill='%23222' points='360 300 270 450 450 450'/%3E%3Cpolygon fill='%23FFF' points='360 300 450 150 270 150'/%3E%3Cpolygon fill='%23444' points='540 300 450 450 630 450'/%3E%3Cpolygon fill='%23222' points='540 300 630 150 450 150'/%3E%3Cpolygon fill='%23AAA' points='720 300 630 450 810 450'/%3E%3Cpolygon fill='%23666' points='720 300 810 150 630 150'/%3E%3Cpolygon fill='%23FFF' points='900 300 810 450 990 450'/%3E%3Cpolygon fill='%23999' points='900 300 990 150 810 150'/%3E%3Cpolygon points='0 600 -90 750 90 750'/%3E%3Cpolygon fill='%23666' points='0 600 90 450 -90 450'/%3E%3Cpolygon fill='%23AAA' points='180 600 90 750 270 750'/%3E%3Cpolygon fill='%23444' points='180 600 270 450 90 450'/%3E%3Cpolygon fill='%23444' points='360 600 270 750 450 750'/%3E%3Cpolygon fill='%23999' points='360 600 450 450 270 450'/%3E%3Cpolygon fill='%23666' points='540 600 630 450 450 450'/%3E%3Cpolygon fill='%23222' points='720 600 630 750 810 750'/%3E%3Cpolygon fill='%23FFF' points='900 600 810 750 990 750'/%3E%3Cpolygon fill='%23222' points='900 600 990 450 810 450'/%3E%3Cpolygon fill='%23DDD' points='0 900 90 750 -90 750'/%3E%3Cpolygon fill='%23444' points='180 900 270 750 90 750'/%3E%3Cpolygon fill='%23FFF' points='360 900 450 750 270 750'/%3E%3Cpolygon fill='%23AAA' points='540 900 630 750 450 750'/%3E%3Cpolygon fill='%23FFF' points='720 900 810 750 630 750'/%3E%3Cpolygon fill='%23222' points='900 900 990 750 810 750'/%3E%3Cpolygon fill='%23222' points='1080 300 990 450 1170 450'/%3E%3Cpolygon fill='%23FFF' points='1080 300 1170 150 990 150'/%3E%3Cpolygon points='1080 600 990 750 1170 750'/%3E%3Cpolygon fill='%23666' points='1080 600 1170 450 990 450'/%3E%3Cpolygon fill='%23DDD' points='1080 900 1170 750 990 750'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect x='0' y='0' fill='url(%23a)' width='100%25' height='100%25'/%3E%3Crect x='0' y='0' fill='url(%23b)' width='100%25' height='100%25'/%3E%3C/svg%3E");
    background-attachment: fixed;
    background-size: cover;
}

.login-box {
    background: var(--gray-100);
    box-shadow: 0 22px 70px 4px rgba(0, 0, 0, 0.56);
    border-radius: 3px;
}

.login-box-header {
    padding-top: 40px;
    padding-bottom: 20px;
    text-align: center;
}

.login-box-header .logo-wrapper {
    display: flex;
    align-items: baseline;
    justify-content: center;
}

.login-box-header svg.logo {
    width: 100px;
    height: 100px;
}

.login-box-header svg.logo stop:first-child {
    stop-color: var(--brand-color); /* hsl(190, 86%, 31%); */
}

.login-box-header svg.logo stop:last-child {
    stop-color: var(--gray-700); /* hsl(32, 4%, 49%); */
}

.login-box-header .logo-title {
    font-family: Cennerik, serif;
    color: var(--gray-700);
    font-size: 2.8rem;
    font-weight: 400;
    margin: 0 0 0 -38px;
}

.login-box-header .logo-title span {
    color: var(--brand-color);
}


.login-box-header .logo-subtitle {
    font-size: 0.8rem;
    margin-bottom: 0;
    margin-top: 1rem;
    color: var(--gray-600);
    padding-inline: 3rem;
}

.login-page .alert {
    margin-bottom: 0;
    background-color: #91E0F4;
    color: #656e6f !important;
    border: none;
}

.login-box-body {
    padding-top: 20px;
    padding-bottom: 20px;
    background: inherit;
    /*border-radius: 3px;*/
}

.login-box-msg {
    color: var(--brand-color);
    font-weight: 600;
}

.login-box input {
    border-radius: 3px;
}

.login-box-body .form-control-feedback {
    color: var(--gray-300);
}

.login-box-body button {
    background-color: var(--brand-color);
    border: none;
}

.login-box-body button:hover {
    background-color: var(--brand-color);
}

.login-box-footer {
    padding-top: 20px;
    padding-bottom: 20px;
    text-align: center;
    border-top: 1px solid var(--gray-400);
}

.animation {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
}

@-webkit-keyframes flipInX {
    0% {
        -webkit-transform: perspective(400px) rotateX(90deg);
        opacity: 0;
    }
    40% {
        -webkit-transform: perspective(400px) rotateX(-10deg);
    }
    70% {
        -webkit-transform: perspective(400px) rotateX(10deg);
    }
    100% {
        -webkit-transform: perspective(400px) rotateX(0deg);
        opacity: 1;
    }
}

@keyframes flipInX {
    0% {
        transform: perspective(400px) rotateX(90deg);
        opacity: 0;
    }
    40% {
        transform: perspective(400px) rotateX(-10deg);
    }
    70% {
        transform: perspective(400px) rotateX(10deg);
    }
    100% {
        transform: perspective(400px) rotateX(0deg);
        opacity: 1;
    }
}

.flipInX {
    -webkit-backface-visibility: visible !important;
    -webkit-animation-name: flipInX;
    backface-visibility: visible !important;
    animation-name: flipInX;
}

/* Register page overrides */
.register-logo {
    margin-bottom: 0;
    margin-top: 0;
    padding: 30px 0;
}

.login-box, .register-box {
    margin: 10% auto;
}

/*
|-------------------------------------------------------------------------------
| Main header
|-------------------------------------------------------------------------------
|
| Overrides theme logo (https://stackoverflow.com/questions/7363141/css-transition-with-linear-gradient)
|
*/

nav .logo {
    display: inline-flex !important;
    gap: 4px;
    height: 50px !important; /* to bypass resizing due to media queries */
    text-align: left !important;
}

nav .logo svg {
    margin-top: 7px;
}

nav .logo svg stop {
    -webkit-transition: .2s ease;
    transition: .2s ease;
}

nav .logo svg stop:first-child {;
    stop-color: var(--brand-color);
    stop-opacity: 1;
}

nav .logo:hover svg stop:first-child {
    stop-color: var(--gray-100);
}

nav .logo svg stop:last-child {;
    stop-color: var(--gray-100);
    stop-opacity: 1;
}

nav .logo:hover svg stop:last-child {
    stop-color: var(--gray-100)
}

nav .logo p {
    display: inline-block;
    font-family: 'Cennerik-Bold', sans-serif;
    font-size: 28px;
    font-weight: 400;
    line-height: 50px;
    color: var(--gray-100);
    padding: 0 2px;
}

nav .nav > li > a {
    color: var(--gray-200);
}

nav .nav > li:hover {
    background-color: var(--brand-color);
}

nav .user-menu .dropdown-menu .user-footer .btn-default {
    background-color: var(--gray-900);
    color: var(--gray-200) !important;
}

nav .user-menu .dropdown-menu .user-footer .btn-default:hover {
    background-color: var(--brand-color);
}

/*
|-------------------------------------------------------------------------------
| Sidebar
|-------------------------------------------------------------------------------
|
*/

.main-header .sidebar-toggle::before {
    content: "\f060";
}

.sidebar-menu > li > a {
    padding: 12px 5px 12px 5px;
}

.sidebar ul.sidebar-menu li:not(.active) > a:hover {
    padding-left: 15px;
}

.sidebar ul.sidebar-menu li > a {
    -webkit-transition: padding-left .4s;
    transition: padding-left .4s;
}

.sidebar-menu .treeview-menu {
    padding-bottom: 20px;
}

.sidebar-menu .treeview-menu > li > a {
    line-height: 1.5rem;
}

/*
|-------------------------------------------------------------------------------
| Sidebar mini
|-------------------------------------------------------------------------------
|
*/

.sidebar-mini.sidebar-collapse .main-header .logo {
    width: auto;
}

.main-header .sidebar-toggle {
    color: #d6d7da !important;
    float: left;
    font-size: 14px !important;
    margin-left: 180px;
}

.main-header .sidebar-toggle:hover {
    color: #fff !important;
}

.sidebar-mini.sidebar-collapse .sidebar-toggle,
.sidebar-mini.sidebar-expanded-on-hover .sidebar-toggle {
    margin-left: 0px;
}

.sidebar-mini.sidebar-collapse .main-header .sidebar-toggle::before {
    content: "\f061";
}

.sidebar-mini.sidebar-expanded-on-hover .main-header .sidebar-toggle::before {
    content: "\f061" !important;
}

.sidebar-mini.sidebar-expanded-on-hover .content-wrapper,
.sidebar-mini.sidebar-expanded-on-hover .main-footer,
.sidebar-mini.sidebar-expanded-on-hover .right-side {
    margin-left: 50px;
}

/*
|-------------------------------------------------------------------------------
| Breadcrumbs
|-------------------------------------------------------------------------------
|
*/
.breadcrumb i {
    display: none;
    visibility: hidden;
}

.breadcrumb i.breadcrumb-home {
    display: inline-block;
    visibility: visible;
}

.content-header > .breadcrumb > li > a:hover {
    color: var(--brand-color);
}

/*
|-------------------------------------------------------------------------------
| Applications home page
|-------------------------------------------------------------------------------
|
*/
.home-section__title {
    margin-top: 2rem;
    margin-bottom: 2rem;
    font-size: 1.6rem;
    font-weight: 300;
}

.home-section__subtitle {
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--gray-600);
}

.home-section__note {
    display: block;
    margin-top: 0.5rem;
}

/*
|-------------------------------------------------------------------------------
| UI Components - Boxes
|-------------------------------------------------------------------------------
|
*/

/* Box header tooltip buttons */
.box.box-solid > .box-header .header-buttons a:hover {
    background: rgba(0, 0, 0, 0.0);
}

.box-header .box-tools {
  color: var(--brand-color);
}

.box-header .header-buttons {
    text-align: right;
}

.box-header .header-buttons [tooltip]:before {
    /* needed - do not touch */
    content: attr(tooltip);
    position: absolute;
    opacity: 0;

    /* customizable */
    margin-left: -200px;
    margin-top: -20px;
    transition: all 0.15s ease;
    padding: 10px;
    /*color: #e5e5e5;*/
    border-radius: 10px;
    box-shadow: 2px 2px 1px silver;
}

.box-header .header-buttons [tooltip]:hover:before {
    /* needed - do not touch */
    opacity: 1;

    /* customizable */
    /*background: black;*/
    margin-top: -50px;
    margin-left: -150px;
    width: 200px;
    text-align: center;
}

.box-header .header-buttons [tooltip]:not([tooltip-persistent]):before {
    pointer-events: none;
}

/* overwrite bs3 tooltips */
.box-header .header-buttons .tooltip-inner {
    padding: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    box-shadow: 2px 2px 1px silver;
}

.box-header .header-buttons .tooltip-btn {
    margin: 0 10px 0 20px;
    border: 0px solid;
    background: unset;
    border: medium none;
    display: inline;
}

.box-header .header-buttons .delete-btn {
    /* We need this, as the delete is inside a button that has default background and border */
    background: unset;
    border: medium none;
    margin: 0;
    padding: 0;
}

.box-header .header-buttons form {
    /* We need this for buttons inside a form (eg. delete) */
    display: inline;
}

/*
|-------------------------------------------------------------------------------
| UI Components - Buttons
|-------------------------------------------------------------------------------
|
*/
.btn {
    font-weight: 600;
}

/* Bootstrap Circle Buttons */
.btn-circle {
    width: 30px;
    height: 30px;
    text-align: center;
    padding: 6px 0;
    font-size: 12px;
    line-height: 1.428571429;
    border-radius: 15px;
}

.btn-circle.btn-sm {
    width: 30px;
    border-radius: 15px;
    padding: 0.357143rem 0.714286rem;
    font-size: 0.85rem;
    line-height: 1.5;
    height: auto;
}

.btn-circle.btn-xs {
    width: 22px;
    border-radius: 15px;
    padding: 0.0714286rem 0.357143rem;
    font-size: 0.85rem;
    line-height: 1.5;
    height: auto;
}

.btn-circle.btn-lg {
    width: 50px;
    height: 50px;
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.33;
    border-radius: 25px;
}

.btn-circle.btn-xl {
    width: 70px;
    height: 70px;
    padding: 10px 16px;
    font-size: 24px;
    line-height: 1.33;
    border-radius: 35px;
}

/* Button transitions (grow, rotate, wobble) */
.grow, button i.grow {
    transition: all .2s ease-in-out;
}

.grow:hover, button i.grow:hover {
    transform: scale(1.2);
}

.rotate {
    transition-duration: 0.8s;
    transition-property: transform;
    overflow: hidden;

}

.rotate:hover {
    transform: rotate(360deg);
}

.wobble:hover {
    animation: shake 0.82s cubic-bezier(.36, .07, .19, .97) both;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
}

@keyframes shake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }

    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }

    30%, 50%, 70% {
        transform: translate3d(-4px, 0, 0);
    }

    40%, 60% {
        transform: translate3d(4px, 0, 0);
    }
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
}

a.action-button,
button.action-button,
button.restore-btn {
    background: unset;
    border: none;
    margin: 0  4px;
    padding: 0;
    cursor: pointer;
    color: var(--gray-600);
}

a.action-button:hover,
button.action-button:hover,
button.restore-btn:hover {
    color: var(--brand-color);
}

a.action-button.action-button--danger:hover,
button.action-button.action-button--danger:hover {
    color: var(--red-600);
}


/*
|-------------------------------------------------------------------------------
| UI Components - Modals
|-------------------------------------------------------------------------------
|
*/
/* Use the following to make modal appear vertically centered
 and with a darker background color*/
.modal {
    text-align: center;
    background: rgba(0, 0, 0, 0.6);
}

/*@media screen and (min-width: 768px)*/
.modal:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -4px; /* Adjusts for spacing */
}

.modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle;
}

.modal-content {
    border-radius: 3px;
}

/*
|-------------------------------------------------------------------------------
| Element UI
|-------------------------------------------------------------------------------
|
*/
#app .el-collapse-item__content {
    font-size: 14px !important;
}

#app .el-input.is-disabled .el-input__inner {
    background: unset;
    border-top: unset;
    border-left: unset;
    border-right: unset;
    color: var(--gray-700);
}

.form-group.has-error .el-input__inner {
    border-color: var(--red-600);
}

/* Important rule is needed because element-ui css loads last through npm */
.el-input, label.el-switch, .el-select {
    display: block !important;
}

.horizontal .el-input,
.horizontal label.el-switch,
.horizontal .el-select,
.form-horizontal .el-input,
.form-horizontal label.el-switch,
.form-horizontal .el-select,
.form-inline .el-input,
.form-inline label.el-switch,
.form-inline .el-select {
    display: inline-block !important;
}

/* Loader Spinning */
.loading-overlay {
    background-color: rgba(0, 0, 0, 0.1);
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 10000;
}

#loader {
    position: absolute;
    left: 50%;
    top: 50%;
    height: 8vw;
    width: 8vw;
    margin: -8vw 0 0 -4vw;
    border: 2px solid transparent;
    border-right-color: #0C90AD;
    border-radius: 50%;
    z-index: 2;
    -webkit-animation: spin 2s linear infinite;
    -moz-animation: spin 2s linear infinite;
    -o-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
}

#loader:before {
    content: "";
    position: absolute;
    top: -8%;
    bottom: -8%;
    left: -8%;
    right: -8%;
    border: 2px solid transparent;
    z-index: 2;
    border-bottom-color: #64A4D9;
    border-radius: 50%;
    -webkit-animation: spin 2.75s linear infinite;
    -moz-animation: spin 2.75s linear infinite;
    -o-animation: spin 2.75s linear infinite;
    animation: spin 2.75s linear infinite;

}

#loader:after {
    content: "";
    position: absolute;
    top: 3%;
    bottom: 3%;
    left: 3%;
    right: 3%;
    border: 2px solid transparent;
    border-top-color: #64A4D9;
    z-index: 2;
    border-radius: 50%;
    -webkit-animation: spin 1.5s linear infinite;
    -moz-animation: spin 1.5s linear infinite;
    -o-animation: spin 1.5s linear infinite;
    animation: spin 1.5s linear infinite;
}

/*Keyframes for spin animation */
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg); /* IE 9 */
        transform: rotate(0deg); /* Firefox 16+, IE 10+, Opera */
    }

    50% {
        -webkit-transform: rotate(360deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg); /* IE 9 */
        transform: rotate(180deg); /* Firefox 16+, IE 10+, Opera */
    }
    100% {
        -webkit-transform: rotate(360deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg); /* IE 9 */
        transform: rotate(360deg); /* Firefox 16+, IE 10+, Opera */
    }
}

@-moz-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg); /* IE 9 */
        transform: rotate(0deg); /* Firefox 16+, IE 10+, Opera */
    }

    50% {
        -webkit-transform: rotate(360deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg); /* IE 9 */
        transform: rotate(180deg); /* Firefox 16+, IE 10+, Opera */
    }
    100% {
        -webkit-transform: rotate(360deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg); /* IE 9 */
        transform: rotate(360deg); /* Firefox 16+, IE 10+, Opera */
    }
}

@-o-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg); /* IE 9 */
        transform: rotate(0deg); /* Firefox 16+, IE 10+, Opera */
    }

    50% {
        -webkit-transform: rotate(360deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg); /* IE 9 */
        transform: rotate(180deg); /* Firefox 16+, IE 10+, Opera */
    }
    100% {
        -webkit-transform: rotate(360deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg); /* IE 9 */
        transform: rotate(360deg); /* Firefox 16+, IE 10+, Opera */
    }
}

@keyframes spin {
    0% {
        -webkit-transform: rotate(0deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg); /* IE 9 */
        transform: rotate(0deg); /* Firefox 16+, IE 10+, Opera */
    }

    50% {
        -webkit-transform: rotate(360deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg); /* IE 9 */
        transform: rotate(180deg); /* Firefox 16+, IE 10+, Opera */
    }
    100% {
        -webkit-transform: rotate(360deg); /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg); /* IE 9 */
        transform: rotate(360deg); /* Firefox 16+, IE 10+, Opera */
    }
}


/*
|-------------------------------------------------------------------------------
| Extra styles
|-------------------------------------------------------------------------------
|
*/
.vfade-enter-active, .vfade-leave-active {
    transition: opacity .5s
}

.vfade-enter, .vfade-leave-to /* .fade-leave-active in <2.1.8 */
{
    opacity: 0
}

/* IE FIX - hide objects if not IE */
.ie_only {
    display: none;
    visibility: hidden;
}

.table.dataTable.small > thead > tr > th {
    padding: 6px 22px 6px 6px;
    font-family: 'Open Sans Condensed';
}

.vertical-gap {
    margin-top: 30px;
}

.input_other {
    width: 250px;
}

/* iCheck override */
.icheckbox_square-blue, .iradio_square-blue {
    margin-left: -20px;
}

.required-asterisk {
    color: #454545;
    font-size: 0.9em;
}

.card-panel {
    border-left: 5px solid #0C90AD;
    padding: 10px 5px 5px 10px;
    margin: 0.5rem 0 1.5rem 0;
    border-radius: 2px;
    background-clip: padding-box;
    background-color: #fff;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
}

.card-panel-clear {
    padding: 10px 5px 5px 10px;
    margin: 0.5rem 0 1.5rem 0;
    border-radius: 2px;
    background-clip: padding-box;
    background-color: #fff;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
}

.small-box {
    height: 120px;
    margin: 10px 0;
}

.small-box h4,
.small-box h3,
.tiny-box h4,
.tiny-box h3 {
    font-weight: 400;
}

.tiny-box h4 {
    font-size: 1rem !important;
}

.small-box p,
.tiny-box p {
    font-weight: 300;
}

.tiny-box {
    height: 80px !important;
    margin: 0 !important;
}

.tiny-box h4,
.tiny-box h3 {
    font-weight: 400;
}

.tiny-box p {
    font-weight: 300;
}

.content .row > div .small-box,
.content .row > div .tiny-box {
    opacity: 1;
    -webkit-animation-name: fadeInOpacity;
    animation-name: fadeInOpacity;
    -webkit-animation-iteration-count: 1;
    animation-iteration-count: 1;
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    -webkit-animation-fill-mode: forwards; /* Safari 4.0 - 8.0 */
    animation-fill-mode: forwards;
}

.content .home-section__title,
.content .home-section__subtitle,
.content .home-section__note {
    display: inline-block;
    opacity: 1.0;
    animation: fadeInOpacity ease 1.1s;
}

@keyframes fadeInOpacity {
    0% {
        opacity: 0;
    }
    33% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

.content .row > div:nth-child(4n+1) .small-box {
    animation-duration: 0.41s
}

.content .row > div:nth-child(4n+2) .small-box {
    animation-duration: 0.69s
}

.content .row > div:nth-child(4n+3) .small-box {
    animation-duration: 0.92s
}

.content .row > div:nth-child(4n+4) .small-box {
    animation-duration: 1.1s
}

/* Override default color for select2 palceholders  */
.select2-container--default .select2-selection--single .select2-selection__placeholder,
.select2-container--default .select2-selection--multiple .select2-selection__placeholder,
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove,
.select2-container--default .select2-results__option[aria-disabled=true] {
    color: var(--gray-500);
}

.dt-buttons > a.btn {
    padding: 3px;
    border-radius: 3px;
    background-color: var(--gray-400) !important;
}

/*
|-------------------------------------------------------------------------------
| MISC (CHRISTMAS MODE)
|-------------------------------------------------------------------------------
|
*/

#santa-hat {
    position: absolute;
    top: -9px;
    left: 27px;
    width: 36px;
    height: 36px;
    -webkit-transform: rotate(48deg);
    transform: rotate(48deg);
}

#santa-hat-login {
    position: absolute;
    top: 16px;
    left: 129px;
    width: 85px;
    height: 85px;
    -webkit-transform: rotate(51deg);
    transform: rotate(51deg);
}

.sf-snow-flake {
    position: fixed;
    top: -20px;
    z-index: 99999;
}

.sf-snow-anim {
    top: 110%;
}

/*
|-------------------------------------------------------------------------------
| Utilities
|-------------------------------------------------------------------------------
|
*/
.max-w-prose {
    max-width: 65ch;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.btn-control {
    border-radius: 3px;
    position: relative;
    padding: 15px 5px;
    /*margin: 0 0 10px 10px;*/
    min-width: 80px;
    height: 60px;
    text-align: center;
    color: #666;
    border: 1px solid #ddd;
    background-color: white;
    font-size: 12px;
}

.btn-control.btn-control--active {
    background-color: var(--brand-color);
    color: #fff;
}

.btn-control.btn-control--active:hover {
    background-color: hsl(191, 87%, 31.3%);
    color: #fff;
}

.btn-control:active {
    box-shadow: inset 0 1px 1px var(--gray-300);
}

.btn-control:hover {
    background-color: var(--gray-300);
}

.btn-control .fa {
    font-size: 20px;
    display: block;
}

/*
Customize sweet alert
 */
.sweet-alert {
    border-radius: 3px;
    box-shadow: 0  9px 46px  8px rgba(0, 0, 0,  0.14),
                0 11px 15px -7px rgba(0, 0, 0, 0.12),
                0 24px 38px  3px rgba(0, 0, 0, 0.20);
}

.sweet-alert .sa-icon {
    margin: 10px auto;
}

.sweet-alert h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 25px 0;
    line-height: 23px;
}

.sweet-alert p {
    font-size: 1rem;
    font-weight: 400;
    color: var(--gray-800);
    text-align: center;
}

.sweet-alert button {
    font-size: 1rem;
    font-weight: 600;
    background-color: var(--brand-color) !important;
    border-radius: 3px;
    padding: 0.5rem 2.1rem;
}

.sweet-alert button:hover {
    background-color: hsl(191, 87%, 31.3%) !important;
}

.at-contractuals-contest-index {
  max-width: 800px;
  margin-inline: auto;
}
