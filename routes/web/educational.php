<?php

use App\Http\Controllers\Educational\ActionController;
use App\Http\Controllers\Educational\ActionExportController;
use App\Http\Controllers\Educational\AssessmentController;
use App\Http\Controllers\Educational\CollaboratorController;
use App\Http\Controllers\Educational\CollaboratorTypeController;
use App\Http\Controllers\Educational\ContextController;
use App\Http\Controllers\Educational\DisseminationController;
use App\Http\Controllers\Educational\DurationController;
use App\Http\Controllers\Educational\FundController;
use App\Http\Controllers\Educational\InvolvementController;
use App\Http\Controllers\Educational\LocationController;
use App\Http\Controllers\Educational\LocationTypeController;
use App\Http\Controllers\Educational\PeriodController;
use App\Http\Controllers\Educational\TargetController;
use App\Http\Controllers\Educational\TargetTypeController;
use App\Http\Controllers\Educational\ToolController;
use App\Http\Controllers\Educational\TypeController;

Route::get('/', fn () => view('educational.home'))->name('home');

Route::get('actions', [ActionController::class, 'index'])->name('actions.index');
Route::get('actions/create', [ActionController::class, 'create'])->name('actions.create');
Route::get('actions/{action}', [ActionController::class, 'show'])->name('actions.show');
Route::get('actions/{action}/edit', [ActionController::class, 'edit'])->name('actions.edit');

Route::post('action-exports', [ActionExportController::class, 'store']);

Route::middleware('permission:educational.admin')->group(function () {
    Route::resource('types', TypeController::class);

    Route::resource('involvements', InvolvementController::class);

    Route::resource('contexts', ContextController::class);

    Route::resource('periods', PeriodController::class);

    Route::resource('durations', DurationController::class);

    Route::resource('locations', LocationController::class);

    Route::resource('location-types', LocationTypeController::class);

    Route::resource('targets', TargetController::class);

    Route::resource('target-types', TargetTypeController::class);

    Route::resource('collaborators', CollaboratorController::class);

    Route::resource('collaborator-types', CollaboratorTypeController::class);

    Route::resource('tools', ToolController::class);

    Route::resource('funds', FundController::class);

    Route::resource('assessments', AssessmentController::class);

    Route::resource('disseminations', DisseminationController::class);
});
