<?php

use App\Http\Controllers\Conservations\DegreeController;
use App\Http\Controllers\Conservations\SchoolController;
use App\Http\Controllers\Conservations\MaterialController;
use App\Http\Controllers\Conservations\StatusTypeController;
use App\Http\Controllers\Conservations\ConservatorController;

Route::get('/', fn () => view('conservations.home'))->name('home');

Route::get('conservator/search', [ConservatorController::class, 'search'])->name('conservator.search');
Route::post('conservator/search', [ConservatorController::class, 'showSearchResults'])->name('conservator.search-results');
Route::get('conservator/search-by-material', [ConservatorController::class, 'searchByMaterial'])->name('conservator.searchByMaterial');
Route::post('conservator/export-by-material', [ConservatorController::class, 'exportByMaterial'])->name('conservator.exportByMaterial');
Route::post('conservator/export-by-material-region', [ConservatorController::class, 'exportByMaterialRegion'])->name('conservator.exportByMaterialRegion');
Route::get('conservator/{conservator}/export', [ConservatorController::class, 'export'])->name('conservator.export');
Route::resource('conservator', ConservatorController::class);

Route::resource('material', MaterialController::class);
Route::put('material/{material}/restore', [MaterialController::class, 'restore'])->name('material.restore')->middleware('permission:conservations.admin');

Route::resource('status-type', StatusTypeController::class);
Route::put('status-type/{statusType}/restore', [StatusTypeController::class, 'restore'])->name('status-type.restore')->middleware('permission:conservations.admin');

Route::resource('degree', DegreeController::class);
Route::put('degree/{degree}/restore', [DegreeController::class, 'restore'])->name('degree.restore')->middleware('permission:conservations.admin');

Route::resource('school', SchoolController::class);
Route::put('school/{school}/restore', [SchoolController::class, 'restore'])->name('school.restore')->middleware('permission:conservations.admin');
