<?php

use App\Http\Controllers\Personnel\UpdateController;
use App\Http\Controllers\Personnel\EmployeeController;
use App\Http\Controllers\Personnel\HrofficeController;
use App\Http\Controllers\Personnel\PositionController;
use App\Http\Controllers\Personnel\AssignmentController;
use App\Http\Controllers\Personnel\SpecializationController;

Route::get('/', fn () => View::make('personnel.home'))->name('home');

Route::any('employee', [EmployeeController::class, 'index'])->name('employee.index')->middleware(['permission:personnel.readAll']);
Route::get('employee/search', [EmployeeController::class, 'search'])->name('employee.search')->middleware(['permission:personnel.readAll']);
Route::post('employee/searchresult', [EmployeeController::class, 'searchResult'])->name('employee.searchresult')->middleware(['permission:personnel.readAll']);

// Resource routes for watching import updates from Compass
Route::resource('update', UpdateController::class)->only(['index', 'update', 'show'])->middleware(['permission:personnel.update']);

Route::post('position/searchresults', [PositionController::class, 'showSearchResults'])->name('position.searchresults');
Route::get('position/totalsBySpecialization-pdf', [PositionController::class, 'totalsBySpecializationPdf'])->name('position.totalsBySpecialization-pdf');
Route::get('position/totalsByUnit-pdf', [PositionController::class, 'totalsByUnitPdf'])->name('position.totalsByUnit-pdf');
Route::get('position/employees/{position}', [PositionController::class, 'employees'])->name('position.employees-modal');
Route::get('position/employees-pdf/{position}', [PositionController::class, 'employeesPdf'])->name('position.employees-pdf');
Route::get('position/overfilled', [PositionController::class, 'overfilled'])->name('position.overfilled')->middleware(['permission:personnel.update']);
Route::resource('position', PositionController::class)->except(['edit', 'show']);

Route::get('assignment', [AssignmentController::class, 'index'])->name('assignment.index');
Route::post('assignment/searchresults', [AssignmentController::class, 'showSearchResults'])->name('assignment.searchresults');
Route::get('assignment/export-pdf', [AssignmentController::class, 'assignmentsPDF'])->name('assignment.export-pdf');

Route::get('specialization/select_by_hroffice/{hroffice}', [SpecializationController::class, 'getSelectOptionsByHroffice']);
Route::get('specialization/select_by_occupation/{occupation}', [SpecializationController::class, 'getSelectOptionsByOccupation']);

Route::get('hroffice/select_by_occupation/{occupation}', [HrofficeController::class, 'getSelectOptionsByOccupation']);
