<?php

use App\Http\Controllers\SummerCamps\ApplicationAdminController;
use App\Http\Controllers\SummerCamps\ApplicationAttachmentZipController;
use App\Http\Controllers\SummerCamps\ApplicationController;
use App\Http\Controllers\SummerCamps\ApplicationExportController;
use App\Http\Controllers\SummerCamps\PaymentApplicationAdminController;
use App\Http\Controllers\SummerCamps\PaymentApplicationAttachmentZipController;
use App\Http\Controllers\SummerCamps\PaymentApplicationController;
use App\Http\Controllers\SummerCamps\PaymentApplicationExportController;
use App\Http\Controllers\SummerCamps\SeasonController;

Route::get('/', fn () => view('summerCamps.home'))->name('home');

//Route::resource('seasons', SeasonController::class);

Route::get('/application-attachment-zip/{application}', [ApplicationAttachmentZipController::class, 'show']);

Route::resource('applications', ApplicationController::class);

Route::get('application-exports/{application}', [ApplicationExportController::class, 'show'])
    ->name('application-export.show');

Route::middleware('permission:summerCamps.admin')
    ->get('application-admin/{season}', [ApplicationAdminController::class, 'index'])
    ->name('application-admin.index');

Route::get('/payment-application-attachment-zip/{paymentApplication}', [PaymentApplicationAttachmentZipController::class, 'show']);

Route::resource('payment-applications', PaymentApplicationController::class);

Route::get('payment-application-exports/{paymentApplication}', [PaymentApplicationExportController::class, 'show'])
    ->name('payment-application-export.show');

Route::middleware('permission:summerCamps.admin')
    ->get('payment-application-admin/{season}', [PaymentApplicationAdminController::class, 'index'])
    ->name('payment-application-admin.index');
