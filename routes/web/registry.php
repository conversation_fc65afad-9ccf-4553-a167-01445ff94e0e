<?php

use App\Http\Controllers\Registry\PhoneController;
use App\Http\Controllers\Registry\StaffController;
use App\Http\Controllers\Registry\MobileController;
use App\Http\Controllers\Registry\RentalController;
use App\Http\Controllers\Registry\UtilityController;
use App\Http\Controllers\Registry\VehicleController;
use App\Http\Controllers\Registry\BuildingController;
use App\Http\Controllers\Registry\AssociateController;
use App\Http\Controllers\Registry\UtilityTypeController;
use App\Http\Controllers\Registry\VehicleTypeController;
use App\Http\Controllers\Registry\AssociateTypeController;
use App\Http\Controllers\Registry\BuildingUsageController;
use App\Http\Controllers\Registry\OwnershipTypeController;
use App\Http\Controllers\Registry\PhoneProviderController;
use App\Http\Controllers\Registry\RentalProcessController;
use App\Http\Controllers\Registry\MobileProviderController;
use App\Http\Controllers\Registry\UtilityProviderController;
use App\Http\Controllers\Registry\VehicleInsuranceController;

Route::get('/', fn () => view('registry.home'))->name('home');

Route::get('building/search', [BuildingController::class, 'search'])->name('building.search');
Route::post('building/search', [BuildingController::class, 'showSearchResults'])->name('building.search-results');
Route::get('building/{building}/printout/{test?}', [BuildingController::class, 'printout'])->name('building.printout');
Route::resource('building', BuildingController::class);

Route::get('rental/search', [RentalController::class, 'search'])->name('rental.search');
Route::post('rental/search', [RentalController::class, 'showSearchResults'])->name('rental.search-results');
Route::resource('rental', RentalController::class);

Route::get('utility/search', [UtilityController::class, 'search'])->name('utility.search');
Route::post('utility/search', [UtilityController::class, 'showSearchResults'])->name('utility.search-results');
Route::resource('utility', UtilityController::class);

Route::get('phone/search', [PhoneController::class, 'search'])->name('phone.search');
Route::post('phone/search', [PhoneController::class, 'showSearchResults'])->name('phone.search-results');
Route::resource('phone', PhoneController::class);

Route::resource('associate', AssociateController::class);

Route::resource('staff', StaffController::class);

Route::get('mobile/search', [MobileController::class, 'search'])->name('mobile.search');
Route::post('mobile/search', [MobileController::class, 'showSearchResults'])->name('mobile.search-results');
Route::get('mobile/{mobile}/printout/{test?}', [MobileController::class, 'printout'])->name('mobile.printout');
Route::resource('mobile', MobileController::class);

Route::get('vehicle/search', [VehicleController::class, 'search'])->name('vehicle.search');
Route::post('vehicle/search', [VehicleController::class, 'showSearchResults'])->name('vehicle.search-results');
Route::get('vehicle/{vehicle}/printout/{test?}', [VehicleController::class, 'printout'])->name('vehicle.printout');
Route::resource('vehicle', VehicleController::class);

Route::get('vehicle-insurance/search', [VehicleInsuranceController::class, 'search'])->name('vehicle-insurance.search');
Route::post('vehicle-insurance/search', [VehicleInsuranceController::class, 'showSearchResults'])->name('vehicle-insurance.search-results');
Route::resource('vehicle-insurance', VehicleInsuranceController::class);

Route::middleware('permission:registry.admin')->group(function () {
    Route::resource('rental-process', RentalProcessController::class);
    Route::put('rental-process/{rentalProcess}/restore', [RentalProcessController::class, 'restore'])->name('rental-process.restore');

    Route::resource('associate-type', AssociateTypeController::class);
    Route::put('associate-type/{associateType}/restore', [AssociateTypeController::class, 'restore'])->name('associate-type.restore');

    Route::resource('building-usage', BuildingUsageController::class);
    Route::put('building-usage/{buildingUsage}/restore', [BuildingUsageController::class, 'restore'])->name('building-usage.restore');

    Route::resource('ownership-type', OwnershipTypeController::class);
    Route::put('ownership-type/{ownershipType}/restore', [OwnershipTypeController::class, 'restore'])->name('ownership-type.restore');

    Route::resource('phone-provider', PhoneProviderController::class);
    Route::put('phone-provider/{phoneProvider}/restore', [PhoneProviderController::class, 'restore'])->name('phone-provider.restore');

    Route::resource('mobile-provider', MobileProviderController::class);
    Route::put('mobile-provider/{mobileProvider}/restore', [MobileProviderController::class, 'restore'])->name('mobile-provider.restore');

    Route::resource('utility-provider', UtilityProviderController::class);
    Route::put('utility-provider/{utilityProvider}/restore', [UtilityProviderController::class, 'restore'])->name('utility-provider.restore');

    Route::resource('utility-type', UtilityTypeController::class);
    Route::put('utility-type/{utilityType}/restore', [UtilityTypeController::class, 'restore'])->name('utility-type.restore');

    Route::resource('vehicle-type', VehicleTypeController::class);
    Route::put('vehicle-type/{vehicleType}/restore', [VehicleTypeController::class, 'restore'])->name('vehicle-type.restore');
});
