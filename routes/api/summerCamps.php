<?php

use App\Http\Controllers\SummerCamps\ApplicationAdminController;
use App\Http\Controllers\SummerCamps\ApplicationAttachmentController;
use App\Http\Controllers\SummerCamps\ApplicationChildController;
use App\Http\Controllers\SummerCamps\ApplicationController;
use App\Http\Controllers\SummerCamps\PaymentApplicationAdminController;
use App\Http\Controllers\SummerCamps\PaymentApplicationAttachmentController;
use App\Http\Controllers\SummerCamps\PaymentApplicationChildController;
use App\Http\Controllers\SummerCamps\PaymentApplicationController;
use App\Http\Controllers\SummerCamps\SignedPaymentApplicationController;
use App\Http\Controllers\SummerCamps\SubmittedApplicationController;
use App\Http\Controllers\SummerCamps\SubmittedPaymentApplicationController;

// Applications
Route::prefix('applications')->name('applications.')->group(function () {
    Route::put('{application}', [ApplicationController::class, 'update'])
        ->name('update');
    Route::resource('/{application}/attachments', ApplicationAttachmentController::class)
        ->only(['index', 'store', 'destroy']);

    Route::post('{application}/application-children', [ApplicationChildController::class, 'store'])
        ->name('application-children.store');
    Route::put('{application}/application-children/{applicationChild}', [ApplicationChildController::class, 'update'])
        ->name('application-children.update');
    Route::delete('{application}/application-children/{applicationChild}', [ApplicationChildController::class, 'destroy'])
        ->name('application0children.destroy');
});

Route::middleware('permission:summerCamps.admin')
    ->get('application-admin/{season}/applications', [ApplicationAdminController::class, 'applications'])
    ->name('application-admin.applications.index');

//
//Route::resource('/submitted-applications', SubmittedApplicationController::class)
//    ->only('store', 'destroy');

Route::post('/submitted-applications/{application}', [SubmittedApplicationController::class, 'store'])
    ->name('submitted-application.store');
Route::delete('/submitted-applications/{application}', [SubmittedApplicationController::class, 'destroy'])
    ->name('submitted-application.destroy');

// Payment Applications
Route::prefix('payment-applications')->name('payment-applications.')->group(function () {
    Route::put('{paymentApplication}', [PaymentApplicationController::class, 'update'])
        ->name('update');
    Route::resource('/{paymentApplication}/attachments', PaymentApplicationAttachmentController::class)
        ->only(['index', 'store', 'destroy']);
    Route::resource('/{paymentApplication}/signed-attachments', SignedPaymentApplicationController::class)
        ->only(['index', 'store', 'destroy']);

    Route::post('{paymentApplication}/payment-application-children', [PaymentApplicationChildController::class, 'store'])
        ->name('payment-application-children.store');
    Route::put('{paymentApplication}/payment-application-children/{paymentApplicationChild}', [PaymentApplicationChildController::class, 'update'])
        ->name('payment-application-children.update');
    Route::delete('{paymentApplication}/payment-application-children/{paymentApplicationChild}', [PaymentApplicationChildController::class, 'destroy'])
        ->name('payment-application-children.destroy');
});

Route::middleware('permission:summerCamps.admin')
    ->get('application-admin/{season}/payment-applications', [PaymentApplicationAdminController::class, 'paymentApplications'])
    ->name('application-admin.payment-applications.index');

Route::post('/submitted-payment-applications/{paymentApplication}', [SubmittedPaymentApplicationController::class, 'store'])
    ->name('submitted-payment-application.store');
Route::delete('/submitted-payment-applications/{paymentApplication}', [SubmittedPaymentApplicationController::class, 'destroy'])
    ->name('submitted-payment-application.destroy');
