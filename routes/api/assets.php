<?php

use App\Http\Controllers\Assets\AssetController;
use App\Http\Controllers\Assets\ContractAssetController;
use App\Http\Controllers\Assets\ContractController;
use App\Http\Controllers\Assets\ContractListController;
use App\Http\Controllers\Assets\ContractSubmittedAssetController;
use App\Http\Controllers\Assets\ContractUnsubmittedAssetController;
use App\Http\Controllers\Assets\SubmittedAssetController;
use App\Http\Controllers\Assets\UnsubmittedAssetController;
use App\Http\Controllers\Assets\UnsubmittedAssetExportController;

// Asset API routes
Route::post('asset', [AssetController::class, 'store']);
Route::put('asset/{asset}', [AssetController::class, 'update']);
Route::patch('asset/{asset}', [AssetController::class, 'update']);
Route::delete('asset/{asset}', [AssetController::class, 'destroy']);

Route::get('unsubmitted-assets', [UnsubmittedAssetController::class, 'index']);
Route::post('unsubmitted-assets-export', [UnsubmittedAssetExportController::class, 'store']);

// Submitted asset routes (index, submission and withdrawl)
Route::get('submitted-assets', [SubmittedAssetController::class, 'index']);
Route::post('submitted-assets', [SubmittedAssetController::class, 'store']);
Route::delete('submitted-assets/{asset}', [SubmittedAssetController::class, 'destroy']);

// Contract routes
Route::post('contract', [ContractController::class, 'store'])->name('api.contract.store');
Route::put('contract/{contract}', [ContractController::class, 'update'])->name('api.contract.update');
Route::patch('contract/{contract}', [ContractController::class, 'update'])->name('api.contract.update');
Route::delete('contract/{contract}', [ContractController::class, 'destroy'])->name('api.contract.destroy');

// Contract list endpoint
Route::get('contract', [ContractListController::class, 'index'])->name('api.contract.index');

// Contract assets endpoints
Route::get('contracts/{contract}/unsubmitted-assets', [ContractUnsubmittedAssetController::class, 'index'])->name('api.contract.unsubmitted-assets.index');
Route::get('contracts/{contract}/submitted-assets', [ContractSubmittedAssetController::class, 'index'])->name('api.contract.submitted-assets.index');
Route::post('contracts/{contract}/asset', [ContractAssetController::class, 'store'])->name('api.contract.asset.store');