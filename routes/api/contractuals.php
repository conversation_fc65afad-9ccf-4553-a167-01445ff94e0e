<?php

use App\Http\Controllers\Contractuals\ApplicationController;
use App\Http\Controllers\Contractuals\ApplicationEmployedInStatusController;
use App\Http\Controllers\Contractuals\ApplicationLockedStatusController;
use App\Http\Controllers\Contractuals\ApplicationPositionCollectionController;
use App\Http\Controllers\Contractuals\ApplicationPositionController;
use App\Http\Controllers\Contractuals\AutoRatedApplicationController;
use App\Http\Controllers\Contractuals\CalculationController;
use App\Http\Controllers\Contractuals\ComputerSkillController;
use App\Http\Controllers\Contractuals\ContestController;
use App\Http\Controllers\Contractuals\ContestLockedStatusController;
use App\Http\Controllers\Contractuals\ContestRankedStatusController;
use App\Http\Controllers\Contractuals\ContestRatedStatusController;
use App\Http\Controllers\Contractuals\ContestRestrictedStatusController;
use App\Http\Controllers\Contractuals\DegreeController;
use App\Http\Controllers\Contractuals\DistributedContestController;
use App\Http\Controllers\Contractuals\DoctorateController;
use App\Http\Controllers\Contractuals\ImportedApplicationController;
use App\Http\Controllers\Contractuals\LanguageSkillController;
use App\Http\Controllers\Contractuals\PostgraduateController;
use App\Http\Controllers\Contractuals\RankedContestController;
use App\Http\Controllers\Contractuals\RankedPositionController;
use App\Http\Controllers\Contractuals\RankedPositionExportController;
use App\Http\Controllers\Contractuals\RatedApplicationController;
use App\Http\Controllers\Contractuals\UnderEvaluationApplicationController;

/*---------------------------------------------------------------------------
| Contest
|--------------------------------------------------------------------------*/

Route::delete('contests/{contest}', [ContestController::class, 'destroy']);

Route::put('contests/{contest}/locked-status', [ContestLockedStatusController::class, 'update']);

Route::put('contests/{contest}/rated-status', [ContestRatedStatusController::class, 'update']);

Route::put('contests/{contest}/restricted-status', [ContestRestrictedStatusController::class, 'update']);

Route::delete('contests/{contest}/ranked-status', [ContestRankedStatusController::class, 'destroy']);

/*---------------------------------------------------------------------------
| Ranked Contest
|--------------------------------------------------------------------------*/

Route::post('ranked-contests/', [RankedContestController::class, 'store']);

Route::post('distributed-contests/', [DistributedContestController::class, 'store']);

/*---------------------------------------------------------------------------
| Application
|--------------------------------------------------------------------------*/

Route::put('contests/{contest}/applications/{application}', [ApplicationController::class, 'update']);

Route::put('contests/{contest}/applications/{application}/locked-status', [ApplicationLockedStatusController::class, 'update'])->name('applications.LockedStatus.update');

Route::put('contests/{contest}/applications/{application}/employed-in-status', [ApplicationEmployedInStatusController::class, 'update'])->name('applications.employedIn.update');

/*---------------------------------------------------------------------------
| AutoRatedApplication
|--------------------------------------------------------------------------*/

Route::get('contests/{contest}/auto-rated-applications/', [AutoRatedApplicationController::class, 'index']);

/*---------------------------------------------------------------------------
| UnderEvaluationApplication
|--------------------------------------------------------------------------*/

Route::get('contests/{contest}/under-evaluation-applications/', [UnderEvaluationApplicationController::class, 'index']);

/*---------------------------------------------------------------------------
| Rated Application
|--------------------------------------------------------------------------*/

Route::get('contests/{contest}/rated-applications/', [RatedApplicationController::class, 'index']);
Route::post('contests/{contest}/rated-applications', [RatedApplicationController::class, 'store']);
Route::delete('contests/{contest}/rated-applications/{application}', [RatedApplicationController::class, 'destroy']);

/*---------------------------------------------------------------------------
| Imported Application
|--------------------------------------------------------------------------*/

Route::post('contests/{contest}/imported-applications', [ImportedApplicationController::class, 'store']);
/*---------------------------------------------------------------------------
| Application Position
|--------------------------------------------------------------------------*/

Route::put('contests/{contest}/applications/{application}/positions/{position}', [ApplicationPositionController::class, 'update']);
Route::patch('contests/{contest}/applications/{application}/positions', [ApplicationPositionCollectionController::class, 'update']);

/*---------------------------------------------------------------------------
| Qualifiables
|--------------------------------------------------------------------------*/

Route::post('contests/{contest}/applications/{application}/degrees', [DegreeController::class, 'store']);
Route::put('contests/{contest}/applications/{application}/degrees/{degree}', [DegreeController::class, 'update']);
Route::delete('contests/{contest}/applications/{application}/degrees/{degree}', [DegreeController::class, 'destroy']);

Route::post('contests/{contest}/applications/{application}/postgraduates', [PostgraduateController::class, 'store']);
Route::put('contests/{contest}/applications/{application}/postgraduates/{postgraduate}', [PostgraduateController::class, 'update']);
Route::delete('contests/{contest}/applications/{application}/postgraduates/{postgraduate}', [PostgraduateController::class, 'destroy']);

Route::post('contests/{contest}/applications/{application}/doctorates', [DoctorateController::class, 'store']);
Route::put('contests/{contest}/applications/{application}/doctorates/{doctorate}', [DoctorateController::class, 'update']);
Route::delete('contests/{contest}/applications/{application}/doctorates/{doctorate}', [DoctorateController::class, 'destroy']);

Route::post('contests/{contest}/applications/{application}/language-skills', [LanguageSkillController::class, 'store']);
Route::put('contests/{contest}/applications/{application}/language-skills/{languageSkill}', [LanguageSkillController::class, 'update']);
Route::delete('contests/{contest}/applications/{application}/language-skills/{languageSkill}', [LanguageSkillController::class, 'destroy']);

Route::post('contests/{contest}/applications/{application}/computer-skills', [ComputerSkillController::class, 'store']);
Route::put('contests/{contest}/applications/{application}/computer-skills/{computerSkill}', [ComputerSkillController::class, 'update']);
Route::delete('contests/{contest}/applications/{application}/computer-skills/{computerSkill}', [ComputerSkillController::class, 'destroy']);

/*---------------------------------------------------------------------------
| Calculation
|--------------------------------------------------------------------------*/

Route::get('contests/{contest}/calculations', [CalculationController::class, 'index']);

/*---------------------------------------------------------------------------
| Ranked Position
|--------------------------------------------------------------------------*/
Route::get('contests/{contest}/ranked-positions', [RankedPositionController::class, 'index']);

Route::post('contests/{contest}/ranked-position-exports', [RankedPositionExportController::class, 'store']);
