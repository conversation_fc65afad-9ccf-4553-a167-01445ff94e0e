<?php

use App\Http\Controllers\Educational\ActionAttachmentController;
use App\Http\Controllers\Educational\ActionController;
use App\Http\Controllers\Educational\ActionExportController;
use App\Http\Controllers\Educational\SubmittedActionController;
use App\Http\Controllers\Educational\UnsubmittedActionController;
use App\Models\Educational\Assessment;
use App\Models\Educational\Collaborator;
use App\Models\Educational\CollaboratorType;
use App\Models\Educational\Context;
use App\Models\Educational\Dissemination;
use App\Models\Educational\Duration;
use App\Models\Educational\Fund;
use App\Models\Educational\Involvement;
use App\Models\Educational\Location;
use App\Models\Educational\LocationType;
use App\Models\Educational\Period;
use App\Models\Educational\Target;
use App\Models\Educational\TargetType;
use App\Models\Educational\Tool;

Route::get('action-form-options', function () {
    $relatedOptions = [
        'periods' => Period::query()
            ->orderByDesc('name')
            ->get()
            ->map->only(['id', 'name']),
        'involvements' => Involvement::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name']),
        'contexts' => Context::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name']),
        'locationTypes' => LocationType::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name']),
        'locations' => Location::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name', 'location_type_id']),
        'targetTypes' => TargetType::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name']),
        'targets' => Target::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name', 'target_type_id']),
        'durations' => Duration::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name']),
        'tools' => Tool::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name']),
        'collaboratorTypes' => CollaboratorType::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name']),
        'collaborators' => Collaborator::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name', 'collaborator_type_id']),
        'funds' => Fund::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name']),
        'assessments' => Assessment::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name']),
        'disseminations' => Dissemination::query()
            ->orderBy('name')
            ->get()
            ->map->only(['id', 'name']),
    ];

    return response()->json(['data' => $relatedOptions]);
});

Route::resource('/actions/{action}/attachments', ActionAttachmentController::class)->only(['index', 'store', 'destroy']);

Route::post('actions-export', [ActionExportController::class, 'store']);

Route::post('actions', [ActionController::class, 'store']);
Route::put('actions/{action}', [ActionController::class, 'update']);
Route::delete('actions/{action}', [ActionController::class, 'destroy']);

Route::get('submitted-actions', [SubmittedActionController::class, 'index']);
Route::post('submitted-actions', [SubmittedActionController::class, 'store']);
Route::delete('submitted-actions/{action}', [SubmittedActionController::class, 'destroy']);

Route::get('unsubmitted-actions', [UnsubmittedActionController::class, 'index']);
