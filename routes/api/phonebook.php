<?php

use App\Http\Controllers\Phonebook\ContactController;
use App\Http\Controllers\Phonebook\EmployeeContactController;
use App\Http\Controllers\Phonebook\FavoriteContactController;
use App\Http\Controllers\Phonebook\ServiceContactController;

Route::get('contact', [ContactController::class, 'index'])->name('contact.index');

Route::resource('employee-contact', EmployeeContactController::class)->except('show');

Route::resource('service-contact', ServiceContactController::class)->except('index', 'create', 'show');

Route::resource('favorite-contact', FavoriteContactController::class)->only('store', 'destroy');

Route::get('public/contact', [ContactController::class, 'index'])->name('public.contact.index')
    ->withoutMiddleware('auth:api');

Route::get('public/employee-contact', [EmployeeContactController::class, 'index'])->name('public.employee-contact.index')
    ->withoutMiddleware('auth:api');
