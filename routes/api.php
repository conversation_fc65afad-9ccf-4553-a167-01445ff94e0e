<?php

use App\Http\Controllers\Admin\OrganogramController;
use App\Http\Controllers\Admin\UnitController;

Route::get('units', [UnitController::class, 'index'])->name('api.units.index')->middleware('auth:api');
Route::get('organograms', [OrganogramController::class, 'index'])->name('api.organograms.index')->middleware('auth:api');

Route::name('api.public.')
    ->prefix('public')
    ->group(__DIR__.'/api/public.php');

Route::name('api.auth.')
    ->middleware('auth:api')
    ->prefix('auth')
    ->group(__DIR__.'/api/authorization.php');

Route::name('api.phonebook.')
    ->middleware('auth:api')
    ->prefix('phonebook')
    ->group(__DIR__.'/api/phonebook.php');

Route::name('api.registry.')
    ->middleware('auth:api')
    ->prefix('registry')
    ->group(__DIR__.'/api/registry.php');

Route::name('api.contractuals.')
    ->middleware(['auth:api'])
    ->prefix('contractuals')
    ->group(__DIR__.'/api/contractuals.php');

Route::name('api.educational.')
    ->middleware('auth:api')
    ->prefix('educational')
    ->group(__DIR__.'/api/educational.php');

Route::name('api.summer-camps.')
    ->middleware('auth:api')
    ->prefix('summer-camps')
    ->group(__DIR__.'/api/summerCamps.php');

Route::name('api.assets.')
    ->middleware('auth:api')
    ->prefix('assets')
    ->group(__DIR__.'/api/assets.php');
