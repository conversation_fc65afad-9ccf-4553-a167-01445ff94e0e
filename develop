#!/bin/bash

# Function that prints the available commands...
function display_help() {
  echo "${bold}Docker development helper script${nc}"
  echo
  echo "${yellow}Usage:${nc}" >&2
  echo "  ./develop COMMAND [options] [arguments]"
  echo
  echo "Unknown commands are passed to the docker compose binary."
  echo
  echo "${yellow}Start docker services:${nc}"
  echo "  ${green}./develop up${nc}           Start the application (auto-detects OS for network config)"
  echo "  ${green}./develop up -d${nc}        Start the application in the background"
  echo "  ${green}./develop down${nc}         Stop the application"
  echo
  echo "${yellow}OS-specific behavior:${nc}"
  echo "  On Linux: Automatically includes docker-compose.external-networks.yml for external networks"
  echo "  On Windows/Mac: Uses only docker-compose.yml (no external networks)"
  echo
  echo "${yellow}Start db:${nc}"
  echo "  ${green}./develop db up ${nc}      Start the local db service in the background"
  echo "  ${green}./develop db down ${nc}    Stop the local db service"
  echo
  echo "${yellow}Build Images:${nc}"
  echo "  ${green}./develop build${nc}        Build the docker images"
  echo
  echo "${yellow}Artisan Commands:${nc}"
  echo "  ${green}./develop artisan ...${nc}  Run an Artisan command"
  echo "  ${green}./develop art ...${nc}      Alias for './develop artisan'"
  echo
  #    echo "${yellow}PHP Commands:${nc}"
  #    echo "  ${green}./develop php ...${nc}   Run a snippet of PHP code"
  #    echo
  echo "${yellow}Composer Commands:${nc}"
  echo "  ${green}./develop composer ...${nc} Run a Composer command"
  echo
  #    echo "${yellow}Node Commands:${nc}"
  #    echo "  ${green}./develop node ...${nc}         Run a Node command"
  #    echo
  echo "${yellow}NPM Commands:${nc}"
  echo "  ${green}./develop npm ...${nc}      Run a npm command"
  echo "  ${green}./develop npx ...${nc}      Run a npx command"
  echo
  echo "${yellow}Running Tests:${nc}"
  echo "  ${green}./develop test${nc}         Run the PHPUnit tests via the Artisan test command"
  echo
  echo "${yellow}Container CLI:${nc}"
  echo "  ${green}./develop shell${nc}        Start a shell session within the application container"
  echo "  ${green}./develop bash${nc}         Alias for './develop shell'"
  echo
  echo "${yellow}Executing commands:${nc}"
  echo "  ${green}./develop exec ...${nc}     Run a command on the application container..."

  exit 1
}

function detect_os() {
  case "$(uname -s)" in
    Linux*)     echo "linux";;
    Darwin*)    echo "mac";;
    CYGWIN*)    echo "windows";;
    MINGW*)     echo "windows";;
    MSYS*)      echo "windows";;
    *)          echo "unknown";;
  esac
}

function main() {
  yellow="$(tput setaf 3)"
  bold="$(tput bold)"
  yellow="$(tput setaf 3)"
  green="$(tput setaf 2)"
  nc="$(tput sgr0)"
  local compose_file="docker-compose.yml"
  local app_service="app"
  local node_service="node"
  local tty=""
  local user_id
  user_id=$(id -u)
  local group_id
  group_id=$(id -g)
  local hash
  hash=$(git rev-parse --short HEAD)
  local os
  os=$(detect_os)

  # Use a different compose file in "ci" environment
  if [ -n "${BUILD_NUMBER}" ]; then
    tty="-T"
    compose_file="docker-compose.ci.yml"
  fi

  local compose="docker compose -f $compose_file"

  # For Linux users, include the shared compose file for external networks
  if [ "$os" == "linux" ] && [ -f "docker-compose.external-networks.yml" ]; then
    echo "${yellow}Detected Linux OS: Including docker-compose.external-networks.yml for external networks${nc}"
    compose="$compose -f docker-compose.external-networks.yml"
  fi

  # Source the ".env" file so Laravel's environment variables are available...
  if [ -f ./.env ]; then
    source ./.env
  fi

  # Proxy the "help" command...
  if [ "$#" -gt 0 ]; then

    if [ "$1" == "help" ] || [ "$1" == "-h" ] || [ "$1" == "-help" ] || [ "$1" == "--help" ]; then
      display_help

    # Build the local docker images...
    elif [ "$1" == "build" ]; then
      shift 1
      COMMIT_SHA=$hash COMPOSE_DOCKER_CLI_BUILD=1 DOCKER_BUILDKIT=1 $compose build "$@"

    # Start/stop the docker services...
    elif [ "$1" == "up" ] || [ "$1" == "down" ]; then
      $compose "$@"

    # Start/stop the database service (local/testing)...
    elif [[ $1 == "db" ]]; then
      shift 1
      if [ "$1" == "start" ]; then
        docker container run -d \
          --restart always \
          --name "${DB_HOST}" \
          --network "${DOCKER_DB_NETWORK}" \
          --publish "${DOCKER_DB_PORT}:${DB_PORT}" \
          -v "${DOCKER_DB_VOLUME}:/var/lib/mysql" \
          -e MYSQL_ROOT_PASSWORD="${DB_ROOT_PASSWORD}" \
          -e MYSQL_DATABASE="${DB_DATABASE}" \
          -e MYSQL_USER="${DB_USERNAME}" \
          -e MYSQL_PASSWORD="${DB_PASSWORD}" \
          mysql:"${DOCKER_MYSQL_VERSION}"
      elif [ "$1" == "stop" ]; then
        docker container stop "${DB_HOST}"
      else
        display_help
      fi

    # Start/stop the Redis service (local/testing)...
    elif [[ $1 == "redis" ]]; then
      shift 1
      if [ "$1" == "start" ]; then
        docker container run -d \
          --restart always \
          --name "${REDIS_HOST}" \
          --network "${DOCKER_REDIS_NETWORK}" \
          --publish "${DOCKER_REDIS_PORT}:${REDIS_PORT}" \
          -v "${DOCKER_REDIS_VOLUME}:/data" \
          redis:"${DOCKER_REDIS_VERSION}" \
          redis-server --save 20 1 --loglevel warning --requirepass "${REDIS_PASSWORD}"
      elif [ "$1" == "stop" ]; then
        docker container stop "${REDIS_HOST}"
      else
        display_help
      fi

    # Proxy Composer commands to the "composer" binary on the application container...
    elif [ "$1" == "composer" ]; then
      shift 1
      $compose run --rm $tty \
        --workdir /var/www/html \
        --user "$user_id:$group_id" \
        --no-deps \
        $app_service \
        composer "$@"

    # Proxy Artisan commands to the "artisan" binary on the application container...
    elif [ "$1" == "artisan" ] || [ "$1" == "art" ]; then
      shift 1
      $compose run --rm $tty \
        --workdir /var/www/html \
        --user "$user_id:$group_id" \
        --no-deps \
        $app_service \
        php artisan "$@"

    # Proxy NPM commands to the "npm" binary on the node container...
    elif [ "$1" == "npm" ]; then
      shift 1
      $compose run --rm $tty \
        --workdir /home/<USER>/app \
        --user "$user_id:$group_id" \
        --no-deps \
        $node_service \
        npm "$@"

    # Proxy NPX commands to the "npx" binary on the node container...
    elif [ "$1" == "npx" ]; then
      shift 1
      $compose run --rm $tty \
        --workdir /home/<USER>/app \
        --user "$user_id:$group_id" \
        --no-deps \
        $node_service \
        npx "$@"

    # Proxy the "test" command to the "php artisan test" Artisan command...
    # IMPORTANT NOTICE
    # The ".env.testing" file will be used instead of the ".env" when
    # running tests, since the phpunit.xml sets APP_ENV=testing.
    elif [ "$1" == "test" ]; then
      shift 1
      $compose run --rm $tty \
        --workdir /var/www/html \
        --user "$user_id:$group_id" \
        --no-deps \
        $app_service \
        php artisan test "$@"

    # Proxy "larastan" command to the "phpstan" binary on the application container...
    elif [[ $1 == "larastan" ]]; then
      shift 1
      $compose exec $tty \
        --workdir /var/www/html \
        --user "$user_id:$group_id" \
        $app_service \
        ./vendor/bin/phpstan analyse "$@"

    # Proxy "php-cs-fixer" command to the "php-cs-fixer" binary on the application container...
    elif [[ $1 == "php-cs-fixer" ]]; then
      shift 1
      $compose exec -T \
        --workdir /var/www/html \
        --user "$user_id:$group_id" \
        $app_service \
        php -n -dmemory_limit=12G -dzend_extension=opcache.so -dopcache.enable_cli=On -dopcache.jit_buffer_size=128M ./vendor/friendsofphp/php-cs-fixer/php-cs-fixer fix "$@"

    # Initiate a Bash shell within the application container...
    elif [[ $1 == "shell" ]] || [[ $1 == "bash" ]]; then
      shift 1
      $compose exec $tty \
        --workdir /var/www/html \
        --user "$user_id:$group_id" \
        $app_service \
        bash "$@"

    # Proxy commands on the application container...
    elif [[ $1 == "exec" ]]; then
      shift 1
      $compose exec $tty \
        --workdir /var/www/html \
        --user "$user_id:$group_id" \
        $app_service \
        "$@"

    else
      display_help
    fi
  else
    display_help
  fi
}

main "$@"
