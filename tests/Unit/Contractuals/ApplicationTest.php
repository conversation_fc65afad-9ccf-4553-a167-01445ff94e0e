<?php

namespace Tests\Unit\Contractuals;

use App\Models\Contractuals\Application;
use App\Models\Contractuals\Evaluation;
use App\Models\Contractuals\Position;
use Carbon\Carbon;
use Database\CustomFactories\Contractuals\ApplicantFactory;
use Database\CustomFactories\Contractuals\ApplicationFactory;
use Database\CustomFactories\Contractuals\ContestFactory;
use DMS\PHPUnitExtensions\ArraySubset\Assert as PHPUnit9;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Assert;
use Tests\ContractualsTestCase;
use Tests\Helpers\Contractuals\EvaluationMappings;

class ApplicationTest extends ContractualsTestCase
{
    use EvaluationMappings, RefreshDatabase;

    protected $contest;

    protected function setUp() : void
    {
        parent::setUp();

        $this->contest = ContestFactory::create([
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'High Degree A',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'High Experience A',
                                'requirement_type' => 'experience',
                            ],
                        ],
                        1 => [
                            [
                                'name' => 'Low Degree A',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'Low Experience A',
                                'requirement_type' => 'experience',
                            ],
                            [
                                'name' => 'Low ECDL A',
                                'requirement_type' => 'computer_skills',
                            ],
                        ],
                    ],
                ],
                [
                    'amount' => 3,
                    'location' => 'Thessaloniki',
                    'specialization' => 'PE',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'High Degree B',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'High Experience B',
                                'requirement_type' => 'experience',
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        Collection::macro('assertContains', function ($model) {
            Assert::assertTrue($this->contains($model), "Failed to assert that \n $this \n contains \n $model");
        });

        Collection::macro('assertNotContain', function ($model) {
            Assert::assertNotTrue($this->contains($model), "Failed to assert that the collection contains the $model");
        });
    }

    /** @test */
    public function can_add_assets_as_qualifications()
    {
        $applicant = ApplicantFactory::create([
            'degrees' => [
                [
                    'name' => 'BSc in Archaeology',
                    'issuer' => 'University of Athens',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'postgraduates' => [
                [
                    'name' => 'MSc in field Archaeology',
                    'issuer' => 'NTUA',
                    'mark' => '7.5',
                    'year' => '2007',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Company A',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Field Archaeologist',
                    'issuer' => 'Company B',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);
        $application = ApplicationFactory::createForContest($this->contest, [
            'applicant_id' => $applicant->id,
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
        ]);
        $this->assertCount(0, $application->qualifications);

        $degreesQualifications = $application->setQualifications('degrees', $applicant->degrees);
        $experiencesQualifications = $application->setQualifications('experiences', $applicant->experiences);

        $this->assertCount(3, $application->fresh()->qualifications);
        // PHPUnit9::assertArraySubset($applicant->fresh()->degrees->toArray(), $application->degrees->toArray());
        // PHPUnit9::assertArraySubset($applicant->fresh()->experiences->toArray(), $application->experiences->toArray());

        $degreeAssets = $degreesQualifications->map(function ($qualification) {
            return $qualification->qualifiable;
        });
        $degreeAssets->assertContains($applicant->degrees()->first());

        $experienceAssets = $experiencesQualifications->map(function ($qualification) {
            return $qualification->qualifiable;
        });
        $this->assertContains($applicant->experiences()->get()->first()->toArray(), $experienceAssets->toArray());
        $this->assertContains($applicant->experiences()->get()->last()->toArray(), $experienceAssets->toArray());
    }

    /** @test */
    public function can_update_its_qualifications()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in Archaeology',
                    'issuer' => 'University of Athens',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'postgraduates' => [
                [
                    'name' => 'MSc in Archaeology',
                    'issuer' => 'NTUA',
                    'mark' => '7.5',
                    'year' => '2007',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Old Company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Field Archaeologist',
                    'issuer' => 'Company B',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);

        $applicant = $application->applicant;

        $this->assertCount(1, $applicant->fresh()->getAssets('degrees'));
        $this->assertCount(2, $applicant->fresh()->getAssets('experiences'));
        $this->assertCount(4, $application->fresh()->qualifications);

        // We update the assets
        $applicant->addAssets('degrees', [
            [
                'id' => '',
                'name' => 'BSc in Electronics Engineering',
                'issuer' => 'TEI of Athens',
                'mark' => '7.9',
                'year' => '2005',
                'verified' => '',
            ],
        ]);

        $applicant->updateAssets('experiences', [
            [
                'id' => $applicant->experiences()->where('issuer', 'Old Company')->first()->id,
                'name' => 'Archaeologist',
                'issuer' => 'New Company',
                'started_at' => '2017-01-01',
                'ended_at' => '2017-06-30',
            ],
            [
                'id' => '',
                'name' => 'New Experience',
                'issuer' => 'Other New Company',
                'started_at' => '2017-07-01',
                'ended_at' => '2017-12-31',
            ],
        ]);

        $this->assertCount(2, $applicant->fresh()->getAssets('degrees'));
        $this->assertCount(3, $applicant->fresh()->getAssets('experiences'));
        $this->assertCount(4, $application->qualifications);

        $degreesQualifications = $application->updateQualifications('degrees', $applicant->fresh()->getAssets('degrees'));
        $experiencesQualifications = $application->updateQualifications('experiences', $applicant->fresh()->getAssets('experiences'));

        $this->assertCount(6, $application->fresh()->qualifications);
        $this->assertCount(2, $application->fresh()->degrees);
        $this->assertCount(3, $application->fresh()->experiences);
        // PHPUnit9::assertArraySubset($applicant->fresh()->degrees->toArray(), $application->fresh()->degrees->toArray());
        // PHPUnit9::assertArraySubset($applicant->fresh()->experiences->toArray(), $application->fresh()->experiences->toArray());

        $degreeAssets = $degreesQualifications->map(function ($qualification) {
            return $qualification->qualifiable;
        });

        $experienceAssets = $experiencesQualifications->map(function ($qualification) {
            return $qualification->qualifiable;
        });
        $this->assertEquals($applicant->fresh()->degrees->toArray(), $degreeAssets->toArray());
        $this->assertEquals($applicant->fresh()->experiences->toArray(), $experienceAssets->toArray());
    }

    /** @test */
    public function can_remove_a_qualification()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in Archaeology',
                    'issuer' => 'University of Athens',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'postgraduates' => [
                [
                    'name' => 'MSc in Archaeology',
                    'issuer' => 'NTUA',
                    'mark' => '7.5',
                    'year' => '2007',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Old Company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Field Archaeologist',
                    'issuer' => 'Company B',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);

        $this->assertCount(4, $application->fresh()->qualifications);

        $assetToBeDeleted = $application->fresh()->experiences->first();
        $application->removeQualification($assetToBeDeleted->type_slug, $assetToBeDeleted->id);

        $this->assertCount(3, $application->fresh()->qualifications);
        $qualifiedAssets = $application->fresh()->qualifications->map(function ($qualification) {
            return $qualification->qualifiable;
        });
        $qualifiedAssets->assertNotContain($assetToBeDeleted);
    }

    /** @test */
    public function can_add_its_positions()
    {
        $application = factory(Application::class)->create();
        $this->assertCount(0, $application->positions);

        $positionsData = $this->contest->positions->map(function ($position, $key) {
            return ['id' => $position->id, 'order' => $key + 1];
        })->toArray(); // [['id' => '1', 'order' => '1'],['id' => '2', 'order' => '2']]

        $positions = $application->setPositions($positionsData);

        $this->assertCount(2, $application->fresh()->positions);
        // PHPUnit9::assertArraySubset(Position::all()->toArray(), $positions->toArray());
    }

    /** @test */
    public function can_update_its_positions()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
        ]);
        $this->assertCount(1, $application->positions);

        $this->assertEquals(1, $application->positions()->find($this->contest->positions->first()->id)->pivot->order);

        // Simulate user adding a new position as her first choice,
        // and moving the previously selected position to the second place
        $newPositionsData = $this->contest->positions->sortByDesc('id')->values()->all();

        $updatedPositions = $application->updatePositions($newPositionsData);

        $this->assertCount(2, $application->fresh()->positions);
        $this->assertEquals(2, $application->fresh()->positions()->find($this->contest->positions->first()->id)->pivot->order);
        $this->assertEquals(1, $application->fresh()->positions()->find($this->contest->positions->last()->id)->pivot->order);
    }

    /** @test */
    public function can_initialize_its_evaluations()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
                ['id' => $this->contest->positions->last()->id, 'order' => 2],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in Archaeology',
                    'issuer' => 'University of Athens',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Old Company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Field Archaeologist',
                    'issuer' => 'Company B',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);

        $this->assertCount(0, $application->evaluations);

        $evaluations = $application->initializeEvaluations();

        $this->assertCount(6, $application->fresh()->evaluations);
        $this->assertEquals(Evaluation::all()->toArray(), $evaluations->toArray());
    }

    /** @test */
    public function can_clear_its_evaluations()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
                ['id' => $this->contest->positions->last()->id, 'order' => 2],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in Archaeology',
                    'issuer' => 'University of Athens',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Old Company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Field Archaeologist',
                    'issuer' => 'Company B',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);

        $this->assertCount(0, $application->evaluations);

        $application->initializeEvaluations();

        $this->assertCount(6, $application->fresh()->evaluations);

        $application->clearEvaluations();

        $this->assertCount(0, $application->evaluations);
    }

    /** @test */
    public function can_check_if_it_meets_global_contest_requirements()
    {
        $applicationA = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'eu_citizen' => false,
            'greek_nationality' => false,
        ]);
        $applicationB = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'healthy' => false,
        ]);
        $applicationC = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'unrestricted' => true,
        ]);
        $applicationD = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'invalidated' => true,
        ]);

        $this->assertFalse($applicationA->meetsGlobalRequirements());
        $this->assertFalse($applicationB->meetsGlobalRequirements());
        $this->assertFalse($applicationC->meetsGlobalRequirements());
        $this->assertFalse($applicationD->meetsGlobalRequirements());
    }

    /** @test */
    public function can_check_if_it_meets_position_requirements()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'healthy' => true,
            'unrestricted' => false,
            'invalidated' => false,
            'eu_citizen' => true,
            'greek_nationality' => true,
            'birthdate' => Carbon::now()->subYears(30),
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
                ['id' => $this->contest->positions->last()->id, 'order' => 2],
            ], 'degrees' => [
                [
                    'name' => 'Degree A',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Experience A',
                ],
                [
                    'name' => 'Experience B',
                ],
            ],
        ]);

        $application->initializeEvaluations();

        // POSITION A EVALUATIONS
        $positionA = $this->contest->positions->find($this->contest->positions->first()->id);

        $evaluationAA = $this->findEvaluationByName('Degree A', $positionA);
        $evaluationAB = $this->findEvaluationByName('Experience A', $positionA);
        $evaluationAC = $this->findEvaluationByName('Experience B', $positionA);

        $requirementAA = $this->findRequirement('Low Degree A', $positionA);
        $requirementAB = $this->findRequirement('Low Experience A', $positionA);
        $requirementAC = $this->findRequirement('Low ECDL A', $positionA);

        $this->mapEvaluations([
            ['evaluation' => $evaluationAA, 'requirement' => $requirementAA, 'relevant' => null],
            ['evaluation' => $evaluationAB, 'requirement' => $requirementAB, 'relevant' => null],
            ['evaluation' => $evaluationAC, 'requirement' => $requirementAB, 'relevant' => null],
        ], $application); // invalid

        // POSITION B EVALUATIONS
        $positionB = $this->contest->positions->find($this->contest->positions->last()->id);

        $evaluationBA = $this->findEvaluationByName('Degree A', $positionB);
        $evaluationBB = $this->findEvaluationByName('Experience A', $positionB);
        $evaluationBC = $this->findEvaluationByName('Experience B', $positionB);

        $requirementBA = $this->findRequirement('High Degree B', $positionB);
        $requirementBB = $this->findRequirement('High Experience B', $positionB);

        $this->mapEvaluations([
            ['evaluation' => $evaluationBA, 'requirement' => $requirementBA, 'relevant' => null],
            ['evaluation' => $evaluationBB, 'requirement' => $requirementBB, 'relevant' => null],
            ['evaluation' => $evaluationBC, 'requirement' => $requirementBB, 'relevant' => null],
        ], $application);

        $this->assertFalse($application->meetsPositionRequirements($positionA));
        $this->assertTrue($application->meetsPositionRequirements($positionB));
    }
}
