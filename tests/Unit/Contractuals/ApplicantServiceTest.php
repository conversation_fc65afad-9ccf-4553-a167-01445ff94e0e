<?php

namespace Tests\Unit\Contractuals;

use App\Models\Contractuals\Applicant;
use App\Models\Contractuals\Degree;
use App\Models\Contractuals\Language;
use App\Models\Contractuals\LanguageLevel;
use App\Services\Contractuals\ApplicantFormService;
use Database\CustomFactories\Contractuals\ApplicantFactory;
use DMS\PHPUnitExtensions\ArraySubset\Assert as PHPUnit9;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\ContractualsTestCase;

class ApplicantServiceTest extends ContractualsTestCase
{
    use RefreshDatabase;
    protected $language;
    protected $languageLevel;

    protected function setUp() : void
    {
        parent::setUp();

        $this->language = factory(Language::class)->create();
        $this->languageLevel = factory(LanguageLevel::class)->create();
    }

    /** @test */
    public function can_create_an_applicant_and_its_assets()
    {
        $applicantService = new ApplicantFormService();

        $this->assertCount(0, Applicant::all());

        $applicantData = $applicantService->store($this->validParams([
            'degrees' => [
                [
                    'id' => '',
                    'name' => 'Degree A',
                    'issuer' => 'University A',
                    'mark' => '8.20',
                    'year' => '2005',
                    'verified' => false,
                ],
                [
                    'id' => '',
                    'name' => 'Degree B',
                    'issuer' => 'University B',
                    'mark' => '7.50',
                    'year' => '2006',
                    'verified' => false,
                ],
            ],
            'postgraduates' => [
                [
                    'id' => '',
                    'name' => 'Postgraduate A',
                    'issuer' => 'University A',
                    'year' => '2007',
                    'verified' => false,
                ],
            ],
            'doctorates' => [
                [
                    'id' => '',
                    'name' => 'Doctorate Α',
                    'issuer' => 'University A',
                    'year' => '2008',
                    'verified' => false,
                ],
            ],
            'greek_languages' => [
                [
                    'id' => '',
                    'name' => 'Greek Knowledge',
                    'issuer' => 'Ministry of Education',
                    'level' => 'A',
                    'verified' => false,
                ],
            ],
            'language_skills' => [
                [
                    'id' => '',
                    'name' => 'Proficiency',
                    'issuer' => 'British Council',
                    'language_id' => $this->language->id,
                    'language_level_id' => $this->languageLevel->id,
                    'verified' => false,
                ],
            ],
            'computer_skills' => [
                [
                    'id' => '',
                    'name' => 'ECDL',
                    'verified' => false,
                    'has_attachments' => false,
                    'media' => [],
                ],
            ],
            'experiences' => [
                [
                    'id' => '',
                    'name' => 'Job A',
                    'issuer' => 'Company A',
                    'months' => 6,
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                    'verified' => false,
                    'has_attachments' => false,
                    'media' => [],
                ],
                [
                    'id' => '',
                    'name' => 'Job B',
                    'issuer' => 'Company B',
                    'months' => 6,
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                    'verified' => false,
                    'has_attachments' => false,
                    'media' => [],
                ],
            ],
            'unemployments' => [
                [
                    'id' => '',
                    'months' => 2,
                    'verified' => false,
                ],
            ],
            'multi_child_families' => [
                [
                    'id' => '',
                    'children' => 5,
                    'siblings' => 6,
                    'verified' => false,
                ],
            ],
            'three_child_families' => [
                [
                    'id' => '',
                    'children' => true,
                    'siblings' => true,
                    'verified' => false,
                ],
            ],
            'single_parent_families' => [
                [
                    'children' => 5,
                    'siblings' => 6,
                    'verified' => false,
                ],
            ],
            'minors' => [
                [
                    'id' => '',
                    'amount' => 2,
                    'verified' => false,
                ],
            ],
            'disabilities' => [
                [
                    'id' => '',
                    'percentage' => 60,
                    'verified' => false,
                ],
            ],
            'family_disabilities' => [
                [
                    'id' => '',
                    'percentage' => 65,
                    'verified' => false,
                ],
            ],
        ]));

        $this->assertCount(1, Applicant::all());
        $this->assertCount(2, Applicant::first()->degrees);
        $this->assertCount(1, Applicant::first()->postgraduates);
        $this->assertCount(1, Applicant::first()->doctorates);
        $this->assertCount(1, Applicant::first()->greekLanguages);
        $this->assertCount(1, Applicant::first()->languageSkills);
        $this->assertCount(1, Applicant::first()->computerSkills);
        $this->assertCount(2, Applicant::first()->experiences);
        $this->assertCount(1, Applicant::first()->unemployments);
        $this->assertCount(1, Applicant::first()->multiChildFamilies);
        $this->assertCount(1, Applicant::first()->threeChildFamilies);
        $this->assertCount(1, Applicant::first()->singleParentFamilies);
        $this->assertCount(1, Applicant::first()->minors);
        $this->assertCount(1, Applicant::first()->disabilities);
        $this->assertCount(1, Applicant::first()->familyDisabilities);
        $this->assertEquals($this->validParams()['name'], $applicantData['applicant']->name);
        // PHPUnit9::assertArraySubset($applicantData['assets']['degrees']->toArray(), Degree::all()->toArray());
    }

    /** @test */
    public function can_update_an_applicant_and_its_assets()
    {
        $applicant = ApplicantFactory::create([
            'degrees' => [
                [
                    'name' => 'Old Degree',
                    'issuer' => 'University of Athens',
                    'mark' => '8.25',
                    'year' => '2005',
                ],
            ],
            'postgraduates' => [
                [
                    'name' => 'Old Postgraduate',
                    'issuer' => 'NTUA',
                    'mark' => '7.50',
                    'year' => '2007',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Old Experience A',
                    'issuer' => 'Company Α',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Old Experience B',
                    'issuer' => 'Company B',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);
        $this->assertCount(1, Applicant::all());
        $this->assertCount(1, $applicant->degrees);
        $this->assertCount(1, $applicant->postgraduates);
        $this->assertCount(2, $applicant->experiences);

        $applicantService = new ApplicantFormService();

        $updatedApplicantData = $applicantService->update($applicant, $this->validParams([
            'name' => 'NewName',
            'degrees' => [
                [
                    'id' => $applicant->degrees->first()->id,
                    'name' => 'New Degree',
                    'issuer' => 'UoA',
                    'mark' => '5.00',
                    'year' => '2000',
                    'verified' => false,
                ],
                [
                    'id' => '',
                    'name' => 'Brand New Degree',
                    'issuer' => 'NTUA',
                    'mark' => '8.00',
                    'year' => '2005',
                    'verified' => false,
                ],
            ],
            'postgraduates' => [
                [
                    'id' => $applicant->postgraduates->first()->id,
                    'name' => 'New Postgraduate',
                    'issuer' => 'NTUA',
                    'year' => '2007',
                ],
            ],
            'experiences' => [
                [
                    'id' => $applicant->experiences->last()->id,
                    'name' => 'New Experience B',
                    'months' => 6,
                    'issuer' => 'Company B',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
            'unemployments' => [
                [
                    'id' => '',
                    'months' => 2,
                    'verified' => false,
                ],
            ],
            'multi_child_families' => [
                [
                    'id' => '',
                    'children' => 5,
                    'siblings' => 6,
                    'verified' => false,
                ],
            ],
        ]));

        $this->assertCount(1, Applicant::all());
        $this->assertEquals('NewName', $applicant->fresh()->name);
        $this->assertCount(2, $applicant->fresh()->degrees);
        $this->assertEquals('New Degree', $applicant->fresh()->degrees->first()->name);
        $this->assertEquals('Brand New Degree', $applicant->fresh()->degrees->last()->name);
        $this->assertCount(1, $applicant->fresh()->postgraduates);
        $this->assertEquals('New Postgraduate', $applicant->fresh()->postgraduates->first()->name);
        $this->assertCount(0, $applicant->fresh()->doctorates);
        $this->assertCount(2, $applicant->fresh()->experiences);
        $this->assertEquals('Old Experience A', $applicant->fresh()->experiences->first()->name);
        $this->assertEquals('New Experience B', $applicant->fresh()->experiences->last()->name);
        $this->assertCount(0, $applicant->fresh()->languageSkills);
        $this->assertCount(0, $applicant->fresh()->computerSkills);
        $this->assertCount(0, $applicant->fresh()->greekLanguages);
        $this->assertCount(0, $applicant->fresh()->minors);
        $this->assertCount(1, $applicant->fresh()->unemployments);
        $this->assertCount(1, $applicant->fresh()->multiChildFamilies);
        $this->assertEquals('NewName', $updatedApplicantData['applicant']->name);
        // PHPUnit9::assertArraySubset($updatedApplicantData['assets']['degrees'][0]->toArray(), Degree::all()->toArray()[0]);
        // PHPUnit9::assertArraySubset($updatedApplicantData['assets']['degrees'][1]->toArray(), Degree::all()->toArray()[1]);
    }

    private function validParams($overrides = [])
    {
        return array_merge([
            'name' => 'John',
            'surname' => 'Doe',
            'fathername' => 'Jack',
            'mothername' => 'Jane',
            'birthdate' => '1980-02-01',
            'policeid_number' => 'AB123456',
            'policeid_date' => '2000-03-25',
            'afm' => '0123456789',
            'doy' => 'IG Athens',
            'amka' => '20028012345',
            'street' => 'Example str.',
            'street_number' => '23',
            'postcode' => '12345',
            'city' => 'Athens',
            'phonenumber1' => '2101234567',
            'phonenumber2' => '2131234567',
            'email' => '<EMAIL>',
            'eu_citizen' => true,
            'greek_nationality' => true,
            'degrees' => [],
            'postgraduates' => [],
            'doctorates' => [],
            'experiences' => [],
            'language_skills' => [],
            'computer_skills' => [],
            'greek_languages' => [],
            'unemployments' => [],
            'multi_child_families' => [],
            'three_child_families' => [],
            'single_parent_families' => [],
            'disabilities' => [],
            'family_disabilities' => [],
            'minors' => [],
        ], $overrides);
    }
}
