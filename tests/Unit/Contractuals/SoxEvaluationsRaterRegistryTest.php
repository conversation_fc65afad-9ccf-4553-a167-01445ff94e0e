<?php

namespace Tests\Unit\Contractuals;

use App\Services\Contractuals\EvaluationRaters\Sox2190EvaluationsRaterRegistry;
use Tests\TestCase;

class SoxEvaluationsRaterRegistryTest extends TestCase
{
    /** @test */
    public function loads_all_required_sox_raters()
    {
        $soxRaterRegistry = resolve(Sox2190EvaluationsRaterRegistry::class);

        $this->assertEquals('DegreesSoxEvaluationsRater', class_basename($soxRaterRegistry->pull('degrees')));
        $this->assertEquals('ExperiencesSoxEvaluationsRater', class_basename($soxRaterRegistry->pull('experiences')));
        $this->assertEquals('UnemploymentsSoxEvaluationsRater', class_basename($soxRaterRegistry->pull('unemployments')));
        $this->assertEquals('MultiChildFamiliesSoxEvaluationsRater', class_basename($soxRaterRegistry->pull('multi_child_families')));
        $this->assertEquals('ThreeChildFamiliesSoxEvaluationsRater', class_basename($soxRaterRegistry->pull('three_child_families')));
        $this->assertEquals('SingleParentFamiliesSoxEvaluationsRater', class_basename($soxRaterRegistry->pull('single_parent_families')));
        $this->assertEquals('MinorsSoxEvaluationsRater', class_basename($soxRaterRegistry->pull('minors')));
        $this->assertEquals('DisabilitiesSoxEvaluationsRater', class_basename($soxRaterRegistry->pull('disabilities')));
        $this->assertEquals('FamilyDisabilitiesSoxEvaluationsRater', class_basename($soxRaterRegistry->pull('family_disabilities')));
    }
}
