<?php

namespace Tests\Unit\Contractuals;

use App\Services\Contractuals\EvaluationFormService;
use Database\CustomFactories\Contractuals\ApplicationFactory;
use Database\CustomFactories\Contractuals\ContestFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\ContractualsTestCase;

class EvaluationServiceTest extends ContractualsTestCase
{
    use RefreshDatabase;

    protected $contest;

    public function setUp() : void
    {
        parent::setUp();

        $this->contest = ContestFactory::create([
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE Αρχαιολόγων',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'Βεβαίωση σπουδών ή αναλυτική βαθμολογία',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'Προυπηρεσία στην Βυζαντινολογία',
                                'requirement_type' => 'experience',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /** @test */
    public function can_update_the_evaluations()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);
        $application->initializeEvaluations();
        $this->assertTrue($application->hasEvaluations());
        $this->assertEquals(0, $application->evaluations->first()->relevant);
        $this->assertNull($application->evaluations->first()->requirement_id);

        // we simulate the user request by changing some evaluations...
        $evaluationsData = $application->evaluations->toArray();
        $evaluationsData[0]['relevant'] = 1;
        $evaluationsData[1]['requirement_id'] = $this->contest->positions->first()->requirements->first()->id;

        (new EvaluationFormService())->update($evaluationsData, $application);

        $this->assertEquals($evaluationsData, $application->fresh()->evaluations->toArray());
    }

    /** @test */
    public function can_update_application_position_auxiliary_level()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);
        $application->initializeEvaluations();
        $this->assertTrue($application->hasEvaluations());
        $this->assertEquals(0, $application->evaluations->first()->relevant);
        $this->assertNull($application->evaluations->first()->requirement_id);
        $this->assertEquals(0, $application->getAuxiliaryLevel($application->positions()->first()));

        // we simulate the user request by changing some evaluations...
        $positionAuxiliaryLevel = 1;
        $evaluationsData = $application->evaluations->toArray();
        $evaluationsData[0]['relevant'] = 1;
        $evaluationsData[0]['auxiliary_level'] = $positionAuxiliaryLevel;
        $evaluationsData[1]['requirement_id'] = $this->contest->positions->first()->requirements->first()->id;
        $evaluationsData[1]['auxiliary_level'] = $positionAuxiliaryLevel;

        (new EvaluationFormService())->update($evaluationsData, $application);

        $this->assertEquals($evaluationsData, $application->fresh()->evaluations->toArray());
        $this->assertSame($positionAuxiliaryLevel, $application->fresh()->getAuxiliaryLevel($application->positions()->first()));
    }
}
