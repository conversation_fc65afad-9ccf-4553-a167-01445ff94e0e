<?php

namespace Tests\Unit\Contractuals;

use App\Services\Contractuals\EvaluationRaters\Sox2190\MinorsEvaluationsRater;
use Database\CustomFactories\Contractuals\ApplicationFactory;
use Database\CustomFactories\Contractuals\ContestFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\ContractualsTestCase;
use Tests\Helpers\Contractuals\EvaluationMappings;

class MinorsSoxRaterTest extends ContractualsTestCase
{
    use EvaluationMappings, RefreshDatabase;

    private $contest;

    protected function setUp() : void
    {
        parent::setUp();

        $this->contest = ContestFactory::create([
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'Experience A',
                                'requirement_type' => 'experiences',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /** @test */
    public function calculates_and_updates_the_evaluation_points_by_giving_30_points_for_each_of_the_first_two_children()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'minors' => [
                [
                    'amount' => 2,
                ],
            ],
        ]);

        $application->initializeEvaluations();

        $positionA = $this->contest->positions->first();

        $evaluation = $this->findEvaluationByType('minors', $positionA)->first();

        $singleParentFamiliesSoxRater = new MinorsEvaluationsRater();
        $singleParentFamiliesSoxRater->rate($application->evaluations, 'ANY_SPECIALIZATION', 0);

        $this->assertEquals(60, $evaluation->fresh()->points);
    }

    /** @test */
    public function calculates_and_updates_the_evaluation_points_by_giving_50_extra_points_for_the_third_child()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'minors' => [
                [
                    'amount' => 4,
                ],
            ],
        ]);

        $application->initializeEvaluations();

        $positionA = $this->contest->positions->first();

        $evaluation = $this->findEvaluationByType('minors', $positionA)->first();

        $singleParentFamiliesSoxRater = new MinorsEvaluationsRater();
        $singleParentFamiliesSoxRater->rate($application->evaluations, 'ANY_SPECIALIZATION', 0);

        $this->assertEquals(110, $evaluation->fresh()->points);
    }
}
