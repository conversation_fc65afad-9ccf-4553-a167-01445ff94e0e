<?php

namespace Tests\Unit\Contractuals;

use App\Models\Contractuals\Applicant;
use App\Models\Contractuals\Degree;
use App\Models\Contractuals\Experience;
use App\Models\Contractuals\Minor;
use App\Models\Contractuals\Postgraduate;
use App\Models\Contractuals\SingleParentFamily;
use Database\CustomFactories\Contractuals\ApplicantFactory;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Assert;
use Tests\ContractualsTestCase;

class ApplicantTest extends ContractualsTestCase
{
    use RefreshDatabase;

    protected function setUp() : void
    {
        parent::setUp();

        Collection::macro('assertContains', function ($model) {
            Assert::assertTrue($this->contains($model), "Failed to assert that the collection contains the $model");
        });
    }

    /** @test */
    public function can_add_its_assets()
    {
        $applicant = factory('App\Models\Contractuals\Applicant')->create();

        $applicant->addAssets('degrees', [
            [
                'id' => '',
                'name' => 'BSc in Electronics Engineering',
                'issuer' => 'TEI of Athens',
                'mark' => '7.9',
                'year' => '2005',
                'verified' => '',
            ],
            [
                'id' => '',
                'name' => 'BSc in Electronics Engineering',
                'issuer' => 'TEI of Athens',
                'mark' => '7.9',
                'year' => '2005',
                'verified' => '',
            ],
        ]);
        $applicant->addAssets('minors', [
            [
                'id' => '',
                'amount' => 3,
                'verified' => false,
            ],
        ]);
        $applicant->addAssets('singleParentFamilies', [
            [
                'id' => '',
                'children' => 3,
                'siblings' => 4,
                'verified' => false,
            ],
        ]);

        $this->assertCount(2, Degree::all());
        $this->assertInstanceOf('App\Models\Contractuals\Degree', Degree::first());
        $this->assertEquals($applicant->id, Degree::first()->applicant_id);
        $this->assertCount(1, Minor::all());
        $this->assertInstanceOf('App\Models\Contractuals\Minor', Minor::first());
        $this->assertEquals($applicant->id, Minor::first()->applicant_id);
        $this->assertCount(1, SingleParentFamily::all());
        $this->assertEquals($applicant->id, SingleParentFamily::first()->applicant_id);
    }

    /** @test */
    public function can_update_its_assets()
    {
        $applicant = ApplicantFactory::create([
            'degrees' => [
                [
                    'name' => 'Old Degree',
                    'issuer' => 'University of Athens',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'minors' => [
                [
                    'amount' => 5,
                    'verified' => '',
                ],
            ],
            'singleParentFamilies' => [
                [
                    'children' => 3,
                    'siblings' => 2,
                    'verified' => '',
                ],
            ],
        ]);
        $this->assertCount(1, Applicant::all());
        $this->assertCount(1, $applicant->degrees);
        $this->assertCount(1, $applicant->minors);
        $this->assertCount(1, $applicant->singleParentFamilies);
//        $this->assertCount(1, $applicant->unemployments);
        $this->assertEquals(5, $applicant->minors->first()->amount);

        $applicant->updateAssets('degrees', [
            [
                'id' => $applicant->degrees->first()->id,
                'name' => 'New Degree',
                'issuer' => 'TEI of Athens',
                'mark' => '7.9',
                'year' => '2005',
                'verified' => '',
            ],
            [
                'id' => '',
                'name' => 'Brand New Degree',
                'issuer' => 'MIT',
                'mark' => '3',
                'year' => '2006',
                'verified' => '',
            ],
        ]);
        $applicant->updateAssets('minors', [
            [
                'id' => $applicant->minors->first()->id,
                'amount' => 10,
                'verified' => '',
            ],
        ]);
        $applicant->addAssets('singleParentFamilies', [
            [
                'id' => '',
                'children' => 3,
                'siblings' => 4,
                'verified' => false,
            ],
        ]);

        $this->assertCount(1, Applicant::all());
        $this->assertCount(2, $applicant->fresh()->degrees);
        $this->assertCount(1, $applicant->fresh()->minors);
        $this->assertEquals('New Degree', $applicant->fresh()->degrees->first()->name);
        $this->assertEquals('Brand New Degree', $applicant->fresh()->degrees->last()->name);
        $this->assertEquals('10', $applicant->fresh()->minors->first()->amount);
    }

    /** @test */
    public function can_get_all_related_assets()
    {
        $applicant = ApplicantFactory::create([
            'degrees' => [
                [
                    'name' => 'BSc in Archaeology',
                    'issuer' => 'University of Athens',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'postgraduates' => [
                [
                    'name' => 'MSc in field Archaeology',
                    'issuer' => 'NTUA',
                    'mark' => '7.5',
                    'year' => '2007',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Company Α',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Field Archaeologist',
                    'issuer' => 'Company B',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);

        $degree = Degree::first();
        $postgraduate = Postgraduate::first();
        $experiences = Experience::all();

        $assets = $applicant->getAssets();
        $this->assertCount(4, $assets);
        $assets->assertContains($degree);
        $assets->assertContains($postgraduate);
        $assets->assertContains($experiences[0]);
        $assets->assertContains($experiences[1]);
    }

    /** @test */
    public function can_get_assets_by_type()
    {
        $applicant = ApplicantFactory::create([
            'degrees' => [
                [
                    'name' => 'BSc in Archaeology',
                    'issuer' => 'University of Athens',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'postgraduates' => [
                [
                    'name' => 'MSc in field Archaeology',
                    'issuer' => 'NTUA',
                    'mark' => '7.5',
                    'year' => '2007',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Company Α',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Field Archaeologist',
                    'issuer' => 'Company B',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);

        $degrees = $applicant->getAssets('degrees');
        $postgraduates = $applicant->getAssets('postgraduates');
        $experiences = $applicant->getAssets('experiences');
        $minors = $applicant->getAssets('minors');

        $this->assertCount(1, $degrees);
        $this->assertCount(1, $postgraduates);
        $this->assertCount(2, $experiences);
        $this->assertCount(0, $minors);

        $degrees->assertContains(Degree::first());
        $postgraduates->assertContains(Postgraduate::first());
        $experiences->assertContains(Experience::all()[0]);
        $experiences->assertContains(Experience::all()[0]);
    }
}
