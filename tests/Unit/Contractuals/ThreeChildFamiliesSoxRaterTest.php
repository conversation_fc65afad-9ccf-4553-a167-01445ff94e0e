<?php

namespace Tests\Unit\Contractuals;

use App\Services\Contractuals\EvaluationRaters\Sox2190\ThreeChildFamiliesEvaluationsRater;
use Database\CustomFactories\Contractuals\ApplicationFactory;
use Database\CustomFactories\Contractuals\ContestFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\ContractualsTestCase;
use Tests\Helpers\Contractuals\EvaluationMappings;

class ThreeChildFamiliesSoxRaterTest extends ContractualsTestCase
{
    use EvaluationMappings, RefreshDatabase;

    private $contest;

    protected function setUp() : void
    {
        parent::setUp();

        $this->contest = ContestFactory::create([
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'Experience A',
                                'requirement_type' => 'experiences',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /** @test */
    public function it_calculates_and_updates_the_evaluation_points_in_case_there_are_three_children_or_three_siblings()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'threeChildFamilies' => [
                [
                    'children' => true,
                    'siblings' => true,
                ],
            ],
        ]);

        $application->initializeEvaluations();

        $positionA = $this->contest->positions->first();

        $evaluation = $this->findEvaluationByType('three_child_families', $positionA)->first();

        $threeChildFamiliesSoxRater = new ThreeChildFamiliesEvaluationsRater();
        $threeChildFamiliesSoxRater->rate($application->evaluations, 'ANY_SPECIALIZATION', 0);

        $this->assertEquals(120, $evaluation->fresh()->points);
    }

    /** @test */
    public function it_calculates_and_updates_the_evaluation_points_in_case_there_are_neither_children_nor_siblings()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'threeChildFamilies' => [
                [
                    'children' => false,
                    'siblings' => false,
                ],
            ],
        ]);

        $application->initializeEvaluations();

        $positionA = $this->contest->positions->first();

        $evaluation = $this->findEvaluationByType('three_child_families', $positionA)->first();

        $threeChildFamiliesSoxRater = new ThreeChildFamiliesEvaluationsRater();
        $threeChildFamiliesSoxRater->rate($application->evaluations, 'ANY_SPECIALIZATION', 0);

        $this->assertEquals(0, $evaluation->fresh()->points);
    }
}
