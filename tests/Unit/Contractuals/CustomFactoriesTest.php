<?php

namespace Tests\Unit\Contractuals;

use App\Models\Contractuals\Applicant;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Degree;
use App\Models\Contractuals\Experience;
use App\Models\Contractuals\Requirement;
use App\Models\Contractuals\Specialization;
use App\Models\Personnel\Position;
use Database\CustomFactories\Contractuals\ApplicantFactory;
use Database\CustomFactories\Contractuals\ApplicationFactory;
use Database\CustomFactories\Contractuals\ContestFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\ContractualsTestCase;

class CustomFactoriesTest extends ContractualsTestCase
{
    use RefreshDatabase;

    /** @test */
    public function applicant_factory_must_create_an_applicant_profile_with_assets()
    {
        $this->assertCount(0, Applicant::all());
        $this->assertCount(0, Degree::all());
        $this->assertCount(0, Experience::all());

        $applicant = ApplicantFactory::create([
            'name' => 'Δημήτρης',
            'surname' => 'Πουλικάκος',
            'degrees' => [
                [
                    'name' => 'Πτυχίο Αρχαιολόγου',
                    'issuer' => 'ΕΚΠΑ',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
                [
                    'name' => 'Πτυχίο Μηχανικού',
                    'issuer' => 'ΕΜΠ',
                    'mark' => '7.5',
                    'year' => '2007',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Αρχαιολόγος',
                    'issuer' => 'Εταιρεία Α',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Μηχανικός',
                    'issuer' => 'Εταιρεία B',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);

        $this->assertCount(1, Applicant::all());
        $this->assertCount(2, $applicant->fresh()->degrees);
        $this->assertCount(2, $applicant->fresh()->experiences);
        $this->assertEquals('Δημήτρης', $applicant->name);
        $this->assertEquals('Πτυχίο Αρχαιολόγου', $applicant->degrees->first()->name);
        $this->assertEquals('Αρχαιολόγος', $applicant->experiences->first()->name);
    }

    /** @test */
    public function contest_factory_must_create_a_contest_with_positions_specialization_and_requirements()
    {
        $this->assertCount(0, Contest::all());
        $this->assertCount(0, Position::all());
        $this->assertCount(0, Specialization::all());
        $this->assertCount(0, Requirement::all());

        $contest = ContestFactory::create([
            'name' => 'Διαγωνισμός',
            'protocol_number' => '123456',
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Αθήνα',
                    'specialization' => 'PE Αρχαιολόγων',
                    'requirements' => [
                        0 => [ // Auxiliary level 0
                            [
                                'name' => 'Βεβαίωση σπουδών ή αναλυτική βαθμολογία',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'Προυπηρεσία στην βυζαντινολογία',
                                'requirement_type' => 'experience',
                            ],
                        ],
                    ],
                ],
                [
                    'amount' => 2,
                    'location' => 'Θεσσαλονίκη',
                    'specialization' => 'PE Μηχανικών',
                    'requirements' => [
                        1 => [
                            [
                                'name' => 'Βεβαίωση σπουδών ή αναλυτική βαθμολογία',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'Προυπηρεσία στις ανασκαφές',
                                'requirement_type' => 'experience',
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $this->assertCount(1, Contest::all());
        $this->assertEquals('Διαγωνισμός', $contest->name);
        $this->assertEquals('123456', $contest->protocol_number);

        $this->assertCount(2, $contest->positions);
        $this->assertEquals(2, $contest->positions->first()->amount);
        $this->assertEquals('PE Αρχαιολόγων', $contest->positions->first()->specialization->name);
        $this->assertEquals('PEΑρχα', $contest->positions->first()->specialization->shortname);
        $this->assertEquals('PE', $contest->positions->first()->specialization->specializationType->name);

        $this->assertCount(2, $contest->positions()->first()->requirements);
        $this->assertEquals('Βεβαίωση σπουδών ή αναλυτική βαθμολογία', $contest->positions->first()->requirements->first()->name);
        $this->assertEquals('Προυπηρεσία στην βυζαντινολογία', $contest->positions->first()->requirements->last()->name);
        $this->assertEquals('Βεβαίωση σπουδών ή αναλυτική βαθμολογία', $contest->positions()->first()->requirements->first()->name);
        $this->assertEquals('Βεβαίωση σπουδών ή αναλυτική βαθμολογία', $contest->positions->last()->requirements->first()->name);
        $this->assertEquals('Προυπηρεσία στις ανασκαφές', $contest->positions->last()->requirements->last()->name);
        $this->assertEquals('degree', $contest->positions->first()->requirements->first()->requirementType->name);
        $this->assertEquals('experience', $contest->positions->first()->requirements->last()->requirementType->name);
        $this->assertEquals('degree', $contest->positions->first()->requirements->first()->requirementType->qualifiable_type);
        $this->assertEquals('degree', $contest->positions->last()->requirements->first()->requirementType->name);
        $this->assertEquals('experience', $contest->positions->last()->requirements->last()->requirementType->name);
        $this->assertEquals('degree', $contest->positions->last()->requirements->first()->requirementType->qualifiable_type);
        $this->assertEquals('0', $contest->positions->first()->requirements->first()->pivot->auxiliary_level);
        $this->assertEquals('1', $contest->positions->last()->requirements->first()->pivot->auxiliary_level);
    }

    /** @test */
    public function application_factory_must_create_a_new_application_with_its_qualifications_and_selected_positions_for_given_contest()
    {
        $contest = ContestFactory::create([
            'name' => 'Διαγωνισμός',
            'protocol_number' => '123456',
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE Αρχαιολόγων',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'Βεβαίωση σπουδών ή αναλυτική βαθμολογία',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'Προυπηρεσία στην Βυζαντινολογία',
                                'requirement_type' => 'experience',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
        $this->assertCount(0, Application::all());
        $this->assertCount(0, Applicant::all());
        $this->assertCount(0, Degree::all());
        $this->assertCount(0, Experience::all());

        $application = ApplicationFactory::createForContest($contest, [
            'positions' => [
                ['id' => $contest->positions->first()->id, 'order' => 1],
            ],
            'protocol_number' => '9999999999',
            'name' => 'Δημήτρης',
            'surname' => 'Πουλικάκος',
            'degrees' => [
                [
                    'name' => 'Πτυχίο Αρχαιολόγου',
                    'issuer' => 'ΕΚΠΑ',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
                [
                    'name' => 'Πτυχίο Μηχανικού',
                    'issuer' => 'ΕΜΠ',
                    'mark' => '7.5',
                    'year' => '2007',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Αρχαιολόγος',
                    'issuer' => 'Εταιρεία Α',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Μηχανικός',
                    'issuer' => 'Εταιρεία B',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);

        $this->assertCount(1, Application::all());
        $this->assertEquals('9999999999', $application->protocol_number);
        $this->assertEquals('Δημήτρης', $application->name);
        $this->assertCount(1, Applicant::all());
        $this->assertCount(1, $application->applicant()->get());
        $this->assertCount(2, $application->applicant->degrees);
        $this->assertCount(2, $application->applicant->experiences);
        $this->assertEquals('Δημήτρης', $application->applicant->name);
        $this->assertEquals('Πτυχίο Αρχαιολόγου', $application->applicant->degrees->first()->name);
        $this->assertEquals('Αρχαιολόγος', $application->applicant->experiences->first()->name);

        $this->assertCount(2, $application->degrees);
        $this->assertCount(2, $application->experiences);
        $this->assertEquals('Πτυχίο Αρχαιολόγου', $application->degrees->first()->name);
        $this->assertEquals('Αρχαιολόγος', $application->experiences->first()->name);

        $this->assertCount(1, $application->fresh()->positions()->get());
    }
}
