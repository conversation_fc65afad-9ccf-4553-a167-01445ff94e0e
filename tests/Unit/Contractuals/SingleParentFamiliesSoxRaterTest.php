<?php

namespace Tests\Unit\Contractuals;

use App\Services\Contractuals\EvaluationRaters\Sox2190\SingleParentFamiliesEvaluationsRater;
use Database\CustomFactories\Contractuals\ApplicationFactory;
use Database\CustomFactories\Contractuals\ContestFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\ContractualsTestCase;
use Tests\Helpers\Contractuals\EvaluationMappings;

class SingleParentFamiliesSoxRaterTest extends ContractualsTestCase
{
    use EvaluationMappings, RefreshDatabase;

    private $contest;

    protected function setUp() : void
    {
        parent::setUp();

        $this->contest = ContestFactory::create([
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'Experience A',
                                'requirement_type' => 'experiences',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /** @test */
    public function calculates_and_updates_the_evaluation_points_according_to_if_there_is_more_children_or_siblings()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'singleParentFamilies' => [
                [
                    'children' => 5,
                    'siblings' => 6,
                ],
            ],
        ]);

        $application->initializeEvaluations();

        $positionA = $this->contest->positions->first();

        $evaluation = $this->findEvaluationByType('single_parent_families', $positionA)->first();

        $singleParentFamiliesSoxRater = new SingleParentFamiliesEvaluationsRater();
        $singleParentFamiliesSoxRater->rate($application->evaluations, 'ANY_SPECIALIZATION', 0);

        $this->assertEquals(300, $evaluation->fresh()->points);
    }
}
