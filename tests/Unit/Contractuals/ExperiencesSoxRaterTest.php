<?php

namespace Tests\Unit\Contractuals;

use App\Services\Contractuals\EvaluationRaters\Sox2190\ExperiencesEvaluationsRater;
use Database\CustomFactories\Contractuals\ApplicationFactory;
use Database\CustomFactories\Contractuals\ContestFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\ContractualsTestCase;
use Tests\Helpers\Contractuals\EvaluationMappings;

class ExperiencesSoxRaterTest extends ContractualsTestCase
{
    use EvaluationMappings, RefreshDatabase;

    private $contest;
    private $application;

    protected function setUp() : void
    {
        parent::setUp();

        $this->contest = ContestFactory::create([
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'Experience A',
                                'requirement_type' => 'experiences',
                            ],
                        ],
                        1 => [
                            [
                                'name' => 'Experience B',
                                'requirement_type' => 'experiences',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
        $this->application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'experiences' => [
                [
                    'name' => 'Relevant Experience A',
                    'months' => 25,
                ],
                [
                    'name' => 'Relevant Experience B',
                    'months' => 36,
                ],
                [
                    'name' => 'Irrelevant Experience',
                    'months' => 40,
                ],
            ],
        ]);
    }

    /** @test */
    public function it_calculates_and_updates_the_evaluation_points_for_experiences()
    {
        $this->application->initializeEvaluations();

        $positionA = $this->contest->positions->first();

        $requirementA = $this->findRequirement('Experience A', $positionA);

        $evaluationA = $this->findEvaluationByName('Relevant Experience A', $positionA);
        $evaluationB = $this->findEvaluationByName('Relevant Experience B', $positionA);
        $evaluationC = $this->findEvaluationByName('Irrelevant Experience', $positionA);

        $this->mapEvaluations([
            ['evaluation' => $evaluationA, 'requirement' => $requirementA, 'relevant' => null],
            ['evaluation' => $evaluationB, 'requirement' => null, 'relevant' => true],
        ], $this->application);

        $experiencesSoxRater = new ExperiencesEvaluationsRater();
        $experiencesSoxRater->rate($this->application->evaluations, 'ANY_SPECIALIZATION', 0);

        $this->assertEquals(175, $evaluationA->fresh()->points);
        $this->assertEquals(252, $evaluationB->fresh()->points);
        $this->assertEquals(0, $evaluationC->fresh()->points);
    }

    /** @test */
    public function calculates_score_for_experiences_evaluations()
    {
        $this->application->initializeEvaluations();

        $positionA = $this->contest->positions->first();

        $requirementA = $this->findRequirement('Experience A', $positionA);

        $evaluationA = $this->findEvaluationByName('Relevant Experience A', $positionA);
        $evaluationB = $this->findEvaluationByName('Relevant Experience B', $positionA);
        $evaluationC = $this->findEvaluationByName('Irrelevant Experience', $positionA);

        $this->mapEvaluations([
            ['evaluation' => $evaluationA, 'requirement' => $requirementA, 'relevant' => null],
            ['evaluation' => $evaluationB, 'requirement' => null, 'relevant' => true],
        ], $this->application);

        $experiencesSoxRater = new ExperiencesEvaluationsRater();
        $experienceEvaluationsScore = $experiencesSoxRater->rate($this->application->evaluations, 'ANY_SPECIALIZATION', 0);

        $this->assertEquals(420, $experienceEvaluationsScore);
    }
}
