<?php

namespace Tests\Unit\Contractuals;

use App\Services\Contractuals\EvaluationRaters\Sox2190\FamilyDisabilitiesEvaluationsRater;
use Database\CustomFactories\Contractuals\ApplicationFactory;
use Database\CustomFactories\Contractuals\ContestFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\ContractualsTestCase;
use Tests\Helpers\Contractuals\EvaluationMappings;

class FamilyDisabilitiesSoxRaterTest extends ContractualsTestCase
{
    use EvaluationMappings, RefreshDatabase;

    private $contest;

    protected function setUp() : void
    {
        parent::setUp();

        $this->contest = ContestFactory::create([
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'Experience A',
                                'requirement_type' => 'experiences',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /** @test */
    public function calculates_and_updates_the_evaluation_points_by_giving_0_points_for_disability_percentage_less_than_50()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'familyDisabilities' => [
                [
                    'percentage' => 49,
                ],
            ],
        ]);

        $application->initializeEvaluations();

        $positionA = $this->contest->positions->first();

        $evaluation = $this->findEvaluationByType('family_disabilities', $positionA)->first();

        $familyDisabilitiesSoxRater = new FamilyDisabilitiesEvaluationsRater();
        $familyDisabilitiesSoxRater->rate($application->evaluations, 'ANY_SPECIALIZATION', 0);

        $this->assertEquals(0, $evaluation->fresh()->points);
    }

    /** @test */
    public function calculates_and_updates_the_evaluation_points_by_giving_3_points_for_each_disability_percentage_more_than_50()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'familyDisabilities' => [
                [
                    'percentage' => 50,
                ],
            ],
        ]);

        $application->initializeEvaluations();

        $positionA = $this->contest->positions->first();

        $evaluation = $this->findEvaluationByType('family_disabilities', $positionA)->first();

        $familyDisabilitiesSoxRater = new FamilyDisabilitiesEvaluationsRater();
        $familyDisabilitiesSoxRater->rate($application->evaluations, 'ANY_SPECIALIZATION', 0);

        $this->assertEquals(100, $evaluation->fresh()->points);
    }
}
