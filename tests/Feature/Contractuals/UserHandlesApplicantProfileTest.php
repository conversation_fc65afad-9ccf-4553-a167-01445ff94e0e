<?php

namespace Tests\Feature\Contractuals;

use App\Models\Contractuals\Applicant;
use App\Models\Contractuals\Language;
use App\Models\Contractuals\LanguageLevel;
use Database\CustomFactories\Contractuals\ApplicantFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\ContractualsTestCase;

class UserHandlesApplicantProfileTest extends ContractualsTestCase
{
    use RefreshDatabase;

    protected $language;
    protected $languageLevel;

    protected function setUp() : void
    {
        parent::setUp();

        $this->language = factory(Language::class)->create();
        $this->languageLevel = factory(LanguageLevel::class)->create();
    }

    /** @test */
    public function an_auth_user_can_view_the_applicant_create_form()
    {
        $response = $this->actingAs($this->user)
            ->get('contractuals/applicants/create');

        $response->assertStatus(200)
            ->assertViewIs('contractuals.applicant.create')
            ->assertViewHasAll([
                'formRelatedModels' => [
                    'languages' => Language::all(),
                    'languageLevels' => LanguageLevel::all(),
                ],
            ]);
    }

    /** @test */
    public function an_auth_user_can_store_an_applicant_profile_with_its_assets()
    {
        $this->assertCount(0, Applicant::all());

        $response = $this->actingAs($this->user)
            ->post('/contractuals/applicants', $this->validParams([
                'degrees' => [
                    [
                        'id' => '',
                        'name' => 'Degree A',
                        'issuer' => 'University A',
                        'mark' => '8.20',
                        'year' => '2005',
                        'verified' => false,
                    ],
                    [
                        'id' => '',
                        'name' => 'Degree B',
                        'issuer' => 'University B',
                        'mark' => '7.55',
                        'year' => '2006',
                        'verified' => false,
                    ],
                ],
                'postgraduates' => [
                    [
                        'id' => '',
                        'name' => 'Postgraduate A',
                        'issuer' => 'University A',
                        'year' => '2007',
                        'verified' => false,
                    ],
                ],
                'doctorates' => [
                    [
                        'id' => '',
                        'name' => 'Doctorate Α',
                        'issuer' => 'University A',
                        'year' => '2008',
                        'verified' => false,
                    ],
                ],
                'greek_languages' => [
                    [
                        'id' => '',
                        'name' => 'Greek Knowledge',
                        'issuer' => 'Ministry of Education',
                        'level' => 'A',
                        'verified' => false,
                    ],
                ],
                'language_skills' => [
                    [
                        'id' => '',
                        'name' => 'Proficiency',
                        'issuer' => 'British Council',
                        'language_id' => $this->language->id,
                        'language_level_id' => $this->languageLevel->id,
                        'verified' => false,
                    ],
                ],
                'computer_skills' => [
                    [
                        'id' => '',
                        'name' => 'ECDL',
                        'verified' => false,
                    ],
                ],
                'experiences' => [
                    [
                        'id' => '',
                        'name' => 'Job A',
                        'issuer' => 'Company A',
                        'months' => 6,
                        'started_at' => '2017-01-01',
                        'ended_at' => '2017-06-30',
                        'verified' => false,
                    ],
                    [
                        'id' => '',
                        'name' => 'Job B',
                        'issuer' => 'Company B',
                        'months' => 6,
                        'started_at' => '2017-07-01',
                        'ended_at' => '2017-12-31',
                        'verified' => false,
                    ],
                ],
                'unemployments' => [
                    [
                        'id' => '',
                        'months' => 2,
                        'verified' => false,
                    ],
                ],
                'multi_child_families' => [
                    [
                        'id' => '',
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'three_child_families' => [
                    [
                        'id' => '',
                        'children' => true,
                        'siblings' => true,
                        'verified' => false,
                    ],
                ],
                'single_parent_families' => [
                    [
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'minors' => [
                    [
                        'id' => '',
                        'amount' => 2,
                        'verified' => false,
                    ],
                ],
                'disabilities' => [
                    [
                        'id' => '',
                        'percentage' => 60,
                        'verified' => false,
                    ],
                ],
                'family_disabilities' => [
                    [
                        'id' => '',
                        'percentage' => 65,
                        'verified' => false,
                    ],
                ],
            ]));

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Applicant profile has been stored',
            'applicant' => [
                'name' => 'John',
                'surname' => 'Doe',
                'fathername' => 'Jack',
                'mothername' => 'Jane',
                'birthdate' => '1980-02-01',
                'policeid_number' => 'AB123456',
                'policeid_date' => '2000-03-25',
                'afm' => '0123456789',
                'doy' => 'IG Athens',
                'amka' => '20028012345',
                'street' => 'Example str.',
                'street_number' => '23',
                'postcode' => '12345',
                'city' => 'Athens',
                'phonenumber1' => '2101234567',
                'phonenumber2' => '2131234567',
                'email' => '<EMAIL>',
                'eu_citizen' => true,
                'greek_nationality' => true,
                'degrees' => [
                    [
                        'name' => 'Degree A',
                        'issuer' => 'University A',
                        'mark' => '8.20',
                        'year' => '2005',
                        'verified' => false,
                    ],
                    [
                        'name' => 'Degree B',
                        'issuer' => 'University B',
                        'mark' => '7.55',
                        'year' => '2006',
                        'verified' => false,
                    ],
                ],
                'postgraduates' => [
                    [
                        'name' => 'Postgraduate A',
                        'issuer' => 'University A',
                        'year' => '2007',
                        'verified' => false,
                    ],
                ],
                'doctorates' => [
                    [
                        'name' => 'Doctorate Α',
                        'issuer' => 'University A',
                        'year' => '2008',
                        'verified' => false,
                    ],
                ],
                'greek_languages' => [
                    [
                        'name' => 'Greek Knowledge',
                        'issuer' => 'Ministry of Education',
                        'level' => 'A',
                        'verified' => false,
                    ],
                ],
                'language_skills' => [
                    [
                        'name' => 'Proficiency',
                        'issuer' => 'British Council',
                        'language_id' => $this->language->id,
                        'language_level_id' => $this->languageLevel->id,
                        'verified' => false,
                    ],
                ],
                'computer_skills' => [
                    [
                        'name' => 'ECDL',
                        'verified' => false,
                    ],
                ],
                'experiences' => [
                    [
                        'name' => 'Job A',
                        'issuer' => 'Company A',
                        'months' => 6,
                        'started_at' => '2017-01-01',
                        'ended_at' => '2017-06-30',
                        'verified' => false,
                    ],
                    [
                        'name' => 'Job B',
                        'issuer' => 'Company B',
                        'months' => 6,
                        'started_at' => '2017-07-01',
                        'ended_at' => '2017-12-31',
                        'verified' => false,
                    ],
                ],
                'unemployments' => [
                    [
                        'months' => 2,
                        'verified' => false,
                    ],
                ],
                'multi_child_families' => [
                    [
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'three_child_families' => [
                    [
                        'children' => true,
                        'siblings' => true,
                        'verified' => false,
                    ],
                ],
                'single_parent_families' => [
                    [
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'minors' => [
                    [
                        'amount' => 2,
                        'verified' => false,
                    ],
                ],
                'disabilities' => [
                    [
                        'percentage' => 60,
                        'verified' => false,
                    ],
                ],
                'family_disabilities' => [
                    [
                        'percentage' => 65,
                        'verified' => false,
                    ],
                ],
            ],
        ]);
        $this->assertCount(1, Applicant::all());
        $this->assertCount(2, Applicant::first()->degrees);
        $this->assertCount(1, Applicant::first()->postgraduates);
        $this->assertCount(1, Applicant::first()->doctorates);
        $this->assertCount(1, Applicant::first()->greekLanguages);
        $this->assertCount(1, Applicant::first()->languageSkills);
        $this->assertCount(1, Applicant::first()->computerSkills);
        $this->assertCount(2, Applicant::first()->experiences);
        $this->assertCount(1, Applicant::first()->unemployments);
        $this->assertCount(1, Applicant::first()->multiChildFamilies);
        $this->assertCount(1, Applicant::first()->threeChildFamilies);
        $this->assertCount(1, Applicant::first()->singleParentFamilies);
        $this->assertCount(1, Applicant::first()->minors);
        $this->assertCount(1, Applicant::first()->disabilities);
        $this->assertCount(1, Applicant::first()->familyDisabilities);
    }

    /** @test */
    public function an_auth_user_can_view_the_applicant_edit_form()
    {
        $existingApplicant = ApplicantFactory::create([
            'degrees' => [
                [
                    'name' => 'Degree A',
                    'issuer' => 'University A',
                    'mark' => '8.20',
                    'year' => '2005',
                ],
                [
                    'name' => 'Degree B',
                    'issuer' => 'University B',
                    'mark' => '7.55',
                    'year' => '2006',
                ],
            ],
            'postgraduates' => [
                [
                    'name' => 'Postgraduate A',
                    'issuer' => 'University A',
                    'year' => '2007',
                ],
            ],
            'doctorates' => [
                [
                    'name' => 'Doctorate Α',
                    'issuer' => 'University A',
                    'year' => '2008',
                ],
            ],
            'greekLanguages' => [
                [
                    'name' => 'Greek Knowledge',
                    'issuer' => 'Ministry of Education',
                    'level' => 'A',
                ],
            ],
            'languageSkills' => [
                [
                    'name' => 'Proficiency',
                    'issuer' => 'British Council',
                    'language_id' => $this->language->id,
                    'language_level_id' => $this->languageLevel->id,
                ],
            ],
            'computerSkills' => [
                [
                    'name' => 'ECDL',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Job A',
                    'issuer' => 'Company A',
                    'months' => 6,
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Job B',
                    'issuer' => 'Company B',
                    'months' => 6,
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
            'unemployments' => [
                [
                    'months' => 2,
                ],
            ],
            'multiChildFamilies' => [
                [
                    'children' => 5,
                    'siblings' => 6,
                ],
            ],
            'threeChildFamilies' => [
                [
                    'children' => true,
                    'siblings' => true,
                ],
            ],
            'singleParentFamilies' => [
                [
                    'children' => 5,
                    'siblings' => 6,
                ],
            ],
            'minors' => [
                [
                    'amount' => 2,
                ],
            ],
            'disabilities' => [
                [
                    'percentage' => 60,
                ],
            ],
            'familyDisabilities' => [
                [
                    'percentage' => 65,
                ],
            ],
        ]);

        $response = $this->actingAs($this->user)
            ->get("contractuals/applicants/{$existingApplicant->id}/edit");

        $response->assertStatus(200)
            ->assertViewIs('contractuals.applicant.edit')
            ->assertViewHasAll([
                'applicant' => $existingApplicant->fresh()->load([
                    'degrees.verifications',
                    'postgraduates.verifications',
                    'doctorates.verifications',
                    'greekLanguages.verifications',
                    'languageSkills.verifications',
                    'computerSkills.verifications',
                    'experiences.verifications',
                    'unemployments.verifications',
                    'multiChildFamilies.verifications',
                    'threeChildFamilies.verifications',
                    'singleParentFamilies.verifications',
                    'minors.verifications',
                    'disabilities.verifications',
                    'familyDisabilities.verifications',
                    'degrees.media',
                    'postgraduates.media',
                    'doctorates.media',
                    'greekLanguages.media',
                    'languageSkills.media',
                    'computerSkills.media',
                    'experiences.media',
                    'unemployments.media',
                    'multiChildFamilies.media',
                    'threeChildFamilies.media',
                    'singleParentFamilies.media',
                    'minors.media',
                    'disabilities.media',
                    'familyDisabilities.media',
                    'applications',
                ]),
            ]);
    }

    /** @test */
    public function an_auth_user_can_update_an_applicant_profile_with_its_assets()
    {
        $applicant = ApplicantFactory::create([
            'name' => 'OldName',
            'degrees' => [
                [
                    'name' => 'Old degree',
                    'issuer' => 'UoA',
                    'mark' => '5.00',
                    'year' => '2000',
                    'verified' => false,
                ],
            ],
        ]);
        $this->assertCount(1, Applicant::all());
        $this->assertCount(1, $applicant->degrees);
        $this->assertCount(0, $applicant->fresh()->postgraduates);
        $this->assertCount(0, $applicant->fresh()->doctorates);
        $this->assertCount(0, $applicant->fresh()->experiences);
        $this->assertCount(0, $applicant->fresh()->languageSkills);
        $this->assertCount(0, $applicant->fresh()->computerSkills);
        $this->assertCount(0, $applicant->fresh()->greekLanguages);
        $this->assertCount(0, $applicant->fresh()->minors);
        $this->assertCount(0, $applicant->fresh()->singleParentFamilies);

        $response = $this->actingAs($this->user)
            ->put("/contractuals/applicants/{$applicant->id}", $this->validParams([
                'name' => 'NewName',
                'degrees' => [
                    [
                        'id' => $applicant->degrees->first()->id,
                        'name' => 'New Degree A',
                        'issuer' => 'New University A',
                        'mark' => '8.20',
                        'year' => '2005',
                        'verified' => false,
                    ],
                    [
                        'id' => '',
                        'name' => 'Degree B',
                        'issuer' => 'University B',
                        'mark' => '7.55',
                        'year' => '2006',
                        'verified' => false,
                    ],
                ],
                'postgraduates' => [
                    [
                        'id' => '',
                        'name' => 'Postgraduate A',
                        'issuer' => 'University A',
                        'year' => '2007',
                        'verified' => false,
                    ],
                ],
                'doctorates' => [
                    [
                        'id' => '',
                        'name' => 'Doctorate Α',
                        'issuer' => 'University A',
                        'year' => '2008',
                        'verified' => false,
                    ],
                ],
                'greek_languages' => [
                    [
                        'id' => '',
                        'name' => 'Greek Knowledge',
                        'issuer' => 'Ministry of Education',
                        'level' => 'A',
                        'verified' => false,
                    ],
                ],
                'language_skills' => [
                    [
                        'id' => '',
                        'name' => 'Proficiency',
                        'issuer' => 'British Council',
                        'language_id' => $this->language->id,
                        'language_level_id' => $this->languageLevel->id,
                        'verified' => false,
                    ],
                ],
                'computer_skills' => [
                    [
                        'id' => '',
                        'name' => 'ECDL',
                        'verified' => false,
                    ],
                ],
                'experiences' => [
                    [
                        'id' => '',
                        'name' => 'Job A',
                        'issuer' => 'Company A',
                        'months' => 6,
                        'started_at' => '2017-01-01',
                        'ended_at' => '2017-06-30',
                        'verified' => false,
                    ],
                    [
                        'id' => '',
                        'name' => 'Job B',
                        'issuer' => 'Company B',
                        'months' => 6,
                        'started_at' => '2017-07-01',
                        'ended_at' => '2017-12-31',
                        'verified' => false,
                    ],
                ],
                'unemployments' => [
                    [
                        'id' => '',
                        'months' => 2,
                        'verified' => false,
                    ],
                ],
                'multi_child_families' => [
                    [
                        'id' => '',
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'three_child_families' => [
                    [
                        'id' => '',
                        'children' => true,
                        'siblings' => true,
                        'verified' => false,
                    ],
                ],
                'single_parent_families' => [
                    [
                        'id' => '',
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'minors' => [
                    [
                        'id' => '',
                        'amount' => 2,
                        'verified' => false,
                    ],
                ],
                'disabilities' => [
                    [
                        'id' => '',
                        'percentage' => 60,
                        'verified' => false,
                    ],
                ],
                'family_disabilities' => [
                    [
                        'id' => '',
                        'percentage' => 65,
                        'verified' => false,
                    ],
                ],
            ]));

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Applicant profile has been updated',
            'applicant' => [
                'name' => 'NewName',
                'surname' => 'Doe',
                'fathername' => 'Jack',
                'mothername' => 'Jane',
                'birthdate' => '1980-02-01',
                'policeid_number' => 'AB123456',
                'policeid_date' => '2000-03-25',
                'afm' => '0123456789',
                'doy' => 'IG Athens',
                'amka' => '20028012345',
                'street' => 'Example str.',
                'street_number' => '23',
                'postcode' => '12345',
                'city' => 'Athens',
                'phonenumber1' => '2101234567',
                'phonenumber2' => '2131234567',
                'email' => '<EMAIL>',
                'eu_citizen' => true,
                'greek_nationality' => true,
                'degrees' => [
                    [
                        'name' => 'New Degree A',
                        'issuer' => 'New University A',
                        'mark' => '8.20',
                        'year' => '2005',
                        'verified' => false,
                    ],
                    [
                        'name' => 'Degree B',
                        'issuer' => 'University B',
                        'mark' => '7.55',
                        'year' => '2006',
                        'verified' => false,
                    ],
                ],
                'postgraduates' => [
                    [
                        'name' => 'Postgraduate A',
                        'issuer' => 'University A',
                        'year' => '2007',
                        'verified' => false,
                    ],
                ],
                'doctorates' => [
                    [
                        'name' => 'Doctorate Α',
                        'issuer' => 'University A',
                        'year' => '2008',
                        'verified' => false,
                    ],
                ],
                'greek_languages' => [
                    [
                        'name' => 'Greek Knowledge',
                        'issuer' => 'Ministry of Education',
                        'level' => 'A',
                        'verified' => false,
                    ],
                ],
                'language_skills' => [
                    [
                        'name' => 'Proficiency',
                        'issuer' => 'British Council',
                        'language_id' => $this->language->id,
                        'language_level_id' => $this->languageLevel->id,
                        'verified' => false,
                    ],
                ],
                'computer_skills' => [
                    [
                        'name' => 'ECDL',
                        'verified' => false,
                    ],
                ],
                'experiences' => [
                    [
                        'name' => 'Job A',
                        'issuer' => 'Company A',
                        'months' => 6,
                        'started_at' => '2017-01-01',
                        'ended_at' => '2017-06-30',
                        'verified' => false,
                    ],
                    [
                        'name' => 'Job B',
                        'issuer' => 'Company B',
                        'months' => 6,
                        'started_at' => '2017-07-01',
                        'ended_at' => '2017-12-31',
                        'verified' => false,
                    ],
                ],
                'unemployments' => [
                    [
                        'months' => 2,
                        'verified' => false,
                    ],
                ],
                'multi_child_families' => [
                    [
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'three_child_families' => [
                    [
                        'children' => true,
                        'siblings' => true,
                        'verified' => false,
                    ],
                ],
                'single_parent_families' => [
                    [
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'minors' => [
                    [
                        'amount' => 2,
                        'verified' => false,
                    ],
                ],
                'disabilities' => [
                    [
                        'percentage' => 60,
                        'verified' => false,
                    ],
                ],
                'family_disabilities' => [
                    [
                        'percentage' => 65,
                        'verified' => false,
                    ],
                ],
            ],
        ]);
        $this->assertCount(1, Applicant::all());
        $this->assertCount(1, Applicant::all());
        $this->assertCount(2, Applicant::first()->degrees);
        $this->assertCount(1, Applicant::first()->postgraduates);
        $this->assertCount(1, Applicant::first()->doctorates);
        $this->assertCount(1, Applicant::first()->greekLanguages);
        $this->assertCount(1, Applicant::first()->languageSkills);
        $this->assertCount(1, Applicant::first()->computerSkills);
        $this->assertCount(2, Applicant::first()->experiences);
        $this->assertCount(1, Applicant::first()->unemployments);
        $this->assertCount(1, Applicant::first()->multiChildFamilies);
        $this->assertCount(1, Applicant::first()->threeChildFamilies);
        $this->assertCount(1, Applicant::first()->singleParentFamilies);
        $this->assertCount(1, Applicant::first()->minors);
        $this->assertCount(1, Applicant::first()->disabilities);
        $this->assertCount(1, Applicant::first()->familyDisabilities);
    }

    private function validParams($overrides = [])
    {
        return array_merge([
            'name' => 'John',
            'surname' => 'Doe',
            'fathername' => 'Jack',
            'mothername' => 'Jane',
            'birthdate' => '1980-02-01',
            'policeid_number' => 'AB123456',
            'policeid_date' => '2000-03-25',
            'afm' => '0123456789',
            'doy' => 'IG Athens',
            'amka' => '20028012345',
            'street' => 'Example str.',
            'street_number' => '23',
            'postcode' => '12345',
            'city' => 'Athens',
            'phonenumber1' => '2101234567',
            'phonenumber2' => '2131234567',
            'email' => '<EMAIL>',
            'eu_citizen' => true,
            'greek_nationality' => true,
            'degrees' => [],
            'postgraduates' => [],
            'doctorates' => [],
            'experiences' => [],
            'language_skills' => [],
            'computer_skills' => [],
            'greek_languages' => [],
            'unemployments' => [],
            'multi_child_families' => [],
            'three_child_families' => [],
            'single_parent_families' => [],
            'disabilities' => [],
            'family_disabilities' => [],
            'minors' => [],
        ], $overrides);
    }
}
