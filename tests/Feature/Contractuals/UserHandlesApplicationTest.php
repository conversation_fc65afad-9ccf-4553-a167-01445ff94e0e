<?php

namespace Tests\Feature\Contractuals;

use App\Models\Contractuals\Applicant;
use App\Models\Contractuals\Application;
use App\Models\Contractuals\Contest;
use App\Models\Contractuals\Degree;
use App\Models\Contractuals\Experience;
use App\Models\Contractuals\Language;
use App\Models\Contractuals\LanguageLevel;
use App\Models\Contractuals\Minor;
use App\Models\Contractuals\Qualification;
use Carbon\Carbon;
use Database\CustomFactories\Contractuals\ApplicationFactory;
use Database\CustomFactories\Contractuals\ContestFactory;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use PHPUnit\Framework\Assert;
use Tests\ContractualsTestCase;

class UserHandlesApplicationTest extends ContractualsTestCase
{
    use RefreshDatabase;

    protected $contest;
    protected $language;
    protected $languageLevel;

    protected function setUp() : void
    {
        parent::setUp();

        $this->contest = ContestFactory::create([
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE Αρχαιολόγων',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'Βεβαίωση σπουδών ή αναλυτική βαθμολογία',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'Προυπηρεσία στην Βυζαντινολογία',
                                'requirement_type' => 'experience',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
        $this->language = factory(Language::class)->create();
        $this->languageLevel = factory(LanguageLevel::class)->create();

        Collection::macro('assertContains', function ($model) {
            Assert::assertTrue($this->contains($model), "Failed to assert that the collection contains the $model");
        });

        Collection::macro('assertNotContain', function ($model) {
            Assert::assertTrue(! $this->contains($model), "Failed to assert that the collection contains the $model");
        });
    }

    /** @test */
    public function an_auth_user_can_view_an_empty_application_create_form_when_no_applicant_profile_exists()
    {
        $this->assertCount(0, Applicant::all());

        $queryStringAfm = '114865648';

        $response = $this->actingAs($this->user)
            ->get("/contractuals/contests/{$this->contest->id}/applications/create?afm=${queryStringAfm}");

        $response->assertStatus(200)
            ->assertViewIs('contractuals.application.create')
            ->assertViewHasAll([
                'contest' => $this->contest->fresh()->load([
                    'positions.specialization.specializationType',
                    'positions.unit',
                ]),
                'applicant' => null,
                'formRelatedModels' => [
                    'languages' => $languages = Language::all(),
                    'languageLevels' => $languageLevels = LanguageLevel::all(),
                    'afm' => $queryStringAfm,
                ],
            ]);
    }

    /** @test */
    public function an_auth_user_can_view_a_precompleted_application_create_form_when_applicant_profile_exists()
    {
        $this->withoutExceptionHandling();

        $existingApplication = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'protocol_number' => '00000001',
            'street' => 'Old address',
            'degrees' => [
                [
                    'id' => '',
                    'name' => 'Degree A',
                    'issuer' => 'University A',
                    'mark' => '8.20',
                    'year' => '2005',
                ],
                [
                    'id' => '',
                    'name' => 'Degree B',
                    'issuer' => 'University B',
                    'mark' => '7.55',
                    'year' => '2006',
                ],
            ],
            'postgraduates' => [
                [
                    'id' => '',
                    'name' => 'Postgraduate A',
                    'issuer' => 'University A',
                    'year' => '2007',
                ],
            ],
            'doctorates' => [
                [
                    'id' => '',
                    'name' => 'Doctorate Α',
                    'issuer' => 'University A',
                    'year' => '2008',
                ],
            ],
            'greekLanguages' => [
                [
                    'id' => '',
                    'name' => 'Greek Knowledge',
                    'issuer' => 'Ministry of Education',
                    'level' => 'A',
                ],
            ],
            'languageSkills' => [
                [
                    'id' => '',
                    'name' => 'Proficiency',
                    'issuer' => 'British Council',
                    'language_id' => $this->language->id,
                    'language_level_id' => $this->languageLevel->id,
                ],
            ],
            'computerSkills' => [
                [
                    'id' => '',
                    'name' => 'ECDL',
                ],
            ],
            'experiences' => [
                [
                    'id' => '',
                    'name' => 'Job A',
                    'issuer' => 'Company A',
                    'months' => 6,
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'id' => '',
                    'name' => 'Job B',
                    'issuer' => 'Company B',
                    'months' => 6,
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
            'unemployments' => [
                [
                    'id' => '',
                    'months' => 2,
                ],
            ],
            'multiChildFamilies' => [
                [
                    'id' => '',
                    'children' => 5,
                    'siblings' => 6,
                ],
            ],
            'threeChildFamilies' => [
                [
                    'id' => '',
                    'children' => 1,
                    'siblings' => 1,
                ],
            ],
            'singleParentFamilies' => [
                [
                    'id' => '',
                    'children' => 5,
                    'siblings' => 6,
                ],
            ],
            'minors' => [
                [
                    'id' => '',
                    'amount' => 2,
                ],
            ],
            'disabilities' => [
                [
                    'id' => '',
                    'percentage' => 60,
                ],
            ],
            'familyDisabilities' => [
                [
                    'id' => '',
                    'percentage' => 65,
                ],
            ],
        ]);

        $existingApplicant = $existingApplication->applicant;

        $response = $this->actingAs($this->user)
            ->get("/contractuals/contests/{$this->contest->id}/applications/create?applicant=$existingApplicant->id");

        $response
            ->assertViewIs('contractuals.application.create')
            ->assertViewHasAll([
                'contest' => $this->contest->fresh()->load([
                    'positions.specialization.specializationType',
                    'positions.unit',
                ]),
                'applicant' => [
                    'id' => $existingApplicant->id,
                    'name' => $existingApplicant->name,
                    'surname' => $existingApplicant->surname,
                    'fathername' => $existingApplicant->fathername,
                    'mothername' => $existingApplicant->mothername,
                    'policeid_number' => $existingApplicant->policeid_number,
                    'policeid_date' => $existingApplicant->policeid_date->format('Y-m-d'),
                    'afm' => (string) $existingApplicant->afm,
                    'doy' => $existingApplicant->doy,
                    'amka' => (string) $existingApplicant->amka,
                    'birthdate' => $existingApplicant->birthdate->format('Y-m-d'),
                    'street' => $existingApplicant->street,
                    'street_number' => $existingApplicant->street_number,
                    'postcode' => $existingApplicant->postcode,
                    'city' => $existingApplicant->city,
                    'phonenumber1' => $existingApplicant->phonenumber1,
                    'phonenumber2' => $existingApplicant->phonenumber2,
                    'email' => $existingApplicant->email,
                    'eu_citizen' => (int) $existingApplicant->eu_citizen,
                    'greek_nationality' => (int) $existingApplicant->greek_nationality,
                    'created_at' => $existingApplicant->created_at->format('Y-m-d'),
                    'updated_at' => $existingApplicant->updated_at->format('Y-m-d'),
                    'deleted_at' => $existingApplicant->deleted_at,
                    'age' => $existingApplicant->age,
                    'degrees' => [
                        [
                            'id' => '',
                            'name' => 'Degree A',
                            'issuer' => 'University A',
                            'mark' => '8.20',
                            'year' => '2005',
                        ],
                        [
                            'id' => '',
                            'name' => 'Degree B',
                            'issuer' => 'University B',
                            'mark' => '7.55',
                            'year' => '2006',
                        ],
                    ],
                    'postgraduates' => [
                        [
                            'id' => '',
                            'name' => 'Postgraduate A',
                            'issuer' => 'University A',
                            'year' => '2007',
                        ],
                    ],
                    'doctorates' => [
                        [
                            'id' => '',
                            'name' => 'Doctorate Α',
                            'issuer' => 'University A',
                            'year' => '2008',
                        ],
                    ],
                    'greek_languages' => [
                        [
                            'id' => '',
                            'name' => 'Greek Knowledge',
                            'issuer' => 'Ministry of Education',
                            'level' => 'A',
                        ],
                    ],
                    'language_skills' => [
                        [
                            'id' => '',
                            'name' => 'Proficiency',
                            'issuer' => 'British Council',
                            'language_id' => $this->language->id,
                            'language_level_id' => $this->languageLevel->id,
                        ],
                    ],
                    'computer_skills' => [
                        [
                            'id' => '',
                            'name' => 'ECDL',
                        ],
                    ],
                    'experiences' => [
                        [
                            'id' => '',
                            'name' => 'Job A',
                            'issuer' => 'Company A',
                            'months' => 6,
                            'started_at' => '2017-01-01',
                            'ended_at' => '2017-06-30',
                        ],
                        [
                            'id' => '',
                            'name' => 'Job B',
                            'issuer' => 'Company B',
                            'months' => 6,
                            'started_at' => '2017-07-01',
                            'ended_at' => '2017-12-31',
                        ],
                    ],
                    'unemployments' => [
                        [
                            'id' => '',
                            'months' => 2,
                        ],
                    ],
                    'multi_child_families' => [
                        [
                            'id' => '',
                            'children' => 5,
                            'siblings' => 6,
                        ],
                    ],
                    'three_child_families' => [
                        [
                            'id' => '',
                            'children' => 1,
                            'siblings' => 1,
                        ],
                    ],
                    'single_parent_families' => [
                        [
                            'id' => '',
                            'children' => 5,
                            'siblings' => 6,
                        ],
                    ],
                    'minors' => [
                        [
                            'id' => '',
                            'amount' => 2,
                        ],
                    ],
                    'disabilities' => [
                        [
                            'id' => '',
                            'percentage' => 60,
                        ],
                    ],
                    'family_disabilities' => [
                        [
                            'id' => '',
                            'percentage' => 65,
                        ],
                    ],
                ],
                'formRelatedModels' => [
                    'languages' => $languages = Language::all(),
                    'languageLevels' => $languageLevels = LanguageLevel::all(),
                    'afm' => null,
                ],
            ]);
    }

    /** @test */
    public function an_auth_user_can_create_an_application_and_consequently_create_the_corresponding_applicant_profile_if_this_does_not_preexists()
    {
        $this->assertCount(0, Applicant::all());
        $this->assertCount(0, Application::all());

        $response = $this->actingAs($this->user)
            ->post("/contractuals/contests/{$this->contest->id}/applications", $this->validParams($this->contest, [
                'applicant_id' => null,
                'degrees' => [
                    [
                        'id' => '',
                        'name' => 'Degree A',
                        'issuer' => 'University A',
                        'mark' => '8.2',
                        'year' => '2005',
                        'verified' => false,
                    ],
                    [
                        'id' => '',
                        'name' => 'Degree B',
                        'issuer' => 'University B',
                        'mark' => '7.5',
                        'year' => '2006',
                        'verified' => false,
                    ],
                ],
                'postgraduates' => [
                    [
                        'id' => '',
                        'name' => 'Postgraduate A',
                        'issuer' => 'University A',
                        'year' => '2007',
                        'verified' => false,
                    ],
                ],
                'doctorates' => [
                    [
                        'id' => '',
                        'name' => 'Doctorate Α',
                        'issuer' => 'University A',
                        'year' => '2008',
                        'verified' => false,
                    ],
                ],
                'greek_languages' => [
                    [
                        'id' => '',
                        'name' => 'Greek Knowledge',
                        'issuer' => 'Ministry of Education',
                        'level' => 'A',
                        'verified' => false,
                    ],
                ],
                'language_skills' => [
                    [
                        'id' => '',
                        'name' => 'Proficiency',
                        'issuer' => 'British Council',
                        'language_id' => $this->language->id,
                        'language_level_id' => $this->languageLevel->id,
                        'verified' => false,
                    ],
                ],
                'computer_skills' => [
                    [
                        'id' => '',
                        'name' => 'ECDL',
                        'verified' => false,
                    ],
                ],
                'experiences' => [
                    [
                        'id' => '',
                        'name' => 'Job A',
                        'issuer' => 'Company A',
                        'months' => 6,
                        'started_at' => '2017-01-01',
                        'ended_at' => '2017-06-30',
                        'verified' => false,
                    ],
                    [
                        'id' => '',
                        'name' => 'Job B',
                        'issuer' => 'Company B',
                        'months' => 6,
                        'started_at' => '2017-07-01',
                        'ended_at' => '2017-12-31',
                        'verified' => false,
                    ],
                ],
                'unemployments' => [
                    [
                        'id' => '',
                        'months' => 2,
                        'verified' => false,
                    ],
                ],
                'multi_child_families' => [
                    [
                        'id' => '',
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'three_child_families' => [
                    [
                        'id' => '',
                        'children' => true,
                        'siblings' => true,
                        'verified' => false,
                    ],
                ],
                'single_parent_families' => [
                    [
                        'children' => 5,
                        'siblings' => 6,
                        'verified' => false,
                    ],
                ],
                'minors' => [
                    [
                        'id' => '',
                        'amount' => 2,
                        'verified' => false,
                    ],
                ],
                'disabilities' => [
                    [
                        'id' => '',
                        'percentage' => 60,
                        'verified' => false,
                    ],
                ],
                'family_disabilities' => [
                    [
                        'id' => '',
                        'percentage' => 65,
                        'verified' => false,
                    ],
                ],
            ]));

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Η αίτηση αποθηκεύτηκε.',
            'application' => [
                'name' => 'John',
                'surname' => 'Doe',
                'degrees' => [
                    ['name' => 'Degree A'],
                ],
                'applicant' => [
                    'name' => 'John',
                ],
            ],
        ]);
        $this->assertCount(1, Applicant::all());
        $this->assertCount(1, Application::all());
        $this->assertCount(2, Applicant::first()->degrees);
        $this->assertCount(1, Applicant::first()->postgraduates);
        $this->assertCount(1, Applicant::first()->doctorates);
        $this->assertCount(1, Applicant::first()->greekLanguages);
        $this->assertCount(1, Applicant::first()->languageSkills);
        $this->assertCount(1, Applicant::first()->computerSkills);
        $this->assertCount(2, Applicant::first()->experiences);
        $this->assertCount(1, Applicant::first()->unemployments);
        $this->assertCount(1, Applicant::first()->multiChildFamilies);
        $this->assertCount(1, Applicant::first()->threeChildFamilies);
        $this->assertCount(1, Applicant::first()->singleParentFamilies);
        $this->assertCount(1, Applicant::first()->minors);
        $this->assertCount(1, Applicant::first()->disabilities);
        $this->assertCount(1, Applicant::first()->familyDisabilities);
    }

    /** @test */
    public function an_auth_user_can_create_an_application_and_consequently_update_the_corresponding_applicant_profile_if_this_preexists()
    {
        $existingApplication = ApplicationFactory::createForContest($this->contest, [
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'protocol_number' => '00000001',
            'street' => 'Old address',
            'degrees' => [
                [
                    'name' => 'Old degree',
                    'issuer' => 'Old university',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Old experience A',
                    'issuer' => 'Old Company A',
                    'months' => 5,
                ],
                [
                    'name' => 'Old experience B',
                    'issuer' => 'Old Company B',
                    'months' => 5,
                ],
            ],
            'minors' => [
                ['id' => '',
                    'amount' => 3,
                    'verified' => false,
                ],
            ],
        ]);

        $this->assertCount(1, Applicant::all());
        $this->assertCount(1, Application::all());
        $this->assertCount(1, Degree::all());
        $this->assertCount(2, Experience::all());
        $this->assertCount(1, Minor::all());

        $response = $this->actingAs($this->user)
            ->post("/contractuals/contests/{$this->contest->id}/applications", $this->validParams($this->contest, [
                'applicant_id' => $existingApplication->applicant_id,
                'name' => $existingApplication->name,
                'street' => 'New address',
                'degrees' => [
                    [
                        'id' => '',                     // old entry (nulified)
                        'name' => 'Old degree',           // Data unchanged
                        'issuer' => 'Old university',
                        'mark' => '8.2',
                        'year' => '2005',
                    ],
                ],
                'experiences' => [
                    [
                        'id' => '',                     // old entry (nulified)
                        'name' => 'Old experience A',     // Data changed
                        'issuer' => 'Old company A',
                        'months' => 12,                     // <-- was 5
                    ],
                    [
                        'id' => '',                     // new entry
                        'name' => 'New experience A',       // new data
                        'issuer' => 'New company A',
                        'months' => 3,
                    ],
                    [
                        'id' => '',                     // new entry
                        'name' => 'New experience B',       // new data
                        'issuer' => 'New company B',
                        'months' => 4,
                    ],
                ],
                'minors' => [
                    [
                        'id' => '',
                        'amount' => 5,
                    ],
                ],
            ]));

        $response->assertStatus(200);

        $response->assertJson([
            'message' => 'Η αίτηση αποθηκεύτηκε.',
            'application' => [
                'street' => 'New address',
                'degrees' => [
                    [
                        'name' => 'Old degree',
                        'issuer' => 'Old university',
                    ],
                ],
                'experiences' => [
                    [
                        'name' => 'Old experience A',
                        'issuer' => 'Old company A',
                        'months' => 12,
                    ],
                    [
                        'name' => 'New experience A',
                        'issuer' => 'New company A',
                        'months' => 3,
                    ],
                    [
                        'name' => 'New experience B',
                        'issuer' => 'New company B',
                        'months' => 4,
                    ],
                ],
                'minors' => [
                    [
                        'amount' => 5,
                    ],
                ],
                'applicant' => [
                    'name' => $existingApplication->name,
                    'street' => 'New address',
                ],
            ],
        ]);

        $newApplication = Application::where('street', 'New address')->first();

        $this->assertCount(1, Applicant::all());
        $this->assertEquals($existingApplication->fresh()->applicant, $newApplication->fresh()->applicant);

        $this->assertCount(2, Application::all());
        $this->assertCount(1, $existingApplication->fresh()->degrees);
        $this->assertCount(2, $existingApplication->fresh()->experiences);
        $this->assertCount(1, $existingApplication->fresh()->minors);
        $this->assertCount(1, $newApplication->fresh()->degrees);
        $this->assertCount(3, $newApplication->fresh()->experiences);
        $this->assertCount(1, $newApplication->fresh()->minors);

        $applicant = $existingApplication->fresh()->applicant;
        $this->assertCount(2, $applicant->fresh()->degrees);
        $this->assertCount(5, $applicant->fresh()->experiences);
        $this->assertCount(2, $applicant->fresh()->minors);

        $this->assertEquals('Old address', $existingApplication->fresh()->street);
        $this->assertEquals('Old degree', $existingApplication->fresh()->degrees->first()->name);
        $this->assertEquals('Old experience A', $existingApplication->fresh()->experiences->first()->name);
        $this->assertEquals('Old experience B', $existingApplication->fresh()->experiences->last()->name);
        $this->assertEquals(3, $existingApplication->fresh()->minors->first()->amount);
    }

    /** @test */
    public function an_auth_user_can_lock_an_application()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => null,
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);
        $this->assertFalse($application->isLocked());

        Passport::actingAs($this->user);
        $response = $this->put("/api/contractuals/applications/{$application->id}/locked-status");

        $response->assertStatus(200);
        $this->assertTrue($application->fresh()->isLocked());
    }

    /** @test */
    public function an_auth_user_can_unlock_an_application_if_evaluations_have_not_been_initialized()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);
        $this->assertTrue($application->isLocked());
        $this->assertFalse($application->hasEvaluations());

        Passport::actingAs($this->user);
        $response = $this->put("/api/contractuals/applications/{$application->id}/locked-status");

        $response->assertStatus(200);
        $this->assertFalse($application->fresh()->isLocked());
    }

    /** @test */
    public function an_auth_user_cannot_unlock_an_application_if_evaluations_have_been_initialized()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);
        $application->initializeEvaluations();
        $this->assertTrue($application->isLocked());
        $this->assertTrue($application->hasEvaluations());

        Passport::actingAs($this->user);
        $response = $this->put("/api/contractuals/applications/{$application->id}/locked-status");

        $response->assertStatus(422);
        $this->assertTrue($application->fresh()->isLocked());
    }

    /** @test */
    public function an_auth_user_can_view_the_edit_form_of_an_unlocked_application()
    {
        $existingApplication = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => null,
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'protocol_number' => '7777777777',
            'street' => 'Old address',
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'Old University',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Old company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
            ],
        ]);

        $response = $this->actingAs($this->user)
            ->get("contractuals/contests/{$this->contest->id}/applications/{$existingApplication->id}/edit");

        $response->assertStatus(200)
            ->assertViewIs('contractuals.application.edit')
            ->assertViewHasAll([
                'contest' => $existingApplication->fresh()->contest()->first()->load([
                    'positions.specialization.specializationType',
                    'positions.requirements.requirementType',
                    'positions.unit',
                ]),
                'applicant' => $existingApplication->fresh()->applicant()->first(),
                'application' => $existingApplication->fresh()->load([
                    'positions.specialization.specializationType',
                    'positions.unit',
                    'degrees.verifications',
                    'postgraduates.verifications',
                    'doctorates.verifications',
                    'greekLanguages.verifications',
                    'languageSkills.verifications',
                    'computerSkills.verifications',
                    'experiences.verifications',
                    'unemployments.verifications',
                    'multiChildFamilies.verifications',
                    'threeChildFamilies.verifications',
                    'singleParentFamilies.verifications',
                    'minors.verifications',
                    'disabilities.verifications',
                    'familyDisabilities.verifications',
                    'degrees.media',
                    'postgraduates.media',
                    'doctorates.media',
                    'greekLanguages.media',
                    'languageSkills.media',
                    'computerSkills.media',
                    'experiences.media',
                    'unemployments.media',
                    'multiChildFamilies.media',
                    'threeChildFamilies.media',
                    'singleParentFamilies.media',
                    'minors.media',
                    'disabilities.media',
                    'familyDisabilities.media',
                ]),
                'formRelatedModels' => [
                    'languages' => Language::all(),
                    'languageLevels' => LanguageLevel::all(),
                ],
            ]);
    }

    /** @test */
    public function an_auth_user_cannot_view_the_edit_form_of_a_locked_application()
    {
        $existingApplication = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'protocol_number' => '7777777777',
            'street' => 'Old address',
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'Old University',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Old company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
            ],
        ]);

        $response = $this->actingAs($this->user)
            ->get("contractuals/contests/{$this->contest->id}/applications/{$existingApplication->id}/edit");

        $response->assertStatus(422);
        $this->assertEquals(
            $response->exception->getMessage(),
            'Application cannot been edited since it is locked'
        );
    }

    /** @test */
    public function an_auth_user_can_update_an_unlocked_application_and_consequently_update_the_corresponding_applicant_profile()
    {
        $this->withoutExceptionHandling();
        $existingApplication = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => null,
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'protocol_number' => '7777777777',
            'street' => 'Old address',
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'Old University',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Old company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
            ],
        ]);
        $existingApplicant = $existingApplication->applicant;
        $this->assertCount(1, $existingApplication->degrees);
        $this->assertCount(1, $existingApplication->experiences);
        $this->assertCount(1, $existingApplicant->degrees);
        $this->assertCount(1, $existingApplicant->experiences);

        $response = $this->actingAs($this->user)
            ->put("contractuals/contests/{$this->contest->id}/applications/{$existingApplication->id}", $this->validParams($this->contest, [
                'applicant_id' => $existingApplicant->id,
                'protocol_number' => '8888888888',
                'street' => 'New address',
                'degrees' => [
                    [
                        'id' => $existingApplication->degrees->first()->id,
                        'name' => 'BSc in archaeology',
                        'issuer' => 'New University',
                        'mark' => '8.2',
                        'year' => '2005',
                    ],
                ],
                'experiences' => [
                    [
                        'id' => $existingApplication->experiences->first()->id,
                        'name' => 'Archaeologist',
                        'issuer' => 'New company',
                        'months' => 6,
                        'started_at' => '2017-01-01',
                        'ended_at' => '2017-06-30',
                    ],
                    [
                        'id' => '',
                        'name' => 'New experience',
                        'issuer' => 'Brand new company',
                        'months' => 6,
                        'started_at' => '2017-07-01',
                        'ended_at' => '2017-12-31',
                    ],
                ],
            ]));

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Η αίτηση ενημερώθηκε.',
            'application' => [
                'id' => $existingApplication->id,
                'protocol_number' => '8888888888',
                'street' => 'New address',
                'degrees' => [
                    [
                        'name' => 'BSc in archaeology',
                        'issuer' => 'New University',
                        'mark' => '8.2',
                        'year' => '2005',
                    ],
                ],
                'experiences' => [
                    [
                        'name' => 'Archaeologist',
                        'issuer' => 'New company',
                        'started_at' => '2017-01-01',
                        'ended_at' => '2017-06-30',
                    ],
                    [
                        'name' => 'New experience',
                        'issuer' => 'Brand new company',
                        'started_at' => '2017-07-01',
                        'ended_at' => '2017-12-31',
                    ],
                ],
                'applicant' => [
                    'id' => $existingApplicant->id,
                    'street' => 'New address',
                ],
            ],
        ]);
        $this->assertCount(1, $existingApplication->fresh()->degrees);
        $this->assertCount(2, $existingApplication->fresh()->experiences);
        $this->assertCount(1, $existingApplicant->fresh()->degrees);
        $this->assertCount(2, $existingApplicant->fresh()->experiences);
    }

    /** @test */
    public function an_auth_user_cannot_update_a_locked_application_and_the_corresponding_applicant_profile_remains_unchanged()
    {
        $existingApplication = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'protocol_number' => '7777777777',
            'street' => 'Old address',
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'Old University',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Old company',
                    'months' => 6,
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
            ],
        ]);
        $existingApplicant = $existingApplication->applicant;

        $response = $this->actingAs($this->user)
            ->put("contractuals/contests/{$this->contest->id}/applications/{$existingApplication->id}", $this->validParams($this->contest, [
                'applicant_id' => $existingApplicant->id,
                'protocol_number' => '8888888888',
                'street' => 'New address',
                'degrees' => [
                    [
                        'id' => $existingApplication->degrees->first()->id,
                        'name' => 'BSc in archaeology',
                        'issuer' => 'New University',
                        'mark' => '8.2',
                        'year' => '2005',
                    ],
                ],
                'experiences' => [
                    [
                        'id' => $existingApplication->experiences->first()->id,
                        'name' => 'Archaeologist',
                        'issuer' => 'New company',
                        'months' => 6,
                        'started_at' => '2017-01-01',
                        'ended_at' => '2017-06-30',
                    ],
                    [
                        'id' => '',
                        'name' => 'New experience',
                        'issuer' => 'Brand new company',
                        'months' => 6,
                        'started_at' => '2017-07-01',
                        'ended_at' => '2017-12-31',
                    ],
                ],
            ]));

        $response->assertStatus(422);
        $this->assertCount(1, Application::all());
        $this->assertEquals(Application::first()->street, $existingApplication->street);
        $this->assertEquals(Application::first()->degrees->first()->issuer, $existingApplication->degrees->first()->issuer);
        $this->assertCount(1, Applicant::all());
        $this->assertEquals(Applicant::first()->street, $existingApplicant->street);
        $this->assertEquals(Applicant::first()->degrees->first()->issuer, $existingApplicant->degrees->first()->issuer);
    }

    /** @test */
    public function an_auth_user_can_delete_qualifications_of_an_unlocked_application_and_remove_the_related_asset_from_applicant_profile_if_it_is_not_referenced_by_other_applications()
    {
        $existingApplication = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => null,
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'protocol_number' => '7777777777',
            'street' => 'Old address',
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'Old University',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Old Company A',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Old Company B',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
            ],
        ]);
        $existingApplicant = $existingApplication->applicant;
        $qualifications = $existingApplication->qualifications;
        $this->assertCount(3, $qualifications);

        $assets = $existingApplication->qualifications->map(function ($qualification) {
            return $qualification->qualifiable;
        });

        $assetToBeDeleted = $assets[2];

        Passport::actingAs($this->user);
        $response = $this->delete("api/contractuals/applications/{$existingApplication->id}/qualifications/{$assetToBeDeleted->type_slug}/{$assetToBeDeleted->id}");

        $response->assertStatus(200);
        $this->assertCount(2, Qualification::all());
        $this->assertCount(1, $existingApplicant->fresh()->experiences);
        $existingApplicant->fresh()->experiences->assertNotContain($assetToBeDeleted);
    }

    /** @test */
    public function an_auth_user_can_delete_qualifications_of_an_unlocked_application_but_keep_the_related_asset_in_applicant_profile_if_it_is_referenced_by_other_applications()
    {
        $contestA = $this->contest;
        $contestB = ContestFactory::create([
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE Archaeologist',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'First degree certificate',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'Experience in excavations',
                                'requirement_type' => 'experience',
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $applicationA = ApplicationFactory::createForContest($contestA, [
            'locked_at' => null,
            'positions' => [
                ['id' => $contestA->positions->first()->id, 'order' => 1],
            ],
            'street' => 'Old address',
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'Old University',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Old Company A',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Old Company B',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
            ],
        ]);

        $applicant = $applicationA->applicant;

        $applicationB = ApplicationFactory::createForContest($contestB, [
            'applicant_id' => $applicant->id,
            'locked_at' => null,
            'street' => 'New Address',
            'positions' => [
                ['id' => $contestB->positions->first()->id, 'order' => 1],
            ],
        ]);
        $applicationB->setQualifications('experiences', $applicationA->experiences);

        $this->assertEquals($applicationA->fresh()->applicant, $applicationB->fresh()->applicant);
        $this->assertCount(3, $applicationA->qualifications);
        $this->assertCount(2, $applicationB->fresh()->qualifications);
        $this->assertEquals(
            $applicationA->fresh()->experiences->pluck('id'),
            $applicationB->fresh()->experiences->pluck('id')
        );
        $this->assertEquals($applicationA->experiences->first()->fresh(), $applicationB->experiences->first()->fresh());

        $commonAsset = $applicationB->experiences->first();

        Passport::actingAs($this->user);
        $response = $this->delete("api/contractuals/applications/{$applicationB->id}/qualifications/{$commonAsset->type_slug}/{$commonAsset->id}");

        $response->assertStatus(200);

        $this->assertCount(1, $applicationB->fresh()->qualifications);
        $this->assertCount(3, $applicationA->fresh()->qualifications);
        $this->assertCount(2, $applicant->fresh()->experiences);
        $applicant->fresh()->experiences->assertContains($commonAsset);
    }

    /** @test */
    public function an_auth_user_cannot_delete_qualifications_of_a_locked_application()
    {
        $existingApplication = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'protocol_number' => '7777777777',
            'street' => 'Old address',
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'Old University',
                    'mark' => '8.2',
                    'year' => '2005',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'Old Company A',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Old Company B',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
            ],
        ]);

        $qualifications = $existingApplication->qualifications;
        $this->assertCount(3, $qualifications);

        $assets = $existingApplication->qualifications->map(function ($qualification) {
            return $qualification->qualifiable;
        });
        $assetType = 'experiences';
        $assetId = $assets[2]->id;

        Passport::actingAs($this->user);
        $response = $this->delete("api/contractuals/applications/{$existingApplication->id}/qualifications/{$assetType}/{$assetId}");

        $response->assertStatus(422);
        $this->assertEquals(
            $response->exception->getMessage(),
            'Qualification cannot be removed because application is locked'
        );
        $this->assertCount(3, $qualifications);
    }

    /**
     * Valid POST params.
     * @param $existedContest - A contest must already exist
     * @param array $overrides
     * @return array
     */
    private function validParams(Contest $existedContest, $overrides = [])
    {
        return array_merge([
            'applicant_id' => '',
            'contest_id' => $existedContest->id,
            'applicant_category' => $existedContest->positions->first()->specialization->specializationType->id,
            'positions' => [
                ['id' => $existedContest->positions->first()->id, 'order' => 1],
            ],
            'protocol_number' => 'ΥΠΠΟΑ/ΓΔΑΔΗΔ/ΔΗΔ/123456',
            'protocol_date' => '2017-05-20',
            'submitted_at' => '2017-05-21',
            'healthy' => true,
            'unrestricted' => true,
            'invalidated' => false,
            'invalidation_description' => '',
            //            'score' => '',
            'name' => 'John',
            'surname' => 'Doe',
            'fathername' => 'Jack',
            'mothername' => 'Jane',
            'birthdate' => '1980-02-01',
            'policeid_number' => 'AB123456',
            'policeid_date' => '2000-03-25',
            'afm' => '114865648',
            'doy' => 'IG Athens',
            'amka' => '01118102258',
            'street' => 'Example str.',
            'street_number' => '23',
            'postcode' => '12345',
            'city' => 'Athens',
            'phonenumber1' => '**********',
            'phonenumber2' => '**********',
            'email' => '<EMAIL>',
            'eu_citizen' => false,
            'greek_nationality' => true,
            'degrees' => [],
            'postgraduates' => [],
            'doctorates' => [],
            'experiences' => [],
            'language_skills' => [],
            'computer_skills' => [],
            'greek_languages' => [['id' => '', 'name' => '', 'issuer' => '', 'level' => '', 'verified' => false]],
            'unemployments' => [['id' => '', 'months' => 0, 'verified' => false]],
            'multi_child_families' => [['id' => '', 'children' => 0, 'siblings' => 0, 'verified' => false]],
            'three_child_families' => [['id' => '', 'children' => 0, 'siblings' => 0, 'verified' => false]],
            'single_parent_families' => [['id' => '', 'children' => 0, 'siblings' => 0, 'verified' => false]],
            'disabilities' => [['id' => '', 'percentage' => 0, 'verified' => false]],
            'family_disabilities' => [['id' => '', 'percentage' => 0, 'verified' => false]],
            'minors' => [['id' => '', 'amount' => 0, 'verified' => false]],
        ], $overrides);
    }
}
