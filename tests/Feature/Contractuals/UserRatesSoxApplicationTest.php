<?php

namespace Tests\Feature\Contractuals;

use App\Models\Contractuals\ContestType;
use Carbon\Carbon;
use Database\CustomFactories\Contractuals\ApplicationFactory;
use Database\CustomFactories\Contractuals\ContestFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use Tests\ContractualsTestCase;
use Tests\Helpers\Contractuals\EvaluationMappings;

class UserRatesSoxApplicationTest extends ContractualsTestCase
{
    use EvaluationMappings, RefreshDatabase;

    protected $contest;
    protected $contestType;

    protected function setUp() : void
    {
        parent::setUp();

        $this->contestType = factory(ContestType::class)->create(['id' => 1, 'name' => 'SOX']);

        $this->contest = ContestFactory::create([
            'type_id' => $this->contestType->id,
            'positions' => [
                [
                    'amount' => 2,
                    'location' => 'Athens',
                    'specialization' => 'PE',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'High Degree A',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'High Experience A',
                                'requirement_type' => 'experience',
                            ],
                        ],
                        1 => [
                            [
                                'name' => 'Low Degree A',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'Low Experience A',
                                'requirement_type' => 'experience',
                            ],
                            [
                                'name' => 'ECDL A',
                                'requirement_type' => 'computerSkills',
                            ],
                        ],
                    ],
                ],
                [
                    'amount' => 3,
                    'location' => 'Thessaloniki',
                    'specialization' => 'PE',
                    'requirements' => [
                        0 => [
                            [
                                'name' => 'High Degree B',
                                'requirement_type' => 'degree',
                            ],
                            [
                                'name' => 'Experience B',
                                'requirement_type' => 'experience',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }

    /** @test */
    public function an_auth_user_cannot_rate_an_application_if_evaluations_have_not_been_initialized()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'BSc in archaeology',
                    'issuer' => 'University of Athens',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Archaeologist',
                    'issuer' => 'New company',
                    'started_at' => '2017-01-01',
                    'ended_at' => '2017-06-30',
                ],
                [
                    'name' => 'Byzantinologist',
                    'issuer' => 'Company C',
                    'started_at' => '2017-07-01',
                    'ended_at' => '2017-12-31',
                ],
            ],
        ]);
        $this->assertFalse($application->isRated());
        $this->assertFalse($application->hasEvaluations());

        Passport::actingAs($this->user);
        $response = $this->post('/api/contractuals/rated-applications', ['application_id' => $application->id]);

        $response->assertStatus(422);
        $this->assertFalse($application->fresh()->isRated());
    }

    /** @test */
    public function an_application_that_does_not_meet_global_requirements_is_rejected_when_an_auth_user_rates_application()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'healthy' => false,
            'unrestricted' => true,
            'invalidated' => true,
            'eu_citizen' => false,
            'greek_nationality' => false,
            'birthdate' => Carbon::now()->subYears(66),
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'unemployments' => [
                [
                    'id' => '',
                    'months' => 4,
                ],
            ],
        ]);

        $application->initializeEvaluations();

        $application->fresh()->positions->each(function ($position) {
            $this->assertNull($position->pivot->rejected);
        });

        Passport::actingAs($this->user);
        $response = $this->post('/api/contractuals/rated-applications', [
            'application_id' => $application->id,
            'contest_type' => $this->contest->type_id,
        ]);

        $response->assertJson([
            'id' => $application->id,
            'rejected' => true,
            'positions' => [
                [
                    'id' => $this->contest->positions->first()->id,
                    'pivot' => [
                        'rejected' => 1,
                    ],
                ],
            ],
        ]);
    }

    /** @test */
    public function an_application_that_does_not_meet_position_requirements_is_rejected_for_this_position()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'healthy' => true,
            'unrestricted' => false,
            'invalidated' => false,
            'eu_citizen' => true,
            'greek_nationality' => true,
            'birthdate' => Carbon::now()->subYears(30),
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'Degree A',
                    'mark' => '7.71',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Experience A',
                    'months' => 25,
                ],
                [
                    'name' => 'Experience B',
                    'months' => 36,
                ],
            ],
        ]);

        $application->initializeEvaluations();

        $positionA = $this->contest->positions->first();

        $requirementA = $this->findRequirement('High Degree A', $positionA);
        $requirementB = $this->findRequirement('High Experience A', $positionA);

        $evaluationA = $this->findEvaluationByName('Degree A', $positionA);
        $evaluationB = $this->findEvaluationByName('Experience A', $positionA);
        $evaluationC = $this->findEvaluationByName('Experience B', $positionA);

        $this->mapEvaluations([
            ['evaluation' => $evaluationA, 'requirement' => $requirementA, 'relevant' => null],
        ], $application);

        $this->assertNull($application->positions->first()->pivot->rejected);

        Passport::actingAs($this->user);
        $response = $this->post('/api/contractuals/rated-applications', [
            'application_id' => $application->id,
            'contest_type' => $this->contest->type_id,
        ]);

        $response->assertJson([
            'id' => $application->id,
            'rejected' => null,
            'positions' => [
                [
                    'id' => $this->contest->positions->first()->id,
                    'pivot' => [
                        'rejected' => 1,
                    ],
                ],
            ],
        ]);
    }

    /** @test */
    public function an_application_that_meets_position_requirements_can_be_rated_for_this_position()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'healthy' => true,
            'unrestricted' => false,
            'invalidated' => false,
            'eu_citizen' => true,
            'greek_nationality' => true,
            'birthdate' => Carbon::now()->subYears(30),
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'Degree A',
                    'mark' => 7.71,
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Experience A',
                    'months' => 25,
                ],
                [
                    'name' => 'Experience B',
                    'months' => 36,
                ],
            ],
            'unemployments' => [
                [
                    'months' => 6,
                ],
            ],
            'multiChildFamilies' => [
                [
                    'children' => 4,
                    'siblings' => 5,
                ],
            ],
            'threeChildFamilies' => [
                [
                    'children' => false,
                    'siblings' => true,
                ],
            ],
            'singleParentFamilies' => [
                [
                    'children' => 4,
                    'siblings' => 3,
                ],
            ],
            'minors' => [
                [
                    'amount' => 3,
                ],
            ],
            'disabilities' => [
                [
                    'percentage' => 65,
                ],
            ],
            'familyDisabilities' => [
                [
                    'percentage' => 87,
                ],
            ],
        ]);
        $application->initializeEvaluations();

        $positionA = $this->contest->positions->first();

        $requirementA = $this->findRequirement('High Degree A', $positionA);
        $requirementB = $this->findRequirement('High Experience A', $positionA);

        $evaluationA = $this->findEvaluationByName('Degree A', $positionA);
        $evaluationB = $this->findEvaluationByName('Experience A', $positionA);
        $evaluationC = $this->findEvaluationByName('Experience B', $positionA);
//        $evaluationD = $this->findEvaluationByType('unemployments', $positionA)->first();

        $this->mapEvaluations([
            ['evaluation' => $evaluationA, 'requirement' => $requirementA, 'relevant' => null],
            ['evaluation' => $evaluationB, 'requirement' => $requirementB, 'relevant' => null],
            ['evaluation' => $evaluationC, 'requirement' => $requirementB, 'relevant' => null],
        ], $application);

        $this->assertNull($application->positions()->first()->pivot->rejected);

        Passport::actingAs($this->user);
        $response = $this->post('/api/contractuals/rated-applications', [
            'application_id' => $application->id,
            'contest_type' => $this->contest->type_id,
        ]);

        $response->assertJson([
            'id' => $application->id,
            'rejected' => null,
            'positions' => [
                [
                    'id' => $this->contest->positions->first()->id,
                    'pivot' => [
                        'rejected' => null,
                        'score' => 2127.40,
                    ],
                ],
            ],
        ]);
    }

    /** @test */
    public function an_auth_user_can_clear_application_rating()
    {
        $application = ApplicationFactory::createForContest($this->contest, [
            'healthy' => true,
            'unrestricted' => false,
            'invalidated' => false,
            'eu_citizen' => true,
            'greek_nationality' => true,
            'birthdate' => Carbon::now()->subYears(30),
            'locked_at' => Carbon::now(),
            'positions' => [
                ['id' => $this->contest->positions->first()->id, 'order' => 1],
            ],
            'degrees' => [
                [
                    'name' => 'Degree A',
                    'mark' => '7.71',
                ],
            ],
            'experiences' => [
                [
                    'name' => 'Experience A',
                    'months' => 25,
                ],
                [
                    'name' => 'Experience B',
                    'months' => 36,
                ],
            ],
        ]);
        $application->initializeEvaluations();

        $position = $this->contest->positions->first();

        $requirementA = $this->findRequirement('Low Degree A', $position);
        $requirementB = $this->findRequirement('Low Experience A', $position);
        $requirementC = $this->findRequirement('ECDL A', $position);

        $evaluationA = $this->findEvaluationByName('Degree A', $position);
        $evaluationB = $this->findEvaluationByName('Experience A', $position);
        $evaluationC = $this->findEvaluationByName('Experience B', $position);

        // Mock application evaluation
        $this->mapEvaluations([
            ['evaluation' => $evaluationA, 'requirement' => $requirementA, 'relevant' => null],
            ['evaluation' => $evaluationB, 'requirement' => $requirementB, 'relevant' => null],
            ['evaluation' => $evaluationC, 'requirement' => $requirementC, 'relevant' => null],
        ], $application);

        // Mock the application rating
        $application->rejected = true;
        $application->rated_at = Carbon::now();
        $application->save();
        $application->positions()->updateExistingPivot($position->id, [
            'rejected' => true,
            'score' => 666.66,
        ]);
        $evaluationA->update(['points' => 100.00]);
        $evaluationB->update(['points' => 200.00]);
        $evaluationC->update(['points' => 300.00]);

        $this->assertNotEquals(0.00, $evaluationA->points);
        $this->assertNotEquals(0.00, $evaluationB->points);
        $this->assertNotEquals(0.00, $evaluationC->points);
        $this->assertNotNull($application->fresh()->positions()->first()->pivot->score);
        $this->assertNotNull($application->fresh()->rejected);
        $this->assertNotNull($application->fresh()->rated_at);
        $this->assertNotNull($application->fresh()->positions()->first()->pivot->rejected);

        Passport::actingAs($this->user);
        $response = $this->delete("/api/contractuals/rated-applications/$application->id");

        $this->assertEquals(0.00, $evaluationA->fresh()->points);
        $this->assertEquals(0.00, $evaluationB->fresh()->points);
        $this->assertEquals(0.00, $evaluationC->fresh()->points);
        $this->assertNull($application->fresh()->positions()->first()->pivot->score);
        $this->assertNull($application->fresh()->rejected);
        $this->assertNull($application->fresh()->rated_at);
        $this->assertNull($application->fresh()->positions()->first()->pivot->rejected);
    }
}
