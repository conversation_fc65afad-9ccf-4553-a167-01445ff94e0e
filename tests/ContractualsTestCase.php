<?php

namespace Tests;

use App\Models\Role;
use App\Models\User;

abstract class ContractualsTestCase extends TestCase
{
    public array $connectionsToTransact = ['mysql_main', 'mysql_contractuals'];

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Due to the global scope applied in Contest model, it is required to have an authorized
        // user exist in the database. Furthermore we assign the "contractuals.admin" role to
        // this user in order to bypass the global scope
        $this->user = factory(User::class)->create();
        $this->user->roles()->attach(factory(Role::class)->create(['name' => 'contractuals.admin']));
        $this->signIn($this->user);
    }
}
