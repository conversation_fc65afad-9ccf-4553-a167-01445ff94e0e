<?php

namespace Tests\Helpers\Contractuals;

use App\Models\Contractuals\Position;

trait EvaluationMappings
{
    public function mapEvaluations($mappings, $application)
    {
        if (collect($mappings)->pluck('requirement.pivot.auxiliary_level')->unique()->count() > 1) {
            throw new \Exception('[TESTING] Mapped requirements must be of the same auxiliary_level');
        }
        $auxiliaryLevel = collect($mappings)->pluck('requirement.pivot.auxiliary_level')->unique()->first();

        if (collect($mappings)->pluck('evaluation.position_id')->unique()->count() > 1) {
            throw new \Exception('[TESTING] Mapped evaluations must be of the same position');
        }
        $positionId = collect($mappings)->pluck('evaluation.position_id')->unique()->first();

        // - updating evaluation.requirement_id
        // - updating evaluation.relevant
        // - updating evaluation.auxiliary_level
        foreach ($mappings as $mapping) {
            $mapping['evaluation']->update(
                [
                    'requirement_id'  => $mapping['requirement'] ? $mapping['requirement']->id : null,
                    'relevant'        => $mapping['relevant'],
                    'auxiliary_level' => $auxiliaryLevel,
                ]
            );
        }

        // - updating application_postion auxiliary_level
        $application->positions()->updateExistingPivot($positionId, ['auxiliary_level' => $auxiliaryLevel]);
    }

    public function findEvaluationByType($type, Position $position)
    {
        $evaluation = $position->evaluations()->get()->filter(function ($evaluation) use ($type) {
            return $evaluation->isOfType($type);
        });

        if (null === $evaluation) {
            throw new \Exception('Could not find the evaluation by qualification type');
        }

        return $evaluation;
    }

    public function findEvaluationByName($name, Position $position)
    {
        $evaluation = $position->evaluations()->with('qualification.qualifiable')->get()->first(function ($evaluation) use ($name) {
            return $evaluation->qualification->qualifiable->name === $name;
        });

        if (null === $evaluation) {
            throw new \Exception('Could not find the evaluation by qualification name');
        }

        return $evaluation;
    }

    public function findRequirement($name, Position $position)
    {
        $requirement = $position->requirements()->where('name', $name)->first();

        if (null === $requirement) {
            throw new \Exception('Could not find the requirement by name');
        }

        return $requirement;
    }
}
